## :warning: WARNING :warning:

### Our documentation has moved to a [new site](https://www.tradingview.com/charting-library-docs/)

You can find the corresponding page here: [Chart Style Properties](https://www.tradingview.com/charting-library-docs/latest/customization/styles/Chart-Style-Properties)

---

<br/>
<br/>

## Chart Style Properties

Every chart style has different set of properties. Here you can find a list of properties for chart styles and their types.

To change properties, use [Series Api](./Series-Api).

### Bar

- `upColor` (`string`)
- `downColor` (`string`)
- `barColorsOnPrevClose` (`boolean`)
- `dontDrawOpen` (`boolean`)
- `thinBars` (`boolean`)

### Candle

- `upColor` (`string`)
- `downColor` (`string`)
- `drawWick` (`boolean`)
- `drawBorder` (`boolean`)
- `borderColor` (`string`)
- `borderUpColor` (`string`)
- `borderDownColor` (`string`)
- `wickColor` (`string`)
- `wickUpColor` (`string`)
- `wickDownColor` (`string`)
- `barColorsOnPrevClose` (`boolean`)

### Line

- `color` (`string`)
- `linestyle` (`number`)
- `linewidth` (`number`)
- `styleType` (`number`)

### Area

- `color1` (`string`)
- `color2` (`string`)
- `linecolor` (`string`)
- `linestyle` (`number`)
- `linewidth` (`number`)
- `transparency` (`number`)

### Column

- `upColor` (`string`)
- `downColor` (`string`)
- `barColorsOnPrevClose` (`boolean`)

### Heikin Ashi

- `upColor` (`string`)
- `downColor` (`string`)
- `drawWick` (`boolean`)
- `drawBorder` (`boolean`)
- `borderColor` (`string`)
- `borderUpColor` (`string`)
- `borderDownColor` (`string`)
- `wickColor` (`string`)
- `wickUpColor` (`string`)
- `wickDownColor` (`string`)
- `showRealLastPrice` (`boolean`)
- `barColorsOnPrevClose` (`boolean`)

### Hollow Candle

- `upColor` (`string`)
- `downColor` (`string`)
- `drawWick` (`boolean`)
- `drawBorder` (`boolean`)
- `borderColor` (`string`)
- `borderUpColor` (`string`)
- `borderDownColor` (`string`)
- `wickColor` (`string`)
- `wickUpColor` (`string`)
- `wickDownColor` (`string`)

### Baseline

- `topFillColor1` (`string`)
- `topFillColor2` (`string`)
- `bottomFillColor1` (`string`)
- `bottomFillColor2` (`string`)
- `topLineColor` (`string`)
- `bottomLineColor` (`string`)
- `baselineColor` (`string`)
- `topLineWidth` (`number`)
- `bottomLineWidth` (`number`)
- `transparency` (`number`)
- `baseLevelPercentage` (`number`)

### Hi Lo

- `color` (`string`)
- `showBorders` (`boolean`)
- `borderColor` (`string`)
- `showLabels` (`boolean`)
- `labelColor` (`string`)

### Renko

:chart: available in Trading Terminal

- `upColor` (`string`)
- `downColor` (`string`)
- `borderUpColor` (`string`)
- `borderDownColor` (`string`)
- `upColorProjection` (`string`)
- `downColorProjection` (`string`)
- `borderUpColorProjection` (`string`)
- `borderDownColorProjection` (`string`)
- `wickUpColor` (`string`)
- `wickDownColor` (`string`)

### Kagi

:chart: available in Trading Terminal

- `upColor` (`string`)
- `downColor` (`string`)
- `upColorProjection` (`string`)
- `downColorProjection` (`string`)

### Point & Figure

:chart: available in Trading Terminal

- `upColor` (`string`)
- `downColor` (`string`)
- `upColorProjection` (`string`)
- `downColorProjection` (`string`)

### Line Break

:chart: available in Trading Terminal

- `upColor` (`string`)
- `downColor` (`string`)
- `borderUpColor` (`string`)
- `borderDownColor` (`string`)
- `upColorProjection` (`string`)
- `downColorProjection` (`string`)
- `borderUpColorProjection` (`string`)
- `borderDownColorProjection` (`string`)

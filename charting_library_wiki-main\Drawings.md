## :warning: WARNING :warning:

### Our documentation has moved to a [new site](https://www.tradingview.com/charting-library-docs/)

You can find the corresponding page here: [Drawings Toolbar
](https://www.tradingview.com/charting-library-docs/latest/ui_elements/Drawings)

---

<br/>
<br/>

## Drawings Toolbar

You can hide some drawings from the toolbar or add custom restrictions for applying them to the chart.

For more information see [drawings_access](Widget-Constructor#drawings_access)

## Styles (colors, visibility of elements etc.)

### Change default properties of drawings

See [overrides](Widget-Constructor#overrides) in the Widget Constructor

### Change the properties on the fly

See [applyStudiesOverrides(overrides)](Widget-Methods#applyoverridesoverrides) in the Widget Methods

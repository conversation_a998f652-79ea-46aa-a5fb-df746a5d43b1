## :warning: WARNING :warning:

### Our documentation has moved to a [new site](https://www.tradingview.com/charting-library-docs/)

---

<br/>
<br/>

### What is Charting Library

Charting library is a standalone solution that you download, host on your servers, connect your own data & use in your site/app for free.

### What to do

1. `git clone` your [copy of Charting Library](https://github.com/tradingview/charting_library) and then [run it](Running-Your-Charting-Library). You will get a working example of our chart running on your host.

2. Plug your data into Charting Library using [one of our APIs](Connecting-Data).

3. Customize your charts (optional).

### Integration Examples

We’ve got a [public GitHub repository](https://github.com/tradingview/charting-library-examples) that includes examples of Charting Library integration using different Web Frameworks.

### Trading Terminal

:chart: Trading Terminal is a ready-to-use product for those who want to have a great charting solution along with the ability to trade right from the chart. [Read more](Trading-Terminal).

### Best Practices

Reading the article with [Best Practices](Best-Practices) will prevent you from making the most common mistakes and will **save your time**. If you have a question, you can give a try to our thoroughly prepared [FAQ](Frequently-Asked-Questions) or [ask the community](https://github.com/tradingview/charting_library/issues/).

### Using the library with online code playgrounds

Get started with our library on a code playground / editor like JSFiddle by following [this guide](Online-Editors). Provided starter templates allow you to quickly create your own examples, produce bug reproductions, and experiment with the library.

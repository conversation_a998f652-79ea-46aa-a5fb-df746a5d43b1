## :warning: WARNING :warning:

### Our documentation has moved to a [new site](https://www.tradingview.com/charting-library-docs/)

You can find the corresponding page here: [Legend](https://www.tradingview.com/charting-library-docs/latest/ui_elements/Legend)

---

<br/>
<br/>

Legend is a list of series and indicators at the top of the chart.

### Formatting

Prices are formatted according to the symbol information - [minmov, pricescale, minmove2, fractional, variableMinTick](Symbology#minmov-pricescale-minmove2-fractional-variablemintick).

You can also apply custom formatting using [numeric_formatting](Widget-Constructor#numeric_formatting).

### Visibility

You can hide the legend widget using [legend_widget](Featuresets) featureset. Edit buttons can be also hidden using [featuresets](Featuresets) `edit_buttons_in_legend`, `show_hide_button_in_legend` etc.

Particular legend elements can be displayed or hidden by default using the [overrides](Widget-Constructor#overrides).

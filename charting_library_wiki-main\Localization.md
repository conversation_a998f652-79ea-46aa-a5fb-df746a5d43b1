## :warning: WARNING :warning:

### Our documentation has moved to a [new site](https://www.tradingview.com/charting-library-docs/)

You can find the corresponding page here: [Localization](https://www.tradingview.com/charting-library-docs/latest/core_concepts/Localization)

---

<br/>
<br/>

The Charting Library supports localization. It has been translated to a number of languages already. All you have to do to change your Charting Library language is to specify `locale` parameter when creating the Library widget.

Our translations are crowd-sourced so everyone can participate. Visit [our page on WebTranslateIt](https://webtranslateit.com/en/projects/11203-TradingView) to participate.

Supported languages:

Language | `locale` argument value
---|---
Arabic|ar
Chinese (Taiwan)|zh_TW
Chinese|zh
Czech|cs
Danish (Denmark)|da_DK
Dutch (Netherlands)|nl_NL
English|en
Estonian (Estonia)|et_EE
French|fr
German (Germany)|de
Greek|el
Hebrew (Israel)|he_IL
Hungarian (Hungary)|hu_HU
Indonesian (Indonesia)|id_ID
Italian|it
Japanese|ja
Korean|ko
Malay (Malaysia)|ms_MY
Norwegian Bokmål|no
Persian (Iran)|fa
Polish|pl
Portuguese|pt
Romanian|ro
Russian|ru
Slovak (Slovakia)|sk_SK
Spanish|es
Swedish|sv
Thai (Thailand)|th
Turkish|tr
Vietnamese|vi

## :warning: WARNING :warning:

### Our documentation has moved to a [new site](https://www.tradingview.com/charting-library-docs/)

You can find the corresponding page here: [Snapshots](https://www.tradingview.com/charting-library-docs/latest/ui_elements/Snapshots)

---

<br/>
<br/>

The Charting Library allows users to take snapshots. When a user clicks the snapshot button, the library saves the snapshot on the server and provides the URL to the captured image.

TradingView allows you to save snapshots to its servers. But you can [change this](Widget-Constructor#snapshot_url) if you wish.

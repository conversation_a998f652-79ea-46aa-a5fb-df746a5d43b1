## :warning: WARNING :warning:

### Our documentation has moved to a [new site](https://www.tradingview.com/charting-library-docs/)

You can find the corresponding page here: [Toolbars](https://www.tradingview.com/charting-library-docs/latest/ui_elements/Toolbars)

---

<br/>
<br/>

### Buttons

You can add your own buttons to the top toolbar.

[createButton(options)](Widget-Methods#createbuttonoptions)
[createDropdown(options)](Widget-Methods#createdropdownoptions)

### Styles

The Charting Library has 2 predifined color themes. They are optimal for displaying the charts. However, since version 16 you can change the colors of the toolbars to perfectly fit to your background. You can see an example of the customization in [themes.html](https://github.com/tradingview/charting_library/blob/master/themed.html).

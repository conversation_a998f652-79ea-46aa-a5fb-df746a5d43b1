#### Getting Started

* [Overview](Home)
* [Package Content](Package-Content)
* [First Start](Running-Your-Charting-Library)
* [Best Practices](Best-Practices)
* [FAQ](Frequently-Asked-Questions)
* [Shortcuts](Shortcuts)
* [Breaking changes](Breaking-Changes)

***

#### Connecting Data

* [Overview](Connecting-Data)
* [JS API](JS-Api)
* [UDF](UDF)
* [Symbology](Symbology)
* [Trading Sessions](Trading-Sessions)
* [Quotes](Quotes)

***

#### Customization

API Reference

* [Widget Constructor](Widget-Constructor)
* [Widget Methods](Widget-Methods)
* [Chart Methods](Chart-Methods)
* [Featuresets](Featuresets)

Customisation by areas

* [Global](Global)
* [Toolbars](Toolbars)
* [Chart](Chart)
* [Symbol Search](Symbol-Search)
* [Legend](Legend)
* [Price Scale](Price-Scale)
* [Time Scale](Time-Scale)
* [Marks](Marks)
* [Indicators](Indicators)
* [Snapshots](Snapshots)
* [Drawings](Drawings)
* [Watchlist](Watch-List)
* [News](News)

[Saving/Loading Charts](Saving-and-Loading-Charts)

Creating Custom Studies

* [Overview](Creating-Custom-Studies)
* [Metainfo](Custom-Studies-Metainfo)
* [Constructor](Custom-Studies-Constructor)
* [Pine JS functions](PineJS-Utility-Functions)
* [Examples](Custom-Studies-Examples)
* [Typescript Examples](Custom-Studies-Typescript-Examples)

***

#### Trading Terminal

* [Brief Intro](Trading-Terminal)
* [Broker API](Broker-API)
* [Trading Host](Trading-Host)
* [Account Manager](Account-Manager)
* [Trading Types](Trading-Objects-and-Constants)
* [Drawing Templates](Drawing-Templates)

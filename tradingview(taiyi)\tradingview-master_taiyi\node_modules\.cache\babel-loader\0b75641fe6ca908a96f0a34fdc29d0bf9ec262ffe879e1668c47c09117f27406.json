{"ast": null, "code": "import { getSymbolHistories } from \"../api\";\nimport { formatTime } from \"../utils\";\n\n// EGX Stock symbols mapping\nconst EGX_STOCKS = {\n  \"EGX:CIB\": {\n    name: \"Commercial International Bank\",\n    exchange: \"EGX\"\n  },\n  \"EGX:ETEL\": {\n    name: \"Egyptian Company for Mobile Services\",\n    exchange: \"EGX\"\n  },\n  \"EGX:HRHO\": {\n    name: \"Hassan Allam Holding\",\n    exchange: \"EGX\"\n  },\n  \"EGX:SWDY\": {\n    name: \"El Sewedy Electric Company\",\n    exchange: \"EGX\"\n  },\n  \"EGX:OCDI\": {\n    name: \"Orascom Construction Industries\",\n    exchange: \"EGX\"\n  }\n};\nconst DataFeed = {\n  onReady: callback => {\n    console.log(\"[onReady]: EGX DataFeed initialized\");\n    const config = {\n      supported_resolutions: [\"1\", \"5\", \"15\", \"30\", \"60\", \"D\", \"W\", \"M\"],\n      supports_group_request: false,\n      supports_marks: false,\n      supports_search: true,\n      supports_timescale_marks: false\n    };\n    setTimeout(() => callback(config));\n  },\n  searchSymbols: (userInput, exchange, symbolType, onResultReadyCallback) => {\n    console.log(\"[searchSymbols]: Searching EGX stocks for:\", userInput);\n    const results = Object.keys(EGX_STOCKS).filter(symbol => symbol.toLowerCase().includes(userInput.toLowerCase()) || EGX_STOCKS[symbol].name.toLowerCase().includes(userInput.toLowerCase())).map(symbol => ({\n      symbol: symbol,\n      full_name: EGX_STOCKS[symbol].name,\n      description: EGX_STOCKS[symbol].name,\n      exchange: \"EGX\",\n      ticker: symbol,\n      type: \"stock\"\n    }));\n    onResultReadyCallback(results);\n  },\n  resolveSymbol: async (symbolName, onSymbolResolvedCallback) => {\n    console.log(\"[resolveSymbol]: Resolving EGX symbol\", symbolName);\n    const stockInfo = EGX_STOCKS[symbolName];\n    const description = stockInfo ? stockInfo.name : symbolName;\n    const symbolInfo = {\n      description: description,\n      name: symbolName,\n      ticker: symbolName,\n      session: \"0930-1530\",\n      // EGX trading hours (9:30 AM - 3:30 PM Cairo time)\n      timezone: \"Africa/Cairo\",\n      type: \"stock\",\n      exchange: \"EGX\",\n      has_intraday: true,\n      has_daily: true,\n      has_weekly_and_monthly: true,\n      minmov: 1,\n      minmove2: 0,\n      fractional: false,\n      currency_code: \"EGP\",\n      pricescale: 100,\n      // 2 decimal places for EGP\n      supported_resolutions: [\"1\", \"5\", \"15\", \"30\", \"60\", \"D\", \"W\", \"M\"]\n    };\n    onSymbolResolvedCallback(symbolInfo);\n  },\n  getBars: async (symbolInfo, resolution, periodParams, onHistoryCallback, onErrorCallback) => {\n    console.log(\"[getBars]: Getting EGX data for\", symbolInfo.name);\n    const symbol = symbolInfo.name;\n    const {\n      from,\n      to\n    } = periodParams;\n    if (symbol) {\n      try {\n        var _data$t;\n        const {\n          data,\n          statusCode\n        } = await getSymbolHistories({\n          resolution,\n          symbol,\n          to,\n          from\n        });\n        console.log(\"[getBars]: EGX API Response\");\n        console.table({\n          symbol: symbol,\n          from: formatTime(from, \"YYYYMMDD\"),\n          to: formatTime(to, \"YYYYMMDD\"),\n          resolution,\n          \"data points\": data === null || data === void 0 ? void 0 : (_data$t = data.t) === null || _data$t === void 0 ? void 0 : _data$t.length\n        });\n        if (statusCode !== 200 || !(data !== null && data !== void 0 && data.t) || data.t.length === 0) {\n          console.log(\"[getBars]: No data available\");\n          onHistoryCallback([], {\n            noData: true\n          });\n          return;\n        }\n        const {\n          l,\n          h,\n          o,\n          c,\n          t\n        } = data;\n        const bars = t === null || t === void 0 ? void 0 : t.map((timestamp, index) => ({\n          time: timestamp * 1000,\n          // Convert to milliseconds\n          low: l[index],\n          high: h[index],\n          open: o[index],\n          close: c[index]\n        }));\n\n        // Sort bars by time\n        bars.sort((a, b) => a.time - b.time);\n        console.log(`[getBars]: Returning ${bars.length} bars for ${symbol}`);\n        onHistoryCallback(bars, {\n          noData: false\n        });\n      } catch (error) {\n        console.error(\"[getBars]: Error fetching EGX data\", error);\n        const errorMessage = error instanceof Error ? error.message : String(error);\n        onErrorCallback(errorMessage);\n      }\n    }\n  },\n  subscribeBars: (symbolInfo, resolution, onRealtimeCallback, subscriberUID) => {\n    console.log(`[subscribeBars]: Subscribing to real-time data for ${symbolInfo.name}`);\n    // TODO: Implement real-time EGX data subscription\n    // For now, just log the subscription\n  },\n  unsubscribeBars: subscriberUID => {\n    console.log(`[unsubscribeBars]: Unsubscribing ${subscriberUID}`);\n    // TODO: Implement real-time data unsubscription\n  }\n};\nexport default DataFeed;", "map": {"version": 3, "names": ["getSymbolHistories", "formatTime", "EGX_STOCKS", "name", "exchange", "DataFeed", "onReady", "callback", "console", "log", "config", "supported_resolutions", "supports_group_request", "supports_marks", "supports_search", "supports_timescale_marks", "setTimeout", "searchSymbols", "userInput", "symbolType", "onResultReadyCallback", "results", "Object", "keys", "filter", "symbol", "toLowerCase", "includes", "map", "full_name", "description", "ticker", "type", "resolveSymbol", "symbolName", "onSymbolResolvedCallback", "stockInfo", "symbolInfo", "session", "timezone", "has_intraday", "has_daily", "has_weekly_and_monthly", "<PERSON><PERSON>v", "minmove2", "fractional", "currency_code", "pricescale", "getBars", "resolution", "periodParams", "onHistoryCallback", "onError<PERSON>allback", "from", "to", "_data$t", "data", "statusCode", "table", "t", "length", "noData", "l", "h", "o", "c", "bars", "timestamp", "index", "time", "low", "high", "open", "close", "sort", "a", "b", "error", "errorMessage", "Error", "message", "String", "subscribeBars", "onRealtimeCallback", "subscriberUID", "unsubscribeBars"], "sources": ["D:/trading-view-charting-library/tradingview(taiyi)/tradingview-master_taiyi/src/tradingview/datafeed.ts"], "sourcesContent": ["import { OnReadyCallback, LibrarySymbolInfo, ResolutionString, PeriodParams, HistoryCallback, Bar } from \"@/public/charting_library\";\r\nimport { getSymbolHistories } from \"../api\";\r\nimport { formatTime } from \"../utils\";\r\n\r\n// EGX Stock symbols mapping\r\nconst EGX_STOCKS = {\r\n  \"EGX:CIB\": { name: \"Commercial International Bank\", exchange: \"EGX\" },\r\n  \"EGX:ETEL\": { name: \"Egyptian Company for Mobile Services\", exchange: \"EGX\" },\r\n  \"EGX:HRHO\": { name: \"Hassan Allam Holding\", exchange: \"EGX\" },\r\n  \"EGX:SWDY\": { name: \"El Sewedy Electric Company\", exchange: \"EGX\" },\r\n  \"EGX:OCDI\": { name: \"Orascom Construction Industries\", exchange: \"EGX\" },\r\n};\r\n\r\nconst DataFeed = {\r\n  onReady: (callback: OnReadyCallback) => {\r\n    console.log(\"[onReady]: EGX DataFeed initialized\");\r\n    const config = {\r\n      supported_resolutions: [\"1\", \"5\", \"15\", \"30\", \"60\", \"D\", \"W\", \"M\"] as ResolutionString[],\r\n      supports_group_request: false,\r\n      supports_marks: false,\r\n      supports_search: true,\r\n      supports_timescale_marks: false,\r\n    };\r\n    setTimeout(() => callback(config));\r\n  },\r\n\r\n  searchSymbols: (userInput: string, exchange: string, symbolType: string, onResultReadyCallback: any) => {\r\n    console.log(\"[searchSymbols]: Searching EGX stocks for:\", userInput);\r\n    const results = Object.keys(EGX_STOCKS)\r\n      .filter(symbol =>\r\n        symbol.toLowerCase().includes(userInput.toLowerCase()) ||\r\n        EGX_STOCKS[symbol as keyof typeof EGX_STOCKS].name.toLowerCase().includes(userInput.toLowerCase())\r\n      )\r\n      .map(symbol => ({\r\n        symbol: symbol,\r\n        full_name: EGX_STOCKS[symbol as keyof typeof EGX_STOCKS].name,\r\n        description: EGX_STOCKS[symbol as keyof typeof EGX_STOCKS].name,\r\n        exchange: \"EGX\",\r\n        ticker: symbol,\r\n        type: \"stock\",\r\n      }));\r\n    onResultReadyCallback(results);\r\n  },\r\n\r\n  resolveSymbol: async (symbolName: string, onSymbolResolvedCallback: (info: LibrarySymbolInfo) => void) => {\r\n    console.log(\"[resolveSymbol]: Resolving EGX symbol\", symbolName);\r\n\r\n    const stockInfo = EGX_STOCKS[symbolName as keyof typeof EGX_STOCKS];\r\n    const description = stockInfo ? stockInfo.name : symbolName;\r\n\r\n    const symbolInfo = {\r\n      description: description,\r\n      name: symbolName,\r\n      ticker: symbolName,\r\n      session: \"0930-1530\", // EGX trading hours (9:30 AM - 3:30 PM Cairo time)\r\n      timezone: \"Africa/Cairo\",\r\n      type: \"stock\",\r\n      exchange: \"EGX\",\r\n      has_intraday: true,\r\n      has_daily: true,\r\n      has_weekly_and_monthly: true,\r\n      minmov: 1,\r\n      minmove2: 0,\r\n      fractional: false,\r\n      currency_code: \"EGP\",\r\n      pricescale: 100, // 2 decimal places for EGP\r\n      supported_resolutions: [\"1\", \"5\", \"15\", \"30\", \"60\", \"D\", \"W\", \"M\"],\r\n    } as LibrarySymbolInfo;\r\n    onSymbolResolvedCallback(symbolInfo);\r\n  },\r\n  getBars: async (\r\n    symbolInfo: LibrarySymbolInfo,\r\n    resolution: ResolutionString,\r\n    periodParams: PeriodParams,\r\n    onHistoryCallback: HistoryCallback,\r\n    onErrorCallback: ErrorCallback\r\n  ) => {\r\n    console.log(\"[getBars]: Getting EGX data for\", symbolInfo.name);\r\n    const symbol = symbolInfo.name;\r\n    const { from, to } = periodParams;\r\n\r\n    if (symbol) {\r\n      try {\r\n        const { data, statusCode } = await getSymbolHistories({\r\n          resolution,\r\n          symbol,\r\n          to,\r\n          from,\r\n        });\r\n\r\n        console.log(\"[getBars]: EGX API Response\");\r\n        console.table({\r\n          symbol: symbol,\r\n          from: formatTime(from, \"YYYYMMDD\"),\r\n          to: formatTime(to, \"YYYYMMDD\"),\r\n          resolution,\r\n          \"data points\": data?.t?.length,\r\n        });\r\n\r\n        if (statusCode !== 200 || !data?.t || data.t.length === 0) {\r\n          console.log(\"[getBars]: No data available\");\r\n          onHistoryCallback([], { noData: true });\r\n          return;\r\n        }\r\n\r\n        const { l, h, o, c, t } = data;\r\n\r\n        const bars = t?.map((timestamp: number, index: number) => ({\r\n          time: timestamp * 1000, // Convert to milliseconds\r\n          low: l[index],\r\n          high: h[index],\r\n          open: o[index],\r\n          close: c[index],\r\n        })) as Bar[];\r\n\r\n        // Sort bars by time\r\n        bars.sort((a: Bar, b: Bar) => a.time - b.time);\r\n\r\n        console.log(`[getBars]: Returning ${bars.length} bars for ${symbol}`);\r\n        onHistoryCallback(bars, { noData: false });\r\n      } catch (error) {\r\n        console.error(\"[getBars]: Error fetching EGX data\", error);\r\n        const errorMessage = error instanceof Error ? error.message : String(error);\r\n        onErrorCallback(errorMessage);\r\n      }\r\n    }\r\n  },\r\n\r\n  subscribeBars: (symbolInfo: LibrarySymbolInfo, resolution: ResolutionString, onRealtimeCallback: any, subscriberUID: string) => {\r\n    console.log(`[subscribeBars]: Subscribing to real-time data for ${symbolInfo.name}`);\r\n    // TODO: Implement real-time EGX data subscription\r\n    // For now, just log the subscription\r\n  },\r\n\r\n  unsubscribeBars: (subscriberUID: string) => {\r\n    console.log(`[unsubscribeBars]: Unsubscribing ${subscriberUID}`);\r\n    // TODO: Implement real-time data unsubscription\r\n  },\r\n};\r\n\r\nexport default DataFeed;\r\n"], "mappings": "AACA,SAASA,kBAAkB,QAAQ,QAAQ;AAC3C,SAASC,UAAU,QAAQ,UAAU;;AAErC;AACA,MAAMC,UAAU,GAAG;EACjB,SAAS,EAAE;IAAEC,IAAI,EAAE,+BAA+B;IAAEC,QAAQ,EAAE;EAAM,CAAC;EACrE,UAAU,EAAE;IAAED,IAAI,EAAE,sCAAsC;IAAEC,QAAQ,EAAE;EAAM,CAAC;EAC7E,UAAU,EAAE;IAAED,IAAI,EAAE,sBAAsB;IAAEC,QAAQ,EAAE;EAAM,CAAC;EAC7D,UAAU,EAAE;IAAED,IAAI,EAAE,4BAA4B;IAAEC,QAAQ,EAAE;EAAM,CAAC;EACnE,UAAU,EAAE;IAAED,IAAI,EAAE,iCAAiC;IAAEC,QAAQ,EAAE;EAAM;AACzE,CAAC;AAED,MAAMC,QAAQ,GAAG;EACfC,OAAO,EAAGC,QAAyB,IAAK;IACtCC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAClD,MAAMC,MAAM,GAAG;MACbC,qBAAqB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAuB;MACxFC,sBAAsB,EAAE,KAAK;MAC7BC,cAAc,EAAE,KAAK;MACrBC,eAAe,EAAE,IAAI;MACrBC,wBAAwB,EAAE;IAC5B,CAAC;IACDC,UAAU,CAAC,MAAMT,QAAQ,CAACG,MAAM,CAAC,CAAC;EACpC,CAAC;EAEDO,aAAa,EAAEA,CAACC,SAAiB,EAAEd,QAAgB,EAAEe,UAAkB,EAAEC,qBAA0B,KAAK;IACtGZ,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAES,SAAS,CAAC;IACpE,MAAMG,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACrB,UAAU,CAAC,CACpCsB,MAAM,CAACC,MAAM,IACZA,MAAM,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACT,SAAS,CAACQ,WAAW,CAAC,CAAC,CAAC,IACtDxB,UAAU,CAACuB,MAAM,CAA4B,CAACtB,IAAI,CAACuB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACT,SAAS,CAACQ,WAAW,CAAC,CAAC,CACnG,CAAC,CACAE,GAAG,CAACH,MAAM,KAAK;MACdA,MAAM,EAAEA,MAAM;MACdI,SAAS,EAAE3B,UAAU,CAACuB,MAAM,CAA4B,CAACtB,IAAI;MAC7D2B,WAAW,EAAE5B,UAAU,CAACuB,MAAM,CAA4B,CAACtB,IAAI;MAC/DC,QAAQ,EAAE,KAAK;MACf2B,MAAM,EAAEN,MAAM;MACdO,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;IACLZ,qBAAqB,CAACC,OAAO,CAAC;EAChC,CAAC;EAEDY,aAAa,EAAE,MAAAA,CAAOC,UAAkB,EAAEC,wBAA2D,KAAK;IACxG3B,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEyB,UAAU,CAAC;IAEhE,MAAME,SAAS,GAAGlC,UAAU,CAACgC,UAAU,CAA4B;IACnE,MAAMJ,WAAW,GAAGM,SAAS,GAAGA,SAAS,CAACjC,IAAI,GAAG+B,UAAU;IAE3D,MAAMG,UAAU,GAAG;MACjBP,WAAW,EAAEA,WAAW;MACxB3B,IAAI,EAAE+B,UAAU;MAChBH,MAAM,EAAEG,UAAU;MAClBI,OAAO,EAAE,WAAW;MAAE;MACtBC,QAAQ,EAAE,cAAc;MACxBP,IAAI,EAAE,OAAO;MACb5B,QAAQ,EAAE,KAAK;MACfoC,YAAY,EAAE,IAAI;MAClBC,SAAS,EAAE,IAAI;MACfC,sBAAsB,EAAE,IAAI;MAC5BC,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE,CAAC;MACXC,UAAU,EAAE,KAAK;MACjBC,aAAa,EAAE,KAAK;MACpBC,UAAU,EAAE,GAAG;MAAE;MACjBpC,qBAAqB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IACnE,CAAsB;IACtBwB,wBAAwB,CAACE,UAAU,CAAC;EACtC,CAAC;EACDW,OAAO,EAAE,MAAAA,CACPX,UAA6B,EAC7BY,UAA4B,EAC5BC,YAA0B,EAC1BC,iBAAkC,EAClCC,eAA8B,KAC3B;IACH5C,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE4B,UAAU,CAAClC,IAAI,CAAC;IAC/D,MAAMsB,MAAM,GAAGY,UAAU,CAAClC,IAAI;IAC9B,MAAM;MAAEkD,IAAI;MAAEC;IAAG,CAAC,GAAGJ,YAAY;IAEjC,IAAIzB,MAAM,EAAE;MACV,IAAI;QAAA,IAAA8B,OAAA;QACF,MAAM;UAAEC,IAAI;UAAEC;QAAW,CAAC,GAAG,MAAMzD,kBAAkB,CAAC;UACpDiD,UAAU;UACVxB,MAAM;UACN6B,EAAE;UACFD;QACF,CAAC,CAAC;QAEF7C,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC1CD,OAAO,CAACkD,KAAK,CAAC;UACZjC,MAAM,EAAEA,MAAM;UACd4B,IAAI,EAAEpD,UAAU,CAACoD,IAAI,EAAE,UAAU,CAAC;UAClCC,EAAE,EAAErD,UAAU,CAACqD,EAAE,EAAE,UAAU,CAAC;UAC9BL,UAAU;UACV,aAAa,EAAEO,IAAI,aAAJA,IAAI,wBAAAD,OAAA,GAAJC,IAAI,CAAEG,CAAC,cAAAJ,OAAA,uBAAPA,OAAA,CAASK;QAC1B,CAAC,CAAC;QAEF,IAAIH,UAAU,KAAK,GAAG,IAAI,EAACD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEG,CAAC,KAAIH,IAAI,CAACG,CAAC,CAACC,MAAM,KAAK,CAAC,EAAE;UACzDpD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAC3C0C,iBAAiB,CAAC,EAAE,EAAE;YAAEU,MAAM,EAAE;UAAK,CAAC,CAAC;UACvC;QACF;QAEA,MAAM;UAAEC,CAAC;UAAEC,CAAC;UAAEC,CAAC;UAAEC,CAAC;UAAEN;QAAE,CAAC,GAAGH,IAAI;QAE9B,MAAMU,IAAI,GAAGP,CAAC,aAADA,CAAC,uBAADA,CAAC,CAAE/B,GAAG,CAAC,CAACuC,SAAiB,EAAEC,KAAa,MAAM;UACzDC,IAAI,EAAEF,SAAS,GAAG,IAAI;UAAE;UACxBG,GAAG,EAAER,CAAC,CAACM,KAAK,CAAC;UACbG,IAAI,EAAER,CAAC,CAACK,KAAK,CAAC;UACdI,IAAI,EAAER,CAAC,CAACI,KAAK,CAAC;UACdK,KAAK,EAAER,CAAC,CAACG,KAAK;QAChB,CAAC,CAAC,CAAU;;QAEZ;QACAF,IAAI,CAACQ,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAKD,CAAC,CAACN,IAAI,GAAGO,CAAC,CAACP,IAAI,CAAC;QAE9C7D,OAAO,CAACC,GAAG,CAAE,wBAAuByD,IAAI,CAACN,MAAO,aAAYnC,MAAO,EAAC,CAAC;QACrE0B,iBAAiB,CAACe,IAAI,EAAE;UAAEL,MAAM,EAAE;QAAM,CAAC,CAAC;MAC5C,CAAC,CAAC,OAAOgB,KAAK,EAAE;QACdrE,OAAO,CAACqE,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D,MAAMC,YAAY,GAAGD,KAAK,YAAYE,KAAK,GAAGF,KAAK,CAACG,OAAO,GAAGC,MAAM,CAACJ,KAAK,CAAC;QAC3EzB,eAAe,CAAC0B,YAAY,CAAC;MAC/B;IACF;EACF,CAAC;EAEDI,aAAa,EAAEA,CAAC7C,UAA6B,EAAEY,UAA4B,EAAEkC,kBAAuB,EAAEC,aAAqB,KAAK;IAC9H5E,OAAO,CAACC,GAAG,CAAE,sDAAqD4B,UAAU,CAAClC,IAAK,EAAC,CAAC;IACpF;IACA;EACF,CAAC;EAEDkF,eAAe,EAAGD,aAAqB,IAAK;IAC1C5E,OAAO,CAACC,GAAG,CAAE,oCAAmC2E,aAAc,EAAC,CAAC;IAChE;EACF;AACF,CAAC;AAED,eAAe/E,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
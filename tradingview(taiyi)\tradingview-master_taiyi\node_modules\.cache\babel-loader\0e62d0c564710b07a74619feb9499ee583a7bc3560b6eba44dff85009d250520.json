{"ast": null, "code": "export const getQuoteBySymbol = (symbol, params) => {\n  const url = `https://ws.api.beta.cnyes.cool/ws/api/v2/quote/quotes/${symbol}?`;\n  return fetch(url + new URLSearchParams(params)).then(response => {\n    if (!response.ok) throw new Error(response.statusText);\n    return response.json();\n  });\n};\n\n// 將tradingview的resolution轉成api的resolution, 1c是跨日\nconst formatResolution = resolution => {\n  switch (resolution) {\n    case \"1\":\n    case \"5\":\n    case \"10\":\n    case \"15\":\n    case \"30\":\n    case \"60\":\n      return \"1c\";\n    default:\n      return /[0-9]+[DWM]/.test(resolution) ? String(resolution).replace(/[0-9]/g, \"\") : resolution;\n  }\n};\nexport const getSymbolHistories = async ({\n  from,\n  to,\n  resolution,\n  symbol\n}) => {\n  const apiResolution = formatResolution(resolution);\n  const res = await GETv1HistoryBySymbol({\n    resolution: apiResolution,\n    symbol,\n    quote: 1,\n    to: from /* api的from是比較大的值,和tradingview相反 */,\n    from: to\n  });\n  const statusCode = res === null || res === void 0 ? void 0 : res.statusCode;\n  const data = res === null || res === void 0 ? void 0 : res.data;\n  return {\n    statusCode,\n    data\n  };\n};\nexport const GETv1HistoryBySymbol = ({\n  resolution = 5,\n  symbol,\n  from,\n  to,\n  quote = 0,\n  compress\n}) => {\n  const url = \"https://ws.api.beta.cnyes.cool/ws/api/v1/charting/history?\";\n  const params = {\n    symbol,\n    quote,\n    resolution\n  };\n  if (from) params.from = from;\n  if (to) params.to = to;\n  if (compress) params.compress = compress;\n  return fetch(url + new URLSearchParams(params)).then(response => {\n    if (!response.ok) throw new Error(response.statusText);\n    return response.json();\n  });\n};\n_c = GETv1HistoryBySymbol;\nvar _c;\n$RefreshReg$(_c, \"GETv1HistoryBySymbol\");", "map": {"version": 3, "names": ["getQuoteBySymbol", "symbol", "params", "url", "fetch", "URLSearchParams", "then", "response", "ok", "Error", "statusText", "json", "formatResolution", "resolution", "test", "String", "replace", "getSymbolHistories", "from", "to", "apiResolution", "res", "GETv1HistoryBySymbol", "quote", "statusCode", "data", "compress", "_c", "$RefreshReg$"], "sources": ["D:/trading-view-charting-library/tradingview(taiyi)/tradingview-master_taiyi/src/api/index.ts"], "sourcesContent": ["import { ResolutionString } from \"@/public/charting_library\";\r\n\r\ntype HistroyParams = {\r\n  resolution?: number | string;\r\n  symbol: string;\r\n  from?: number;\r\n  to?: number;\r\n  quote?: number;\r\n  compress?: number;\r\n};\r\n\r\nexport const getQuoteBySymbol = (symbol: string, params?: { column?: string }) => {\r\n  const url = `https://ws.api.beta.cnyes.cool/ws/api/v2/quote/quotes/${symbol}?`;\r\n\r\n  return fetch(url + new URLSearchParams(params)).then((response) => {\r\n    if (!response.ok) throw new Error(response.statusText);\r\n    return response.json();\r\n  });\r\n};\r\n\r\n// 將tradingview的resolution轉成api的resolution, 1c是跨日\r\nconst formatResolution = (resolution: ResolutionString) => {\r\n  switch (resolution) {\r\n    case \"1\":\r\n    case \"5\":\r\n    case \"10\":\r\n    case \"15\":\r\n    case \"30\":\r\n    case \"60\":\r\n      return \"1c\";\r\n    default:\r\n      return /[0-9]+[DWM]/.test(resolution) ? String(resolution).replace(/[0-9]/g, \"\") : resolution;\r\n  }\r\n};\r\n\r\ntype HistoryProps = {\r\n  from: number;\r\n  to: number;\r\n  resolution: ResolutionString;\r\n  symbol: string;\r\n};\r\n\r\nexport const getSymbolHistories = async ({ from, to, resolution, symbol }: HistoryProps) => {\r\n  const apiResolution = formatResolution(resolution);\r\n  const res = await GETv1HistoryBySymbol({\r\n    resolution: apiResolution,\r\n    symbol,\r\n    quote: 1,\r\n    to: from /* api的from是比較大的值,和tradingview相反 */,\r\n    from: to,\r\n  });\r\n  const statusCode = res?.statusCode;\r\n  const data = res?.data;\r\n\r\n  return { statusCode, data };\r\n};\r\n\r\nexport const GETv1HistoryBySymbol = ({ resolution = 5, symbol, from, to, quote = 0, compress }: HistroyParams) => {\r\n  const url = \"https://ws.api.beta.cnyes.cool/ws/api/v1/charting/history?\";\r\n  const params: any = {\r\n    symbol,\r\n    quote,\r\n    resolution,\r\n  };\r\n\r\n  if (from) params.from = from;\r\n  if (to) params.to = to;\r\n  if (compress) params.compress = compress;\r\n\r\n  return fetch(url + new URLSearchParams(params)).then((response) => {\r\n    if (!response.ok) throw new Error(response.statusText);\r\n    return response.json();\r\n  });\r\n};\r\n"], "mappings": "AAWA,OAAO,MAAMA,gBAAgB,GAAGA,CAACC,MAAc,EAAEC,MAA4B,KAAK;EAChF,MAAMC,GAAG,GAAI,yDAAwDF,MAAO,GAAE;EAE9E,OAAOG,KAAK,CAACD,GAAG,GAAG,IAAIE,eAAe,CAACH,MAAM,CAAC,CAAC,CAACI,IAAI,CAAEC,QAAQ,IAAK;IACjE,IAAI,CAACA,QAAQ,CAACC,EAAE,EAAE,MAAM,IAAIC,KAAK,CAACF,QAAQ,CAACG,UAAU,CAAC;IACtD,OAAOH,QAAQ,CAACI,IAAI,CAAC,CAAC;EACxB,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMC,gBAAgB,GAAIC,UAA4B,IAAK;EACzD,QAAQA,UAAU;IAChB,KAAK,GAAG;IACR,KAAK,GAAG;IACR,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;MACP,OAAO,IAAI;IACb;MACE,OAAO,aAAa,CAACC,IAAI,CAACD,UAAU,CAAC,GAAGE,MAAM,CAACF,UAAU,CAAC,CAACG,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,GAAGH,UAAU;EACjG;AACF,CAAC;AASD,OAAO,MAAMI,kBAAkB,GAAG,MAAAA,CAAO;EAAEC,IAAI;EAAEC,EAAE;EAAEN,UAAU;EAAEZ;AAAqB,CAAC,KAAK;EAC1F,MAAMmB,aAAa,GAAGR,gBAAgB,CAACC,UAAU,CAAC;EAClD,MAAMQ,GAAG,GAAG,MAAMC,oBAAoB,CAAC;IACrCT,UAAU,EAAEO,aAAa;IACzBnB,MAAM;IACNsB,KAAK,EAAE,CAAC;IACRJ,EAAE,EAAED,IAAI,CAAC;IACTA,IAAI,EAAEC;EACR,CAAC,CAAC;EACF,MAAMK,UAAU,GAAGH,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEG,UAAU;EAClC,MAAMC,IAAI,GAAGJ,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEI,IAAI;EAEtB,OAAO;IAAED,UAAU;IAAEC;EAAK,CAAC;AAC7B,CAAC;AAED,OAAO,MAAMH,oBAAoB,GAAGA,CAAC;EAAET,UAAU,GAAG,CAAC;EAAEZ,MAAM;EAAEiB,IAAI;EAAEC,EAAE;EAAEI,KAAK,GAAG,CAAC;EAAEG;AAAwB,CAAC,KAAK;EAChH,MAAMvB,GAAG,GAAG,4DAA4D;EACxE,MAAMD,MAAW,GAAG;IAClBD,MAAM;IACNsB,KAAK;IACLV;EACF,CAAC;EAED,IAAIK,IAAI,EAAEhB,MAAM,CAACgB,IAAI,GAAGA,IAAI;EAC5B,IAAIC,EAAE,EAAEjB,MAAM,CAACiB,EAAE,GAAGA,EAAE;EACtB,IAAIO,QAAQ,EAAExB,MAAM,CAACwB,QAAQ,GAAGA,QAAQ;EAExC,OAAOtB,KAAK,CAACD,GAAG,GAAG,IAAIE,eAAe,CAACH,MAAM,CAAC,CAAC,CAACI,IAAI,CAAEC,QAAQ,IAAK;IACjE,IAAI,CAACA,QAAQ,CAACC,EAAE,EAAE,MAAM,IAAIC,KAAK,CAACF,QAAQ,CAACG,UAAU,CAAC;IACtD,OAAOH,QAAQ,CAACI,IAAI,CAAC,CAAC;EACxB,CAAC,CAAC;AACJ,CAAC;AAACgB,EAAA,GAhBWL,oBAAoB;AAAA,IAAAK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
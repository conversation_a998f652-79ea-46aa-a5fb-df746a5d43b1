{"ast": null, "code": "// EGX API functions (placeholder - replace with actual EGX data source)\nexport const getEGXQuote = (symbol, params) => {\n  // TODO: Replace with actual EGX API endpoint\n  // For now, return mock data\n  console.log(`[EGX API] Getting quote for ${symbol}`);\n  return Promise.resolve({\n    statusCode: 200,\n    data: [{\n      \"200009\": symbol.split(\":\")[1],\n      // Stock code\n      \"200026\": \"25.50\" // Mock price\n    }]\n  });\n};\n\n// Legacy function for backward compatibility\nexport const getQuoteBySymbol = getEGXQuote;\n\n// 將tradingview的resolution轉成api的resolution, 1c是跨日\nconst formatResolution = resolution => {\n  switch (resolution) {\n    case \"1\":\n    case \"5\":\n    case \"10\":\n    case \"15\":\n    case \"30\":\n    case \"60\":\n      return \"1c\";\n    default:\n      return /[0-9]+[DWM]/.test(resolution) ? String(resolution).replace(/[0-9]/g, \"\") : resolution;\n  }\n};\n// EGX Historical data function\nexport const getEGXHistories = async ({\n  from,\n  to,\n  resolution,\n  symbol\n}) => {\n  console.log(`[EGX API] Getting history for ${symbol} from ${from} to ${to}`);\n\n  // TODO: Replace with actual EGX historical data API\n  // For now, generate mock OHLC data\n  const mockData = generateMockOHLCData(from, to, resolution);\n  return {\n    statusCode: 200,\n    data: mockData\n  };\n};\n\n// Generate mock OHLC data for demonstration\nconst generateMockOHLCData = (from, to, resolution) => {\n  const bars = [];\n  const timeStep = getTimeStep(resolution);\n  let currentTime = from;\n  let price = 25.0; // Starting price\n\n  while (currentTime <= to) {\n    const open = price;\n    const change = (Math.random() - 0.5) * 2; // Random change between -1 and +1\n    const close = Math.max(0.1, open + change);\n    const high = Math.max(open, close) + Math.random() * 0.5;\n    const low = Math.min(open, close) - Math.random() * 0.5;\n    bars.push({\n      t: currentTime,\n      o: parseFloat(open.toFixed(2)),\n      h: parseFloat(high.toFixed(2)),\n      l: parseFloat(Math.max(0.1, low).toFixed(2)),\n      c: parseFloat(close.toFixed(2))\n    });\n    price = close;\n    currentTime += timeStep;\n  }\n  return {\n    t: bars.map(bar => bar.t),\n    o: bars.map(bar => bar.o),\n    h: bars.map(bar => bar.h),\n    l: bars.map(bar => bar.l),\n    c: bars.map(bar => bar.c)\n  };\n};\nconst getTimeStep = resolution => {\n  switch (resolution) {\n    case \"1\":\n      return 60;\n    // 1 minute\n    case \"5\":\n      return 300;\n    // 5 minutes\n    case \"15\":\n      return 900;\n    // 15 minutes\n    case \"30\":\n      return 1800;\n    // 30 minutes\n    case \"60\":\n      return 3600;\n    // 1 hour\n    case \"D\":\n      return 86400;\n    // 1 day\n    case \"W\":\n      return 604800;\n    // 1 week\n    case \"M\":\n      return 2592000;\n    // 1 month (30 days)\n    default:\n      return 86400;\n    // Default to 1 day\n  }\n};\n\n// Legacy function for backward compatibility\nexport const getSymbolHistories = getEGXHistories;\nexport const GETv1HistoryBySymbol = ({\n  resolution = 5,\n  symbol,\n  from,\n  to,\n  quote = 0,\n  compress\n}) => {\n  // Redirect to EGX function\n  return getEGXHistories({\n    from: from || 0,\n    to: to || Math.floor(Date.now() / 1000),\n    resolution: resolution.toString(),\n    symbol\n  });\n};\n_c = GETv1HistoryBySymbol;\nvar _c;\n$RefreshReg$(_c, \"GETv1HistoryBySymbol\");", "map": {"version": 3, "names": ["getEGXQuote", "symbol", "params", "console", "log", "Promise", "resolve", "statusCode", "data", "split", "getQuoteBySymbol", "formatResolution", "resolution", "test", "String", "replace", "getEGXHistories", "from", "to", "mockData", "generateMockOHLCData", "bars", "timeStep", "getTimeStep", "currentTime", "price", "open", "change", "Math", "random", "close", "max", "high", "low", "min", "push", "t", "o", "parseFloat", "toFixed", "h", "l", "c", "map", "bar", "getSymbolHistories", "GETv1HistoryBySymbol", "quote", "compress", "floor", "Date", "now", "toString", "_c", "$RefreshReg$"], "sources": ["D:/trading-view-charting-library/tradingview(taiyi)/tradingview-master_taiyi/src/api/index.ts"], "sourcesContent": ["import { ResolutionString } from \"@/public/charting_library\";\r\n\r\ntype HistroyParams = {\r\n  resolution?: number | string;\r\n  symbol: string;\r\n  from?: number;\r\n  to?: number;\r\n  quote?: number;\r\n  compress?: number;\r\n};\r\n\r\n// EGX API functions (placeholder - replace with actual EGX data source)\r\nexport const getEGXQuote = (symbol: string, params?: { column?: string }) => {\r\n  // TODO: Replace with actual EGX API endpoint\r\n  // For now, return mock data\r\n  console.log(`[EGX API] Getting quote for ${symbol}`);\r\n\r\n  return Promise.resolve({\r\n    statusCode: 200,\r\n    data: [{\r\n      \"200009\": symbol.split(\":\")[1], // Stock code\r\n      \"200026\": \"25.50\", // Mock price\r\n    }]\r\n  });\r\n};\r\n\r\n// Legacy function for backward compatibility\r\nexport const getQuoteBySymbol = getEGXQuote;\r\n\r\n// 將tradingview的resolution轉成api的resolution, 1c是跨日\r\nconst formatResolution = (resolution: ResolutionString) => {\r\n  switch (resolution) {\r\n    case \"1\":\r\n    case \"5\":\r\n    case \"10\":\r\n    case \"15\":\r\n    case \"30\":\r\n    case \"60\":\r\n      return \"1c\";\r\n    default:\r\n      return /[0-9]+[DWM]/.test(resolution) ? String(resolution).replace(/[0-9]/g, \"\") : resolution;\r\n  }\r\n};\r\n\r\ntype HistoryProps = {\r\n  from: number;\r\n  to: number;\r\n  resolution: ResolutionString;\r\n  symbol: string;\r\n};\r\n\r\n// EGX Historical data function\r\nexport const getEGXHistories = async ({ from, to, resolution, symbol }: HistoryProps) => {\r\n  console.log(`[EGX API] Getting history for ${symbol} from ${from} to ${to}`);\r\n\r\n  // TODO: Replace with actual EGX historical data API\r\n  // For now, generate mock OHLC data\r\n  const mockData = generateMockOHLCData(from, to, resolution);\r\n\r\n  return {\r\n    statusCode: 200,\r\n    data: mockData\r\n  };\r\n};\r\n\r\n// Generate mock OHLC data for demonstration\r\nconst generateMockOHLCData = (from: number, to: number, resolution: ResolutionString) => {\r\n  const bars = [];\r\n  const timeStep = getTimeStep(resolution);\r\n  let currentTime = from;\r\n  let price = 25.0; // Starting price\r\n\r\n  while (currentTime <= to) {\r\n    const open = price;\r\n    const change = (Math.random() - 0.5) * 2; // Random change between -1 and +1\r\n    const close = Math.max(0.1, open + change);\r\n    const high = Math.max(open, close) + Math.random() * 0.5;\r\n    const low = Math.min(open, close) - Math.random() * 0.5;\r\n\r\n    bars.push({\r\n      t: currentTime,\r\n      o: parseFloat(open.toFixed(2)),\r\n      h: parseFloat(high.toFixed(2)),\r\n      l: parseFloat(Math.max(0.1, low).toFixed(2)),\r\n      c: parseFloat(close.toFixed(2)),\r\n    });\r\n\r\n    price = close;\r\n    currentTime += timeStep;\r\n  }\r\n\r\n  return {\r\n    t: bars.map(bar => bar.t),\r\n    o: bars.map(bar => bar.o),\r\n    h: bars.map(bar => bar.h),\r\n    l: bars.map(bar => bar.l),\r\n    c: bars.map(bar => bar.c),\r\n  };\r\n};\r\n\r\nconst getTimeStep = (resolution: ResolutionString): number => {\r\n  switch (resolution) {\r\n    case \"1\": return 60; // 1 minute\r\n    case \"5\": return 300; // 5 minutes\r\n    case \"15\": return 900; // 15 minutes\r\n    case \"30\": return 1800; // 30 minutes\r\n    case \"60\": return 3600; // 1 hour\r\n    case \"D\": return 86400; // 1 day\r\n    case \"W\": return 604800; // 1 week\r\n    case \"M\": return 2592000; // 1 month (30 days)\r\n    default: return 86400; // Default to 1 day\r\n  }\r\n};\r\n\r\n// Legacy function for backward compatibility\r\nexport const getSymbolHistories = getEGXHistories;\r\n\r\nexport const GETv1HistoryBySymbol = ({ resolution = 5, symbol, from, to, quote = 0, compress }: HistroyParams) => {\r\n  // Redirect to EGX function\r\n  return getEGXHistories({\r\n    from: from || 0,\r\n    to: to || Math.floor(Date.now() / 1000),\r\n    resolution: resolution.toString() as ResolutionString,\r\n    symbol\r\n  });\r\n};\r\n"], "mappings": "AAWA;AACA,OAAO,MAAMA,WAAW,GAAGA,CAACC,MAAc,EAAEC,MAA4B,KAAK;EAC3E;EACA;EACAC,OAAO,CAACC,GAAG,CAAE,+BAA8BH,MAAO,EAAC,CAAC;EAEpD,OAAOI,OAAO,CAACC,OAAO,CAAC;IACrBC,UAAU,EAAE,GAAG;IACfC,IAAI,EAAE,CAAC;MACL,QAAQ,EAAEP,MAAM,CAACQ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAAE;MAChC,QAAQ,EAAE,OAAO,CAAE;IACrB,CAAC;EACH,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAO,MAAMC,gBAAgB,GAAGV,WAAW;;AAE3C;AACA,MAAMW,gBAAgB,GAAIC,UAA4B,IAAK;EACzD,QAAQA,UAAU;IAChB,KAAK,GAAG;IACR,KAAK,GAAG;IACR,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;MACP,OAAO,IAAI;IACb;MACE,OAAO,aAAa,CAACC,IAAI,CAACD,UAAU,CAAC,GAAGE,MAAM,CAACF,UAAU,CAAC,CAACG,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,GAAGH,UAAU;EACjG;AACF,CAAC;AASD;AACA,OAAO,MAAMI,eAAe,GAAG,MAAAA,CAAO;EAAEC,IAAI;EAAEC,EAAE;EAAEN,UAAU;EAAEX;AAAqB,CAAC,KAAK;EACvFE,OAAO,CAACC,GAAG,CAAE,iCAAgCH,MAAO,SAAQgB,IAAK,OAAMC,EAAG,EAAC,CAAC;;EAE5E;EACA;EACA,MAAMC,QAAQ,GAAGC,oBAAoB,CAACH,IAAI,EAAEC,EAAE,EAAEN,UAAU,CAAC;EAE3D,OAAO;IACLL,UAAU,EAAE,GAAG;IACfC,IAAI,EAAEW;EACR,CAAC;AACH,CAAC;;AAED;AACA,MAAMC,oBAAoB,GAAGA,CAACH,IAAY,EAAEC,EAAU,EAAEN,UAA4B,KAAK;EACvF,MAAMS,IAAI,GAAG,EAAE;EACf,MAAMC,QAAQ,GAAGC,WAAW,CAACX,UAAU,CAAC;EACxC,IAAIY,WAAW,GAAGP,IAAI;EACtB,IAAIQ,KAAK,GAAG,IAAI,CAAC,CAAC;;EAElB,OAAOD,WAAW,IAAIN,EAAE,EAAE;IACxB,MAAMQ,IAAI,GAAGD,KAAK;IAClB,MAAME,MAAM,GAAG,CAACC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;IAC1C,MAAMC,KAAK,GAAGF,IAAI,CAACG,GAAG,CAAC,GAAG,EAAEL,IAAI,GAAGC,MAAM,CAAC;IAC1C,MAAMK,IAAI,GAAGJ,IAAI,CAACG,GAAG,CAACL,IAAI,EAAEI,KAAK,CAAC,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;IACxD,MAAMI,GAAG,GAAGL,IAAI,CAACM,GAAG,CAACR,IAAI,EAAEI,KAAK,CAAC,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;IAEvDR,IAAI,CAACc,IAAI,CAAC;MACRC,CAAC,EAAEZ,WAAW;MACda,CAAC,EAAEC,UAAU,CAACZ,IAAI,CAACa,OAAO,CAAC,CAAC,CAAC,CAAC;MAC9BC,CAAC,EAAEF,UAAU,CAACN,IAAI,CAACO,OAAO,CAAC,CAAC,CAAC,CAAC;MAC9BE,CAAC,EAAEH,UAAU,CAACV,IAAI,CAACG,GAAG,CAAC,GAAG,EAAEE,GAAG,CAAC,CAACM,OAAO,CAAC,CAAC,CAAC,CAAC;MAC5CG,CAAC,EAAEJ,UAAU,CAACR,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC;IAEFd,KAAK,GAAGK,KAAK;IACbN,WAAW,IAAIF,QAAQ;EACzB;EAEA,OAAO;IACLc,CAAC,EAAEf,IAAI,CAACsB,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACR,CAAC,CAAC;IACzBC,CAAC,EAAEhB,IAAI,CAACsB,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACP,CAAC,CAAC;IACzBG,CAAC,EAAEnB,IAAI,CAACsB,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACJ,CAAC,CAAC;IACzBC,CAAC,EAAEpB,IAAI,CAACsB,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACH,CAAC,CAAC;IACzBC,CAAC,EAAErB,IAAI,CAACsB,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACF,CAAC;EAC1B,CAAC;AACH,CAAC;AAED,MAAMnB,WAAW,GAAIX,UAA4B,IAAa;EAC5D,QAAQA,UAAU;IAChB,KAAK,GAAG;MAAE,OAAO,EAAE;IAAE;IACrB,KAAK,GAAG;MAAE,OAAO,GAAG;IAAE;IACtB,KAAK,IAAI;MAAE,OAAO,GAAG;IAAE;IACvB,KAAK,IAAI;MAAE,OAAO,IAAI;IAAE;IACxB,KAAK,IAAI;MAAE,OAAO,IAAI;IAAE;IACxB,KAAK,GAAG;MAAE,OAAO,KAAK;IAAE;IACxB,KAAK,GAAG;MAAE,OAAO,MAAM;IAAE;IACzB,KAAK,GAAG;MAAE,OAAO,OAAO;IAAE;IAC1B;MAAS,OAAO,KAAK;IAAE;EACzB;AACF,CAAC;;AAED;AACA,OAAO,MAAMiC,kBAAkB,GAAG7B,eAAe;AAEjD,OAAO,MAAM8B,oBAAoB,GAAGA,CAAC;EAAElC,UAAU,GAAG,CAAC;EAAEX,MAAM;EAAEgB,IAAI;EAAEC,EAAE;EAAE6B,KAAK,GAAG,CAAC;EAAEC;AAAwB,CAAC,KAAK;EAChH;EACA,OAAOhC,eAAe,CAAC;IACrBC,IAAI,EAAEA,IAAI,IAAI,CAAC;IACfC,EAAE,EAAEA,EAAE,IAAIU,IAAI,CAACqB,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;IACvCvC,UAAU,EAAEA,UAAU,CAACwC,QAAQ,CAAC,CAAqB;IACrDnD;EACF,CAAC,CAAC;AACJ,CAAC;AAACoD,EAAA,GARWP,oBAAoB;AAAA,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
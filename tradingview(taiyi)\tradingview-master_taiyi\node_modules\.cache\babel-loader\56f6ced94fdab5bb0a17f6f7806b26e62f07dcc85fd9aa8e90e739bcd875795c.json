{"ast": null, "code": "var _jsxFileName = \"D:\\\\trading-view-charting-library\\\\tradingview(taiyi)\\\\tradingview-master_taiyi\\\\src\\\\tradingview\\\\index.tsx\",\n  _s = $RefreshSig$();\nimport { useEffect } from \"react\";\nimport DataFeed from \"./datafeed\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst index = () => {\n  _s();\n  useEffect(() => {\n    const tvWidget = new window.TradingView.widget({\n      container: \"chartContainer\",\n      locale: \"zh_TW\",\n      library_path: \"charting_library/\",\n      datafeed: DataFeed,\n      symbol: \"GI:DXY:INDEX\",\n      interval: \"1D\",\n      fullscreen: true\n    });\n    return () => {\n      tvWidget.remove();\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: \"chartContainer\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 10\n  }, this);\n};\n_s(index, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\nexport default index;", "map": {"version": 3, "names": ["useEffect", "DataFeed", "jsxDEV", "_jsxDEV", "index", "_s", "tvWidget", "window", "TradingView", "widget", "container", "locale", "library_path", "datafeed", "symbol", "interval", "fullscreen", "remove", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["D:/trading-view-charting-library/tradingview(taiyi)/tradingview-master_taiyi/src/tradingview/index.tsx"], "sourcesContent": ["import { useEffect } from \"react\";\r\nimport DataFeed from \"./datafeed\";\r\n\r\ndeclare global {\r\n  interface Window {\r\n    TradingView: any;\r\n    Datafeeds: any;\r\n  }\r\n}\r\n\r\nconst index = () => {\r\n  useEffect(() => {\r\n    const tvWidget = new window.TradingView.widget({\r\n      container: \"chartContainer\",\r\n      locale: \"zh_TW\",\r\n      library_path: \"charting_library/\",\r\n      datafeed: DataFeed,\r\n      symbol: \"GI:DXY:INDEX\",\r\n      interval: \"1D\",\r\n      fullscreen: true,\r\n    });\r\n\r\n    return () => {\r\n      tvWidget.remove();\r\n    };\r\n  }, []);\r\n\r\n  return <div id=\"chartContainer\"></div>;\r\n};\r\n\r\nexport default index;\r\n"], "mappings": ";;AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,QAAQ,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AASlC,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClBL,SAAS,CAAC,MAAM;IACd,MAAMM,QAAQ,GAAG,IAAIC,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC;MAC7CC,SAAS,EAAE,gBAAgB;MAC3BC,MAAM,EAAE,OAAO;MACfC,YAAY,EAAE,mBAAmB;MACjCC,QAAQ,EAAEZ,QAAQ;MAClBa,MAAM,EAAE,cAAc;MACtBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,CAAC;IAEF,OAAO,MAAM;MACXV,QAAQ,CAACW,MAAM,CAAC,CAAC;IACnB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBAAOd,OAAA;IAAKe,EAAE,EAAC;EAAgB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;AACxC,CAAC;AAACjB,EAAA,CAlBID,KAAK;AAoBX,eAAeA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
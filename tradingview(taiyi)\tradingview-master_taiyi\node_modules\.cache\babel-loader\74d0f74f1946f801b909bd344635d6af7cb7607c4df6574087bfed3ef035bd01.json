{"ast": null, "code": "var _jsxFileName = \"D:\\\\trading-view-charting-library\\\\tradingview(taiyi)\\\\tradingview-master_taiyi\\\\src\\\\index.tsx\";\nimport ReactDOM from \"react-dom/client\";\nimport App from \"./App\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst root = ReactDOM.createRoot(document.getElementById(\"root\"));\nroot.render( /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 5,\n  columnNumber: 13\n}, this));", "map": {"version": 3, "names": ["ReactDOM", "App", "jsxDEV", "_jsxDEV", "root", "createRoot", "document", "getElementById", "render", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["D:/trading-view-charting-library/tradingview(taiyi)/tradingview-master_taiyi/src/index.tsx"], "sourcesContent": ["import ReactDOM from \"react-dom/client\";\r\nimport App from \"./App\";\r\n\r\nconst root = ReactDOM.createRoot(document.getElementById(\"root\") as HTMLElement);\r\nroot.render(<App />);\r\n"], "mappings": ";AAAA,OAAOA,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,GAAG,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,IAAI,GAAGJ,QAAQ,CAACK,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAgB,CAAC;AAChFH,IAAI,CAACI,MAAM,eAACL,OAAA,CAACF,GAAG;EAAAQ,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAE,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
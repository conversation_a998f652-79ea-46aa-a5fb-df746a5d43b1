{"ast": null, "code": "// EODHD API Configuration\nconst EODHD_API_KEY = process.env.REACT_APP_EODHD_API_KEY || 'demo'; // Replace with your API key\nconst EODHD_BASE_URL = 'https://eodhd.com/api';\n// EGX Stock symbols mapping\nexport const EGX_STOCKS = {\n  \"COMI.EGX\": {\n    name: \"Commercial International Bank\",\n    sector: \"Banking\"\n  },\n  \"ETEL.EGX\": {\n    name: \"Egyptian Company for Mobile Services\",\n    sector: \"Telecom\"\n  },\n  \"SWDY.EGX\": {\n    name: \"El Sewedy Electric Company\",\n    sector: \"Industrial\"\n  },\n  \"HRHO.EGX\": {\n    name: \"Hassan Allam Holding\",\n    sector: \"Construction\"\n  },\n  \"EAST.EGX\": {\n    name: \"Eastern Company\",\n    sector: \"Tobacco\"\n  },\n  \"EGCH.EGX\": {\n    name: \"Egyptian Chemical Industries (Kima)\",\n    sector: \"Chemicals\"\n  },\n  \"EGAL.EGX\": {\n    name: \"Egypt Aluminum\",\n    sector: \"Industrial\"\n  },\n  \"ALEX.EGX\": {\n    name: \"Alexandria Cement\",\n    sector: \"Construction Materials\"\n  },\n  \"DOMT.EGX\": {\n    name: \"Arabian Food Industries DOMTY\",\n    sector: \"Food\"\n  },\n  \"EFID.EGX\": {\n    name: \"Edita Food Industries\",\n    sector: \"Food\"\n  }\n};\n\n// EODHD API functions for EGX data\nexport const getEODHDQuote = async symbol => {\n  try {\n    var _data$close;\n    const response = await fetch(`${EODHD_BASE_URL}/real-time/${symbol}?api_token=${EODHD_API_KEY}&fmt=json`);\n    if (!response.ok) {\n      throw new Error(`EODHD API error: ${response.statusText}`);\n    }\n    const data = await response.json();\n\n    // Transform EODHD response to match our expected format\n    return {\n      statusCode: 200,\n      data: [{\n        \"200009\": symbol.split(\".\")[0],\n        // Stock code (COMI from COMI.EGX)\n        \"200026\": ((_data$close = data.close) === null || _data$close === void 0 ? void 0 : _data$close.toString()) || \"0\",\n        // Current price\n        timestamp: data.timestamp,\n        change: data.change,\n        change_p: data.change_p\n      }]\n    };\n  } catch (error) {\n    console.error(`[EODHD API] Error fetching quote for ${symbol}:`, error);\n    return {\n      statusCode: 500,\n      data: []\n    };\n  }\n};\n\n// Legacy function for backward compatibility\nexport const getQuoteBySymbol = getEODHDQuote;\n\n// 將tradingview的resolution轉成api的resolution, 1c是跨日\nconst formatResolution = resolution => {\n  switch (resolution) {\n    case \"1\":\n    case \"5\":\n    case \"10\":\n    case \"15\":\n    case \"30\":\n    case \"60\":\n      return \"1c\";\n    default:\n      return /[0-9]+[DWM]/.test(resolution) ? String(resolution).replace(/[0-9]/g, \"\") : resolution;\n  }\n};\n// EGX Historical data function\nexport const getEGXHistories = async ({\n  from,\n  to,\n  resolution,\n  symbol\n}) => {\n  console.log(`[EGX API] Getting history for ${symbol} from ${from} to ${to}`);\n\n  // TODO: Replace with actual EGX historical data API\n  // For now, generate mock OHLC data\n  const mockData = generateMockOHLCData(from, to, resolution);\n  return {\n    statusCode: 200,\n    data: mockData\n  };\n};\n\n// EODHD Historical data function\nexport const getEODHDHistoricalData = async ({\n  from,\n  to,\n  resolution,\n  symbol\n}) => {\n  try {\n    console.log(`[EODHD API] Getting historical data for ${symbol} from ${from} to ${to}`);\n\n    // Convert timestamps to EODHD format (YYYY-MM-DD)\n    const fromDate = new Date(from * 1000).toISOString().split('T')[0];\n    const toDate = new Date(to * 1000).toISOString().split('T')[0];\n\n    // Determine API endpoint based on resolution\n    let endpoint = '';\n    let period = '';\n    if (resolution === 'D' || resolution === 'W' || resolution === 'M') {\n      // End of day data\n      endpoint = `${EODHD_BASE_URL}/eod/${symbol}`;\n      period = resolution === 'W' ? 'w' : resolution === 'M' ? 'm' : 'd';\n    } else {\n      // Intraday data\n      endpoint = `${EODHD_BASE_URL}/intraday/${symbol}`;\n      period = `${resolution}m`; // Convert to minutes format\n    }\n    const url = `${endpoint}?api_token=${EODHD_API_KEY}&from=${fromDate}&to=${toDate}&period=${period}&fmt=json`;\n    const response = await fetch(url);\n    if (!response.ok) {\n      throw new Error(`EODHD API error: ${response.statusText}`);\n    }\n    const data = await response.json();\n    if (!Array.isArray(data) || data.length === 0) {\n      return {\n        statusCode: 200,\n        data: {\n          t: [],\n          o: [],\n          h: [],\n          l: [],\n          c: []\n        }\n      };\n    }\n\n    // Transform EODHD response to TradingView format\n    const transformedData = {\n      t: data.map(item => {\n        // Convert date/datetime to timestamp\n        const date = item.datetime || item.date;\n        return Math.floor(new Date(date).getTime() / 1000);\n      }),\n      o: data.map(item => parseFloat(item.open)),\n      h: data.map(item => parseFloat(item.high)),\n      l: data.map(item => parseFloat(item.low)),\n      c: data.map(item => parseFloat(item.close))\n    };\n    console.log(`[EODHD API] Successfully fetched ${data.length} data points for ${symbol}`);\n    return {\n      statusCode: 200,\n      data: transformedData\n    };\n  } catch (error) {\n    console.error(`[EODHD API] Error fetching historical data for ${symbol}:`, error);\n\n    // Fallback to mock data for development\n    console.log(`[EODHD API] Falling back to mock data for ${symbol}`);\n    const mockData = generateMockOHLCData(from, to, resolution);\n    return {\n      statusCode: 200,\n      data: mockData\n    };\n  }\n};\n\n// Generate mock OHLC data for demonstration/fallback\nconst generateMockOHLCData = (from, to, resolution) => {\n  const bars = [];\n  const timeStep = getTimeStep(resolution);\n  let currentTime = from;\n  let price = 25.0; // Starting price\n\n  while (currentTime <= to && bars.length < 1000) {\n    // Limit to prevent infinite loops\n    const open = price;\n    const change = (Math.random() - 0.5) * 2; // Random change between -1 and +1\n    const close = Math.max(0.1, open + change);\n    const high = Math.max(open, close) + Math.random() * 0.5;\n    const low = Math.min(open, close) - Math.random() * 0.5;\n    bars.push({\n      t: currentTime,\n      o: parseFloat(open.toFixed(2)),\n      h: parseFloat(high.toFixed(2)),\n      l: parseFloat(Math.max(0.1, low).toFixed(2)),\n      c: parseFloat(close.toFixed(2))\n    });\n    price = close;\n    currentTime += timeStep;\n  }\n  return {\n    t: bars.map(bar => bar.t),\n    o: bars.map(bar => bar.o),\n    h: bars.map(bar => bar.h),\n    l: bars.map(bar => bar.l),\n    c: bars.map(bar => bar.c)\n  };\n};\nconst getTimeStep = resolution => {\n  switch (resolution) {\n    case \"1\":\n      return 60;\n    // 1 minute\n    case \"5\":\n      return 300;\n    // 5 minutes\n    case \"15\":\n      return 900;\n    // 15 minutes\n    case \"30\":\n      return 1800;\n    // 30 minutes\n    case \"60\":\n      return 3600;\n    // 1 hour\n    case \"D\":\n      return 86400;\n    // 1 day\n    case \"W\":\n      return 604800;\n    // 1 week\n    case \"M\":\n      return 2592000;\n    // 1 month (30 days)\n    default:\n      return 86400;\n    // Default to 1 day\n  }\n};\n\n// Legacy functions for backward compatibility\nexport const getSymbolHistories = getEODHDHistoricalData;\nexport const GETv1HistoryBySymbol = ({\n  resolution = 5,\n  symbol,\n  from,\n  to,\n  quote = 0,\n  compress\n}) => {\n  // Redirect to EODHD function\n  return getEODHDHistoricalData({\n    from: from || 0,\n    to: to || Math.floor(Date.now() / 1000),\n    resolution: resolution.toString(),\n    symbol\n  });\n};\n_c = GETv1HistoryBySymbol;\nvar _c;\n$RefreshReg$(_c, \"GETv1HistoryBySymbol\");", "map": {"version": 3, "names": ["EODHD_API_KEY", "process", "env", "REACT_APP_EODHD_API_KEY", "EODHD_BASE_URL", "EGX_STOCKS", "name", "sector", "getEODHDQuote", "symbol", "_data$close", "response", "fetch", "ok", "Error", "statusText", "data", "json", "statusCode", "split", "close", "toString", "timestamp", "change", "change_p", "error", "console", "getQuoteBySymbol", "formatResolution", "resolution", "test", "String", "replace", "getEGXHistories", "from", "to", "log", "mockData", "generateMockOHLCData", "getEODHDHistoricalData", "fromDate", "Date", "toISOString", "toDate", "endpoint", "period", "url", "Array", "isArray", "length", "t", "o", "h", "l", "c", "transformedData", "map", "item", "date", "datetime", "Math", "floor", "getTime", "parseFloat", "open", "high", "low", "bars", "timeStep", "getTimeStep", "currentTime", "price", "random", "max", "min", "push", "toFixed", "bar", "getSymbolHistories", "GETv1HistoryBySymbol", "quote", "compress", "now", "_c", "$RefreshReg$"], "sources": ["D:/trading-view-charting-library/tradingview(taiyi)/tradingview-master_taiyi/src/api/index.ts"], "sourcesContent": ["import { ResolutionString } from \"@/public/charting_library\";\r\n\r\n// EODHD API Configuration\r\nconst EODHD_API_KEY = process.env.REACT_APP_EODHD_API_KEY || 'demo'; // Replace with your API key\r\nconst EODHD_BASE_URL = 'https://eodhd.com/api';\r\n\r\ntype HistroyParams = {\r\n  resolution?: number | string;\r\n  symbol: string;\r\n  from?: number;\r\n  to?: number;\r\n  quote?: number;\r\n  compress?: number;\r\n};\r\n\r\n// EGX Stock symbols mapping\r\nexport const EGX_STOCKS = {\r\n  \"COMI.EGX\": { name: \"Commercial International Bank\", sector: \"Banking\" },\r\n  \"ETEL.EGX\": { name: \"Egyptian Company for Mobile Services\", sector: \"Telecom\" },\r\n  \"SWDY.EGX\": { name: \"El Sewedy Electric Company\", sector: \"Industrial\" },\r\n  \"HRHO.EGX\": { name: \"Hassan Allam Holding\", sector: \"Construction\" },\r\n  \"EAST.EGX\": { name: \"Eastern Company\", sector: \"Tobacco\" },\r\n  \"EGCH.EGX\": { name: \"Egyptian Chemical Industries (Kima)\", sector: \"Chemicals\" },\r\n  \"EGAL.EGX\": { name: \"Egypt Aluminum\", sector: \"Industrial\" },\r\n  \"ALEX.EGX\": { name: \"Alexandria Cement\", sector: \"Construction Materials\" },\r\n  \"DOMT.EGX\": { name: \"Arabian Food Industries DOMTY\", sector: \"Food\" },\r\n  \"EFID.EGX\": { name: \"Edita Food Industries\", sector: \"Food\" },\r\n};\r\n\r\n// EODHD API functions for EGX data\r\nexport const getEODHDQuote = async (symbol: string) => {\r\n  try {\r\n    const response = await fetch(\r\n      `${EODHD_BASE_URL}/real-time/${symbol}?api_token=${EODHD_API_KEY}&fmt=json`\r\n    );\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`EODHD API error: ${response.statusText}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n\r\n    // Transform EODHD response to match our expected format\r\n    return {\r\n      statusCode: 200,\r\n      data: [{\r\n        \"200009\": symbol.split(\".\")[0], // Stock code (COMI from COMI.EGX)\r\n        \"200026\": data.close?.toString() || \"0\", // Current price\r\n        timestamp: data.timestamp,\r\n        change: data.change,\r\n        change_p: data.change_p,\r\n      }]\r\n    };\r\n  } catch (error) {\r\n    console.error(`[EODHD API] Error fetching quote for ${symbol}:`, error);\r\n    return {\r\n      statusCode: 500,\r\n      data: []\r\n    };\r\n  }\r\n};\r\n\r\n// Legacy function for backward compatibility\r\nexport const getQuoteBySymbol = getEODHDQuote;\r\n\r\n// 將tradingview的resolution轉成api的resolution, 1c是跨日\r\nconst formatResolution = (resolution: ResolutionString) => {\r\n  switch (resolution) {\r\n    case \"1\":\r\n    case \"5\":\r\n    case \"10\":\r\n    case \"15\":\r\n    case \"30\":\r\n    case \"60\":\r\n      return \"1c\";\r\n    default:\r\n      return /[0-9]+[DWM]/.test(resolution) ? String(resolution).replace(/[0-9]/g, \"\") : resolution;\r\n  }\r\n};\r\n\r\ntype HistoryProps = {\r\n  from: number;\r\n  to: number;\r\n  resolution: ResolutionString;\r\n  symbol: string;\r\n};\r\n\r\n// EGX Historical data function\r\nexport const getEGXHistories = async ({ from, to, resolution, symbol }: HistoryProps) => {\r\n  console.log(`[EGX API] Getting history for ${symbol} from ${from} to ${to}`);\r\n\r\n  // TODO: Replace with actual EGX historical data API\r\n  // For now, generate mock OHLC data\r\n  const mockData = generateMockOHLCData(from, to, resolution);\r\n\r\n  return {\r\n    statusCode: 200,\r\n    data: mockData\r\n  };\r\n};\r\n\r\n// EODHD Historical data function\r\nexport const getEODHDHistoricalData = async ({ from, to, resolution, symbol }: HistoryProps) => {\r\n  try {\r\n    console.log(`[EODHD API] Getting historical data for ${symbol} from ${from} to ${to}`);\r\n\r\n    // Convert timestamps to EODHD format (YYYY-MM-DD)\r\n    const fromDate = new Date(from * 1000).toISOString().split('T')[0];\r\n    const toDate = new Date(to * 1000).toISOString().split('T')[0];\r\n\r\n    // Determine API endpoint based on resolution\r\n    let endpoint = '';\r\n    let period = '';\r\n\r\n    if (resolution === 'D' || resolution === 'W' || resolution === 'M') {\r\n      // End of day data\r\n      endpoint = `${EODHD_BASE_URL}/eod/${symbol}`;\r\n      period = resolution === 'W' ? 'w' : resolution === 'M' ? 'm' : 'd';\r\n    } else {\r\n      // Intraday data\r\n      endpoint = `${EODHD_BASE_URL}/intraday/${symbol}`;\r\n      period = `${resolution}m`; // Convert to minutes format\r\n    }\r\n\r\n    const url = `${endpoint}?api_token=${EODHD_API_KEY}&from=${fromDate}&to=${toDate}&period=${period}&fmt=json`;\r\n\r\n    const response = await fetch(url);\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`EODHD API error: ${response.statusText}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n\r\n    if (!Array.isArray(data) || data.length === 0) {\r\n      return { statusCode: 200, data: { t: [], o: [], h: [], l: [], c: [] } };\r\n    }\r\n\r\n    // Transform EODHD response to TradingView format\r\n    const transformedData = {\r\n      t: data.map(item => {\r\n        // Convert date/datetime to timestamp\r\n        const date = item.datetime || item.date;\r\n        return Math.floor(new Date(date).getTime() / 1000);\r\n      }),\r\n      o: data.map(item => parseFloat(item.open)),\r\n      h: data.map(item => parseFloat(item.high)),\r\n      l: data.map(item => parseFloat(item.low)),\r\n      c: data.map(item => parseFloat(item.close)),\r\n    };\r\n\r\n    console.log(`[EODHD API] Successfully fetched ${data.length} data points for ${symbol}`);\r\n    return { statusCode: 200, data: transformedData };\r\n\r\n  } catch (error) {\r\n    console.error(`[EODHD API] Error fetching historical data for ${symbol}:`, error);\r\n\r\n    // Fallback to mock data for development\r\n    console.log(`[EODHD API] Falling back to mock data for ${symbol}`);\r\n    const mockData = generateMockOHLCData(from, to, resolution);\r\n    return { statusCode: 200, data: mockData };\r\n  }\r\n};\r\n\r\n// Generate mock OHLC data for demonstration/fallback\r\nconst generateMockOHLCData = (from: number, to: number, resolution: ResolutionString) => {\r\n  const bars = [];\r\n  const timeStep = getTimeStep(resolution);\r\n  let currentTime = from;\r\n  let price = 25.0; // Starting price\r\n\r\n  while (currentTime <= to && bars.length < 1000) { // Limit to prevent infinite loops\r\n    const open = price;\r\n    const change = (Math.random() - 0.5) * 2; // Random change between -1 and +1\r\n    const close = Math.max(0.1, open + change);\r\n    const high = Math.max(open, close) + Math.random() * 0.5;\r\n    const low = Math.min(open, close) - Math.random() * 0.5;\r\n\r\n    bars.push({\r\n      t: currentTime,\r\n      o: parseFloat(open.toFixed(2)),\r\n      h: parseFloat(high.toFixed(2)),\r\n      l: parseFloat(Math.max(0.1, low).toFixed(2)),\r\n      c: parseFloat(close.toFixed(2)),\r\n    });\r\n\r\n    price = close;\r\n    currentTime += timeStep;\r\n  }\r\n\r\n  return {\r\n    t: bars.map(bar => bar.t),\r\n    o: bars.map(bar => bar.o),\r\n    h: bars.map(bar => bar.h),\r\n    l: bars.map(bar => bar.l),\r\n    c: bars.map(bar => bar.c),\r\n  };\r\n};\r\n\r\nconst getTimeStep = (resolution: ResolutionString): number => {\r\n  switch (resolution) {\r\n    case \"1\": return 60; // 1 minute\r\n    case \"5\": return 300; // 5 minutes\r\n    case \"15\": return 900; // 15 minutes\r\n    case \"30\": return 1800; // 30 minutes\r\n    case \"60\": return 3600; // 1 hour\r\n    case \"D\": return 86400; // 1 day\r\n    case \"W\": return 604800; // 1 week\r\n    case \"M\": return 2592000; // 1 month (30 days)\r\n    default: return 86400; // Default to 1 day\r\n  }\r\n};\r\n\r\n// Legacy functions for backward compatibility\r\nexport const getSymbolHistories = getEODHDHistoricalData;\r\n\r\nexport const GETv1HistoryBySymbol = ({ resolution = 5, symbol, from, to, quote = 0, compress }: HistroyParams) => {\r\n  // Redirect to EODHD function\r\n  return getEODHDHistoricalData({\r\n    from: from || 0,\r\n    to: to || Math.floor(Date.now() / 1000),\r\n    resolution: resolution.toString() as ResolutionString,\r\n    symbol\r\n  });\r\n};\r\n"], "mappings": "AAEA;AACA,MAAMA,aAAa,GAAGC,OAAO,CAACC,GAAG,CAACC,uBAAuB,IAAI,MAAM,CAAC,CAAC;AACrE,MAAMC,cAAc,GAAG,uBAAuB;AAW9C;AACA,OAAO,MAAMC,UAAU,GAAG;EACxB,UAAU,EAAE;IAAEC,IAAI,EAAE,+BAA+B;IAAEC,MAAM,EAAE;EAAU,CAAC;EACxE,UAAU,EAAE;IAAED,IAAI,EAAE,sCAAsC;IAAEC,MAAM,EAAE;EAAU,CAAC;EAC/E,UAAU,EAAE;IAAED,IAAI,EAAE,4BAA4B;IAAEC,MAAM,EAAE;EAAa,CAAC;EACxE,UAAU,EAAE;IAAED,IAAI,EAAE,sBAAsB;IAAEC,MAAM,EAAE;EAAe,CAAC;EACpE,UAAU,EAAE;IAAED,IAAI,EAAE,iBAAiB;IAAEC,MAAM,EAAE;EAAU,CAAC;EAC1D,UAAU,EAAE;IAAED,IAAI,EAAE,qCAAqC;IAAEC,MAAM,EAAE;EAAY,CAAC;EAChF,UAAU,EAAE;IAAED,IAAI,EAAE,gBAAgB;IAAEC,MAAM,EAAE;EAAa,CAAC;EAC5D,UAAU,EAAE;IAAED,IAAI,EAAE,mBAAmB;IAAEC,MAAM,EAAE;EAAyB,CAAC;EAC3E,UAAU,EAAE;IAAED,IAAI,EAAE,+BAA+B;IAAEC,MAAM,EAAE;EAAO,CAAC;EACrE,UAAU,EAAE;IAAED,IAAI,EAAE,uBAAuB;IAAEC,MAAM,EAAE;EAAO;AAC9D,CAAC;;AAED;AACA,OAAO,MAAMC,aAAa,GAAG,MAAOC,MAAc,IAAK;EACrD,IAAI;IAAA,IAAAC,WAAA;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CACzB,GAAER,cAAe,cAAaK,MAAO,cAAaT,aAAc,WACnE,CAAC;IAED,IAAI,CAACW,QAAQ,CAACE,EAAE,EAAE;MAChB,MAAM,IAAIC,KAAK,CAAE,oBAAmBH,QAAQ,CAACI,UAAW,EAAC,CAAC;IAC5D;IAEA,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;;IAElC;IACA,OAAO;MACLC,UAAU,EAAE,GAAG;MACfF,IAAI,EAAE,CAAC;QACL,QAAQ,EAAEP,MAAM,CAACU,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAAE;QAChC,QAAQ,EAAE,EAAAT,WAAA,GAAAM,IAAI,CAACI,KAAK,cAAAV,WAAA,uBAAVA,WAAA,CAAYW,QAAQ,CAAC,CAAC,KAAI,GAAG;QAAE;QACzCC,SAAS,EAAEN,IAAI,CAACM,SAAS;QACzBC,MAAM,EAAEP,IAAI,CAACO,MAAM;QACnBC,QAAQ,EAAER,IAAI,CAACQ;MACjB,CAAC;IACH,CAAC;EACH,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAE,wCAAuChB,MAAO,GAAE,EAAEgB,KAAK,CAAC;IACvE,OAAO;MACLP,UAAU,EAAE,GAAG;MACfF,IAAI,EAAE;IACR,CAAC;EACH;AACF,CAAC;;AAED;AACA,OAAO,MAAMW,gBAAgB,GAAGnB,aAAa;;AAE7C;AACA,MAAMoB,gBAAgB,GAAIC,UAA4B,IAAK;EACzD,QAAQA,UAAU;IAChB,KAAK,GAAG;IACR,KAAK,GAAG;IACR,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;MACP,OAAO,IAAI;IACb;MACE,OAAO,aAAa,CAACC,IAAI,CAACD,UAAU,CAAC,GAAGE,MAAM,CAACF,UAAU,CAAC,CAACG,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,GAAGH,UAAU;EACjG;AACF,CAAC;AASD;AACA,OAAO,MAAMI,eAAe,GAAG,MAAAA,CAAO;EAAEC,IAAI;EAAEC,EAAE;EAAEN,UAAU;EAAEpB;AAAqB,CAAC,KAAK;EACvFiB,OAAO,CAACU,GAAG,CAAE,iCAAgC3B,MAAO,SAAQyB,IAAK,OAAMC,EAAG,EAAC,CAAC;;EAE5E;EACA;EACA,MAAME,QAAQ,GAAGC,oBAAoB,CAACJ,IAAI,EAAEC,EAAE,EAAEN,UAAU,CAAC;EAE3D,OAAO;IACLX,UAAU,EAAE,GAAG;IACfF,IAAI,EAAEqB;EACR,CAAC;AACH,CAAC;;AAED;AACA,OAAO,MAAME,sBAAsB,GAAG,MAAAA,CAAO;EAAEL,IAAI;EAAEC,EAAE;EAAEN,UAAU;EAAEpB;AAAqB,CAAC,KAAK;EAC9F,IAAI;IACFiB,OAAO,CAACU,GAAG,CAAE,2CAA0C3B,MAAO,SAAQyB,IAAK,OAAMC,EAAG,EAAC,CAAC;;IAEtF;IACA,MAAMK,QAAQ,GAAG,IAAIC,IAAI,CAACP,IAAI,GAAG,IAAI,CAAC,CAACQ,WAAW,CAAC,CAAC,CAACvB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAClE,MAAMwB,MAAM,GAAG,IAAIF,IAAI,CAACN,EAAE,GAAG,IAAI,CAAC,CAACO,WAAW,CAAC,CAAC,CAACvB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;IAE9D;IACA,IAAIyB,QAAQ,GAAG,EAAE;IACjB,IAAIC,MAAM,GAAG,EAAE;IAEf,IAAIhB,UAAU,KAAK,GAAG,IAAIA,UAAU,KAAK,GAAG,IAAIA,UAAU,KAAK,GAAG,EAAE;MAClE;MACAe,QAAQ,GAAI,GAAExC,cAAe,QAAOK,MAAO,EAAC;MAC5CoC,MAAM,GAAGhB,UAAU,KAAK,GAAG,GAAG,GAAG,GAAGA,UAAU,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;IACpE,CAAC,MAAM;MACL;MACAe,QAAQ,GAAI,GAAExC,cAAe,aAAYK,MAAO,EAAC;MACjDoC,MAAM,GAAI,GAAEhB,UAAW,GAAE,CAAC,CAAC;IAC7B;IAEA,MAAMiB,GAAG,GAAI,GAAEF,QAAS,cAAa5C,aAAc,SAAQwC,QAAS,OAAMG,MAAO,WAAUE,MAAO,WAAU;IAE5G,MAAMlC,QAAQ,GAAG,MAAMC,KAAK,CAACkC,GAAG,CAAC;IAEjC,IAAI,CAACnC,QAAQ,CAACE,EAAE,EAAE;MAChB,MAAM,IAAIC,KAAK,CAAE,oBAAmBH,QAAQ,CAACI,UAAW,EAAC,CAAC;IAC5D;IAEA,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;IAElC,IAAI,CAAC8B,KAAK,CAACC,OAAO,CAAChC,IAAI,CAAC,IAAIA,IAAI,CAACiC,MAAM,KAAK,CAAC,EAAE;MAC7C,OAAO;QAAE/B,UAAU,EAAE,GAAG;QAAEF,IAAI,EAAE;UAAEkC,CAAC,EAAE,EAAE;UAAEC,CAAC,EAAE,EAAE;UAAEC,CAAC,EAAE,EAAE;UAAEC,CAAC,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAG;MAAE,CAAC;IACzE;;IAEA;IACA,MAAMC,eAAe,GAAG;MACtBL,CAAC,EAAElC,IAAI,CAACwC,GAAG,CAACC,IAAI,IAAI;QAClB;QACA,MAAMC,IAAI,GAAGD,IAAI,CAACE,QAAQ,IAAIF,IAAI,CAACC,IAAI;QACvC,OAAOE,IAAI,CAACC,KAAK,CAAC,IAAIpB,IAAI,CAACiB,IAAI,CAAC,CAACI,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;MACpD,CAAC,CAAC;MACFX,CAAC,EAAEnC,IAAI,CAACwC,GAAG,CAACC,IAAI,IAAIM,UAAU,CAACN,IAAI,CAACO,IAAI,CAAC,CAAC;MAC1CZ,CAAC,EAAEpC,IAAI,CAACwC,GAAG,CAACC,IAAI,IAAIM,UAAU,CAACN,IAAI,CAACQ,IAAI,CAAC,CAAC;MAC1CZ,CAAC,EAAErC,IAAI,CAACwC,GAAG,CAACC,IAAI,IAAIM,UAAU,CAACN,IAAI,CAACS,GAAG,CAAC,CAAC;MACzCZ,CAAC,EAAEtC,IAAI,CAACwC,GAAG,CAACC,IAAI,IAAIM,UAAU,CAACN,IAAI,CAACrC,KAAK,CAAC;IAC5C,CAAC;IAEDM,OAAO,CAACU,GAAG,CAAE,oCAAmCpB,IAAI,CAACiC,MAAO,oBAAmBxC,MAAO,EAAC,CAAC;IACxF,OAAO;MAAES,UAAU,EAAE,GAAG;MAAEF,IAAI,EAAEuC;IAAgB,CAAC;EAEnD,CAAC,CAAC,OAAO9B,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAE,kDAAiDhB,MAAO,GAAE,EAAEgB,KAAK,CAAC;;IAEjF;IACAC,OAAO,CAACU,GAAG,CAAE,6CAA4C3B,MAAO,EAAC,CAAC;IAClE,MAAM4B,QAAQ,GAAGC,oBAAoB,CAACJ,IAAI,EAAEC,EAAE,EAAEN,UAAU,CAAC;IAC3D,OAAO;MAAEX,UAAU,EAAE,GAAG;MAAEF,IAAI,EAAEqB;IAAS,CAAC;EAC5C;AACF,CAAC;;AAED;AACA,MAAMC,oBAAoB,GAAGA,CAACJ,IAAY,EAAEC,EAAU,EAAEN,UAA4B,KAAK;EACvF,MAAMsC,IAAI,GAAG,EAAE;EACf,MAAMC,QAAQ,GAAGC,WAAW,CAACxC,UAAU,CAAC;EACxC,IAAIyC,WAAW,GAAGpC,IAAI;EACtB,IAAIqC,KAAK,GAAG,IAAI,CAAC,CAAC;;EAElB,OAAOD,WAAW,IAAInC,EAAE,IAAIgC,IAAI,CAAClB,MAAM,GAAG,IAAI,EAAE;IAAE;IAChD,MAAMe,IAAI,GAAGO,KAAK;IAClB,MAAMhD,MAAM,GAAG,CAACqC,IAAI,CAACY,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;IAC1C,MAAMpD,KAAK,GAAGwC,IAAI,CAACa,GAAG,CAAC,GAAG,EAAET,IAAI,GAAGzC,MAAM,CAAC;IAC1C,MAAM0C,IAAI,GAAGL,IAAI,CAACa,GAAG,CAACT,IAAI,EAAE5C,KAAK,CAAC,GAAGwC,IAAI,CAACY,MAAM,CAAC,CAAC,GAAG,GAAG;IACxD,MAAMN,GAAG,GAAGN,IAAI,CAACc,GAAG,CAACV,IAAI,EAAE5C,KAAK,CAAC,GAAGwC,IAAI,CAACY,MAAM,CAAC,CAAC,GAAG,GAAG;IAEvDL,IAAI,CAACQ,IAAI,CAAC;MACRzB,CAAC,EAAEoB,WAAW;MACdnB,CAAC,EAAEY,UAAU,CAACC,IAAI,CAACY,OAAO,CAAC,CAAC,CAAC,CAAC;MAC9BxB,CAAC,EAAEW,UAAU,CAACE,IAAI,CAACW,OAAO,CAAC,CAAC,CAAC,CAAC;MAC9BvB,CAAC,EAAEU,UAAU,CAACH,IAAI,CAACa,GAAG,CAAC,GAAG,EAAEP,GAAG,CAAC,CAACU,OAAO,CAAC,CAAC,CAAC,CAAC;MAC5CtB,CAAC,EAAES,UAAU,CAAC3C,KAAK,CAACwD,OAAO,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC;IAEFL,KAAK,GAAGnD,KAAK;IACbkD,WAAW,IAAIF,QAAQ;EACzB;EAEA,OAAO;IACLlB,CAAC,EAAEiB,IAAI,CAACX,GAAG,CAACqB,GAAG,IAAIA,GAAG,CAAC3B,CAAC,CAAC;IACzBC,CAAC,EAAEgB,IAAI,CAACX,GAAG,CAACqB,GAAG,IAAIA,GAAG,CAAC1B,CAAC,CAAC;IACzBC,CAAC,EAAEe,IAAI,CAACX,GAAG,CAACqB,GAAG,IAAIA,GAAG,CAACzB,CAAC,CAAC;IACzBC,CAAC,EAAEc,IAAI,CAACX,GAAG,CAACqB,GAAG,IAAIA,GAAG,CAACxB,CAAC,CAAC;IACzBC,CAAC,EAAEa,IAAI,CAACX,GAAG,CAACqB,GAAG,IAAIA,GAAG,CAACvB,CAAC;EAC1B,CAAC;AACH,CAAC;AAED,MAAMe,WAAW,GAAIxC,UAA4B,IAAa;EAC5D,QAAQA,UAAU;IAChB,KAAK,GAAG;MAAE,OAAO,EAAE;IAAE;IACrB,KAAK,GAAG;MAAE,OAAO,GAAG;IAAE;IACtB,KAAK,IAAI;MAAE,OAAO,GAAG;IAAE;IACvB,KAAK,IAAI;MAAE,OAAO,IAAI;IAAE;IACxB,KAAK,IAAI;MAAE,OAAO,IAAI;IAAE;IACxB,KAAK,GAAG;MAAE,OAAO,KAAK;IAAE;IACxB,KAAK,GAAG;MAAE,OAAO,MAAM;IAAE;IACzB,KAAK,GAAG;MAAE,OAAO,OAAO;IAAE;IAC1B;MAAS,OAAO,KAAK;IAAE;EACzB;AACF,CAAC;;AAED;AACA,OAAO,MAAMiD,kBAAkB,GAAGvC,sBAAsB;AAExD,OAAO,MAAMwC,oBAAoB,GAAGA,CAAC;EAAElD,UAAU,GAAG,CAAC;EAAEpB,MAAM;EAAEyB,IAAI;EAAEC,EAAE;EAAE6C,KAAK,GAAG,CAAC;EAAEC;AAAwB,CAAC,KAAK;EAChH;EACA,OAAO1C,sBAAsB,CAAC;IAC5BL,IAAI,EAAEA,IAAI,IAAI,CAAC;IACfC,EAAE,EAAEA,EAAE,IAAIyB,IAAI,CAACC,KAAK,CAACpB,IAAI,CAACyC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;IACvCrD,UAAU,EAAEA,UAAU,CAACR,QAAQ,CAAC,CAAqB;IACrDZ;EACF,CAAC,CAAC;AACJ,CAAC;AAAC0E,EAAA,GARWJ,oBAAoB;AAAA,IAAAI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
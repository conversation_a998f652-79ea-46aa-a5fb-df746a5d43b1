{"ast": null, "code": "var _jsxFileName = \"D:\\\\trading-view-charting-library\\\\tradingview(taiyi)\\\\tradingview-master_taiyi\\\\src\\\\tradingview\\\\index.tsx\",\n  _s = $RefreshSig$();\nimport { useEffect } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst index = () => {\n  _s();\n  useEffect(() => {\n    const tvWidget = new window.TradingView.widget({\n      container: \"chartContainer\",\n      locale: \"en\",\n      library_path: \"charting_library/\",\n      datafeed: new window.Datafeeds.UDFCompatibleDatafeed(\"https://demo_feed.tradingview.com\"),\n      symbol: \"AAPL\",\n      interval: \"1D\",\n      fullscreen: true,\n      disabled_features: [\"use_localstorage_for_settings\"],\n      enabled_features: [\"study_templates\"]\n    });\n    return () => {\n      tvWidget.remove();\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: \"chartContainer\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 10\n  }, this);\n};\n_s(index, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\nexport default index;", "map": {"version": 3, "names": ["useEffect", "jsxDEV", "_jsxDEV", "index", "_s", "tvWidget", "window", "TradingView", "widget", "container", "locale", "library_path", "datafeed", "Datafeeds", "UDFCompatibleDatafeed", "symbol", "interval", "fullscreen", "disabled_features", "enabled_features", "remove", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["D:/trading-view-charting-library/tradingview(taiyi)/tradingview-master_taiyi/src/tradingview/index.tsx"], "sourcesContent": ["import { useEffect } from \"react\";\r\n\r\ndeclare global {\r\n  interface Window {\r\n    TradingView: any;\r\n    Datafeeds: any;\r\n  }\r\n}\r\n\r\nconst index = () => {\r\n  useEffect(() => {\r\n    const tvWidget = new window.TradingView.widget({\r\n      container: \"chartContainer\",\r\n      locale: \"en\",\r\n      library_path: \"charting_library/\",\r\n      datafeed: new window.Datafeeds.UDFCompatibleDatafeed(\"https://demo_feed.tradingview.com\"),\r\n      symbol: \"AAPL\",\r\n      interval: \"1D\",\r\n      fullscreen: true,\r\n      disabled_features: [\"use_localstorage_for_settings\"],\r\n      enabled_features: [\"study_templates\"],\r\n    });\r\n\r\n    return () => {\r\n      tvWidget.remove();\r\n    };\r\n  }, []);\r\n\r\n  return <div id=\"chartContainer\"></div>;\r\n};\r\n\r\nexport default index;\r\n"], "mappings": ";;AAAA,SAASA,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AASlC,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClBJ,SAAS,CAAC,MAAM;IACd,MAAMK,QAAQ,GAAG,IAAIC,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC;MAC7CC,SAAS,EAAE,gBAAgB;MAC3BC,MAAM,EAAE,IAAI;MACZC,YAAY,EAAE,mBAAmB;MACjCC,QAAQ,EAAE,IAAIN,MAAM,CAACO,SAAS,CAACC,qBAAqB,CAAC,mCAAmC,CAAC;MACzFC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,IAAI;MAChBC,iBAAiB,EAAE,CAAC,+BAA+B,CAAC;MACpDC,gBAAgB,EAAE,CAAC,iBAAiB;IACtC,CAAC,CAAC;IAEF,OAAO,MAAM;MACXd,QAAQ,CAACe,MAAM,CAAC,CAAC;IACnB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBAAOlB,OAAA;IAAKmB,EAAE,EAAC;EAAgB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;AACxC,CAAC;AAACrB,EAAA,CApBID,KAAK;AAsBX,eAAeA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "export const formatTime = (timestamp, format, sep = \"-\") => {\n  if (!timestamp) return \"\";\n  const offset = 8 * 60 * 60 * 1000;\n  const dateISO = new Date(timestamp * 1000 + offset).toISOString();\n  const [date, timeISO] = dateISO.split(\"T\");\n  const time = timeISO.slice(0, 5);\n  const [YYYY, MM, DD] = date.split(\"-\");\n  if (format === \"YYYYMMDDHHMM\") return `${date} ${time}`;\n  if (format === \"YYYYMMDD\") return [YYYY, MM, DD].join(sep);\n  if (format === \"MMDDHHMM\") return `${MM}${sep}${DD} ${time}`;\n  if (format === \"MMDD\") return `${MM}${sep}${DD}`;\n  if (format === \"HHMM\") return time;\n  return date;\n};", "map": {"version": 3, "names": ["formatTime", "timestamp", "format", "sep", "offset", "dateISO", "Date", "toISOString", "date", "timeISO", "split", "time", "slice", "YYYY", "MM", "DD", "join"], "sources": ["D:/trading-view-charting-library/tradingview(taiyi)/tradingview-master_taiyi/src/utils/index.ts"], "sourcesContent": ["type DateFormat = \"YYYYMMDD\" | \"YYYYMMDDHHMM\" | \"MMDDHHMM\" | \"MMDD\" | \"HHMM\";\r\n\r\nexport const formatTime = (timestamp: number, format: DateFormat, sep = \"-\"): string => {\r\n  if (!timestamp) return \"\";\r\n\r\n  const offset = 8 * 60 * 60 * 1000;\r\n  const dateISO = new Date(timestamp * 1000 + offset).toISOString();\r\n  const [date, timeISO] = dateISO.split(\"T\");\r\n  const time = timeISO.slice(0, 5);\r\n  const [YYYY, MM, DD] = date.split(\"-\");\r\n\r\n  if (format === \"YYYYMMDDHHMM\") return `${date} ${time}`;\r\n  if (format === \"YYYYMMDD\") return [YYYY, MM, DD].join(sep);\r\n  if (format === \"MMDDHHMM\") return `${MM}${sep}${DD} ${time}`;\r\n  if (format === \"MMDD\") return `${MM}${sep}${DD}`;\r\n  if (format === \"HHMM\") return time;\r\n\r\n  return date;\r\n};\r\n"], "mappings": "AAEA,OAAO,MAAMA,UAAU,GAAGA,CAACC,SAAiB,EAAEC,MAAkB,EAAEC,GAAG,GAAG,GAAG,KAAa;EACtF,IAAI,CAACF,SAAS,EAAE,OAAO,EAAE;EAEzB,MAAMG,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;EACjC,MAAMC,OAAO,GAAG,IAAIC,IAAI,CAACL,SAAS,GAAG,IAAI,GAAGG,MAAM,CAAC,CAACG,WAAW,CAAC,CAAC;EACjE,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGJ,OAAO,CAACK,KAAK,CAAC,GAAG,CAAC;EAC1C,MAAMC,IAAI,GAAGF,OAAO,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChC,MAAM,CAACC,IAAI,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAGP,IAAI,CAACE,KAAK,CAAC,GAAG,CAAC;EAEtC,IAAIR,MAAM,KAAK,cAAc,EAAE,OAAQ,GAAEM,IAAK,IAAGG,IAAK,EAAC;EACvD,IAAIT,MAAM,KAAK,UAAU,EAAE,OAAO,CAACW,IAAI,EAAEC,EAAE,EAAEC,EAAE,CAAC,CAACC,IAAI,CAACb,GAAG,CAAC;EAC1D,IAAID,MAAM,KAAK,UAAU,EAAE,OAAQ,GAAEY,EAAG,GAAEX,GAAI,GAAEY,EAAG,IAAGJ,IAAK,EAAC;EAC5D,IAAIT,MAAM,KAAK,MAAM,EAAE,OAAQ,GAAEY,EAAG,GAAEX,GAAI,GAAEY,EAAG,EAAC;EAChD,IAAIb,MAAM,KAAK,MAAM,EAAE,OAAOS,IAAI;EAElC,OAAOH,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
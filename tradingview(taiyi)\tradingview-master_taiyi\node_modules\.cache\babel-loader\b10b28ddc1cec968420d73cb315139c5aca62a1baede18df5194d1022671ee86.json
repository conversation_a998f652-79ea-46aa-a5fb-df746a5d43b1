{"ast": null, "code": "import { getSymbolHistories, getQuoteBySymbol, EGX_STOCKS } from \"../api\";\nimport { formatTime } from \"../utils\";\n\n// EGX-specific datafeed for TradingView\nconst EGXDataFeed = {\n  onReady: callback => {\n    console.log(\"[EGX DataFeed]: Initializing EGX datafeed\");\n    const config = {\n      supported_resolutions: [\"1\", \"5\", \"15\", \"30\", \"60\", \"D\", \"W\", \"M\"],\n      supports_group_request: false,\n      supports_marks: false,\n      supports_search: true,\n      supports_timescale_marks: false,\n      currency_codes: [\"EGP\"],\n      exchanges: [{\n        value: \"EGX\",\n        name: \"Egyptian Exchange\",\n        desc: \"Egyptian Exchange (EGX)\"\n      }]\n    };\n    setTimeout(() => callback(config));\n  },\n  searchSymbols: (userInput, exchange, symbolType, onResultReadyCallback) => {\n    console.log(\"[EGX DataFeed]: Searching for symbols:\", userInput);\n    const results = Object.keys(EGX_STOCKS).filter(symbol => symbol.toLowerCase().includes(userInput.toLowerCase()) || EGX_STOCKS[symbol].name.toLowerCase().includes(userInput.toLowerCase())).map(symbol => ({\n      symbol: symbol,\n      full_name: `EGX:${symbol.split('.')[0]}`,\n      description: EGX_STOCKS[symbol].name,\n      exchange: \"EGX\",\n      ticker: symbol,\n      type: \"stock\"\n    }));\n    console.log(`[EGX DataFeed]: Found ${results.length} matching symbols`);\n    onResultReadyCallback(results);\n  },\n  resolveSymbol: async (symbolName, onSymbolResolvedCallback) => {\n    console.log(\"[EGX DataFeed]: Resolving symbol\", symbolName);\n\n    // Convert symbol format (handle both EGX:COMI and COMI.EGX formats)\n    let eodhSymbol = symbolName;\n    if (symbolName.startsWith(\"EGX:\")) {\n      eodhSymbol = symbolName.replace(\"EGX:\", \"\") + \".EGX\";\n    }\n    const stockInfo = EGX_STOCKS[eodhSymbol];\n    const description = stockInfo ? stockInfo.name : symbolName;\n    try {\n      var _quoteResponse$data, _quoteResponse$data$, _price$toString$split;\n      // Try to get current quote to validate symbol and get price scale\n      const quoteResponse = await getQuoteBySymbol(eodhSymbol);\n      const price = (quoteResponse === null || quoteResponse === void 0 ? void 0 : (_quoteResponse$data = quoteResponse.data) === null || _quoteResponse$data === void 0 ? void 0 : (_quoteResponse$data$ = _quoteResponse$data[0]) === null || _quoteResponse$data$ === void 0 ? void 0 : _quoteResponse$data$[\"200026\"]) || \"25.00\";\n      const countDecimal = ((_price$toString$split = price.toString().split(\".\")[1]) === null || _price$toString$split === void 0 ? void 0 : _price$toString$split.length) || 2;\n      const pricescale = Math.pow(10, countDecimal);\n      const symbolInfo = {\n        description: description,\n        name: symbolName,\n        full_name: `EGX:${symbolName}`,\n        ticker: eodhSymbol,\n        session: \"0930-1530\",\n        // EGX trading hours (9:30 AM - 3:30 PM Cairo time)\n        timezone: \"Africa/Cairo\",\n        type: \"stock\",\n        exchange: \"EGX\",\n        listed_exchange: \"EGX\",\n        format: \"price\",\n        has_intraday: true,\n        has_daily: true,\n        has_weekly_and_monthly: true,\n        minmov: 1,\n        minmove2: 0,\n        fractional: false,\n        currency_code: \"EGP\",\n        pricescale: pricescale,\n        supported_resolutions: [\"1\", \"5\", \"15\", \"30\", \"60\", \"D\", \"W\", \"M\"],\n        volume_precision: 0,\n        data_status: \"streaming\"\n      };\n      console.log(`[EGX DataFeed]: Successfully resolved ${symbolName}`);\n      onSymbolResolvedCallback(symbolInfo);\n    } catch (error) {\n      console.error(`[EGX DataFeed]: Error resolving symbol ${symbolName}:`, error);\n\n      // Fallback symbol info\n      const fallbackSymbolInfo = {\n        description: description,\n        name: symbolName,\n        ticker: eodhSymbol,\n        session: \"0930-1530\",\n        timezone: \"Africa/Cairo\",\n        type: \"stock\",\n        exchange: \"EGX\",\n        listed_exchange: \"EGX\",\n        has_intraday: true,\n        has_daily: true,\n        has_weekly_and_monthly: true,\n        minmov: 1,\n        minmove2: 0,\n        fractional: false,\n        currency_code: \"EGP\",\n        pricescale: 100,\n        // Default 2 decimal places\n        supported_resolutions: [\"1\", \"5\", \"15\", \"30\", \"60\", \"D\", \"W\", \"M\"],\n        volume_precision: 0,\n        data_status: \"streaming\"\n      };\n      onSymbolResolvedCallback(fallbackSymbolInfo);\n    }\n  },\n  getBars: async (symbolInfo, resolution, periodParams, onHistoryCallback, onErrorCallback) => {\n    console.log(\"[EGX DataFeed]: Getting bars for\", symbolInfo.name);\n    const symbol = symbolInfo.ticker || symbolInfo.name;\n    const {\n      from,\n      to\n    } = periodParams;\n    if (symbol) {\n      try {\n        var _data$t;\n        const {\n          data,\n          statusCode\n        } = await getSymbolHistories({\n          resolution,\n          symbol,\n          to,\n          from\n        });\n        console.log(\"[EGX DataFeed]: EODHD API Response\");\n        console.table({\n          symbol: symbol,\n          from: formatTime(from, \"YYYYMMDD\"),\n          to: formatTime(to, \"YYYYMMDD\"),\n          resolution,\n          \"data points\": data === null || data === void 0 ? void 0 : (_data$t = data.t) === null || _data$t === void 0 ? void 0 : _data$t.length\n        });\n        if (statusCode !== 200 || !(data !== null && data !== void 0 && data.t) || data.t.length === 0) {\n          console.log(\"[EGX DataFeed]: No data available\");\n          onHistoryCallback([], {\n            noData: true\n          });\n          return;\n        }\n        const {\n          l,\n          h,\n          o,\n          c,\n          t\n        } = data;\n        const bars = t === null || t === void 0 ? void 0 : t.map((timestamp, index) => ({\n          time: timestamp * 1000,\n          // Convert to milliseconds\n          low: l[index],\n          high: h[index],\n          open: o[index],\n          close: c[index]\n        }));\n\n        // Sort bars by time\n        bars.sort((a, b) => a.time - b.time);\n        console.log(`[EGX DataFeed]: Returning ${bars.length} bars for ${symbol}`);\n        onHistoryCallback(bars, {\n          noData: false\n        });\n      } catch (error) {\n        console.error(\"[EGX DataFeed]: Error fetching data\", error);\n        const errorMessage = error instanceof Error ? error.message : String(error);\n        onErrorCallback(errorMessage);\n      }\n    }\n  },\n  subscribeBars: (symbolInfo, resolution, onRealtimeCallback, subscriberUID) => {\n    console.log(`[EGX DataFeed]: Subscribing to real-time data for ${symbolInfo.name}`);\n    // TODO: Implement real-time EGX data subscription when needed\n  },\n  unsubscribeBars: subscriberUID => {\n    console.log(`[EGX DataFeed]: Unsubscribing ${subscriberUID}`);\n    // TODO: Implement real-time data unsubscription when needed\n  }\n};\nexport default EGXDataFeed;", "map": {"version": 3, "names": ["getSymbolHistories", "getQuoteBySymbol", "EGX_STOCKS", "formatTime", "EGXDataFeed", "onReady", "callback", "console", "log", "config", "supported_resolutions", "supports_group_request", "supports_marks", "supports_search", "supports_timescale_marks", "currency_codes", "exchanges", "value", "name", "desc", "setTimeout", "searchSymbols", "userInput", "exchange", "symbolType", "onResultReadyCallback", "results", "Object", "keys", "filter", "symbol", "toLowerCase", "includes", "map", "full_name", "split", "description", "ticker", "type", "length", "resolveSymbol", "symbolName", "onSymbolResolvedCallback", "eodhSymbol", "startsWith", "replace", "stockInfo", "_quoteResponse$data", "_quoteResponse$data$", "_price$toString$split", "quoteResponse", "price", "data", "countDecimal", "toString", "pricescale", "Math", "pow", "symbolInfo", "session", "timezone", "listed_exchange", "format", "has_intraday", "has_daily", "has_weekly_and_monthly", "<PERSON><PERSON>v", "minmove2", "fractional", "currency_code", "volume_precision", "data_status", "error", "fallbackSymbolInfo", "getBars", "resolution", "periodParams", "onHistoryCallback", "onError<PERSON>allback", "from", "to", "_data$t", "statusCode", "table", "t", "noData", "l", "h", "o", "c", "bars", "timestamp", "index", "time", "low", "high", "open", "close", "sort", "a", "b", "errorMessage", "Error", "message", "String", "subscribeBars", "onRealtimeCallback", "subscriberUID", "unsubscribeBars"], "sources": ["D:/trading-view-charting-library/tradingview(taiyi)/tradingview-master_taiyi/src/tradingview/egx-datafeed.ts"], "sourcesContent": ["import { OnReadyCallback, LibrarySymbolInfo, ResolutionString, PeriodParams, HistoryCallback, Bar, ErrorCallback } from \"@/public/charting_library\";\nimport { getSymbolHistories, getQuoteBySymbol, EGX_STOCKS } from \"../api\";\nimport { formatTime } from \"../utils\";\n\n// EGX-specific datafeed for TradingView\nconst EGXDataFeed = {\n  onReady: (callback: OnReadyCallback) => {\n    console.log(\"[EGX DataFeed]: Initializing EGX datafeed\");\n    const config = {\n      supported_resolutions: [\"1\", \"5\", \"15\", \"30\", \"60\", \"D\", \"W\", \"M\"] as ResolutionString[],\n      supports_group_request: false,\n      supports_marks: false,\n      supports_search: true,\n      supports_timescale_marks: false,\n      currency_codes: [\"EGP\"],\n      exchanges: [\n        {\n          value: \"EGX\",\n          name: \"Egyptian Exchange\",\n          desc: \"Egyptian Exchange (EGX)\"\n        }\n      ]\n    };\n    setTimeout(() => callback(config));\n  },\n  \n  searchSymbols: (userInput: string, exchange: string, symbolType: string, onResultReadyCallback: any) => {\n    console.log(\"[EGX DataFeed]: Searching for symbols:\", userInput);\n    \n    const results = Object.keys(EGX_STOCKS)\n      .filter(symbol => \n        symbol.toLowerCase().includes(userInput.toLowerCase()) ||\n        EGX_STOCKS[symbol as keyof typeof EGX_STOCKS].name.toLowerCase().includes(userInput.toLowerCase())\n      )\n      .map(symbol => ({\n        symbol: symbol,\n        full_name: `EGX:${symbol.split('.')[0]}`,\n        description: EGX_STOCKS[symbol as keyof typeof EGX_STOCKS].name,\n        exchange: \"EGX\",\n        ticker: symbol,\n        type: \"stock\",\n      }));\n    \n    console.log(`[EGX DataFeed]: Found ${results.length} matching symbols`);\n    onResultReadyCallback(results);\n  },\n\n  resolveSymbol: async (symbolName: string, onSymbolResolvedCallback: (info: LibrarySymbolInfo) => void) => {\n    console.log(\"[EGX DataFeed]: Resolving symbol\", symbolName);\n    \n    // Convert symbol format (handle both EGX:COMI and COMI.EGX formats)\n    let eodhSymbol = symbolName;\n    if (symbolName.startsWith(\"EGX:\")) {\n      eodhSymbol = symbolName.replace(\"EGX:\", \"\") + \".EGX\";\n    }\n    \n    const stockInfo = EGX_STOCKS[eodhSymbol as keyof typeof EGX_STOCKS];\n    const description = stockInfo ? stockInfo.name : symbolName;\n\n    try {\n      // Try to get current quote to validate symbol and get price scale\n      const quoteResponse = await getQuoteBySymbol(eodhSymbol);\n      const price = quoteResponse?.data?.[0]?.[\"200026\"] || \"25.00\";\n      const countDecimal = price.toString().split(\".\")[1]?.length || 2;\n      const pricescale = Math.pow(10, countDecimal);\n\n      const symbolInfo: LibrarySymbolInfo = {\n        description: description,\n        name: symbolName,\n        full_name: `EGX:${symbolName}`,\n        ticker: eodhSymbol,\n        session: \"0930-1530\", // EGX trading hours (9:30 AM - 3:30 PM Cairo time)\n        timezone: \"Africa/Cairo\",\n        type: \"stock\",\n        exchange: \"EGX\",\n        listed_exchange: \"EGX\",\n        format: \"price\",\n        has_intraday: true,\n        has_daily: true,\n        has_weekly_and_monthly: true,\n        minmov: 1,\n        minmove2: 0,\n        fractional: false,\n        currency_code: \"EGP\",\n        pricescale: pricescale,\n        supported_resolutions: [\"1\", \"5\", \"15\", \"30\", \"60\", \"D\", \"W\", \"M\"] as ResolutionString[],\n        volume_precision: 0,\n        data_status: \"streaming\",\n      };\n      \n      console.log(`[EGX DataFeed]: Successfully resolved ${symbolName}`);\n      onSymbolResolvedCallback(symbolInfo);\n    } catch (error) {\n      console.error(`[EGX DataFeed]: Error resolving symbol ${symbolName}:`, error);\n      \n      // Fallback symbol info\n      const fallbackSymbolInfo: LibrarySymbolInfo = {\n        description: description,\n        name: symbolName,\n        ticker: eodhSymbol,\n        session: \"0930-1530\",\n        timezone: \"Africa/Cairo\",\n        type: \"stock\",\n        exchange: \"EGX\",\n        listed_exchange: \"EGX\",\n        has_intraday: true,\n        has_daily: true,\n        has_weekly_and_monthly: true,\n        minmov: 1,\n        minmove2: 0,\n        fractional: false,\n        currency_code: \"EGP\",\n        pricescale: 100, // Default 2 decimal places\n        supported_resolutions: [\"1\", \"5\", \"15\", \"30\", \"60\", \"D\", \"W\", \"M\"] as ResolutionString[],\n        volume_precision: 0,\n        data_status: \"streaming\",\n      };\n      \n      onSymbolResolvedCallback(fallbackSymbolInfo);\n    }\n  },\n\n  getBars: async (\n    symbolInfo: LibrarySymbolInfo,\n    resolution: ResolutionString,\n    periodParams: PeriodParams,\n    onHistoryCallback: HistoryCallback,\n    onErrorCallback: ErrorCallback\n  ) => {\n    console.log(\"[EGX DataFeed]: Getting bars for\", symbolInfo.name);\n    const symbol = symbolInfo.ticker || symbolInfo.name;\n    const { from, to } = periodParams;\n\n    if (symbol) {\n      try {\n        const { data, statusCode } = await getSymbolHistories({\n          resolution,\n          symbol,\n          to,\n          from,\n        });\n\n        console.log(\"[EGX DataFeed]: EODHD API Response\");\n        console.table({\n          symbol: symbol,\n          from: formatTime(from, \"YYYYMMDD\"),\n          to: formatTime(to, \"YYYYMMDD\"),\n          resolution,\n          \"data points\": data?.t?.length,\n        });\n\n        if (statusCode !== 200 || !data?.t || data.t.length === 0) {\n          console.log(\"[EGX DataFeed]: No data available\");\n          onHistoryCallback([], { noData: true });\n          return;\n        }\n\n        const { l, h, o, c, t } = data;\n\n        const bars = t?.map((timestamp: number, index: number) => ({\n          time: timestamp * 1000, // Convert to milliseconds\n          low: l[index],\n          high: h[index],\n          open: o[index],\n          close: c[index],\n        })) as Bar[];\n\n        // Sort bars by time\n        bars.sort((a: Bar, b: Bar) => a.time - b.time);\n\n        console.log(`[EGX DataFeed]: Returning ${bars.length} bars for ${symbol}`);\n        onHistoryCallback(bars, { noData: false });\n      } catch (error) {\n        console.error(\"[EGX DataFeed]: Error fetching data\", error);\n        const errorMessage = error instanceof Error ? error.message : String(error);\n        onErrorCallback(errorMessage);\n      }\n    }\n  },\n  \n  subscribeBars: (symbolInfo: LibrarySymbolInfo, resolution: ResolutionString, onRealtimeCallback: any, subscriberUID: string) => {\n    console.log(`[EGX DataFeed]: Subscribing to real-time data for ${symbolInfo.name}`);\n    // TODO: Implement real-time EGX data subscription when needed\n  },\n  \n  unsubscribeBars: (subscriberUID: string) => {\n    console.log(`[EGX DataFeed]: Unsubscribing ${subscriberUID}`);\n    // TODO: Implement real-time data unsubscription when needed\n  },\n};\n\nexport default EGXDataFeed;\n"], "mappings": "AACA,SAASA,kBAAkB,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,QAAQ;AACzE,SAASC,UAAU,QAAQ,UAAU;;AAErC;AACA,MAAMC,WAAW,GAAG;EAClBC,OAAO,EAAGC,QAAyB,IAAK;IACtCC,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;IACxD,MAAMC,MAAM,GAAG;MACbC,qBAAqB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAuB;MACxFC,sBAAsB,EAAE,KAAK;MAC7BC,cAAc,EAAE,KAAK;MACrBC,eAAe,EAAE,IAAI;MACrBC,wBAAwB,EAAE,KAAK;MAC/BC,cAAc,EAAE,CAAC,KAAK,CAAC;MACvBC,SAAS,EAAE,CACT;QACEC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE,mBAAmB;QACzBC,IAAI,EAAE;MACR,CAAC;IAEL,CAAC;IACDC,UAAU,CAAC,MAAMd,QAAQ,CAACG,MAAM,CAAC,CAAC;EACpC,CAAC;EAEDY,aAAa,EAAEA,CAACC,SAAiB,EAAEC,QAAgB,EAAEC,UAAkB,EAAEC,qBAA0B,KAAK;IACtGlB,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEc,SAAS,CAAC;IAEhE,MAAMI,OAAO,GAAGC,MAAM,CAACC,IAAI,CAAC1B,UAAU,CAAC,CACpC2B,MAAM,CAACC,MAAM,IACZA,MAAM,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACV,SAAS,CAACS,WAAW,CAAC,CAAC,CAAC,IACtD7B,UAAU,CAAC4B,MAAM,CAA4B,CAACZ,IAAI,CAACa,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACV,SAAS,CAACS,WAAW,CAAC,CAAC,CACnG,CAAC,CACAE,GAAG,CAACH,MAAM,KAAK;MACdA,MAAM,EAAEA,MAAM;MACdI,SAAS,EAAG,OAAMJ,MAAM,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE,EAAC;MACxCC,WAAW,EAAElC,UAAU,CAAC4B,MAAM,CAA4B,CAACZ,IAAI;MAC/DK,QAAQ,EAAE,KAAK;MACfc,MAAM,EAAEP,MAAM;MACdQ,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;IAEL/B,OAAO,CAACC,GAAG,CAAE,yBAAwBkB,OAAO,CAACa,MAAO,mBAAkB,CAAC;IACvEd,qBAAqB,CAACC,OAAO,CAAC;EAChC,CAAC;EAEDc,aAAa,EAAE,MAAAA,CAAOC,UAAkB,EAAEC,wBAA2D,KAAK;IACxGnC,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEiC,UAAU,CAAC;;IAE3D;IACA,IAAIE,UAAU,GAAGF,UAAU;IAC3B,IAAIA,UAAU,CAACG,UAAU,CAAC,MAAM,CAAC,EAAE;MACjCD,UAAU,GAAGF,UAAU,CAACI,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,MAAM;IACtD;IAEA,MAAMC,SAAS,GAAG5C,UAAU,CAACyC,UAAU,CAA4B;IACnE,MAAMP,WAAW,GAAGU,SAAS,GAAGA,SAAS,CAAC5B,IAAI,GAAGuB,UAAU;IAE3D,IAAI;MAAA,IAAAM,mBAAA,EAAAC,oBAAA,EAAAC,qBAAA;MACF;MACA,MAAMC,aAAa,GAAG,MAAMjD,gBAAgB,CAAC0C,UAAU,CAAC;MACxD,MAAMQ,KAAK,GAAG,CAAAD,aAAa,aAAbA,aAAa,wBAAAH,mBAAA,GAAbG,aAAa,CAAEE,IAAI,cAAAL,mBAAA,wBAAAC,oBAAA,GAAnBD,mBAAA,CAAsB,CAAC,CAAC,cAAAC,oBAAA,uBAAxBA,oBAAA,CAA2B,QAAQ,CAAC,KAAI,OAAO;MAC7D,MAAMK,YAAY,GAAG,EAAAJ,qBAAA,GAAAE,KAAK,CAACG,QAAQ,CAAC,CAAC,CAACnB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,cAAAc,qBAAA,uBAA9BA,qBAAA,CAAgCV,MAAM,KAAI,CAAC;MAChE,MAAMgB,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEJ,YAAY,CAAC;MAE7C,MAAMK,UAA6B,GAAG;QACpCtB,WAAW,EAAEA,WAAW;QACxBlB,IAAI,EAAEuB,UAAU;QAChBP,SAAS,EAAG,OAAMO,UAAW,EAAC;QAC9BJ,MAAM,EAAEM,UAAU;QAClBgB,OAAO,EAAE,WAAW;QAAE;QACtBC,QAAQ,EAAE,cAAc;QACxBtB,IAAI,EAAE,OAAO;QACbf,QAAQ,EAAE,KAAK;QACfsC,eAAe,EAAE,KAAK;QACtBC,MAAM,EAAE,OAAO;QACfC,YAAY,EAAE,IAAI;QAClBC,SAAS,EAAE,IAAI;QACfC,sBAAsB,EAAE,IAAI;QAC5BC,MAAM,EAAE,CAAC;QACTC,QAAQ,EAAE,CAAC;QACXC,UAAU,EAAE,KAAK;QACjBC,aAAa,EAAE,KAAK;QACpBd,UAAU,EAAEA,UAAU;QACtB7C,qBAAqB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAuB;QACxF4D,gBAAgB,EAAE,CAAC;QACnBC,WAAW,EAAE;MACf,CAAC;MAEDhE,OAAO,CAACC,GAAG,CAAE,yCAAwCiC,UAAW,EAAC,CAAC;MAClEC,wBAAwB,CAACgB,UAAU,CAAC;IACtC,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdjE,OAAO,CAACiE,KAAK,CAAE,0CAAyC/B,UAAW,GAAE,EAAE+B,KAAK,CAAC;;MAE7E;MACA,MAAMC,kBAAqC,GAAG;QAC5CrC,WAAW,EAAEA,WAAW;QACxBlB,IAAI,EAAEuB,UAAU;QAChBJ,MAAM,EAAEM,UAAU;QAClBgB,OAAO,EAAE,WAAW;QACpBC,QAAQ,EAAE,cAAc;QACxBtB,IAAI,EAAE,OAAO;QACbf,QAAQ,EAAE,KAAK;QACfsC,eAAe,EAAE,KAAK;QACtBE,YAAY,EAAE,IAAI;QAClBC,SAAS,EAAE,IAAI;QACfC,sBAAsB,EAAE,IAAI;QAC5BC,MAAM,EAAE,CAAC;QACTC,QAAQ,EAAE,CAAC;QACXC,UAAU,EAAE,KAAK;QACjBC,aAAa,EAAE,KAAK;QACpBd,UAAU,EAAE,GAAG;QAAE;QACjB7C,qBAAqB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAuB;QACxF4D,gBAAgB,EAAE,CAAC;QACnBC,WAAW,EAAE;MACf,CAAC;MAED7B,wBAAwB,CAAC+B,kBAAkB,CAAC;IAC9C;EACF,CAAC;EAEDC,OAAO,EAAE,MAAAA,CACPhB,UAA6B,EAC7BiB,UAA4B,EAC5BC,YAA0B,EAC1BC,iBAAkC,EAClCC,eAA8B,KAC3B;IACHvE,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEkD,UAAU,CAACxC,IAAI,CAAC;IAChE,MAAMY,MAAM,GAAG4B,UAAU,CAACrB,MAAM,IAAIqB,UAAU,CAACxC,IAAI;IACnD,MAAM;MAAE6D,IAAI;MAAEC;IAAG,CAAC,GAAGJ,YAAY;IAEjC,IAAI9C,MAAM,EAAE;MACV,IAAI;QAAA,IAAAmD,OAAA;QACF,MAAM;UAAE7B,IAAI;UAAE8B;QAAW,CAAC,GAAG,MAAMlF,kBAAkB,CAAC;UACpD2E,UAAU;UACV7C,MAAM;UACNkD,EAAE;UACFD;QACF,CAAC,CAAC;QAEFxE,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACjDD,OAAO,CAAC4E,KAAK,CAAC;UACZrD,MAAM,EAAEA,MAAM;UACdiD,IAAI,EAAE5E,UAAU,CAAC4E,IAAI,EAAE,UAAU,CAAC;UAClCC,EAAE,EAAE7E,UAAU,CAAC6E,EAAE,EAAE,UAAU,CAAC;UAC9BL,UAAU;UACV,aAAa,EAAEvB,IAAI,aAAJA,IAAI,wBAAA6B,OAAA,GAAJ7B,IAAI,CAAEgC,CAAC,cAAAH,OAAA,uBAAPA,OAAA,CAAS1C;QAC1B,CAAC,CAAC;QAEF,IAAI2C,UAAU,KAAK,GAAG,IAAI,EAAC9B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgC,CAAC,KAAIhC,IAAI,CAACgC,CAAC,CAAC7C,MAAM,KAAK,CAAC,EAAE;UACzDhC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;UAChDqE,iBAAiB,CAAC,EAAE,EAAE;YAAEQ,MAAM,EAAE;UAAK,CAAC,CAAC;UACvC;QACF;QAEA,MAAM;UAAEC,CAAC;UAAEC,CAAC;UAAEC,CAAC;UAAEC,CAAC;UAAEL;QAAE,CAAC,GAAGhC,IAAI;QAE9B,MAAMsC,IAAI,GAAGN,CAAC,aAADA,CAAC,uBAADA,CAAC,CAAEnD,GAAG,CAAC,CAAC0D,SAAiB,EAAEC,KAAa,MAAM;UACzDC,IAAI,EAAEF,SAAS,GAAG,IAAI;UAAE;UACxBG,GAAG,EAAER,CAAC,CAACM,KAAK,CAAC;UACbG,IAAI,EAAER,CAAC,CAACK,KAAK,CAAC;UACdI,IAAI,EAAER,CAAC,CAACI,KAAK,CAAC;UACdK,KAAK,EAAER,CAAC,CAACG,KAAK;QAChB,CAAC,CAAC,CAAU;;QAEZ;QACAF,IAAI,CAACQ,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAKD,CAAC,CAACN,IAAI,GAAGO,CAAC,CAACP,IAAI,CAAC;QAE9CtF,OAAO,CAACC,GAAG,CAAE,6BAA4BkF,IAAI,CAACnD,MAAO,aAAYT,MAAO,EAAC,CAAC;QAC1E+C,iBAAiB,CAACa,IAAI,EAAE;UAAEL,MAAM,EAAE;QAAM,CAAC,CAAC;MAC5C,CAAC,CAAC,OAAOb,KAAK,EAAE;QACdjE,OAAO,CAACiE,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3D,MAAM6B,YAAY,GAAG7B,KAAK,YAAY8B,KAAK,GAAG9B,KAAK,CAAC+B,OAAO,GAAGC,MAAM,CAAChC,KAAK,CAAC;QAC3EM,eAAe,CAACuB,YAAY,CAAC;MAC/B;IACF;EACF,CAAC;EAEDI,aAAa,EAAEA,CAAC/C,UAA6B,EAAEiB,UAA4B,EAAE+B,kBAAuB,EAAEC,aAAqB,KAAK;IAC9HpG,OAAO,CAACC,GAAG,CAAE,qDAAoDkD,UAAU,CAACxC,IAAK,EAAC,CAAC;IACnF;EACF,CAAC;EAED0F,eAAe,EAAGD,aAAqB,IAAK;IAC1CpG,OAAO,CAACC,GAAG,CAAE,iCAAgCmG,aAAc,EAAC,CAAC;IAC7D;EACF;AACF,CAAC;AAED,eAAevG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
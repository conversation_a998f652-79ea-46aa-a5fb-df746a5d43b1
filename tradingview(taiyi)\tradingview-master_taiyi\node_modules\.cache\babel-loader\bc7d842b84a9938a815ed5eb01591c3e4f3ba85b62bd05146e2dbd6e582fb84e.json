{"ast": null, "code": "import { formatTime } from \"../utils\";\n\n// EGX Stock symbols mapping\nconst EGX_STOCKS = {\n  \"EGX:CIB\": {\n    name: \"Commercial International Bank\",\n    exchange: \"EGX\"\n  },\n  \"EGX:ETEL\": {\n    name: \"Egyptian Company for Mobile Services\",\n    exchange: \"EGX\"\n  },\n  \"EGX:HRHO\": {\n    name: \"Hassan Allam Holding\",\n    exchange: \"EGX\"\n  },\n  \"EGX:SWDY\": {\n    name: \"El Sewedy Electric Company\",\n    exchange: \"EGX\"\n  },\n  \"EGX:OCDI\": {\n    name: \"Orascom Construction Industries\",\n    exchange: \"EGX\"\n  }\n};\nconst DataFeed = {\n  onReady: callback => {\n    console.log(\"[onReady]: EGX DataFeed initialized\");\n    const config = {\n      supported_resolutions: [\"1\", \"5\", \"15\", \"30\", \"60\", \"D\", \"W\", \"M\"],\n      supports_group_request: false,\n      supports_marks: false,\n      supports_search: true,\n      supports_timescale_marks: false\n    };\n    setTimeout(() => callback(config));\n  },\n  searchSymbols: (userInput, exchange, symbolType, onResultReadyCallback) => {\n    console.log(\"[searchSymbols]: Searching EGX stocks for:\", userInput);\n    const results = Object.keys(EGX_STOCKS).filter(symbol => symbol.toLowerCase().includes(userInput.toLowerCase()) || EGX_STOCKS[symbol].name.toLowerCase().includes(userInput.toLowerCase())).map(symbol => ({\n      symbol: symbol,\n      full_name: EGX_STOCKS[symbol].name,\n      description: EGX_STOCKS[symbol].name,\n      exchange: \"EGX\",\n      ticker: symbol,\n      type: \"stock\"\n    }));\n    onResultReadyCallback(results);\n  },\n  resolveSymbol: async (symbolName, onSymbolResolvedCallback) => {\n    console.log(\"[resolveSymbol]: Resolving EGX symbol\", symbolName);\n    const stockInfo = EGX_STOCKS[symbolName];\n    const description = stockInfo ? stockInfo.name : symbolName;\n    const symbolInfo = {\n      description: description,\n      name: symbolName,\n      ticker: symbolName,\n      session: \"0930-1530\",\n      // EGX trading hours (9:30 AM - 3:30 PM Cairo time)\n      timezone: \"Africa/Cairo\",\n      type: \"stock\",\n      exchange: \"EGX\",\n      has_intraday: true,\n      has_daily: true,\n      has_weekly_and_monthly: true,\n      minmov: 1,\n      minmove2: 0,\n      fractional: false,\n      currency_code: \"EGP\",\n      pricescale: 100,\n      // 2 decimal places for EGP\n      supported_resolutions: [\"1\", \"5\", \"15\", \"30\", \"60\", \"D\", \"W\", \"M\"]\n    };\n    onSymbolResolvedCallback(symbolInfo);\n  },\n  getBars: async (symbolInfo, resolution, periodParams, onHistoryCallback, onErrorCallback) => {\n    // tradingview會設定每個尺度(resolution)下要有幾個bars,如果沒有滿足數量可能會造成一直call getBars的無窮迴圈\n    const symbol = symbolInfo.name;\n    const {\n      from,\n      to\n    } = periodParams;\n    if (symbol) {\n      try {\n        var _data$t, _data$t2;\n        const {\n          data,\n          statusCode\n        } = await getSymbolHistories({\n          resolution,\n          symbol,\n          to,\n          from\n        });\n        console.log(\"[getBars]: Method call\");\n        console.table({\n          from: formatTime(from, \"YYYYMMDD\"),\n          to: formatTime(to, \"YYYYMMDD\"),\n          resolution,\n          \"api data length\": data === null || data === void 0 ? void 0 : (_data$t = data.t) === null || _data$t === void 0 ? void 0 : _data$t.length\n        });\n        if (statusCode !== 200 || (data === null || data === void 0 ? void 0 : (_data$t2 = data.t) === null || _data$t2 === void 0 ? void 0 : _data$t2.length) === 0) {\n          onHistoryCallback([], {\n            noData: true\n          });\n          return;\n        }\n        const {\n          l,\n          h,\n          o,\n          c,\n          t\n        } = data;\n        const bars = t === null || t === void 0 ? void 0 : t.reduce((acc, timestamp, index) => {\n          acc.push({\n            time: timestamp * 1000,\n            low: l[index],\n            high: h[index],\n            open: o[index],\n            close: c[index]\n          });\n          return acc;\n        }, []);\n        bars.sort((a, b) => a.time - b.time);\n        onHistoryCallback(bars, {\n          noData: false\n        });\n      } catch (error) {\n        console.log(error);\n      }\n    }\n  },\n  subscribeBars: () => {\n    console.log(\"[subscribeBars]: Method call with subscriberUID:\");\n  },\n  unsubscribeBars: () => {\n    console.log(\"[unsubscribeBars]: Method call with subscriberUID:\");\n  }\n};\nexport default DataFeed;", "map": {"version": 3, "names": ["formatTime", "EGX_STOCKS", "name", "exchange", "DataFeed", "onReady", "callback", "console", "log", "config", "supported_resolutions", "supports_group_request", "supports_marks", "supports_search", "supports_timescale_marks", "setTimeout", "searchSymbols", "userInput", "symbolType", "onResultReadyCallback", "results", "Object", "keys", "filter", "symbol", "toLowerCase", "includes", "map", "full_name", "description", "ticker", "type", "resolveSymbol", "symbolName", "onSymbolResolvedCallback", "stockInfo", "symbolInfo", "session", "timezone", "has_intraday", "has_daily", "has_weekly_and_monthly", "<PERSON><PERSON>v", "minmove2", "fractional", "currency_code", "pricescale", "getBars", "resolution", "periodParams", "onHistoryCallback", "onError<PERSON>allback", "from", "to", "_data$t", "_data$t2", "data", "statusCode", "getSymbolHistories", "table", "t", "length", "noData", "l", "h", "o", "c", "bars", "reduce", "acc", "timestamp", "index", "push", "time", "low", "high", "open", "close", "sort", "a", "b", "error", "subscribeBars", "unsubscribeBars"], "sources": ["D:/trading-view-charting-library/tradingview(taiyi)/tradingview-master_taiyi/src/tradingview/datafeed.ts"], "sourcesContent": ["import { OnReadyCallback, LibrarySymbolInfo, ResolutionString, PeriodParams, HistoryCallback, Bar } from \"@/public/charting_library\";\r\nimport { getEGXQuote, getEGXHistories } from \"../api\";\r\nimport { formatTime } from \"../utils\";\r\n\r\n// EGX Stock symbols mapping\r\nconst EGX_STOCKS = {\r\n  \"EGX:CIB\": { name: \"Commercial International Bank\", exchange: \"EGX\" },\r\n  \"EGX:ETEL\": { name: \"Egyptian Company for Mobile Services\", exchange: \"EGX\" },\r\n  \"EGX:HRHO\": { name: \"Hassan Allam Holding\", exchange: \"EGX\" },\r\n  \"EGX:SWDY\": { name: \"El Sewedy Electric Company\", exchange: \"EGX\" },\r\n  \"EGX:OCDI\": { name: \"Orascom Construction Industries\", exchange: \"EGX\" },\r\n};\r\n\r\nconst DataFeed = {\r\n  onReady: (callback: OnReadyCallback) => {\r\n    console.log(\"[onReady]: EGX DataFeed initialized\");\r\n    const config = {\r\n      supported_resolutions: [\"1\", \"5\", \"15\", \"30\", \"60\", \"D\", \"W\", \"M\"],\r\n      supports_group_request: false,\r\n      supports_marks: false,\r\n      supports_search: true,\r\n      supports_timescale_marks: false,\r\n    };\r\n    setTimeout(() => callback(config));\r\n  },\r\n\r\n  searchSymbols: (userInput: string, exchange: string, symbolType: string, onResultReadyCallback: any) => {\r\n    console.log(\"[searchSymbols]: Searching EGX stocks for:\", userInput);\r\n    const results = Object.keys(EGX_STOCKS)\r\n      .filter(symbol =>\r\n        symbol.toLowerCase().includes(userInput.toLowerCase()) ||\r\n        EGX_STOCKS[symbol as keyof typeof EGX_STOCKS].name.toLowerCase().includes(userInput.toLowerCase())\r\n      )\r\n      .map(symbol => ({\r\n        symbol: symbol,\r\n        full_name: EGX_STOCKS[symbol as keyof typeof EGX_STOCKS].name,\r\n        description: EGX_STOCKS[symbol as keyof typeof EGX_STOCKS].name,\r\n        exchange: \"EGX\",\r\n        ticker: symbol,\r\n        type: \"stock\",\r\n      }));\r\n    onResultReadyCallback(results);\r\n  },\r\n\r\n  resolveSymbol: async (symbolName: string, onSymbolResolvedCallback: (info: LibrarySymbolInfo) => void) => {\r\n    console.log(\"[resolveSymbol]: Resolving EGX symbol\", symbolName);\r\n\r\n    const stockInfo = EGX_STOCKS[symbolName as keyof typeof EGX_STOCKS];\r\n    const description = stockInfo ? stockInfo.name : symbolName;\r\n\r\n    const symbolInfo = {\r\n      description: description,\r\n      name: symbolName,\r\n      ticker: symbolName,\r\n      session: \"0930-1530\", // EGX trading hours (9:30 AM - 3:30 PM Cairo time)\r\n      timezone: \"Africa/Cairo\",\r\n      type: \"stock\",\r\n      exchange: \"EGX\",\r\n      has_intraday: true,\r\n      has_daily: true,\r\n      has_weekly_and_monthly: true,\r\n      minmov: 1,\r\n      minmove2: 0,\r\n      fractional: false,\r\n      currency_code: \"EGP\",\r\n      pricescale: 100, // 2 decimal places for EGP\r\n      supported_resolutions: [\"1\", \"5\", \"15\", \"30\", \"60\", \"D\", \"W\", \"M\"],\r\n    } as LibrarySymbolInfo;\r\n    onSymbolResolvedCallback(symbolInfo);\r\n  },\r\n  getBars: async (\r\n    symbolInfo: LibrarySymbolInfo,\r\n    resolution: ResolutionString,\r\n    periodParams: PeriodParams,\r\n    onHistoryCallback: HistoryCallback,\r\n    onErrorCallback: ErrorCallback\r\n  ) => {\r\n    // tradingview會設定每個尺度(resolution)下要有幾個bars,如果沒有滿足數量可能會造成一直call getBars的無窮迴圈\r\n    const symbol = symbolInfo.name;\r\n    const { from, to } = periodParams;\r\n\r\n    if (symbol) {\r\n      try {\r\n        const { data, statusCode } = await getSymbolHistories({\r\n          resolution,\r\n          symbol,\r\n          to,\r\n          from,\r\n        });\r\n\r\n        console.log(\"[getBars]: Method call\");\r\n        console.table({\r\n          from: formatTime(from, \"YYYYMMDD\"),\r\n          to: formatTime(to, \"YYYYMMDD\"),\r\n          resolution,\r\n          \"api data length\": data?.t?.length,\r\n        });\r\n\r\n        if (statusCode !== 200 || data?.t?.length === 0) {\r\n          onHistoryCallback([], { noData: true });\r\n          return;\r\n        }\r\n\r\n        const { l, h, o, c, t } = data;\r\n\r\n        const bars = t?.reduce((acc: Bar[], timestamp: number, index: number) => {\r\n          acc.push({\r\n            time: timestamp * 1000,\r\n            low: l[index],\r\n            high: h[index],\r\n            open: o[index],\r\n            close: c[index],\r\n          });\r\n          return acc;\r\n        }, [] as Bar[]);\r\n\r\n        bars.sort((a: Bar, b: Bar) => a.time - b.time);\r\n\r\n        onHistoryCallback(bars, { noData: false });\r\n      } catch (error) {\r\n        console.log(error);\r\n      }\r\n    }\r\n  },\r\n  subscribeBars: () => {\r\n    console.log(\"[subscribeBars]: Method call with subscriberUID:\");\r\n  },\r\n  unsubscribeBars: () => {\r\n    console.log(\"[unsubscribeBars]: Method call with subscriberUID:\");\r\n  },\r\n};\r\n\r\nexport default DataFeed;\r\n"], "mappings": "AAEA,SAASA,UAAU,QAAQ,UAAU;;AAErC;AACA,MAAMC,UAAU,GAAG;EACjB,SAAS,EAAE;IAAEC,IAAI,EAAE,+BAA+B;IAAEC,QAAQ,EAAE;EAAM,CAAC;EACrE,UAAU,EAAE;IAAED,IAAI,EAAE,sCAAsC;IAAEC,QAAQ,EAAE;EAAM,CAAC;EAC7E,UAAU,EAAE;IAAED,IAAI,EAAE,sBAAsB;IAAEC,QAAQ,EAAE;EAAM,CAAC;EAC7D,UAAU,EAAE;IAAED,IAAI,EAAE,4BAA4B;IAAEC,QAAQ,EAAE;EAAM,CAAC;EACnE,UAAU,EAAE;IAAED,IAAI,EAAE,iCAAiC;IAAEC,QAAQ,EAAE;EAAM;AACzE,CAAC;AAED,MAAMC,QAAQ,GAAG;EACfC,OAAO,EAAGC,QAAyB,IAAK;IACtCC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAClD,MAAMC,MAAM,GAAG;MACbC,qBAAqB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAClEC,sBAAsB,EAAE,KAAK;MAC7BC,cAAc,EAAE,KAAK;MACrBC,eAAe,EAAE,IAAI;MACrBC,wBAAwB,EAAE;IAC5B,CAAC;IACDC,UAAU,CAAC,MAAMT,QAAQ,CAACG,MAAM,CAAC,CAAC;EACpC,CAAC;EAEDO,aAAa,EAAEA,CAACC,SAAiB,EAAEd,QAAgB,EAAEe,UAAkB,EAAEC,qBAA0B,KAAK;IACtGZ,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAES,SAAS,CAAC;IACpE,MAAMG,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACrB,UAAU,CAAC,CACpCsB,MAAM,CAACC,MAAM,IACZA,MAAM,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACT,SAAS,CAACQ,WAAW,CAAC,CAAC,CAAC,IACtDxB,UAAU,CAACuB,MAAM,CAA4B,CAACtB,IAAI,CAACuB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACT,SAAS,CAACQ,WAAW,CAAC,CAAC,CACnG,CAAC,CACAE,GAAG,CAACH,MAAM,KAAK;MACdA,MAAM,EAAEA,MAAM;MACdI,SAAS,EAAE3B,UAAU,CAACuB,MAAM,CAA4B,CAACtB,IAAI;MAC7D2B,WAAW,EAAE5B,UAAU,CAACuB,MAAM,CAA4B,CAACtB,IAAI;MAC/DC,QAAQ,EAAE,KAAK;MACf2B,MAAM,EAAEN,MAAM;MACdO,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;IACLZ,qBAAqB,CAACC,OAAO,CAAC;EAChC,CAAC;EAEDY,aAAa,EAAE,MAAAA,CAAOC,UAAkB,EAAEC,wBAA2D,KAAK;IACxG3B,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEyB,UAAU,CAAC;IAEhE,MAAME,SAAS,GAAGlC,UAAU,CAACgC,UAAU,CAA4B;IACnE,MAAMJ,WAAW,GAAGM,SAAS,GAAGA,SAAS,CAACjC,IAAI,GAAG+B,UAAU;IAE3D,MAAMG,UAAU,GAAG;MACjBP,WAAW,EAAEA,WAAW;MACxB3B,IAAI,EAAE+B,UAAU;MAChBH,MAAM,EAAEG,UAAU;MAClBI,OAAO,EAAE,WAAW;MAAE;MACtBC,QAAQ,EAAE,cAAc;MACxBP,IAAI,EAAE,OAAO;MACb5B,QAAQ,EAAE,KAAK;MACfoC,YAAY,EAAE,IAAI;MAClBC,SAAS,EAAE,IAAI;MACfC,sBAAsB,EAAE,IAAI;MAC5BC,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE,CAAC;MACXC,UAAU,EAAE,KAAK;MACjBC,aAAa,EAAE,KAAK;MACpBC,UAAU,EAAE,GAAG;MAAE;MACjBpC,qBAAqB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IACnE,CAAsB;IACtBwB,wBAAwB,CAACE,UAAU,CAAC;EACtC,CAAC;EACDW,OAAO,EAAE,MAAAA,CACPX,UAA6B,EAC7BY,UAA4B,EAC5BC,YAA0B,EAC1BC,iBAAkC,EAClCC,eAA8B,KAC3B;IACH;IACA,MAAM3B,MAAM,GAAGY,UAAU,CAAClC,IAAI;IAC9B,MAAM;MAAEkD,IAAI;MAAEC;IAAG,CAAC,GAAGJ,YAAY;IAEjC,IAAIzB,MAAM,EAAE;MACV,IAAI;QAAA,IAAA8B,OAAA,EAAAC,QAAA;QACF,MAAM;UAAEC,IAAI;UAAEC;QAAW,CAAC,GAAG,MAAMC,kBAAkB,CAAC;UACpDV,UAAU;UACVxB,MAAM;UACN6B,EAAE;UACFD;QACF,CAAC,CAAC;QAEF7C,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;QACrCD,OAAO,CAACoD,KAAK,CAAC;UACZP,IAAI,EAAEpD,UAAU,CAACoD,IAAI,EAAE,UAAU,CAAC;UAClCC,EAAE,EAAErD,UAAU,CAACqD,EAAE,EAAE,UAAU,CAAC;UAC9BL,UAAU;UACV,iBAAiB,EAAEQ,IAAI,aAAJA,IAAI,wBAAAF,OAAA,GAAJE,IAAI,CAAEI,CAAC,cAAAN,OAAA,uBAAPA,OAAA,CAASO;QAC9B,CAAC,CAAC;QAEF,IAAIJ,UAAU,KAAK,GAAG,IAAI,CAAAD,IAAI,aAAJA,IAAI,wBAAAD,QAAA,GAAJC,IAAI,CAAEI,CAAC,cAAAL,QAAA,uBAAPA,QAAA,CAASM,MAAM,MAAK,CAAC,EAAE;UAC/CX,iBAAiB,CAAC,EAAE,EAAE;YAAEY,MAAM,EAAE;UAAK,CAAC,CAAC;UACvC;QACF;QAEA,MAAM;UAAEC,CAAC;UAAEC,CAAC;UAAEC,CAAC;UAAEC,CAAC;UAAEN;QAAE,CAAC,GAAGJ,IAAI;QAE9B,MAAMW,IAAI,GAAGP,CAAC,aAADA,CAAC,uBAADA,CAAC,CAAEQ,MAAM,CAAC,CAACC,GAAU,EAAEC,SAAiB,EAAEC,KAAa,KAAK;UACvEF,GAAG,CAACG,IAAI,CAAC;YACPC,IAAI,EAAEH,SAAS,GAAG,IAAI;YACtBI,GAAG,EAAEX,CAAC,CAACQ,KAAK,CAAC;YACbI,IAAI,EAAEX,CAAC,CAACO,KAAK,CAAC;YACdK,IAAI,EAAEX,CAAC,CAACM,KAAK,CAAC;YACdM,KAAK,EAAEX,CAAC,CAACK,KAAK;UAChB,CAAC,CAAC;UACF,OAAOF,GAAG;QACZ,CAAC,EAAE,EAAW,CAAC;QAEfF,IAAI,CAACW,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAKD,CAAC,CAACN,IAAI,GAAGO,CAAC,CAACP,IAAI,CAAC;QAE9CvB,iBAAiB,CAACiB,IAAI,EAAE;UAAEL,MAAM,EAAE;QAAM,CAAC,CAAC;MAC5C,CAAC,CAAC,OAAOmB,KAAK,EAAE;QACd1E,OAAO,CAACC,GAAG,CAACyE,KAAK,CAAC;MACpB;IACF;EACF,CAAC;EACDC,aAAa,EAAEA,CAAA,KAAM;IACnB3E,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;EACjE,CAAC;EACD2E,eAAe,EAAEA,CAAA,KAAM;IACrB5E,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;EACnE;AACF,CAAC;AAED,eAAeJ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
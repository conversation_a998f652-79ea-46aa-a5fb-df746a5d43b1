{"ast": null, "code": "import { getQuoteBySymbol, getSymbolHistories } from \"../api\";\nimport { formatTime } from \"../utils\";\nconst DataFeed = {\n  onReady: callback => {\n    console.log(\"[onReady]: Method call\");\n    const config = {};\n    setTimeout(() => callback(config));\n  },\n  resolveSymbol: async (symbolName, onSymbolResolvedCallback) => {\n    var _res$data, _res$data$, _res$data2, _res$data2$;\n    console.log(\"[resolveSymbol]: Method call\", symbolName);\n    const [market, code] = symbolName.split(\":\");\n    const name = `${market}:${code}`;\n    const res = await getQuoteBySymbol(symbolName, {\n      column: \"D_FORMAT\"\n    });\n    const chineseName = res === null || res === void 0 ? void 0 : (_res$data = res.data) === null || _res$data === void 0 ? void 0 : (_res$data$ = _res$data[0]) === null || _res$data$ === void 0 ? void 0 : _res$data$[\"200009\"];\n    const price = res === null || res === void 0 ? void 0 : (_res$data2 = res.data) === null || _res$data2 === void 0 ? void 0 : (_res$data2$ = _res$data2[0]) === null || _res$data2$ === void 0 ? void 0 : _res$data2$[\"200026\"]; // 字串小數位數由後端控制\n    const countDecimal = price.toString().split(\".\")[1].length; // 有幾位小數\n    const pricescale = Math.pow(10, countDecimal);\n    const symbolInfo = {\n      description: chineseName || symbolName /* 顯示的名稱 */,\n      name: symbolName,\n      ticker: name,\n      session: \"24x7\",\n      timezone: \"Asia/Taipei\",\n      type: \"forex\",\n      has_intraday: true,\n      has_daily: true,\n      exchange: \"\",\n      minmov: 1,\n      minmove2: 0,\n      fractional: false,\n      currency_code: \"\",\n      pricescale,\n      supported_resolutions: [\"1\", \"5\", \"10\", \"15\", \"30\", \"60\", \"D\", \"W\", \"M\"]\n    };\n    onSymbolResolvedCallback(symbolInfo);\n  },\n  getBars: async (symbolInfo, resolution, periodParams, onHistoryCallback, onErrorCallback) => {\n    // tradingview會設定每個尺度(resolution)下要有幾個bars,如果沒有滿足數量可能會造成一直call getBars的無窮迴圈\n    const symbol = symbolInfo.name;\n    const {\n      from,\n      to\n    } = periodParams;\n    if (symbol) {\n      try {\n        var _data$t, _data$t2;\n        const {\n          data,\n          statusCode\n        } = await getSymbolHistories({\n          resolution,\n          symbol,\n          to,\n          from\n        });\n        console.log(\"[getBars]: Method call\");\n        console.table({\n          from: formatTime(from, \"YYYYMMDD\"),\n          to: formatTime(to, \"YYYYMMDD\"),\n          resolution,\n          \"api data length\": data === null || data === void 0 ? void 0 : (_data$t = data.t) === null || _data$t === void 0 ? void 0 : _data$t.length\n        });\n        if (statusCode !== 200 || (data === null || data === void 0 ? void 0 : (_data$t2 = data.t) === null || _data$t2 === void 0 ? void 0 : _data$t2.length) === 0) {\n          onHistoryCallback([], {\n            noData: true\n          });\n          return;\n        }\n        const {\n          l,\n          h,\n          o,\n          c,\n          t\n        } = data;\n        const bars = t === null || t === void 0 ? void 0 : t.reduce((acc, timestamp, index) => {\n          acc.push({\n            time: timestamp * 1000,\n            low: l[index],\n            high: h[index],\n            open: o[index],\n            close: c[index]\n          });\n          return acc;\n        }, []);\n        bars.sort((a, b) => a.time - b.time);\n        onHistoryCallback(bars, {\n          noData: false\n        });\n      } catch (error) {\n        console.log(error);\n      }\n    }\n  },\n  subscribeBars: () => {\n    console.log(\"[subscribeBars]: Method call with subscriberUID:\");\n  },\n  unsubscribeBars: () => {\n    console.log(\"[unsubscribeBars]: Method call with subscriberUID:\");\n  }\n};\nexport default DataFeed;", "map": {"version": 3, "names": ["getQuoteBySymbol", "getSymbolHistories", "formatTime", "DataFeed", "onReady", "callback", "console", "log", "config", "setTimeout", "resolveSymbol", "symbolName", "onSymbolResolvedCallback", "_res$data", "_res$data$", "_res$data2", "_res$data2$", "market", "code", "split", "name", "res", "column", "chineseName", "data", "price", "countDecimal", "toString", "length", "pricescale", "Math", "pow", "symbolInfo", "description", "ticker", "session", "timezone", "type", "has_intraday", "has_daily", "exchange", "<PERSON><PERSON>v", "minmove2", "fractional", "currency_code", "supported_resolutions", "getBars", "resolution", "periodParams", "onHistoryCallback", "onError<PERSON>allback", "symbol", "from", "to", "_data$t", "_data$t2", "statusCode", "table", "t", "noData", "l", "h", "o", "c", "bars", "reduce", "acc", "timestamp", "index", "push", "time", "low", "high", "open", "close", "sort", "a", "b", "error", "subscribeBars", "unsubscribeBars"], "sources": ["D:/trading-view-charting-library/tradingview(taiyi)/tradingview-master_taiyi/src/tradingview/datafeed.ts"], "sourcesContent": ["import { OnReadyCallback, LibrarySymbolInfo, ResolutionString, PeriodParams, HistoryCallback, Bar } from \"@/public/charting_library\";\r\nimport { getQuoteBySymbol, getSymbolHistories } from \"../api\";\r\nimport { formatTime } from \"../utils\";\r\n\r\nconst DataFeed = {\r\n  onReady: (callback: OnReadyCallback) => {\r\n    console.log(\"[onReady]: Method call\");\r\n    const config = {};\r\n    setTimeout(() => callback(config));\r\n  },\r\n  resolveSymbol: async (symbolName: string, onSymbolResolvedCallback: (info: LibrarySymbolInfo) => void) => {\r\n    console.log(\"[resolveSymbol]: Method call\", symbolName);\r\n    const [market, code] = symbolName.split(\":\");\r\n    const name = `${market}:${code}`;\r\n    const res = await getQuoteBySymbol(symbolName, { column: \"D_FORMAT\" });\r\n    const chineseName = res?.data?.[0]?.[\"200009\"];\r\n    const price = res?.data?.[0]?.[\"200026\"]; // 字串小數位數由後端控制\r\n    const countDecimal = price.toString().split(\".\")[1].length; // 有幾位小數\r\n    const pricescale = Math.pow(10, countDecimal);\r\n\r\n    const symbolInfo = {\r\n      description: chineseName || symbolName /* 顯示的名稱 */,\r\n      name: symbolName,\r\n      ticker: name,\r\n      session: \"24x7\",\r\n      timezone: \"Asia/Taipei\",\r\n      type: \"forex\",\r\n      has_intraday: true,\r\n      has_daily: true,\r\n      exchange: \"\",\r\n      minmov: 1,\r\n      minmove2: 0,\r\n      fractional: false,\r\n      currency_code: \"\",\r\n      pricescale,\r\n      supported_resolutions: [\"1\", \"5\", \"10\", \"15\", \"30\", \"60\", \"D\", \"W\", \"M\"],\r\n    } as LibrarySymbolInfo;\r\n    onSymbolResolvedCallback(symbolInfo);\r\n  },\r\n  getBars: async (\r\n    symbolInfo: LibrarySymbolInfo,\r\n    resolution: ResolutionString,\r\n    periodParams: PeriodParams,\r\n    onHistoryCallback: HistoryCallback,\r\n    onErrorCallback: ErrorCallback\r\n  ) => {\r\n    // tradingview會設定每個尺度(resolution)下要有幾個bars,如果沒有滿足數量可能會造成一直call getBars的無窮迴圈\r\n    const symbol = symbolInfo.name;\r\n    const { from, to } = periodParams;\r\n\r\n    if (symbol) {\r\n      try {\r\n        const { data, statusCode } = await getSymbolHistories({\r\n          resolution,\r\n          symbol,\r\n          to,\r\n          from,\r\n        });\r\n\r\n        console.log(\"[getBars]: Method call\");\r\n        console.table({\r\n          from: formatTime(from, \"YYYYMMDD\"),\r\n          to: formatTime(to, \"YYYYMMDD\"),\r\n          resolution,\r\n          \"api data length\": data?.t?.length,\r\n        });\r\n\r\n        if (statusCode !== 200 || data?.t?.length === 0) {\r\n          onHistoryCallback([], { noData: true });\r\n          return;\r\n        }\r\n\r\n        const { l, h, o, c, t } = data;\r\n\r\n        const bars = t?.reduce((acc: Bar[], timestamp: number, index: number) => {\r\n          acc.push({\r\n            time: timestamp * 1000,\r\n            low: l[index],\r\n            high: h[index],\r\n            open: o[index],\r\n            close: c[index],\r\n          });\r\n          return acc;\r\n        }, [] as Bar[]);\r\n\r\n        bars.sort((a: Bar, b: Bar) => a.time - b.time);\r\n\r\n        onHistoryCallback(bars, { noData: false });\r\n      } catch (error) {\r\n        console.log(error);\r\n      }\r\n    }\r\n  },\r\n  subscribeBars: () => {\r\n    console.log(\"[subscribeBars]: Method call with subscriberUID:\");\r\n  },\r\n  unsubscribeBars: () => {\r\n    console.log(\"[unsubscribeBars]: Method call with subscriberUID:\");\r\n  },\r\n};\r\n\r\nexport default DataFeed;\r\n"], "mappings": "AACA,SAASA,gBAAgB,EAAEC,kBAAkB,QAAQ,QAAQ;AAC7D,SAASC,UAAU,QAAQ,UAAU;AAErC,MAAMC,QAAQ,GAAG;EACfC,OAAO,EAAGC,QAAyB,IAAK;IACtCC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrC,MAAMC,MAAM,GAAG,CAAC,CAAC;IACjBC,UAAU,CAAC,MAAMJ,QAAQ,CAACG,MAAM,CAAC,CAAC;EACpC,CAAC;EACDE,aAAa,EAAE,MAAAA,CAAOC,UAAkB,EAAEC,wBAA2D,KAAK;IAAA,IAAAC,SAAA,EAAAC,UAAA,EAAAC,UAAA,EAAAC,WAAA;IACxGV,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEI,UAAU,CAAC;IACvD,MAAM,CAACM,MAAM,EAAEC,IAAI,CAAC,GAAGP,UAAU,CAACQ,KAAK,CAAC,GAAG,CAAC;IAC5C,MAAMC,IAAI,GAAI,GAAEH,MAAO,IAAGC,IAAK,EAAC;IAChC,MAAMG,GAAG,GAAG,MAAMrB,gBAAgB,CAACW,UAAU,EAAE;MAAEW,MAAM,EAAE;IAAW,CAAC,CAAC;IACtE,MAAMC,WAAW,GAAGF,GAAG,aAAHA,GAAG,wBAAAR,SAAA,GAAHQ,GAAG,CAAEG,IAAI,cAAAX,SAAA,wBAAAC,UAAA,GAATD,SAAA,CAAY,CAAC,CAAC,cAAAC,UAAA,uBAAdA,UAAA,CAAiB,QAAQ,CAAC;IAC9C,MAAMW,KAAK,GAAGJ,GAAG,aAAHA,GAAG,wBAAAN,UAAA,GAAHM,GAAG,CAAEG,IAAI,cAAAT,UAAA,wBAAAC,WAAA,GAATD,UAAA,CAAY,CAAC,CAAC,cAAAC,WAAA,uBAAdA,WAAA,CAAiB,QAAQ,CAAC,CAAC,CAAC;IAC1C,MAAMU,YAAY,GAAGD,KAAK,CAACE,QAAQ,CAAC,CAAC,CAACR,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACS,MAAM,CAAC,CAAC;IAC5D,MAAMC,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEL,YAAY,CAAC;IAE7C,MAAMM,UAAU,GAAG;MACjBC,WAAW,EAAEV,WAAW,IAAIZ,UAAU,CAAC;MACvCS,IAAI,EAAET,UAAU;MAChBuB,MAAM,EAAEd,IAAI;MACZe,OAAO,EAAE,MAAM;MACfC,QAAQ,EAAE,aAAa;MACvBC,IAAI,EAAE,OAAO;MACbC,YAAY,EAAE,IAAI;MAClBC,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE,CAAC;MACXC,UAAU,EAAE,KAAK;MACjBC,aAAa,EAAE,EAAE;MACjBf,UAAU;MACVgB,qBAAqB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IACzE,CAAsB;IACtBjC,wBAAwB,CAACoB,UAAU,CAAC;EACtC,CAAC;EACDc,OAAO,EAAE,MAAAA,CACPd,UAA6B,EAC7Be,UAA4B,EAC5BC,YAA0B,EAC1BC,iBAAkC,EAClCC,eAA8B,KAC3B;IACH;IACA,MAAMC,MAAM,GAAGnB,UAAU,CAACZ,IAAI;IAC9B,MAAM;MAAEgC,IAAI;MAAEC;IAAG,CAAC,GAAGL,YAAY;IAEjC,IAAIG,MAAM,EAAE;MACV,IAAI;QAAA,IAAAG,OAAA,EAAAC,QAAA;QACF,MAAM;UAAE/B,IAAI;UAAEgC;QAAW,CAAC,GAAG,MAAMvD,kBAAkB,CAAC;UACpD8C,UAAU;UACVI,MAAM;UACNE,EAAE;UACFD;QACF,CAAC,CAAC;QAEF9C,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;QACrCD,OAAO,CAACmD,KAAK,CAAC;UACZL,IAAI,EAAElD,UAAU,CAACkD,IAAI,EAAE,UAAU,CAAC;UAClCC,EAAE,EAAEnD,UAAU,CAACmD,EAAE,EAAE,UAAU,CAAC;UAC9BN,UAAU;UACV,iBAAiB,EAAEvB,IAAI,aAAJA,IAAI,wBAAA8B,OAAA,GAAJ9B,IAAI,CAAEkC,CAAC,cAAAJ,OAAA,uBAAPA,OAAA,CAAS1B;QAC9B,CAAC,CAAC;QAEF,IAAI4B,UAAU,KAAK,GAAG,IAAI,CAAAhC,IAAI,aAAJA,IAAI,wBAAA+B,QAAA,GAAJ/B,IAAI,CAAEkC,CAAC,cAAAH,QAAA,uBAAPA,QAAA,CAAS3B,MAAM,MAAK,CAAC,EAAE;UAC/CqB,iBAAiB,CAAC,EAAE,EAAE;YAAEU,MAAM,EAAE;UAAK,CAAC,CAAC;UACvC;QACF;QAEA,MAAM;UAAEC,CAAC;UAAEC,CAAC;UAAEC,CAAC;UAAEC,CAAC;UAAEL;QAAE,CAAC,GAAGlC,IAAI;QAE9B,MAAMwC,IAAI,GAAGN,CAAC,aAADA,CAAC,uBAADA,CAAC,CAAEO,MAAM,CAAC,CAACC,GAAU,EAAEC,SAAiB,EAAEC,KAAa,KAAK;UACvEF,GAAG,CAACG,IAAI,CAAC;YACPC,IAAI,EAAEH,SAAS,GAAG,IAAI;YACtBI,GAAG,EAAEX,CAAC,CAACQ,KAAK,CAAC;YACbI,IAAI,EAAEX,CAAC,CAACO,KAAK,CAAC;YACdK,IAAI,EAAEX,CAAC,CAACM,KAAK,CAAC;YACdM,KAAK,EAAEX,CAAC,CAACK,KAAK;UAChB,CAAC,CAAC;UACF,OAAOF,GAAG;QACZ,CAAC,EAAE,EAAW,CAAC;QAEfF,IAAI,CAACW,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAKD,CAAC,CAACN,IAAI,GAAGO,CAAC,CAACP,IAAI,CAAC;QAE9CrB,iBAAiB,CAACe,IAAI,EAAE;UAAEL,MAAM,EAAE;QAAM,CAAC,CAAC;MAC5C,CAAC,CAAC,OAAOmB,KAAK,EAAE;QACdxE,OAAO,CAACC,GAAG,CAACuE,KAAK,CAAC;MACpB;IACF;EACF,CAAC;EACDC,aAAa,EAAEA,CAAA,KAAM;IACnBzE,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;EACjE,CAAC;EACDyE,eAAe,EAAEA,CAAA,KAAM;IACrB1E,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;EACnE;AACF,CAAC;AAED,eAAeJ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
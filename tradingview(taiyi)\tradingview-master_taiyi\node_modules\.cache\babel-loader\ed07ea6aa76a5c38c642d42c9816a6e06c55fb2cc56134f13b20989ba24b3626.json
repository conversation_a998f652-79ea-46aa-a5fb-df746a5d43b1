{"ast": null, "code": "var _jsxFileName = \"D:\\\\trading-view-charting-library\\\\tradingview(taiyi)\\\\tradingview-master_taiyi\\\\src\\\\tradingview\\\\index.tsx\",\n  _s = $RefreshSig$();\nimport { useEffect } from \"react\";\nimport EGXDataFeed from \"./egx-datafeed\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst index = () => {\n  _s();\n  useEffect(() => {\n    const tvWidget = new window.TradingView.widget({\n      container: \"chartContainer\",\n      locale: \"en\",\n      // Could be \"ar\" for Arabic later\n      library_path: \"charting_library/\",\n      datafeed: EGXDataFeed,\n      // Use our custom EGX datafeed\n      symbol: \"COMI.EGX\",\n      // Commercial International Bank - EGX stock!\n      interval: \"1D\",\n      fullscreen: true,\n      disabled_features: [\"use_localstorage_for_settings\"],\n      enabled_features: [\"study_templates\"],\n      // EGX-specific configuration\n      timezone: \"Africa/Cairo\",\n      theme: \"light\",\n      toolbar_bg: \"#f1f3f6\",\n      studies_overrides: {},\n      // EGX branding\n      custom_css_url: \"/egx-theme.css\",\n      // We'll create this later\n      loading_screen: {\n        backgroundColor: \"#ffffff\"\n      }\n    });\n    return () => {\n      tvWidget.remove();\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: \"10px\",\n        backgroundColor: \"#1e3a8a\",\n        color: \"white\",\n        textAlign: \"center\",\n        fontSize: \"18px\",\n        fontWeight: \"bold\"\n      },\n      children: \"\\uD83C\\uDDEA\\uD83C\\uDDEC EGX Trading Platform - Egyptian Exchange\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      id: \"chartContainer\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_s(index, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\nexport default index;", "map": {"version": 3, "names": ["useEffect", "EGXDataFeed", "jsxDEV", "_jsxDEV", "index", "_s", "tvWidget", "window", "TradingView", "widget", "container", "locale", "library_path", "datafeed", "symbol", "interval", "fullscreen", "disabled_features", "enabled_features", "timezone", "theme", "toolbar_bg", "studies_overrides", "custom_css_url", "loading_screen", "backgroundColor", "remove", "children", "style", "padding", "color", "textAlign", "fontSize", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id"], "sources": ["D:/trading-view-charting-library/tradingview(taiyi)/tradingview-master_taiyi/src/tradingview/index.tsx"], "sourcesContent": ["import { useEffect } from \"react\";\r\nimport EGXDataFeed from \"./egx-datafeed\";\r\n\r\ndeclare global {\r\n  interface Window {\r\n    TradingView: any;\r\n    Datafeeds: any;\r\n  }\r\n}\r\n\r\nconst index = () => {\r\n  useEffect(() => {\r\n    const tvWidget = new window.TradingView.widget({\r\n      container: \"chartContainer\",\r\n      locale: \"en\", // Could be \"ar\" for Arabic later\r\n      library_path: \"charting_library/\",\r\n      datafeed: EGXDataFeed, // Use our custom EGX datafeed\r\n      symbol: \"COMI.EGX\", // Commercial International Bank - EGX stock!\r\n      interval: \"1D\",\r\n      fullscreen: true,\r\n      disabled_features: [\"use_localstorage_for_settings\"],\r\n      enabled_features: [\"study_templates\"],\r\n      // EGX-specific configuration\r\n      timezone: \"Africa/Cairo\",\r\n      theme: \"light\",\r\n      toolbar_bg: \"#f1f3f6\",\r\n      studies_overrides: {},\r\n      // EGX branding\r\n      custom_css_url: \"/egx-theme.css\", // We'll create this later\r\n      loading_screen: { backgroundColor: \"#ffffff\" },\r\n    });\r\n\r\n    return () => {\r\n      tvWidget.remove();\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <div>\r\n      <div style={{\r\n        padding: \"10px\",\r\n        backgroundColor: \"#1e3a8a\",\r\n        color: \"white\",\r\n        textAlign: \"center\",\r\n        fontSize: \"18px\",\r\n        fontWeight: \"bold\"\r\n      }}>\r\n        🇪🇬 EGX Trading Platform - Egyptian Exchange\r\n      </div>\r\n      <div id=\"chartContainer\"></div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default index;\r\n"], "mappings": ";;AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,WAAW,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AASzC,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClBL,SAAS,CAAC,MAAM;IACd,MAAMM,QAAQ,GAAG,IAAIC,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC;MAC7CC,SAAS,EAAE,gBAAgB;MAC3BC,MAAM,EAAE,IAAI;MAAE;MACdC,YAAY,EAAE,mBAAmB;MACjCC,QAAQ,EAAEZ,WAAW;MAAE;MACvBa,MAAM,EAAE,UAAU;MAAE;MACpBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,IAAI;MAChBC,iBAAiB,EAAE,CAAC,+BAA+B,CAAC;MACpDC,gBAAgB,EAAE,CAAC,iBAAiB,CAAC;MACrC;MACAC,QAAQ,EAAE,cAAc;MACxBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,SAAS;MACrBC,iBAAiB,EAAE,CAAC,CAAC;MACrB;MACAC,cAAc,EAAE,gBAAgB;MAAE;MAClCC,cAAc,EAAE;QAAEC,eAAe,EAAE;MAAU;IAC/C,CAAC,CAAC;IAEF,OAAO,MAAM;MACXnB,QAAQ,CAACoB,MAAM,CAAC,CAAC;IACnB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEvB,OAAA;IAAAwB,QAAA,gBACExB,OAAA;MAAKyB,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfJ,eAAe,EAAE,SAAS;QAC1BK,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,QAAQ;QACnBC,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE;MACd,CAAE;MAAAN,QAAA,EAAC;IAEH;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACNlC,OAAA;MAAKmC,EAAE,EAAC;IAAgB;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5B,CAAC;AAEV,CAAC;AAAChC,EAAA,CA1CID,KAAK;AA4CX,eAAeA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
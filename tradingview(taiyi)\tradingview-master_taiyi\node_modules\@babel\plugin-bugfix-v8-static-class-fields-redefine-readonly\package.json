{"name": "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly", "version": "7.24.1", "description": "Transform static class fields assignments that are affected by https://crbug.com/v8/12421", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-bugfix-v8-static-class-fields-redefine-readonly"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-v8-static-class-fields-redefine-readonly", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "keywords": ["babel-plugin", "bugfix"], "dependencies": {"@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-plugin-utils": "^7.24.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1", "@babel/traverse": "^7.24.1"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}
{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_helperCompilationTargets", "_helperAnnotateAsPure", "_helperFunctionName", "_helperSplitExportDeclaration", "_core", "_globals", "_transformClass", "getBuiltinClasses", "category", "Object", "keys", "globals", "filter", "name", "test", "builtinClasses", "Set", "_default", "exports", "default", "declare", "api", "options", "_api$assumption", "_api$assumption2", "_api$assumption3", "_api$assumption4", "assertVersion", "loose", "setClassMethods", "assumption", "constant<PERSON>uper", "superIsCallableConstructor", "noClassCalls", "supportUnicodeId", "isRequired", "targets", "VISITED", "WeakSet", "visitor", "ExportDefaultDeclaration", "path", "get", "isClassDeclaration", "splitExportDeclaration", "ClassDeclaration", "node", "ref", "id", "scope", "generateUidIdentifier", "replaceWith", "t", "variableDeclaration", "variableDeclarator", "toExpression", "ClassExpression", "state", "has", "inferred", "nameFunction", "undefined", "add", "<PERSON><PERSON><PERSON>", "transformClass", "file", "isCallExpression", "annotateAsPure", "callee", "isArrowFunctionExpression", "arrowFunctionToExpression"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { isRequired } from \"@babel/helper-compilation-targets\";\nimport annotateAsPure from \"@babel/helper-annotate-as-pure\";\nimport nameFunction from \"@babel/helper-function-name\";\nimport splitExportDeclaration from \"@babel/helper-split-export-declaration\";\nimport { types as t } from \"@babel/core\";\nimport globals from \"globals\";\nimport transformClass from \"./transformClass.ts\";\n\nconst getBuiltinClasses = (category: keyof typeof globals) =>\n  Object.keys(globals[category]).filter(name => /^[A-Z]/.test(name));\n\nconst builtinClasses = new Set([\n  ...getBuiltinClasses(\"builtin\"),\n  ...getBuiltinClasses(\"browser\"),\n]);\n\nexport interface Options {\n  loose?: boolean;\n}\n\nexport default declare((api, options: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const { loose = false } = options;\n\n  const setClassMethods = api.assumption(\"setClassMethods\") ?? loose;\n  const constantSuper = api.assumption(\"constantSuper\") ?? loose;\n  const superIsCallableConstructor =\n    api.assumption(\"superIsCallableConstructor\") ?? loose;\n  const noClassCalls = api.assumption(\"noClassCalls\") ?? loose;\n  const supportUnicodeId = !isRequired(\n    \"transform-unicode-escapes\",\n    api.targets(),\n  );\n\n  // todo: investigate traversal requeueing\n  const VISITED = new WeakSet();\n\n  return {\n    name: \"transform-classes\",\n\n    visitor: {\n      ExportDefaultDeclaration(path) {\n        if (!path.get(\"declaration\").isClassDeclaration()) return;\n        splitExportDeclaration(path);\n      },\n\n      ClassDeclaration(path) {\n        const { node } = path;\n\n        const ref = node.id || path.scope.generateUidIdentifier(\"class\");\n\n        path.replaceWith(\n          t.variableDeclaration(\"let\", [\n            t.variableDeclarator(ref, t.toExpression(node)),\n          ]),\n        );\n      },\n\n      ClassExpression(path, state) {\n        const { node } = path;\n        if (VISITED.has(node)) return;\n\n        const inferred = nameFunction(path, undefined, supportUnicodeId);\n        if (inferred && inferred !== node) {\n          path.replaceWith(inferred);\n          return;\n        }\n\n        VISITED.add(node);\n\n        const [replacedPath] = path.replaceWith(\n          transformClass(\n            path,\n            state.file,\n            builtinClasses,\n            loose,\n            {\n              setClassMethods,\n              constantSuper,\n              superIsCallableConstructor,\n              noClassCalls,\n            },\n            supportUnicodeId,\n          ),\n        );\n\n        if (replacedPath.isCallExpression()) {\n          annotateAsPure(replacedPath);\n          const callee = replacedPath.get(\"callee\");\n          if (callee.isArrowFunctionExpression()) {\n            // This is an IIFE, so we don't need to worry about the noNewArrows assumption\n            callee.arrowFunctionToExpression();\n          }\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,yBAAA,GAAAD,OAAA;AACA,IAAAE,qBAAA,GAAAF,OAAA;AACA,IAAAG,mBAAA,GAAAH,OAAA;AACA,IAAAI,6BAAA,GAAAJ,OAAA;AACA,IAAAK,KAAA,GAAAL,OAAA;AACA,IAAAM,QAAA,GAAAN,OAAA;AACA,IAAAO,eAAA,GAAAP,OAAA;AAEA,MAAMQ,iBAAiB,GAAIC,QAA8B,IACvDC,MAAM,CAACC,IAAI,CAACC,QAAO,CAACH,QAAQ,CAAC,CAAC,CAACI,MAAM,CAACC,IAAI,IAAI,QAAQ,CAACC,IAAI,CAACD,IAAI,CAAC,CAAC;AAEpE,MAAME,cAAc,GAAG,IAAIC,GAAG,CAAC,CAC7B,GAAGT,iBAAiB,CAAC,SAAS,CAAC,EAC/B,GAAGA,iBAAiB,CAAC,SAAS,CAAC,CAChC,CAAC;AAAC,IAAAU,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAMY,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAEC,OAAgB,KAAK;EAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA;EAChDL,GAAG,CAACM,aAAa,CAAkB,CAAE,CAAC;EAEtC,MAAM;IAAEC,KAAK,GAAG;EAAM,CAAC,GAAGN,OAAO;EAEjC,MAAMO,eAAe,IAAAN,eAAA,GAAGF,GAAG,CAACS,UAAU,CAAC,iBAAiB,CAAC,YAAAP,eAAA,GAAIK,KAAK;EAClE,MAAMG,aAAa,IAAAP,gBAAA,GAAGH,GAAG,CAACS,UAAU,CAAC,eAAe,CAAC,YAAAN,gBAAA,GAAII,KAAK;EAC9D,MAAMI,0BAA0B,IAAAP,gBAAA,GAC9BJ,GAAG,CAACS,UAAU,CAAC,4BAA4B,CAAC,YAAAL,gBAAA,GAAIG,KAAK;EACvD,MAAMK,YAAY,IAAAP,gBAAA,GAAGL,GAAG,CAACS,UAAU,CAAC,cAAc,CAAC,YAAAJ,gBAAA,GAAIE,KAAK;EAC5D,MAAMM,gBAAgB,GAAG,CAAC,IAAAC,oCAAU,EAClC,2BAA2B,EAC3Bd,GAAG,CAACe,OAAO,CAAC,CACd,CAAC;EAGD,MAAMC,OAAO,GAAG,IAAIC,OAAO,CAAC,CAAC;EAE7B,OAAO;IACLzB,IAAI,EAAE,mBAAmB;IAEzB0B,OAAO,EAAE;MACPC,wBAAwBA,CAACC,IAAI,EAAE;QAC7B,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,aAAa,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAE;QACnD,IAAAC,qCAAsB,EAACH,IAAI,CAAC;MAC9B,CAAC;MAEDI,gBAAgBA,CAACJ,IAAI,EAAE;QACrB,MAAM;UAAEK;QAAK,CAAC,GAAGL,IAAI;QAErB,MAAMM,GAAG,GAAGD,IAAI,CAACE,EAAE,IAAIP,IAAI,CAACQ,KAAK,CAACC,qBAAqB,CAAC,OAAO,CAAC;QAEhET,IAAI,CAACU,WAAW,CACdC,WAAC,CAACC,mBAAmB,CAAC,KAAK,EAAE,CAC3BD,WAAC,CAACE,kBAAkB,CAACP,GAAG,EAAEK,WAAC,CAACG,YAAY,CAACT,IAAI,CAAC,CAAC,CAChD,CACH,CAAC;MACH,CAAC;MAEDU,eAAeA,CAACf,IAAI,EAAEgB,KAAK,EAAE;QAC3B,MAAM;UAAEX;QAAK,CAAC,GAAGL,IAAI;QACrB,IAAIJ,OAAO,CAACqB,GAAG,CAACZ,IAAI,CAAC,EAAE;QAEvB,MAAMa,QAAQ,GAAG,IAAAC,2BAAY,EAACnB,IAAI,EAAEoB,SAAS,EAAE3B,gBAAgB,CAAC;QAChE,IAAIyB,QAAQ,IAAIA,QAAQ,KAAKb,IAAI,EAAE;UACjCL,IAAI,CAACU,WAAW,CAACQ,QAAQ,CAAC;UAC1B;QACF;QAEAtB,OAAO,CAACyB,GAAG,CAAChB,IAAI,CAAC;QAEjB,MAAM,CAACiB,YAAY,CAAC,GAAGtB,IAAI,CAACU,WAAW,CACrC,IAAAa,uBAAc,EACZvB,IAAI,EACJgB,KAAK,CAACQ,IAAI,EACVlD,cAAc,EACda,KAAK,EACL;UACEC,eAAe;UACfE,aAAa;UACbC,0BAA0B;UAC1BC;QACF,CAAC,EACDC,gBACF,CACF,CAAC;QAED,IAAI6B,YAAY,CAACG,gBAAgB,CAAC,CAAC,EAAE;UACnC,IAAAC,6BAAc,EAACJ,YAAY,CAAC;UAC5B,MAAMK,MAAM,GAAGL,YAAY,CAACrB,GAAG,CAAC,QAAQ,CAAC;UACzC,IAAI0B,MAAM,CAACC,yBAAyB,CAAC,CAAC,EAAE;YAEtCD,MAAM,CAACE,yBAAyB,CAAC,CAAC;UACpC;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}
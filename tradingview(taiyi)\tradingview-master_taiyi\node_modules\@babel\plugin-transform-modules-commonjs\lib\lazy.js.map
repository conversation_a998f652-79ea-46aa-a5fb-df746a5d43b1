{"version": 3, "names": ["_core", "require", "_helperModuleTransforms", "lazyImportsHook", "lazy", "name", "version", "getWrapperPayload", "source", "metadata", "isSideEffectImport", "reexportAll", "test", "Array", "isArray", "indexOf", "buildRequireWrapper", "init", "payload", "referenced", "template", "statement", "ast", "wrapReference", "ref", "t", "callExpression", "exports"], "sources": ["../src/lazy.ts"], "sourcesContent": ["import { template, types as t } from \"@babel/core\";\nimport { isSideEffectImport } from \"@babel/helper-module-transforms\";\nimport type { CommonJSHook } from \"./hooks.ts\";\n\ntype Lazy = boolean | string[] | ((source: string) => boolean);\n\nexport const lazyImportsHook = (lazy: Lazy): CommonJSHook => ({\n  name: `${PACKAGE_JSON.name}/lazy`,\n  version: PACKAGE_JSON.version,\n  getWrapperPayload(source, metadata) {\n    if (isSideEffectImport(metadata) || metadata.reexportAll) {\n      return null;\n    }\n    if (lazy === true) {\n      // 'true' means that local relative files are eagerly loaded and\n      // dependency modules are loaded lazily.\n      return /\\./.test(source) ? null : \"lazy/function\";\n    }\n    if (Array.isArray(lazy)) {\n      return lazy.indexOf(source) === -1 ? null : \"lazy/function\";\n    }\n    if (typeof lazy === \"function\") {\n      return lazy(source) ? \"lazy/function\" : null;\n    }\n  },\n  buildRequireWrapper(name, init, payload, referenced) {\n    if (payload === \"lazy/function\") {\n      if (!referenced) return false;\n      return template.statement.ast`\n        function ${name}() {\n          const data = ${init};\n          ${name} = function(){ return data; };\n          return data;\n        }\n      `;\n    }\n  },\n  wrapReference(ref, payload) {\n    if (payload === \"lazy/function\") return t.callExpression(ref, []);\n  },\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAD,OAAA;AAKO,MAAME,eAAe,GAAIC,IAAU,KAAoB;EAC5DC,IAAI,EAAG,6CAAoB,OAAM;EACjCC,OAAO,UAAsB;EAC7BC,iBAAiBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;IAClC,IAAI,IAAAC,0CAAkB,EAACD,QAAQ,CAAC,IAAIA,QAAQ,CAACE,WAAW,EAAE;MACxD,OAAO,IAAI;IACb;IACA,IAAIP,IAAI,KAAK,IAAI,EAAE;MAGjB,OAAO,IAAI,CAACQ,IAAI,CAACJ,MAAM,CAAC,GAAG,IAAI,GAAG,eAAe;IACnD;IACA,IAAIK,KAAK,CAACC,OAAO,CAACV,IAAI,CAAC,EAAE;MACvB,OAAOA,IAAI,CAACW,OAAO,CAACP,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG,eAAe;IAC7D;IACA,IAAI,OAAOJ,IAAI,KAAK,UAAU,EAAE;MAC9B,OAAOA,IAAI,CAACI,MAAM,CAAC,GAAG,eAAe,GAAG,IAAI;IAC9C;EACF,CAAC;EACDQ,mBAAmBA,CAACX,IAAI,EAAEY,IAAI,EAAEC,OAAO,EAAEC,UAAU,EAAE;IACnD,IAAID,OAAO,KAAK,eAAe,EAAE;MAC/B,IAAI,CAACC,UAAU,EAAE,OAAO,KAAK;MAC7B,OAAOC,cAAQ,CAACC,SAAS,CAACC,GAAI;AACpC,mBAAmBjB,IAAK;AACxB,yBAAyBY,IAAK;AAC9B,YAAYZ,IAAK;AACjB;AACA;AACA,OAAO;IACH;EACF,CAAC;EACDkB,aAAaA,CAACC,GAAG,EAAEN,OAAO,EAAE;IAC1B,IAAIA,OAAO,KAAK,eAAe,EAAE,OAAOO,WAAC,CAACC,cAAc,CAACF,GAAG,EAAE,EAAE,CAAC;EACnE;AACF,CAAC,CAAC;AAACG,OAAA,CAAAxB,eAAA,GAAAA,eAAA", "ignoreList": []}
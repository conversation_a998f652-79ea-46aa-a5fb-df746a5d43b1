{"version": 3, "file": "index.js", "sources": ["../src/shouldStoreRHSInTemporaryVariable.ts", "../src/compat-data.ts", "../src/index.ts"], "sourcesContent": ["import { types as t } from \"@babel/core\";\n\nconst {\n  isObjectProperty,\n  isArrayPattern,\n  isObjectPattern,\n  isAssignmentPattern,\n  isRestElement,\n  isIdentifier,\n} = t;\n/**\n * This is a helper function to determine if we should create an intermediate variable\n * such that the RHS of an assignment is not duplicated.\n *\n * See https://github.com/babel/babel/pull/13711#issuecomment-914388382 for discussion\n * on further optimizations.\n */\nexport default function shouldStoreRHSInTemporaryVariable(\n  node: t.LVal,\n): boolean {\n  if (isArrayPattern(node)) {\n    const nonNullElements = node.elements.filter(element => element !== null);\n    if (nonNullElements.length > 1) return true;\n    else return shouldStoreRHSInTemporaryVariable(nonNullElements[0]);\n  } else if (isObjectPattern(node)) {\n    const { properties } = node;\n    if (properties.length > 1) return true;\n    else if (properties.length === 0) return false;\n    else {\n      const firstProperty = properties[0];\n      if (isObjectProperty(firstProperty)) {\n        // the value of the property must be an LVal\n        return shouldStoreRHSInTemporaryVariable(firstProperty.value as t.LVal);\n      } else {\n        return shouldStoreRHSInTemporaryVariable(firstProperty);\n      }\n    }\n  } else if (isAssignmentPattern(node)) {\n    return shouldStoreRHSInTemporaryVariable(node.left);\n  } else if (isRestElement(node)) {\n    if (isIdentifier(node.argument)) return true;\n    return shouldStoreRHSInTemporaryVariable(node.argument);\n  } else {\n    // node is Identifier or MemberExpression\n    return false;\n  }\n}\n", "export default {\n  \"Object.assign\": {\n    chrome: \"49\",\n    opera: \"36\",\n    edge: \"13\",\n    firefox: \"36\",\n    safari: \"10\",\n    node: \"6\",\n    deno: \"1\",\n    ios: \"10\",\n    samsung: \"5\",\n    opera_mobile: \"36\",\n    electron: \"0.37\",\n  },\n};\n", "import { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t } from \"@babel/core\";\nimport type { PluginPass } from \"@babel/core\";\nimport type { NodePath, Scope } from \"@babel/traverse\";\nimport { convertFunctionParams } from \"@babel/plugin-transform-parameters\";\nimport { isRequired } from \"@babel/helper-compilation-targets\";\nimport shouldStoreRHSInTemporaryVariable from \"./shouldStoreRHSInTemporaryVariable.ts\";\nimport compatData from \"./compat-data.ts\";\n\nconst { isAssignmentPattern, isObjectProperty } = t;\n// @babel/types <=7.3.3 counts FOO as referenced in var { x: FOO }.\n// We need to detect this bug to know if \"unused\" means 0 or 1 references.\nif (!process.env.BABEL_8_BREAKING) {\n  const node = t.identifier(\"a\");\n  const property = t.objectProperty(t.identifier(\"key\"), node);\n  const pattern = t.objectPattern([property]);\n\n  // eslint-disable-next-line no-var\n  var ZERO_REFS = t.isReferenced(node, property, pattern) ? 1 : 0;\n}\n\ntype Param = NodePath<t.Function[\"params\"][number]>;\nexport interface Options {\n  useBuiltIns?: boolean;\n  loose?: boolean;\n}\n\nexport default declare((api, opts: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const targets = api.targets();\n  const supportsObjectAssign = !isRequired(\"Object.assign\", targets, {\n    compatData,\n  });\n\n  const { useBuiltIns = supportsObjectAssign, loose = false } = opts;\n\n  if (typeof loose !== \"boolean\") {\n    throw new Error(\".loose must be a boolean, or undefined\");\n  }\n\n  const ignoreFunctionLength = api.assumption(\"ignoreFunctionLength\") ?? loose;\n  const objectRestNoSymbols = api.assumption(\"objectRestNoSymbols\") ?? loose;\n  const pureGetters = api.assumption(\"pureGetters\") ?? loose;\n  const setSpreadProperties = api.assumption(\"setSpreadProperties\") ?? loose;\n\n  function getExtendsHelper(\n    file: PluginPass,\n  ): t.MemberExpression | t.Identifier {\n    return useBuiltIns\n      ? t.memberExpression(t.identifier(\"Object\"), t.identifier(\"assign\"))\n      : file.addHelper(\"extends\");\n  }\n\n  function hasRestElement(path: Param) {\n    let foundRestElement = false;\n    visitRestElements(path, restElement => {\n      foundRestElement = true;\n      restElement.stop();\n    });\n    return foundRestElement;\n  }\n\n  function hasObjectPatternRestElement(path: NodePath): boolean {\n    let foundRestElement = false;\n    visitRestElements(path, restElement => {\n      if (restElement.parentPath.isObjectPattern()) {\n        foundRestElement = true;\n        restElement.stop();\n      }\n    });\n    return foundRestElement;\n  }\n\n  function visitRestElements(\n    path: NodePath,\n    visitor: (path: NodePath<t.RestElement>) => any,\n  ) {\n    path.traverse({\n      Expression(path) {\n        const { parent, key } = path;\n        if (\n          (isAssignmentPattern(parent) && key === \"right\") ||\n          (isObjectProperty(parent) && parent.computed && key === \"key\")\n        ) {\n          path.skip();\n        }\n      },\n      RestElement: visitor,\n    });\n  }\n\n  function hasSpread(node: t.ObjectExpression): boolean {\n    for (const prop of node.properties) {\n      if (t.isSpreadElement(prop)) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  // returns an array of all keys of an object, and a status flag indicating if all extracted keys\n  // were converted to stringLiterals or not\n  // e.g. extracts {keys: [\"a\", \"b\", \"3\", ++x], allPrimitives: false }\n  // from ast of {a: \"foo\", b, 3: \"bar\", [++x]: \"baz\"}\n  // `allPrimitives: false` doesn't necessarily mean that there is a non-primitive, but just\n  // that we are not sure.\n  function extractNormalizedKeys(node: t.ObjectPattern) {\n    // RestElement has been removed in createObjectRest\n    const props = node.properties as t.ObjectProperty[];\n    const keys: t.Expression[] = [];\n    let allPrimitives = true;\n    let hasTemplateLiteral = false;\n\n    for (const prop of props) {\n      const { key } = prop;\n      if (t.isIdentifier(key) && !prop.computed) {\n        // since a key {a: 3} is equivalent to {\"a\": 3}, use the latter\n        keys.push(t.stringLiteral(key.name));\n      } else if (t.isTemplateLiteral(key)) {\n        keys.push(t.cloneNode(key));\n        hasTemplateLiteral = true;\n      } else if (t.isLiteral(key)) {\n        keys.push(\n          t.stringLiteral(\n            String(\n              // @ts-expect-error prop.key can not be a NullLiteral\n              key.value,\n            ),\n          ),\n        );\n      } else {\n        // @ts-expect-error private name has been handled by destructuring-private\n        keys.push(t.cloneNode(key));\n\n        if (\n          (t.isMemberExpression(key, { computed: false }) &&\n            t.isIdentifier(key.object, { name: \"Symbol\" })) ||\n          (t.isCallExpression(key) &&\n            t.matchesPattern(key.callee, \"Symbol.for\"))\n        ) {\n          // there all return a primitive\n        } else {\n          allPrimitives = false;\n        }\n      }\n    }\n\n    return { keys, allPrimitives, hasTemplateLiteral };\n  }\n\n  // replaces impure computed keys with new identifiers\n  // and returns variable declarators of these new identifiers\n  function replaceImpureComputedKeys(\n    properties: NodePath<t.ObjectProperty>[],\n    scope: Scope,\n  ) {\n    const impureComputedPropertyDeclarators: t.VariableDeclarator[] = [];\n    for (const propPath of properties) {\n      // PrivateName is handled in destructuring-private plugin\n      const key = propPath.get(\"key\") as NodePath<t.Expression>;\n      if (propPath.node.computed && !key.isPure()) {\n        const name = scope.generateUidBasedOnNode(key.node);\n        const declarator = t.variableDeclarator(t.identifier(name), key.node);\n        impureComputedPropertyDeclarators.push(declarator);\n        key.replaceWith(t.identifier(name));\n      }\n    }\n    return impureComputedPropertyDeclarators;\n  }\n\n  function removeUnusedExcludedKeys(path: NodePath<t.ObjectPattern>): void {\n    const bindings = path.getOuterBindingIdentifierPaths();\n\n    Object.keys(bindings).forEach(bindingName => {\n      const bindingParentPath = bindings[bindingName].parentPath;\n      if (\n        path.scope.getBinding(bindingName).references >\n          (process.env.BABEL_8_BREAKING ? 0 : ZERO_REFS) ||\n        !bindingParentPath.isObjectProperty()\n      ) {\n        return;\n      }\n      bindingParentPath.remove();\n    });\n  }\n\n  //expects path to an object pattern\n  function createObjectRest(\n    path: NodePath<t.ObjectPattern>,\n    file: PluginPass,\n    objRef: t.Identifier | t.MemberExpression,\n  ): [t.VariableDeclarator[], t.LVal, t.CallExpression] {\n    const props = path.get(\"properties\");\n    const last = props[props.length - 1];\n    t.assertRestElement(last.node);\n    const restElement = t.cloneNode(last.node);\n    last.remove();\n\n    const impureComputedPropertyDeclarators = replaceImpureComputedKeys(\n      path.get(\"properties\") as NodePath<t.ObjectProperty>[],\n      path.scope,\n    );\n    const { keys, allPrimitives, hasTemplateLiteral } = extractNormalizedKeys(\n      path.node,\n    );\n\n    if (keys.length === 0) {\n      return [\n        impureComputedPropertyDeclarators,\n        restElement.argument,\n        t.callExpression(getExtendsHelper(file), [\n          t.objectExpression([]),\n          t.sequenceExpression([\n            t.callExpression(file.addHelper(\"objectDestructuringEmpty\"), [\n              t.cloneNode(objRef),\n            ]),\n            t.cloneNode(objRef),\n          ]),\n        ]),\n      ];\n    }\n\n    let keyExpression;\n    if (!allPrimitives) {\n      // map to toPropertyKey to handle the possible non-string values\n      keyExpression = t.callExpression(\n        t.memberExpression(t.arrayExpression(keys), t.identifier(\"map\")),\n        [file.addHelper(\"toPropertyKey\")],\n      );\n    } else {\n      keyExpression = t.arrayExpression(keys);\n\n      if (!hasTemplateLiteral && !t.isProgram(path.scope.block)) {\n        // Hoist definition of excluded keys, so that it's not created each time.\n        const program = path.findParent(path => path.isProgram());\n        const id = path.scope.generateUidIdentifier(\"excluded\");\n\n        program.scope.push({\n          id,\n          init: keyExpression,\n          kind: \"const\",\n        });\n\n        keyExpression = t.cloneNode(id);\n      }\n    }\n\n    return [\n      impureComputedPropertyDeclarators,\n      restElement.argument,\n      t.callExpression(\n        file.addHelper(\n          `objectWithoutProperties${objectRestNoSymbols ? \"Loose\" : \"\"}`,\n        ),\n        [t.cloneNode(objRef), keyExpression],\n      ),\n    ];\n  }\n\n  function replaceRestElement(\n    parentPath: NodePath<t.Function | t.CatchClause>,\n    paramPath: NodePath<\n      t.Function[\"params\"][number] | t.AssignmentPattern[\"left\"]\n    >,\n    container?: t.VariableDeclaration[],\n  ): void {\n    if (paramPath.isAssignmentPattern()) {\n      replaceRestElement(parentPath, paramPath.get(\"left\"), container);\n      return;\n    }\n\n    if (paramPath.isArrayPattern() && hasRestElement(paramPath)) {\n      const elements = paramPath.get(\"elements\");\n\n      for (let i = 0; i < elements.length; i++) {\n        replaceRestElement(parentPath, elements[i], container);\n      }\n    }\n\n    if (paramPath.isObjectPattern() && hasRestElement(paramPath)) {\n      const uid = parentPath.scope.generateUidIdentifier(\"ref\");\n\n      const declar = t.variableDeclaration(\"let\", [\n        t.variableDeclarator(paramPath.node, uid),\n      ]);\n\n      if (container) {\n        container.push(declar);\n      } else {\n        parentPath.ensureBlock();\n        parentPath.get(\"body\").unshiftContainer(\"body\", declar);\n      }\n      paramPath.replaceWith(t.cloneNode(uid));\n    }\n  }\n\n  return {\n    name: \"transform-object-rest-spread\",\n    inherits:\n      USE_ESM || IS_STANDALONE || api.version[0] === \"8\"\n        ? undefined\n        : // eslint-disable-next-line no-restricted-globals\n          require(\"@babel/plugin-syntax-object-rest-spread\").default,\n\n    visitor: {\n      // function a({ b, ...c }) {}\n      Function(path) {\n        const params = path.get(\"params\");\n        const paramsWithRestElement = new Set<number>();\n        const idsInRestParams = new Set();\n        for (let i = 0; i < params.length; ++i) {\n          const param = params[i];\n          if (hasRestElement(param)) {\n            paramsWithRestElement.add(i);\n            for (const name of Object.keys(param.getBindingIdentifiers())) {\n              idsInRestParams.add(name);\n            }\n          }\n        }\n\n        // if true, a parameter exists that has an id in its initializer\n        // that is also an id bound in a rest parameter\n        // example: f({...R}, a = R)\n        let idInRest = false;\n\n        const IdentifierHandler = function (\n          path: NodePath<t.Identifier>,\n          functionScope: Scope,\n        ) {\n          const name = path.node.name;\n          if (\n            path.scope.getBinding(name) === functionScope.getBinding(name) &&\n            idsInRestParams.has(name)\n          ) {\n            idInRest = true;\n            path.stop();\n          }\n        };\n\n        let i: number;\n        for (i = 0; i < params.length && !idInRest; ++i) {\n          const param = params[i];\n          if (!paramsWithRestElement.has(i)) {\n            if (param.isReferencedIdentifier() || param.isBindingIdentifier()) {\n              IdentifierHandler(param, path.scope);\n            } else {\n              param.traverse(\n                {\n                  \"Scope|TypeAnnotation|TSTypeAnnotation\": path => path.skip(),\n                  \"ReferencedIdentifier|BindingIdentifier\": IdentifierHandler,\n                },\n                path.scope,\n              );\n            }\n          }\n        }\n\n        if (!idInRest) {\n          for (let i = 0; i < params.length; ++i) {\n            const param = params[i];\n            if (paramsWithRestElement.has(i)) {\n              replaceRestElement(path, param);\n            }\n          }\n        } else {\n          const shouldTransformParam = (idx: number) =>\n            idx >= i - 1 || paramsWithRestElement.has(idx);\n          convertFunctionParams(\n            path,\n            ignoreFunctionLength,\n            shouldTransformParam,\n            replaceRestElement,\n          );\n        }\n      },\n\n      // adapted from transform-destructuring/src/index.js#pushObjectRest\n      // const { a, ...b } = c;\n      VariableDeclarator(path, file) {\n        if (!path.get(\"id\").isObjectPattern()) {\n          return;\n        }\n\n        let insertionPath = path;\n        const originalPath = path;\n\n        visitRestElements(path.get(\"id\"), path => {\n          if (!path.parentPath.isObjectPattern()) {\n            // Return early if the parent is not an ObjectPattern, but\n            // (for example) an ArrayPattern or Function, because that\n            // means this RestElement is an not an object property.\n            return;\n          }\n\n          if (\n            // skip single-property case, e.g.\n            // const { ...x } = foo();\n            // since the RHS will not be duplicated\n            shouldStoreRHSInTemporaryVariable(originalPath.node.id) &&\n            !t.isIdentifier(originalPath.node.init)\n          ) {\n            // const { a, ...b } = foo();\n            // to avoid calling foo() twice, as a first step convert it to:\n            // const _foo = foo(),\n            //       { a, ...b } = _foo;\n            const initRef = path.scope.generateUidIdentifierBasedOnNode(\n              originalPath.node.init,\n              \"ref\",\n            );\n            // insert _foo = foo()\n            originalPath.insertBefore(\n              t.variableDeclarator(initRef, originalPath.node.init),\n            );\n            // replace foo() with _foo\n            originalPath.replaceWith(\n              t.variableDeclarator(originalPath.node.id, t.cloneNode(initRef)),\n            );\n\n            return;\n          }\n\n          let ref = originalPath.node.init;\n          const refPropertyPath: NodePath<t.ObjectProperty>[] = [];\n          let kind;\n\n          path.findParent((path: NodePath): boolean => {\n            if (path.isObjectProperty()) {\n              refPropertyPath.unshift(path);\n            } else if (path.isVariableDeclarator()) {\n              kind = path.parentPath.node.kind;\n              return true;\n            }\n          });\n\n          const impureObjRefComputedDeclarators = replaceImpureComputedKeys(\n            refPropertyPath,\n            path.scope,\n          );\n          refPropertyPath.forEach(prop => {\n            const { node } = prop;\n            ref = t.memberExpression(\n              ref,\n              t.cloneNode(node.key),\n              node.computed || t.isLiteral(node.key),\n            );\n          });\n\n          //@ts-expect-error: findParent can not apply assertions on result shape\n          const objectPatternPath: NodePath<t.ObjectPattern> = path.findParent(\n            path => path.isObjectPattern(),\n          );\n\n          const [impureComputedPropertyDeclarators, argument, callExpression] =\n            createObjectRest(\n              objectPatternPath,\n              file,\n              ref as t.MemberExpression,\n            );\n\n          if (pureGetters) {\n            removeUnusedExcludedKeys(objectPatternPath);\n          }\n\n          t.assertIdentifier(argument);\n\n          insertionPath.insertBefore(impureComputedPropertyDeclarators);\n\n          insertionPath.insertBefore(impureObjRefComputedDeclarators);\n\n          insertionPath = insertionPath.insertAfter(\n            t.variableDeclarator(argument, callExpression),\n          )[0] as NodePath<t.VariableDeclarator>;\n\n          path.scope.registerBinding(kind, insertionPath);\n\n          if (objectPatternPath.node.properties.length === 0) {\n            objectPatternPath\n              .findParent(\n                path => path.isObjectProperty() || path.isVariableDeclarator(),\n              )\n              .remove();\n          }\n        });\n      },\n\n      // taken from transform-destructuring/src/index.js#visitor\n      // export var { a, ...b } = c;\n      ExportNamedDeclaration(path) {\n        const declaration = path.get(\"declaration\");\n        if (!declaration.isVariableDeclaration()) return;\n\n        const hasRest = declaration\n          .get(\"declarations\")\n          .some(path => hasObjectPatternRestElement(path.get(\"id\")));\n        if (!hasRest) return;\n\n        const specifiers = [];\n\n        for (const name of Object.keys(path.getOuterBindingIdentifiers(true))) {\n          specifiers.push(\n            t.exportSpecifier(t.identifier(name), t.identifier(name)),\n          );\n        }\n\n        // Split the declaration and export list into two declarations so that the variable\n        // declaration can be split up later without needing to worry about not being a\n        // top-level statement.\n        path.replaceWith(declaration.node);\n        path.insertAfter(t.exportNamedDeclaration(null, specifiers));\n      },\n\n      // try {} catch ({a, ...b}) {}\n      CatchClause(path) {\n        const paramPath = path.get(\"param\");\n        replaceRestElement(path, paramPath);\n      },\n\n      // ({a, ...b} = c);\n      AssignmentExpression(path, file) {\n        const leftPath = path.get(\"left\");\n        if (leftPath.isObjectPattern() && hasRestElement(leftPath)) {\n          const nodes = [];\n\n          const refName = path.scope.generateUidBasedOnNode(\n            path.node.right,\n            \"ref\",\n          );\n\n          nodes.push(\n            t.variableDeclaration(\"var\", [\n              t.variableDeclarator(t.identifier(refName), path.node.right),\n            ]),\n          );\n\n          const [impureComputedPropertyDeclarators, argument, callExpression] =\n            createObjectRest(leftPath, file, t.identifier(refName));\n\n          if (impureComputedPropertyDeclarators.length > 0) {\n            nodes.push(\n              t.variableDeclaration(\"var\", impureComputedPropertyDeclarators),\n            );\n          }\n\n          const nodeWithoutSpread = t.cloneNode(path.node);\n          nodeWithoutSpread.right = t.identifier(refName);\n          nodes.push(t.expressionStatement(nodeWithoutSpread));\n          nodes.push(\n            t.expressionStatement(\n              t.assignmentExpression(\"=\", argument, callExpression),\n            ),\n          );\n          nodes.push(t.expressionStatement(t.identifier(refName)));\n\n          path.replaceWithMultiple(nodes);\n        }\n      },\n\n      // taken from transform-destructuring/src/index.js#visitor\n      ForXStatement(path: NodePath<t.ForXStatement>) {\n        const { node, scope } = path;\n        const leftPath = path.get(\"left\");\n        const left = node.left;\n\n        if (!hasObjectPatternRestElement(leftPath)) {\n          return;\n        }\n\n        if (!t.isVariableDeclaration(left)) {\n          // for ({a, ...b} of []) {}\n          const temp = scope.generateUidIdentifier(\"ref\");\n\n          node.left = t.variableDeclaration(\"var\", [\n            t.variableDeclarator(temp),\n          ]);\n\n          path.ensureBlock();\n          const body = path.node.body;\n\n          if (body.body.length === 0 && path.isCompletionRecord()) {\n            body.body.unshift(\n              t.expressionStatement(scope.buildUndefinedNode()),\n            );\n          }\n\n          body.body.unshift(\n            t.expressionStatement(\n              t.assignmentExpression(\"=\", left, t.cloneNode(temp)),\n            ),\n          );\n        } else {\n          // for (var {a, ...b} of []) {}\n          const pattern = left.declarations[0].id;\n\n          const key = scope.generateUidIdentifier(\"ref\");\n          node.left = t.variableDeclaration(left.kind, [\n            t.variableDeclarator(key, null),\n          ]);\n\n          path.ensureBlock();\n          const body = node.body as t.BlockStatement;\n\n          body.body.unshift(\n            t.variableDeclaration(node.left.kind, [\n              t.variableDeclarator(pattern, t.cloneNode(key)),\n            ]),\n          );\n        }\n      },\n\n      // [{a, ...b}] = c;\n      ArrayPattern(path) {\n        const objectPatterns: t.VariableDeclarator[] = [];\n\n        visitRestElements(path, path => {\n          if (!path.parentPath.isObjectPattern()) {\n            // Return early if the parent is not an ObjectPattern, but\n            // (for example) an ArrayPattern or Function, because that\n            // means this RestElement is an not an object property.\n            return;\n          }\n\n          const objectPattern = path.parentPath;\n\n          const uid = path.scope.generateUidIdentifier(\"ref\");\n          objectPatterns.push(t.variableDeclarator(objectPattern.node, uid));\n\n          objectPattern.replaceWith(t.cloneNode(uid));\n          path.skip();\n        });\n\n        if (objectPatterns.length > 0) {\n          const statementPath = path.getStatementParent();\n          const statementNode = statementPath.node;\n          const kind =\n            statementNode.type === \"VariableDeclaration\"\n              ? statementNode.kind\n              : \"var\";\n          statementPath.insertAfter(\n            t.variableDeclaration(kind, objectPatterns),\n          );\n        }\n      },\n\n      // var a = { ...b, ...c }\n      ObjectExpression(path, file) {\n        if (!hasSpread(path.node)) return;\n\n        let helper: t.Identifier | t.MemberExpression;\n        if (setSpreadProperties) {\n          helper = getExtendsHelper(file);\n        } else {\n          if (process.env.BABEL_8_BREAKING) {\n            helper = file.addHelper(\"objectSpread2\");\n          } else {\n            try {\n              helper = file.addHelper(\"objectSpread2\");\n            } catch {\n              // TODO: This is needed to workaround https://github.com/babel/babel/issues/10187\n              // and https://github.com/babel/babel/issues/10179 for older @babel/core versions\n              // where #10187 isn't fixed.\n              this.file.declarations[\"objectSpread2\"] = null;\n\n              // objectSpread2 has been introduced in v7.5.0\n              // We have to maintain backward compatibility.\n              helper = file.addHelper(\"objectSpread\");\n            }\n          }\n        }\n\n        let exp: t.CallExpression = null;\n        let props: t.ObjectMember[] = [];\n\n        function make() {\n          const hadProps = props.length > 0;\n          const obj = t.objectExpression(props);\n          props = [];\n\n          if (!exp) {\n            exp = t.callExpression(helper, [obj]);\n            return;\n          }\n\n          // When we can assume that getters are pure and don't depend on\n          // the order of evaluation, we can avoid making multiple calls.\n          if (pureGetters) {\n            if (hadProps) {\n              exp.arguments.push(obj);\n            }\n            return;\n          }\n\n          exp = t.callExpression(t.cloneNode(helper), [\n            exp,\n            // If we have static props, we need to insert an empty object\n            // because the odd arguments are copied with [[Get]], not\n            // [[GetOwnProperty]]\n            ...(hadProps ? [t.objectExpression([]), obj] : []),\n          ]);\n        }\n\n        for (const prop of path.node.properties) {\n          if (t.isSpreadElement(prop)) {\n            make();\n            exp.arguments.push(prop.argument);\n          } else {\n            props.push(prop);\n          }\n        }\n\n        if (props.length) make();\n\n        path.replaceWith(exp);\n      },\n    },\n  };\n});\n"], "names": ["isObjectProperty", "isArrayPattern", "isObjectPattern", "isAssignmentPattern", "isRestElement", "isIdentifier", "t", "shouldStoreRHSInTemporaryVariable", "node", "nonNullElements", "elements", "filter", "element", "length", "properties", "firstProperty", "value", "left", "argument", "chrome", "opera", "edge", "firefox", "safari", "deno", "ios", "samsung", "opera_mobile", "electron", "identifier", "property", "objectProperty", "pattern", "objectPattern", "ZERO_REFS", "isReferenced", "declare", "api", "opts", "_api$assumption", "_api$assumption2", "_api$assumption3", "_api$assumption4", "assertVersion", "targets", "supportsObjectAssign", "isRequired", "compatData", "useBuiltIns", "loose", "Error", "ignoreFunctionLength", "assumption", "objectRestNoSymbols", "pureGetters", "setSpreadProperties", "getExtendsHelper", "file", "memberExpression", "addHelper", "hasRestElement", "path", "foundRestElement", "visitRestElements", "restElement", "stop", "hasObjectPatternRestElement", "parentPath", "visitor", "traverse", "Expression", "parent", "key", "computed", "skip", "RestElement", "hasSpread", "prop", "isSpreadElement", "extractNormalizedKeys", "props", "keys", "allPrimitives", "hasTemplateLiteral", "push", "stringLiteral", "name", "isTemplateLiteral", "cloneNode", "isLiteral", "String", "isMemberExpression", "object", "isCallExpression", "matchesPattern", "callee", "replaceImpureComputedKeys", "scope", "impureComputedPropertyDeclarators", "prop<PERSON>ath", "get", "isPure", "generateUidBasedOnNode", "declarator", "variableDeclarator", "replaceWith", "removeUnusedExcludedKeys", "bindings", "getOuterBindingIdentifierPaths", "Object", "for<PERSON>ach", "bindingName", "bindingParentPath", "getBinding", "references", "remove", "createObjectRest", "objRef", "last", "assertRestElement", "callExpression", "objectExpression", "sequenceExpression", "keyExpression", "arrayExpression", "isProgram", "block", "program", "findParent", "id", "generateUidIdentifier", "init", "kind", "replaceRestElement", "<PERSON><PERSON><PERSON><PERSON>", "container", "i", "uid", "declar", "variableDeclaration", "ensureBlock", "unshiftContainer", "inherits", "version", "undefined", "require", "default", "Function", "params", "paramsWithRestElement", "Set", "idsInRestParams", "param", "add", "getBindingIdentifiers", "idInRest", "IdentifierHandler", "functionScope", "has", "isReferencedIdentifier", "isBindingIdentifier", "shouldTransformParam", "idx", "convertFunctionParams", "VariableDeclarator", "insertionPath", "originalPath", "initRef", "generateUidIdentifierBasedOnNode", "insertBefore", "ref", "refProper<PERSON><PERSON>ath", "unshift", "isVariableDeclarator", "impureObjRefComputedDeclarators", "objectPatternPath", "assertIdentifier", "insertAfter", "registerBinding", "ExportNamedDeclaration", "declaration", "isVariableDeclaration", "hasRest", "some", "specifiers", "getOuterBindingIdentifiers", "exportSpecifier", "exportNamedDeclaration", "CatchClause", "AssignmentExpression", "leftPath", "nodes", "refName", "right", "nodeWithoutSpread", "expressionStatement", "assignmentExpression", "replaceWithMultiple", "ForXStatement", "temp", "body", "isCompletionRecord", "buildUndefinedNode", "declarations", "ArrayPattern", "objectPatterns", "statementPath", "getStatementParent", "statementNode", "type", "ObjectExpression", "helper", "_unused", "exp", "make", "hadProps", "obj", "arguments"], "mappings": ";;;;;;;;;AAEA,MAAM;oBACJA,kBAAgB;EAChBC,cAAc;EACdC,eAAe;uBACfC,qBAAmB;EACnBC,aAAa;AACbC,EAAAA,YAAAA;AACF,CAAC,GAAGC,UAAC,CAAA;AAQU,SAASC,iCAAiCA,CACvDC,IAAY,EACH;AACT,EAAA,IAAIP,cAAc,CAACO,IAAI,CAAC,EAAE;AACxB,IAAA,MAAMC,eAAe,GAAGD,IAAI,CAACE,QAAQ,CAACC,MAAM,CAACC,OAAO,IAAIA,OAAO,KAAK,IAAI,CAAC,CAAA;AACzE,IAAA,IAAIH,eAAe,CAACI,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI,CAAC,KACvC,OAAON,iCAAiC,CAACE,eAAe,CAAC,CAAC,CAAC,CAAC,CAAA;AACnE,GAAC,MAAM,IAAIP,eAAe,CAACM,IAAI,CAAC,EAAE;IAChC,MAAM;AAAEM,MAAAA,UAAAA;AAAW,KAAC,GAAGN,IAAI,CAAA;IAC3B,IAAIM,UAAU,CAACD,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI,CAAC,KAClC,IAAIC,UAAU,CAACD,MAAM,KAAK,CAAC,EAAE,OAAO,KAAK,CAAC,KAC1C;AACH,MAAA,MAAME,aAAa,GAAGD,UAAU,CAAC,CAAC,CAAC,CAAA;AACnC,MAAA,IAAId,kBAAgB,CAACe,aAAa,CAAC,EAAE;AAEnC,QAAA,OAAOR,iCAAiC,CAACQ,aAAa,CAACC,KAAe,CAAC,CAAA;AACzE,OAAC,MAAM;QACL,OAAOT,iCAAiC,CAACQ,aAAa,CAAC,CAAA;AACzD,OAAA;AACF,KAAA;AACF,GAAC,MAAM,IAAIZ,qBAAmB,CAACK,IAAI,CAAC,EAAE;AACpC,IAAA,OAAOD,iCAAiC,CAACC,IAAI,CAACS,IAAI,CAAC,CAAA;AACrD,GAAC,MAAM,IAAIb,aAAa,CAACI,IAAI,CAAC,EAAE;IAC9B,IAAIH,YAAY,CAACG,IAAI,CAACU,QAAQ,CAAC,EAAE,OAAO,IAAI,CAAA;AAC5C,IAAA,OAAOX,iCAAiC,CAACC,IAAI,CAACU,QAAQ,CAAC,CAAA;AACzD,GAAC,MAAM;AAEL,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;AACF;;AC9CA,iBAAe;AACb,EAAA,eAAe,EAAE;AACfC,IAAAA,MAAM,EAAE,IAAI;AACZC,IAAAA,KAAK,EAAE,IAAI;AACXC,IAAAA,IAAI,EAAE,IAAI;AACVC,IAAAA,OAAO,EAAE,IAAI;AACbC,IAAAA,MAAM,EAAE,IAAI;AACZf,IAAAA,IAAI,EAAE,GAAG;AACTgB,IAAAA,IAAI,EAAE,GAAG;AACTC,IAAAA,GAAG,EAAE,IAAI;AACTC,IAAAA,OAAO,EAAE,GAAG;AACZC,IAAAA,YAAY,EAAE,IAAI;AAClBC,IAAAA,QAAQ,EAAE,MAAA;AACZ,GAAA;AACF,CAAC;;ACLD,MAAM;EAAEzB,mBAAmB;AAAEH,EAAAA,gBAAAA;AAAiB,CAAC,GAAGM,UAAC,CAAA;AAGhB;AACjC,EAAA,MAAME,IAAI,GAAGF,UAAC,CAACuB,UAAU,CAAC,GAAG,CAAC,CAAA;AAC9B,EAAA,MAAMC,QAAQ,GAAGxB,UAAC,CAACyB,cAAc,CAACzB,UAAC,CAACuB,UAAU,CAAC,KAAK,CAAC,EAAErB,IAAI,CAAC,CAAA;EAC5D,MAAMwB,OAAO,GAAG1B,UAAC,CAAC2B,aAAa,CAAC,CAACH,QAAQ,CAAC,CAAC,CAAA;AAG3C,EAAA,IAAII,SAAS,GAAG5B,UAAC,CAAC6B,YAAY,CAAC3B,IAAI,EAAEsB,QAAQ,EAAEE,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;AACjE,CAAA;AAQA,YAAeI,yBAAO,CAAC,CAACC,GAAG,EAAEC,IAAa,KAAK;AAAA,EAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,CAAA;AAC7CL,EAAAA,GAAG,CAACM,aAAa,CAAkB,CAAE,CAAC,CAAA;AAEtC,EAAA,MAAMC,OAAO,GAAGP,GAAG,CAACO,OAAO,EAAE,CAAA;EAC7B,MAAMC,oBAAoB,GAAG,CAACC,mCAAU,CAAC,eAAe,EAAEF,OAAO,EAAE;AACjEG,IAAAA,UAAAA;AACF,GAAC,CAAC,CAAA;EAEF,MAAM;AAAEC,IAAAA,WAAW,GAAGH,oBAAoB;AAAEI,IAAAA,KAAK,GAAG,KAAA;AAAM,GAAC,GAAGX,IAAI,CAAA;AAElE,EAAA,IAAI,OAAOW,KAAK,KAAK,SAAS,EAAE;AAC9B,IAAA,MAAM,IAAIC,KAAK,CAAC,wCAAwC,CAAC,CAAA;AAC3D,GAAA;AAEA,EAAA,MAAMC,oBAAoB,GAAA,CAAAZ,eAAA,GAAGF,GAAG,CAACe,UAAU,CAAC,sBAAsB,CAAC,KAAAb,IAAAA,GAAAA,eAAA,GAAIU,KAAK,CAAA;AAC5E,EAAA,MAAMI,mBAAmB,GAAA,CAAAb,gBAAA,GAAGH,GAAG,CAACe,UAAU,CAAC,qBAAqB,CAAC,KAAAZ,IAAAA,GAAAA,gBAAA,GAAIS,KAAK,CAAA;AAC1E,EAAA,MAAMK,WAAW,GAAA,CAAAb,gBAAA,GAAGJ,GAAG,CAACe,UAAU,CAAC,aAAa,CAAC,KAAAX,IAAAA,GAAAA,gBAAA,GAAIQ,KAAK,CAAA;AAC1D,EAAA,MAAMM,mBAAmB,GAAA,CAAAb,gBAAA,GAAGL,GAAG,CAACe,UAAU,CAAC,qBAAqB,CAAC,KAAAV,IAAAA,GAAAA,gBAAA,GAAIO,KAAK,CAAA;EAE1E,SAASO,gBAAgBA,CACvBC,IAAgB,EACmB;IACnC,OAAOT,WAAW,GACd1C,UAAC,CAACoD,gBAAgB,CAACpD,UAAC,CAACuB,UAAU,CAAC,QAAQ,CAAC,EAAEvB,UAAC,CAACuB,UAAU,CAAC,QAAQ,CAAC,CAAC,GAClE4B,IAAI,CAACE,SAAS,CAAC,SAAS,CAAC,CAAA;AAC/B,GAAA;EAEA,SAASC,cAAcA,CAACC,IAAW,EAAE;IACnC,IAAIC,gBAAgB,GAAG,KAAK,CAAA;AAC5BC,IAAAA,iBAAiB,CAACF,IAAI,EAAEG,WAAW,IAAI;AACrCF,MAAAA,gBAAgB,GAAG,IAAI,CAAA;MACvBE,WAAW,CAACC,IAAI,EAAE,CAAA;AACpB,KAAC,CAAC,CAAA;AACF,IAAA,OAAOH,gBAAgB,CAAA;AACzB,GAAA;EAEA,SAASI,2BAA2BA,CAACL,IAAc,EAAW;IAC5D,IAAIC,gBAAgB,GAAG,KAAK,CAAA;AAC5BC,IAAAA,iBAAiB,CAACF,IAAI,EAAEG,WAAW,IAAI;AACrC,MAAA,IAAIA,WAAW,CAACG,UAAU,CAACjE,eAAe,EAAE,EAAE;AAC5C4D,QAAAA,gBAAgB,GAAG,IAAI,CAAA;QACvBE,WAAW,CAACC,IAAI,EAAE,CAAA;AACpB,OAAA;AACF,KAAC,CAAC,CAAA;AACF,IAAA,OAAOH,gBAAgB,CAAA;AACzB,GAAA;AAEA,EAAA,SAASC,iBAAiBA,CACxBF,IAAc,EACdO,OAA+C,EAC/C;IACAP,IAAI,CAACQ,QAAQ,CAAC;MACZC,UAAUA,CAACT,IAAI,EAAE;QACf,MAAM;UAAEU,MAAM;AAAEC,UAAAA,GAAAA;AAAI,SAAC,GAAGX,IAAI,CAAA;QAC5B,IACG1D,mBAAmB,CAACoE,MAAM,CAAC,IAAIC,GAAG,KAAK,OAAO,IAC9CxE,gBAAgB,CAACuE,MAAM,CAAC,IAAIA,MAAM,CAACE,QAAQ,IAAID,GAAG,KAAK,KAAM,EAC9D;UACAX,IAAI,CAACa,IAAI,EAAE,CAAA;AACb,SAAA;OACD;AACDC,MAAAA,WAAW,EAAEP,OAAAA;AACf,KAAC,CAAC,CAAA;AACJ,GAAA;EAEA,SAASQ,SAASA,CAACpE,IAAwB,EAAW;AACpD,IAAA,KAAK,MAAMqE,IAAI,IAAIrE,IAAI,CAACM,UAAU,EAAE;AAClC,MAAA,IAAIR,UAAC,CAACwE,eAAe,CAACD,IAAI,CAAC,EAAE;AAC3B,QAAA,OAAO,IAAI,CAAA;AACb,OAAA;AACF,KAAA;AACA,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;EAQA,SAASE,qBAAqBA,CAACvE,IAAqB,EAAE;AAEpD,IAAA,MAAMwE,KAAK,GAAGxE,IAAI,CAACM,UAAgC,CAAA;IACnD,MAAMmE,IAAoB,GAAG,EAAE,CAAA;IAC/B,IAAIC,aAAa,GAAG,IAAI,CAAA;IACxB,IAAIC,kBAAkB,GAAG,KAAK,CAAA;AAE9B,IAAA,KAAK,MAAMN,IAAI,IAAIG,KAAK,EAAE;MACxB,MAAM;AAAER,QAAAA,GAAAA;AAAI,OAAC,GAAGK,IAAI,CAAA;MACpB,IAAIvE,UAAC,CAACD,YAAY,CAACmE,GAAG,CAAC,IAAI,CAACK,IAAI,CAACJ,QAAQ,EAAE;QAEzCQ,IAAI,CAACG,IAAI,CAAC9E,UAAC,CAAC+E,aAAa,CAACb,GAAG,CAACc,IAAI,CAAC,CAAC,CAAA;OACrC,MAAM,IAAIhF,UAAC,CAACiF,iBAAiB,CAACf,GAAG,CAAC,EAAE;QACnCS,IAAI,CAACG,IAAI,CAAC9E,UAAC,CAACkF,SAAS,CAAChB,GAAG,CAAC,CAAC,CAAA;AAC3BW,QAAAA,kBAAkB,GAAG,IAAI,CAAA;OAC1B,MAAM,IAAI7E,UAAC,CAACmF,SAAS,CAACjB,GAAG,CAAC,EAAE;AAC3BS,QAAAA,IAAI,CAACG,IAAI,CACP9E,UAAC,CAAC+E,aAAa,CACbK,MAAM,CAEJlB,GAAG,CAACxD,KACN,CACF,CACF,CAAC,CAAA;AACH,OAAC,MAAM;QAELiE,IAAI,CAACG,IAAI,CAAC9E,UAAC,CAACkF,SAAS,CAAChB,GAAG,CAAC,CAAC,CAAA;AAE3B,QAAA,IACGlE,UAAC,CAACqF,kBAAkB,CAACnB,GAAG,EAAE;AAAEC,UAAAA,QAAQ,EAAE,KAAA;SAAO,CAAC,IAC7CnE,UAAC,CAACD,YAAY,CAACmE,GAAG,CAACoB,MAAM,EAAE;AAAEN,UAAAA,IAAI,EAAE,QAAA;SAAU,CAAC,IAC/ChF,UAAC,CAACuF,gBAAgB,CAACrB,GAAG,CAAC,IACtBlE,UAAC,CAACwF,cAAc,CAACtB,GAAG,CAACuB,MAAM,EAAE,YAAY,CAAE,EAC7C,CAED,MAAM;AACLb,UAAAA,aAAa,GAAG,KAAK,CAAA;AACvB,SAAA;AACF,OAAA;AACF,KAAA;IAEA,OAAO;MAAED,IAAI;MAAEC,aAAa;AAAEC,MAAAA,kBAAAA;KAAoB,CAAA;AACpD,GAAA;AAIA,EAAA,SAASa,yBAAyBA,CAChClF,UAAwC,EACxCmF,KAAY,EACZ;IACA,MAAMC,iCAAyD,GAAG,EAAE,CAAA;AACpE,IAAA,KAAK,MAAMC,QAAQ,IAAIrF,UAAU,EAAE;AAEjC,MAAA,MAAM0D,GAAG,GAAG2B,QAAQ,CAACC,GAAG,CAAC,KAAK,CAA2B,CAAA;AACzD,MAAA,IAAID,QAAQ,CAAC3F,IAAI,CAACiE,QAAQ,IAAI,CAACD,GAAG,CAAC6B,MAAM,EAAE,EAAE;QAC3C,MAAMf,IAAI,GAAGW,KAAK,CAACK,sBAAsB,CAAC9B,GAAG,CAAChE,IAAI,CAAC,CAAA;AACnD,QAAA,MAAM+F,UAAU,GAAGjG,UAAC,CAACkG,kBAAkB,CAAClG,UAAC,CAACuB,UAAU,CAACyD,IAAI,CAAC,EAAEd,GAAG,CAAChE,IAAI,CAAC,CAAA;AACrE0F,QAAAA,iCAAiC,CAACd,IAAI,CAACmB,UAAU,CAAC,CAAA;QAClD/B,GAAG,CAACiC,WAAW,CAACnG,UAAC,CAACuB,UAAU,CAACyD,IAAI,CAAC,CAAC,CAAA;AACrC,OAAA;AACF,KAAA;AACA,IAAA,OAAOY,iCAAiC,CAAA;AAC1C,GAAA;EAEA,SAASQ,wBAAwBA,CAAC7C,IAA+B,EAAQ;AACvE,IAAA,MAAM8C,QAAQ,GAAG9C,IAAI,CAAC+C,8BAA8B,EAAE,CAAA;IAEtDC,MAAM,CAAC5B,IAAI,CAAC0B,QAAQ,CAAC,CAACG,OAAO,CAACC,WAAW,IAAI;AAC3C,MAAA,MAAMC,iBAAiB,GAAGL,QAAQ,CAACI,WAAW,CAAC,CAAC5C,UAAU,CAAA;AAC1D,MAAA,IACEN,IAAI,CAACoC,KAAK,CAACgB,UAAU,CAACF,WAAW,CAAC,CAACG,UAAU,GACPhF,SAAU,IAChD,CAAC8E,iBAAiB,CAAChH,gBAAgB,EAAE,EACrC;AACA,QAAA,OAAA;AACF,OAAA;MACAgH,iBAAiB,CAACG,MAAM,EAAE,CAAA;AAC5B,KAAC,CAAC,CAAA;AACJ,GAAA;AAGA,EAAA,SAASC,gBAAgBA,CACvBvD,IAA+B,EAC/BJ,IAAgB,EAChB4D,MAAyC,EACW;AACpD,IAAA,MAAMrC,KAAK,GAAGnB,IAAI,CAACuC,GAAG,CAAC,YAAY,CAAC,CAAA;IACpC,MAAMkB,IAAI,GAAGtC,KAAK,CAACA,KAAK,CAACnE,MAAM,GAAG,CAAC,CAAC,CAAA;AACpCP,IAAAA,UAAC,CAACiH,iBAAiB,CAACD,IAAI,CAAC9G,IAAI,CAAC,CAAA;IAC9B,MAAMwD,WAAW,GAAG1D,UAAC,CAACkF,SAAS,CAAC8B,IAAI,CAAC9G,IAAI,CAAC,CAAA;IAC1C8G,IAAI,CAACH,MAAM,EAAE,CAAA;AAEb,IAAA,MAAMjB,iCAAiC,GAAGF,yBAAyB,CACjEnC,IAAI,CAACuC,GAAG,CAAC,YAAY,CAAC,EACtBvC,IAAI,CAACoC,KACP,CAAC,CAAA;IACD,MAAM;MAAEhB,IAAI;MAAEC,aAAa;AAAEC,MAAAA,kBAAAA;AAAmB,KAAC,GAAGJ,qBAAqB,CACvElB,IAAI,CAACrD,IACP,CAAC,CAAA;AAED,IAAA,IAAIyE,IAAI,CAACpE,MAAM,KAAK,CAAC,EAAE;AACrB,MAAA,OAAO,CACLqF,iCAAiC,EACjClC,WAAW,CAAC9C,QAAQ,EACpBZ,UAAC,CAACkH,cAAc,CAAChE,gBAAgB,CAACC,IAAI,CAAC,EAAE,CACvCnD,UAAC,CAACmH,gBAAgB,CAAC,EAAE,CAAC,EACtBnH,UAAC,CAACoH,kBAAkB,CAAC,CACnBpH,UAAC,CAACkH,cAAc,CAAC/D,IAAI,CAACE,SAAS,CAAC,0BAA0B,CAAC,EAAE,CAC3DrD,UAAC,CAACkF,SAAS,CAAC6B,MAAM,CAAC,CACpB,CAAC,EACF/G,UAAC,CAACkF,SAAS,CAAC6B,MAAM,CAAC,CACpB,CAAC,CACH,CAAC,CACH,CAAA;AACH,KAAA;AAEA,IAAA,IAAIM,aAAa,CAAA;IACjB,IAAI,CAACzC,aAAa,EAAE;AAElByC,MAAAA,aAAa,GAAGrH,UAAC,CAACkH,cAAc,CAC9BlH,UAAC,CAACoD,gBAAgB,CAACpD,UAAC,CAACsH,eAAe,CAAC3C,IAAI,CAAC,EAAE3E,UAAC,CAACuB,UAAU,CAAC,KAAK,CAAC,CAAC,EAChE,CAAC4B,IAAI,CAACE,SAAS,CAAC,eAAe,CAAC,CAClC,CAAC,CAAA;AACH,KAAC,MAAM;AACLgE,MAAAA,aAAa,GAAGrH,UAAC,CAACsH,eAAe,CAAC3C,IAAI,CAAC,CAAA;AAEvC,MAAA,IAAI,CAACE,kBAAkB,IAAI,CAAC7E,UAAC,CAACuH,SAAS,CAAChE,IAAI,CAACoC,KAAK,CAAC6B,KAAK,CAAC,EAAE;AAEzD,QAAA,MAAMC,OAAO,GAAGlE,IAAI,CAACmE,UAAU,CAACnE,IAAI,IAAIA,IAAI,CAACgE,SAAS,EAAE,CAAC,CAAA;QACzD,MAAMI,EAAE,GAAGpE,IAAI,CAACoC,KAAK,CAACiC,qBAAqB,CAAC,UAAU,CAAC,CAAA;AAEvDH,QAAAA,OAAO,CAAC9B,KAAK,CAACb,IAAI,CAAC;UACjB6C,EAAE;AACFE,UAAAA,IAAI,EAAER,aAAa;AACnBS,UAAAA,IAAI,EAAE,OAAA;AACR,SAAC,CAAC,CAAA;AAEFT,QAAAA,aAAa,GAAGrH,UAAC,CAACkF,SAAS,CAACyC,EAAE,CAAC,CAAA;AACjC,OAAA;AACF,KAAA;AAEA,IAAA,OAAO,CACL/B,iCAAiC,EACjClC,WAAW,CAAC9C,QAAQ,EACpBZ,UAAC,CAACkH,cAAc,CACd/D,IAAI,CAACE,SAAS,CACX,CAAyBN,uBAAAA,EAAAA,mBAAmB,GAAG,OAAO,GAAG,EAAG,CAAA,CAC/D,CAAC,EACD,CAAC/C,UAAC,CAACkF,SAAS,CAAC6B,MAAM,CAAC,EAAEM,aAAa,CACrC,CAAC,CACF,CAAA;AACH,GAAA;AAEA,EAAA,SAASU,kBAAkBA,CACzBlE,UAAgD,EAChDmE,SAEC,EACDC,SAAmC,EAC7B;AACN,IAAA,IAAID,SAAS,CAACnI,mBAAmB,EAAE,EAAE;MACnCkI,kBAAkB,CAAClE,UAAU,EAAEmE,SAAS,CAAClC,GAAG,CAAC,MAAM,CAAC,EAAEmC,SAAS,CAAC,CAAA;AAChE,MAAA,OAAA;AACF,KAAA;IAEA,IAAID,SAAS,CAACrI,cAAc,EAAE,IAAI2D,cAAc,CAAC0E,SAAS,CAAC,EAAE;AAC3D,MAAA,MAAM5H,QAAQ,GAAG4H,SAAS,CAAClC,GAAG,CAAC,UAAU,CAAC,CAAA;AAE1C,MAAA,KAAK,IAAIoC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9H,QAAQ,CAACG,MAAM,EAAE2H,CAAC,EAAE,EAAE;QACxCH,kBAAkB,CAAClE,UAAU,EAAEzD,QAAQ,CAAC8H,CAAC,CAAC,EAAED,SAAS,CAAC,CAAA;AACxD,OAAA;AACF,KAAA;IAEA,IAAID,SAAS,CAACpI,eAAe,EAAE,IAAI0D,cAAc,CAAC0E,SAAS,CAAC,EAAE;MAC5D,MAAMG,GAAG,GAAGtE,UAAU,CAAC8B,KAAK,CAACiC,qBAAqB,CAAC,KAAK,CAAC,CAAA;MAEzD,MAAMQ,MAAM,GAAGpI,UAAC,CAACqI,mBAAmB,CAAC,KAAK,EAAE,CAC1CrI,UAAC,CAACkG,kBAAkB,CAAC8B,SAAS,CAAC9H,IAAI,EAAEiI,GAAG,CAAC,CAC1C,CAAC,CAAA;AAEF,MAAA,IAAIF,SAAS,EAAE;AACbA,QAAAA,SAAS,CAACnD,IAAI,CAACsD,MAAM,CAAC,CAAA;AACxB,OAAC,MAAM;QACLvE,UAAU,CAACyE,WAAW,EAAE,CAAA;QACxBzE,UAAU,CAACiC,GAAG,CAAC,MAAM,CAAC,CAACyC,gBAAgB,CAAC,MAAM,EAAEH,MAAM,CAAC,CAAA;AACzD,OAAA;MACAJ,SAAS,CAAC7B,WAAW,CAACnG,UAAC,CAACkF,SAAS,CAACiD,GAAG,CAAC,CAAC,CAAA;AACzC,KAAA;AACF,GAAA;EAEA,OAAO;AACLnD,IAAAA,IAAI,EAAE,8BAA8B;AACpCwD,IAAAA,QAAQ,EACsBzG,GAAG,CAAC0G,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,GAC9CC,SAAS,GAETC,OAAO,CAAC,yCAAyC,CAAC,CAACC,OAAO;AAEhE9E,IAAAA,OAAO,EAAE;MAEP+E,QAAQA,CAACtF,IAAI,EAAE;AACb,QAAA,MAAMuF,MAAM,GAAGvF,IAAI,CAACuC,GAAG,CAAC,QAAQ,CAAC,CAAA;AACjC,QAAA,MAAMiD,qBAAqB,GAAG,IAAIC,GAAG,EAAU,CAAA;AAC/C,QAAA,MAAMC,eAAe,GAAG,IAAID,GAAG,EAAE,CAAA;AACjC,QAAA,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,MAAM,CAACvI,MAAM,EAAE,EAAE2H,CAAC,EAAE;AACtC,UAAA,MAAMgB,KAAK,GAAGJ,MAAM,CAACZ,CAAC,CAAC,CAAA;AACvB,UAAA,IAAI5E,cAAc,CAAC4F,KAAK,CAAC,EAAE;AACzBH,YAAAA,qBAAqB,CAACI,GAAG,CAACjB,CAAC,CAAC,CAAA;AAC5B,YAAA,KAAK,MAAMlD,IAAI,IAAIuB,MAAM,CAAC5B,IAAI,CAACuE,KAAK,CAACE,qBAAqB,EAAE,CAAC,EAAE;AAC7DH,cAAAA,eAAe,CAACE,GAAG,CAACnE,IAAI,CAAC,CAAA;AAC3B,aAAA;AACF,WAAA;AACF,SAAA;QAKA,IAAIqE,QAAQ,GAAG,KAAK,CAAA;AAEpB,QAAA,MAAMC,iBAAiB,GAAG,UACxB/F,IAA4B,EAC5BgG,aAAoB,EACpB;AACA,UAAA,MAAMvE,IAAI,GAAGzB,IAAI,CAACrD,IAAI,CAAC8E,IAAI,CAAA;UAC3B,IACEzB,IAAI,CAACoC,KAAK,CAACgB,UAAU,CAAC3B,IAAI,CAAC,KAAKuE,aAAa,CAAC5C,UAAU,CAAC3B,IAAI,CAAC,IAC9DiE,eAAe,CAACO,GAAG,CAACxE,IAAI,CAAC,EACzB;AACAqE,YAAAA,QAAQ,GAAG,IAAI,CAAA;YACf9F,IAAI,CAACI,IAAI,EAAE,CAAA;AACb,WAAA;SACD,CAAA;AAED,QAAA,IAAIuE,CAAS,CAAA;AACb,QAAA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,MAAM,CAACvI,MAAM,IAAI,CAAC8I,QAAQ,EAAE,EAAEnB,CAAC,EAAE;AAC/C,UAAA,MAAMgB,KAAK,GAAGJ,MAAM,CAACZ,CAAC,CAAC,CAAA;AACvB,UAAA,IAAI,CAACa,qBAAqB,CAACS,GAAG,CAACtB,CAAC,CAAC,EAAE;YACjC,IAAIgB,KAAK,CAACO,sBAAsB,EAAE,IAAIP,KAAK,CAACQ,mBAAmB,EAAE,EAAE;AACjEJ,cAAAA,iBAAiB,CAACJ,KAAK,EAAE3F,IAAI,CAACoC,KAAK,CAAC,CAAA;AACtC,aAAC,MAAM;cACLuD,KAAK,CAACnF,QAAQ,CACZ;AACE,gBAAA,uCAAuC,EAAER,IAAI,IAAIA,IAAI,CAACa,IAAI,EAAE;AAC5D,gBAAA,wCAAwC,EAAEkF,iBAAAA;AAC5C,eAAC,EACD/F,IAAI,CAACoC,KACP,CAAC,CAAA;AACH,aAAA;AACF,WAAA;AACF,SAAA;QAEA,IAAI,CAAC0D,QAAQ,EAAE;AACb,UAAA,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,MAAM,CAACvI,MAAM,EAAE,EAAE2H,CAAC,EAAE;AACtC,YAAA,MAAMgB,KAAK,GAAGJ,MAAM,CAACZ,CAAC,CAAC,CAAA;AACvB,YAAA,IAAIa,qBAAqB,CAACS,GAAG,CAACtB,CAAC,CAAC,EAAE;AAChCH,cAAAA,kBAAkB,CAACxE,IAAI,EAAE2F,KAAK,CAAC,CAAA;AACjC,aAAA;AACF,WAAA;AACF,SAAC,MAAM;AACL,UAAA,MAAMS,oBAAoB,GAAIC,GAAW,IACvCA,GAAG,IAAI1B,CAAC,GAAG,CAAC,IAAIa,qBAAqB,CAACS,GAAG,CAACI,GAAG,CAAC,CAAA;UAChDC,+CAAqB,CACnBtG,IAAI,EACJV,oBAAoB,EACpB8G,oBAAoB,EACpB5B,kBACF,CAAC,CAAA;AACH,SAAA;OACD;AAID+B,MAAAA,kBAAkBA,CAACvG,IAAI,EAAEJ,IAAI,EAAE;QAC7B,IAAI,CAACI,IAAI,CAACuC,GAAG,CAAC,IAAI,CAAC,CAAClG,eAAe,EAAE,EAAE;AACrC,UAAA,OAAA;AACF,SAAA;QAEA,IAAImK,aAAa,GAAGxG,IAAI,CAAA;QACxB,MAAMyG,YAAY,GAAGzG,IAAI,CAAA;QAEzBE,iBAAiB,CAACF,IAAI,CAACuC,GAAG,CAAC,IAAI,CAAC,EAAEvC,IAAI,IAAI;UACxC,IAAI,CAACA,IAAI,CAACM,UAAU,CAACjE,eAAe,EAAE,EAAE;AAItC,YAAA,OAAA;AACF,WAAA;UAEA,IAIEK,iCAAiC,CAAC+J,YAAY,CAAC9J,IAAI,CAACyH,EAAE,CAAC,IACvD,CAAC3H,UAAC,CAACD,YAAY,CAACiK,YAAY,CAAC9J,IAAI,CAAC2H,IAAI,CAAC,EACvC;AAKA,YAAA,MAAMoC,OAAO,GAAG1G,IAAI,CAACoC,KAAK,CAACuE,gCAAgC,CACzDF,YAAY,CAAC9J,IAAI,CAAC2H,IAAI,EACtB,KACF,CAAC,CAAA;AAEDmC,YAAAA,YAAY,CAACG,YAAY,CACvBnK,UAAC,CAACkG,kBAAkB,CAAC+D,OAAO,EAAED,YAAY,CAAC9J,IAAI,CAAC2H,IAAI,CACtD,CAAC,CAAA;YAEDmC,YAAY,CAAC7D,WAAW,CACtBnG,UAAC,CAACkG,kBAAkB,CAAC8D,YAAY,CAAC9J,IAAI,CAACyH,EAAE,EAAE3H,UAAC,CAACkF,SAAS,CAAC+E,OAAO,CAAC,CACjE,CAAC,CAAA;AAED,YAAA,OAAA;AACF,WAAA;AAEA,UAAA,IAAIG,GAAG,GAAGJ,YAAY,CAAC9J,IAAI,CAAC2H,IAAI,CAAA;UAChC,MAAMwC,eAA6C,GAAG,EAAE,CAAA;AACxD,UAAA,IAAIvC,IAAI,CAAA;AAERvE,UAAAA,IAAI,CAACmE,UAAU,CAAEnE,IAAc,IAAc;AAC3C,YAAA,IAAIA,IAAI,CAAC7D,gBAAgB,EAAE,EAAE;AAC3B2K,cAAAA,eAAe,CAACC,OAAO,CAAC/G,IAAI,CAAC,CAAA;AAC/B,aAAC,MAAM,IAAIA,IAAI,CAACgH,oBAAoB,EAAE,EAAE;AACtCzC,cAAAA,IAAI,GAAGvE,IAAI,CAACM,UAAU,CAAC3D,IAAI,CAAC4H,IAAI,CAAA;AAChC,cAAA,OAAO,IAAI,CAAA;AACb,aAAA;AACF,WAAC,CAAC,CAAA;UAEF,MAAM0C,+BAA+B,GAAG9E,yBAAyB,CAC/D2E,eAAe,EACf9G,IAAI,CAACoC,KACP,CAAC,CAAA;AACD0E,UAAAA,eAAe,CAAC7D,OAAO,CAACjC,IAAI,IAAI;YAC9B,MAAM;AAAErE,cAAAA,IAAAA;AAAK,aAAC,GAAGqE,IAAI,CAAA;AACrB6F,YAAAA,GAAG,GAAGpK,UAAC,CAACoD,gBAAgB,CACtBgH,GAAG,EACHpK,UAAC,CAACkF,SAAS,CAAChF,IAAI,CAACgE,GAAG,CAAC,EACrBhE,IAAI,CAACiE,QAAQ,IAAInE,UAAC,CAACmF,SAAS,CAACjF,IAAI,CAACgE,GAAG,CACvC,CAAC,CAAA;AACH,WAAC,CAAC,CAAA;AAGF,UAAA,MAAMuG,iBAA4C,GAAGlH,IAAI,CAACmE,UAAU,CAClEnE,IAAI,IAAIA,IAAI,CAAC3D,eAAe,EAC9B,CAAC,CAAA;AAED,UAAA,MAAM,CAACgG,iCAAiC,EAAEhF,QAAQ,EAAEsG,cAAc,CAAC,GACjEJ,gBAAgB,CACd2D,iBAAiB,EACjBtH,IAAI,EACJiH,GACF,CAAC,CAAA;AAEH,UAAA,IAAIpH,WAAW,EAAE;YACfoD,wBAAwB,CAACqE,iBAAiB,CAAC,CAAA;AAC7C,WAAA;AAEAzK,UAAAA,UAAC,CAAC0K,gBAAgB,CAAC9J,QAAQ,CAAC,CAAA;AAE5BmJ,UAAAA,aAAa,CAACI,YAAY,CAACvE,iCAAiC,CAAC,CAAA;AAE7DmE,UAAAA,aAAa,CAACI,YAAY,CAACK,+BAA+B,CAAC,CAAA;AAE3DT,UAAAA,aAAa,GAAGA,aAAa,CAACY,WAAW,CACvC3K,UAAC,CAACkG,kBAAkB,CAACtF,QAAQ,EAAEsG,cAAc,CAC/C,CAAC,CAAC,CAAC,CAAmC,CAAA;UAEtC3D,IAAI,CAACoC,KAAK,CAACiF,eAAe,CAAC9C,IAAI,EAAEiC,aAAa,CAAC,CAAA;UAE/C,IAAIU,iBAAiB,CAACvK,IAAI,CAACM,UAAU,CAACD,MAAM,KAAK,CAAC,EAAE;YAClDkK,iBAAiB,CACd/C,UAAU,CACTnE,IAAI,IAAIA,IAAI,CAAC7D,gBAAgB,EAAE,IAAI6D,IAAI,CAACgH,oBAAoB,EAC9D,CAAC,CACA1D,MAAM,EAAE,CAAA;AACb,WAAA;AACF,SAAC,CAAC,CAAA;OACH;MAIDgE,sBAAsBA,CAACtH,IAAI,EAAE;AAC3B,QAAA,MAAMuH,WAAW,GAAGvH,IAAI,CAACuC,GAAG,CAAC,aAAa,CAAC,CAAA;AAC3C,QAAA,IAAI,CAACgF,WAAW,CAACC,qBAAqB,EAAE,EAAE,OAAA;QAE1C,MAAMC,OAAO,GAAGF,WAAW,CACxBhF,GAAG,CAAC,cAAc,CAAC,CACnBmF,IAAI,CAAC1H,IAAI,IAAIK,2BAA2B,CAACL,IAAI,CAACuC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QAC5D,IAAI,CAACkF,OAAO,EAAE,OAAA;QAEd,MAAME,UAAU,GAAG,EAAE,CAAA;AAErB,QAAA,KAAK,MAAMlG,IAAI,IAAIuB,MAAM,CAAC5B,IAAI,CAACpB,IAAI,CAAC4H,0BAA0B,CAAC,IAAI,CAAC,CAAC,EAAE;UACrED,UAAU,CAACpG,IAAI,CACb9E,UAAC,CAACoL,eAAe,CAACpL,UAAC,CAACuB,UAAU,CAACyD,IAAI,CAAC,EAAEhF,UAAC,CAACuB,UAAU,CAACyD,IAAI,CAAC,CAC1D,CAAC,CAAA;AACH,SAAA;AAKAzB,QAAAA,IAAI,CAAC4C,WAAW,CAAC2E,WAAW,CAAC5K,IAAI,CAAC,CAAA;QAClCqD,IAAI,CAACoH,WAAW,CAAC3K,UAAC,CAACqL,sBAAsB,CAAC,IAAI,EAAEH,UAAU,CAAC,CAAC,CAAA;OAC7D;MAGDI,WAAWA,CAAC/H,IAAI,EAAE;AAChB,QAAA,MAAMyE,SAAS,GAAGzE,IAAI,CAACuC,GAAG,CAAC,OAAO,CAAC,CAAA;AACnCiC,QAAAA,kBAAkB,CAACxE,IAAI,EAAEyE,SAAS,CAAC,CAAA;OACpC;AAGDuD,MAAAA,oBAAoBA,CAAChI,IAAI,EAAEJ,IAAI,EAAE;AAC/B,QAAA,MAAMqI,QAAQ,GAAGjI,IAAI,CAACuC,GAAG,CAAC,MAAM,CAAC,CAAA;QACjC,IAAI0F,QAAQ,CAAC5L,eAAe,EAAE,IAAI0D,cAAc,CAACkI,QAAQ,CAAC,EAAE;UAC1D,MAAMC,KAAK,GAAG,EAAE,CAAA;AAEhB,UAAA,MAAMC,OAAO,GAAGnI,IAAI,CAACoC,KAAK,CAACK,sBAAsB,CAC/CzC,IAAI,CAACrD,IAAI,CAACyL,KAAK,EACf,KACF,CAAC,CAAA;AAEDF,UAAAA,KAAK,CAAC3G,IAAI,CACR9E,UAAC,CAACqI,mBAAmB,CAAC,KAAK,EAAE,CAC3BrI,UAAC,CAACkG,kBAAkB,CAAClG,UAAC,CAACuB,UAAU,CAACmK,OAAO,CAAC,EAAEnI,IAAI,CAACrD,IAAI,CAACyL,KAAK,CAAC,CAC7D,CACH,CAAC,CAAA;UAED,MAAM,CAAC/F,iCAAiC,EAAEhF,QAAQ,EAAEsG,cAAc,CAAC,GACjEJ,gBAAgB,CAAC0E,QAAQ,EAAErI,IAAI,EAAEnD,UAAC,CAACuB,UAAU,CAACmK,OAAO,CAAC,CAAC,CAAA;AAEzD,UAAA,IAAI9F,iCAAiC,CAACrF,MAAM,GAAG,CAAC,EAAE;YAChDkL,KAAK,CAAC3G,IAAI,CACR9E,UAAC,CAACqI,mBAAmB,CAAC,KAAK,EAAEzC,iCAAiC,CAChE,CAAC,CAAA;AACH,WAAA;UAEA,MAAMgG,iBAAiB,GAAG5L,UAAC,CAACkF,SAAS,CAAC3B,IAAI,CAACrD,IAAI,CAAC,CAAA;UAChD0L,iBAAiB,CAACD,KAAK,GAAG3L,UAAC,CAACuB,UAAU,CAACmK,OAAO,CAAC,CAAA;UAC/CD,KAAK,CAAC3G,IAAI,CAAC9E,UAAC,CAAC6L,mBAAmB,CAACD,iBAAiB,CAAC,CAAC,CAAA;AACpDH,UAAAA,KAAK,CAAC3G,IAAI,CACR9E,UAAC,CAAC6L,mBAAmB,CACnB7L,UAAC,CAAC8L,oBAAoB,CAAC,GAAG,EAAElL,QAAQ,EAAEsG,cAAc,CACtD,CACF,CAAC,CAAA;AACDuE,UAAAA,KAAK,CAAC3G,IAAI,CAAC9E,UAAC,CAAC6L,mBAAmB,CAAC7L,UAAC,CAACuB,UAAU,CAACmK,OAAO,CAAC,CAAC,CAAC,CAAA;AAExDnI,UAAAA,IAAI,CAACwI,mBAAmB,CAACN,KAAK,CAAC,CAAA;AACjC,SAAA;OACD;MAGDO,aAAaA,CAACzI,IAA+B,EAAE;QAC7C,MAAM;UAAErD,IAAI;AAAEyF,UAAAA,KAAAA;AAAM,SAAC,GAAGpC,IAAI,CAAA;AAC5B,QAAA,MAAMiI,QAAQ,GAAGjI,IAAI,CAACuC,GAAG,CAAC,MAAM,CAAC,CAAA;AACjC,QAAA,MAAMnF,IAAI,GAAGT,IAAI,CAACS,IAAI,CAAA;AAEtB,QAAA,IAAI,CAACiD,2BAA2B,CAAC4H,QAAQ,CAAC,EAAE;AAC1C,UAAA,OAAA;AACF,SAAA;AAEA,QAAA,IAAI,CAACxL,UAAC,CAAC+K,qBAAqB,CAACpK,IAAI,CAAC,EAAE;AAElC,UAAA,MAAMsL,IAAI,GAAGtG,KAAK,CAACiC,qBAAqB,CAAC,KAAK,CAAC,CAAA;AAE/C1H,UAAAA,IAAI,CAACS,IAAI,GAAGX,UAAC,CAACqI,mBAAmB,CAAC,KAAK,EAAE,CACvCrI,UAAC,CAACkG,kBAAkB,CAAC+F,IAAI,CAAC,CAC3B,CAAC,CAAA;UAEF1I,IAAI,CAAC+E,WAAW,EAAE,CAAA;AAClB,UAAA,MAAM4D,IAAI,GAAG3I,IAAI,CAACrD,IAAI,CAACgM,IAAI,CAAA;AAE3B,UAAA,IAAIA,IAAI,CAACA,IAAI,CAAC3L,MAAM,KAAK,CAAC,IAAIgD,IAAI,CAAC4I,kBAAkB,EAAE,EAAE;AACvDD,YAAAA,IAAI,CAACA,IAAI,CAAC5B,OAAO,CACftK,UAAC,CAAC6L,mBAAmB,CAAClG,KAAK,CAACyG,kBAAkB,EAAE,CAClD,CAAC,CAAA;AACH,WAAA;UAEAF,IAAI,CAACA,IAAI,CAAC5B,OAAO,CACftK,UAAC,CAAC6L,mBAAmB,CACnB7L,UAAC,CAAC8L,oBAAoB,CAAC,GAAG,EAAEnL,IAAI,EAAEX,UAAC,CAACkF,SAAS,CAAC+G,IAAI,CAAC,CACrD,CACF,CAAC,CAAA;AACH,SAAC,MAAM;UAEL,MAAMvK,OAAO,GAAGf,IAAI,CAAC0L,YAAY,CAAC,CAAC,CAAC,CAAC1E,EAAE,CAAA;AAEvC,UAAA,MAAMzD,GAAG,GAAGyB,KAAK,CAACiC,qBAAqB,CAAC,KAAK,CAAC,CAAA;UAC9C1H,IAAI,CAACS,IAAI,GAAGX,UAAC,CAACqI,mBAAmB,CAAC1H,IAAI,CAACmH,IAAI,EAAE,CAC3C9H,UAAC,CAACkG,kBAAkB,CAAChC,GAAG,EAAE,IAAI,CAAC,CAChC,CAAC,CAAA;UAEFX,IAAI,CAAC+E,WAAW,EAAE,CAAA;AAClB,UAAA,MAAM4D,IAAI,GAAGhM,IAAI,CAACgM,IAAwB,CAAA;AAE1CA,UAAAA,IAAI,CAACA,IAAI,CAAC5B,OAAO,CACftK,UAAC,CAACqI,mBAAmB,CAACnI,IAAI,CAACS,IAAI,CAACmH,IAAI,EAAE,CACpC9H,UAAC,CAACkG,kBAAkB,CAACxE,OAAO,EAAE1B,UAAC,CAACkF,SAAS,CAAChB,GAAG,CAAC,CAAC,CAChD,CACH,CAAC,CAAA;AACH,SAAA;OACD;MAGDoI,YAAYA,CAAC/I,IAAI,EAAE;QACjB,MAAMgJ,cAAsC,GAAG,EAAE,CAAA;AAEjD9I,QAAAA,iBAAiB,CAACF,IAAI,EAAEA,IAAI,IAAI;UAC9B,IAAI,CAACA,IAAI,CAACM,UAAU,CAACjE,eAAe,EAAE,EAAE;AAItC,YAAA,OAAA;AACF,WAAA;AAEA,UAAA,MAAM+B,aAAa,GAAG4B,IAAI,CAACM,UAAU,CAAA;UAErC,MAAMsE,GAAG,GAAG5E,IAAI,CAACoC,KAAK,CAACiC,qBAAqB,CAAC,KAAK,CAAC,CAAA;AACnD2E,UAAAA,cAAc,CAACzH,IAAI,CAAC9E,UAAC,CAACkG,kBAAkB,CAACvE,aAAa,CAACzB,IAAI,EAAEiI,GAAG,CAAC,CAAC,CAAA;UAElExG,aAAa,CAACwE,WAAW,CAACnG,UAAC,CAACkF,SAAS,CAACiD,GAAG,CAAC,CAAC,CAAA;UAC3C5E,IAAI,CAACa,IAAI,EAAE,CAAA;AACb,SAAC,CAAC,CAAA;AAEF,QAAA,IAAImI,cAAc,CAAChM,MAAM,GAAG,CAAC,EAAE;AAC7B,UAAA,MAAMiM,aAAa,GAAGjJ,IAAI,CAACkJ,kBAAkB,EAAE,CAAA;AAC/C,UAAA,MAAMC,aAAa,GAAGF,aAAa,CAACtM,IAAI,CAAA;AACxC,UAAA,MAAM4H,IAAI,GACR4E,aAAa,CAACC,IAAI,KAAK,qBAAqB,GACxCD,aAAa,CAAC5E,IAAI,GAClB,KAAK,CAAA;UACX0E,aAAa,CAAC7B,WAAW,CACvB3K,UAAC,CAACqI,mBAAmB,CAACP,IAAI,EAAEyE,cAAc,CAC5C,CAAC,CAAA;AACH,SAAA;OACD;AAGDK,MAAAA,gBAAgBA,CAACrJ,IAAI,EAAEJ,IAAI,EAAE;AAC3B,QAAA,IAAI,CAACmB,SAAS,CAACf,IAAI,CAACrD,IAAI,CAAC,EAAE,OAAA;AAE3B,QAAA,IAAI2M,MAAyC,CAAA;AAC7C,QAAA,IAAI5J,mBAAmB,EAAE;AACvB4J,UAAAA,MAAM,GAAG3J,gBAAgB,CAACC,IAAI,CAAC,CAAA;AACjC,SAAC,MAAM;AAGE,UAAA;YACL,IAAI;AACF0J,cAAAA,MAAM,GAAG1J,IAAI,CAACE,SAAS,CAAC,eAAe,CAAC,CAAA;aACzC,CAAC,OAAAyJ,OAAA,EAAM;cAIN,IAAI,CAAC3J,IAAI,CAACkJ,YAAY,CAAC,eAAe,CAAC,GAAG,IAAI,CAAA;AAI9CQ,cAAAA,MAAM,GAAG1J,IAAI,CAACE,SAAS,CAAC,cAAc,CAAC,CAAA;AACzC,aAAA;AACF,WAAA;AACF,SAAA;QAEA,IAAI0J,GAAqB,GAAG,IAAI,CAAA;QAChC,IAAIrI,KAAuB,GAAG,EAAE,CAAA;QAEhC,SAASsI,IAAIA,GAAG;AACd,UAAA,MAAMC,QAAQ,GAAGvI,KAAK,CAACnE,MAAM,GAAG,CAAC,CAAA;AACjC,UAAA,MAAM2M,GAAG,GAAGlN,UAAC,CAACmH,gBAAgB,CAACzC,KAAK,CAAC,CAAA;AACrCA,UAAAA,KAAK,GAAG,EAAE,CAAA;UAEV,IAAI,CAACqI,GAAG,EAAE;YACRA,GAAG,GAAG/M,UAAC,CAACkH,cAAc,CAAC2F,MAAM,EAAE,CAACK,GAAG,CAAC,CAAC,CAAA;AACrC,YAAA,OAAA;AACF,WAAA;AAIA,UAAA,IAAIlK,WAAW,EAAE;AACf,YAAA,IAAIiK,QAAQ,EAAE;AACZF,cAAAA,GAAG,CAACI,SAAS,CAACrI,IAAI,CAACoI,GAAG,CAAC,CAAA;AACzB,aAAA;AACA,YAAA,OAAA;AACF,WAAA;AAEAH,UAAAA,GAAG,GAAG/M,UAAC,CAACkH,cAAc,CAAClH,UAAC,CAACkF,SAAS,CAAC2H,MAAM,CAAC,EAAE,CAC1CE,GAAG,EAIH,IAAIE,QAAQ,GAAG,CAACjN,UAAC,CAACmH,gBAAgB,CAAC,EAAE,CAAC,EAAE+F,GAAG,CAAC,GAAG,EAAE,EAClD,CAAC,CAAA;AACJ,SAAA;QAEA,KAAK,MAAM3I,IAAI,IAAIhB,IAAI,CAACrD,IAAI,CAACM,UAAU,EAAE;AACvC,UAAA,IAAIR,UAAC,CAACwE,eAAe,CAACD,IAAI,CAAC,EAAE;AAC3ByI,YAAAA,IAAI,EAAE,CAAA;YACND,GAAG,CAACI,SAAS,CAACrI,IAAI,CAACP,IAAI,CAAC3D,QAAQ,CAAC,CAAA;AACnC,WAAC,MAAM;AACL8D,YAAAA,KAAK,CAACI,IAAI,CAACP,IAAI,CAAC,CAAA;AAClB,WAAA;AACF,SAAA;AAEA,QAAA,IAAIG,KAAK,CAACnE,MAAM,EAAEyM,IAAI,EAAE,CAAA;AAExBzJ,QAAAA,IAAI,CAAC4C,WAAW,CAAC4G,GAAG,CAAC,CAAA;AACvB,OAAA;AACF,KAAA;GACD,CAAA;AACH,CAAC,CAAC;;;;"}
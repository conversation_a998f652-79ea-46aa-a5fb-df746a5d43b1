{"version": 3, "names": ["_pluginSyntaxJsx", "require", "_helper<PERSON>lugin<PERSON><PERSON>s", "_core", "_helperModuleImports", "_helperAnnotateAsPure", "DEFAULT", "importSource", "runtime", "pragma", "pragmaFrag", "JSX_SOURCE_ANNOTATION_REGEX", "JSX_RUNTIME_ANNOTATION_REGEX", "JSX_ANNOTATION_REGEX", "JSX_FRAG_ANNOTATION_REGEX", "get", "pass", "name", "set", "v", "hasProto", "node", "properties", "some", "value", "t", "isObjectProperty", "computed", "shorthand", "isIdentifier", "key", "isStringLiteral", "createPlugin", "development", "declare", "_", "options", "pure", "PURE_ANNOTATION", "throwIfNamespace", "filter", "RUNTIME_DEFAULT", "IMPORT_SOURCE_DEFAULT", "PRAGMA_DEFAULT", "PRAGMA_FRAG_DEFAULT", "useSpread", "useBuiltIns", "Error", "injectMetaPropertiesVisitor", "JSXOpeningElement", "path", "state", "attributes", "isThisAllowed", "scope", "push", "jsxAttribute", "jsxIdentifier", "jsxExpressionContainer", "thisExpression", "makeSource", "pushContainer", "inherits", "jsx", "visitor", "JSXNamespacedName", "buildCodeFrameError", "JSXSpreadChild", "Program", "enter", "file", "source", "sourceSet", "pragmaSet", "pragmaFragSet", "ast", "comments", "comment", "sourceMatches", "exec", "runtimeMatches", "jsxMatches", "jsxFragMatches", "createElement", "toMemberExpression", "fragment", "cloneNode", "define", "id", "createImportLazily", "traverse", "JSXFragment", "exit", "callExpr", "buildCreateElementFragmentCall", "buildJSXFragmentCall", "replaceWith", "JSXElement", "shouldUseCreateElement", "buildCreateElementCall", "buildJSXElementCall", "JSXAttribute", "isJSXElement", "isDerivedClass", "classPath", "superClass", "isFunctionParent", "isArrowFunctionExpression", "isMethod", "kind", "parentPath", "isTSModuleBlock", "parent", "call", "args", "callExpression", "annotateAsPure", "openingPath", "seenPropsSpread", "i", "length", "attr", "isJSXAttribute", "isJSXSpreadAttribute", "convertJSXIdentifier", "isJSXIdentifier", "isReferenced", "isValidIdentifier", "type", "stringLiteral", "isJSXMemberExpression", "memberExpression", "object", "property", "isJSXNamespacedName", "namespace", "convertAttributeValue", "isJSXExpressionContainer", "expression", "accumulateAttribute", "array", "attribute", "arg", "argument", "isObjectExpression", "spreadElement", "booleanLiteral", "_value$extra", "replace", "extra", "raw", "objectProperty", "buildChildrenProperty", "children", "childrenNode", "arrayExpression", "undefined", "identifier", "getTag", "attribsArray", "extracted", "Object", "create", "sourceSelfError", "keyValue", "react", "buildChildren", "attribs", "buildJSXOpeningElementAttributes", "objectExpression", "_extracted$key", "buildUndefinedNode", "__source", "__self", "props", "reduce", "nullLiteral", "buildCreateElementOpeningElementAttributes", "tagExpr", "tagName", "isCompatTag", "objs", "start", "for<PERSON>ach", "prop", "isSpreadElement", "slice", "unshift", "helper", "addHelper", "found", "getSource", "importName", "actualSource", "isModule", "reference", "addNamed", "importedInterop", "importPosition", "addNamespace", "split", "map", "location", "loc", "fileNameIdentifier", "filename", "generateUidIdentifier", "getProgramParent", "init", "makeTrace", "line", "column", "lineNumber", "column0Based", "fileLineLiteral", "numericLiteral", "fileColumnLiteral", "template", "pluginName"], "sources": ["../src/create-plugin.ts"], "sourcesContent": ["import jsx from \"@babel/plugin-syntax-jsx\";\nimport { declare } from \"@babel/helper-plugin-utils\";\nimport { template, types as t } from \"@babel/core\";\nimport type { PluginPass } from \"@babel/core\";\nimport type { <PERSON>de<PERSON><PERSON>, Scope, Visitor } from \"@babel/traverse\";\nimport { addNamed, addNamespace, isModule } from \"@babel/helper-module-imports\";\nimport annotateAsPure from \"@babel/helper-annotate-as-pure\";\nimport type {\n  CallExpression,\n  Class,\n  Expression,\n  Identifier,\n  JSXAttribute,\n  JSXElement,\n  JSXFragment,\n  JSXOpeningElement,\n  JSXSpreadAttribute,\n  MemberExpression,\n  ObjectExpression,\n  Program,\n} from \"@babel/types\";\n\nconst DEFAULT = {\n  importSource: \"react\",\n  runtime: \"automatic\",\n  pragma: \"React.createElement\",\n  pragmaFrag: \"React.Fragment\",\n};\n\nconst JSX_SOURCE_ANNOTATION_REGEX =\n  /^\\s*\\*?\\s*@jsxImportSource\\s+([^\\s]+)\\s*$/m;\nconst JSX_RUNTIME_ANNOTATION_REGEX = /^\\s*\\*?\\s*@jsxRuntime\\s+([^\\s]+)\\s*$/m;\n\nconst JSX_ANNOTATION_REGEX = /^\\s*\\*?\\s*@jsx\\s+([^\\s]+)\\s*$/m;\nconst JSX_FRAG_ANNOTATION_REGEX = /^\\s*\\*?\\s*@jsxFrag\\s+([^\\s]+)\\s*$/m;\n\nconst get = (pass: PluginPass, name: string) =>\n  pass.get(`@babel/plugin-react-jsx/${name}`);\nconst set = (pass: PluginPass, name: string, v: any) =>\n  pass.set(`@babel/plugin-react-jsx/${name}`, v);\n\nfunction hasProto(node: t.ObjectExpression) {\n  return node.properties.some(\n    value =>\n      t.isObjectProperty(value, { computed: false, shorthand: false }) &&\n      (t.isIdentifier(value.key, { name: \"__proto__\" }) ||\n        t.isStringLiteral(value.key, { value: \"__proto__\" })),\n  );\n}\n\nexport interface Options {\n  filter?: (node: t.Node, pass: PluginPass) => boolean;\n  importSource?: string;\n  pragma?: string;\n  pragmaFrag?: string;\n  pure?: string;\n  runtime?: \"automatic\" | \"classic\";\n  throwIfNamespace?: boolean;\n  useBuiltIns: boolean;\n  useSpread?: boolean;\n}\nexport default function createPlugin({\n  name,\n  development,\n}: {\n  name: string;\n  development: boolean;\n}) {\n  return declare((_, options: Options) => {\n    const {\n      pure: PURE_ANNOTATION,\n\n      throwIfNamespace = true,\n\n      filter,\n\n      runtime: RUNTIME_DEFAULT = process.env.BABEL_8_BREAKING\n        ? \"automatic\"\n        : development\n          ? \"automatic\"\n          : \"classic\",\n\n      importSource: IMPORT_SOURCE_DEFAULT = DEFAULT.importSource,\n      pragma: PRAGMA_DEFAULT = DEFAULT.pragma,\n      pragmaFrag: PRAGMA_FRAG_DEFAULT = DEFAULT.pragmaFrag,\n    } = options;\n\n    if (process.env.BABEL_8_BREAKING) {\n      if (\"useSpread\" in options) {\n        throw new Error(\n          '@babel/plugin-transform-react-jsx: Since Babel 8, an inline object with spread elements is always used, and the \"useSpread\" option is no longer available. Please remove it from your config.',\n        );\n      }\n\n      if (\"useBuiltIns\" in options) {\n        const useBuiltInsFormatted = JSON.stringify(options.useBuiltIns);\n        throw new Error(\n          `@babel/plugin-transform-react-jsx: Since \"useBuiltIns\" is removed in Babel 8, you can remove it from the config.\n- Babel 8 now transforms JSX spread to object spread. If you need to transpile object spread with\n\\`useBuiltIns: ${useBuiltInsFormatted}\\`, you can use the following config\n{\n  \"plugins\": [\n    \"@babel/plugin-transform-react-jsx\"\n    [\"@babel/plugin-transform-object-rest-spread\", { \"loose\": true, \"useBuiltIns\": ${useBuiltInsFormatted} }]\n  ]\n}`,\n        );\n      }\n\n      if (filter != null && RUNTIME_DEFAULT === \"automatic\") {\n        throw new Error(\n          '@babel/plugin-transform-react-jsx: \"filter\" option can not be used with automatic runtime. If you are upgrading from Babel 7, please specify `runtime: \"classic\"`.',\n        );\n      }\n    } else {\n      // eslint-disable-next-line no-var\n      var { useSpread = false, useBuiltIns = false } = options;\n\n      if (RUNTIME_DEFAULT === \"classic\") {\n        if (typeof useSpread !== \"boolean\") {\n          throw new Error(\n            \"transform-react-jsx currently only accepts a boolean option for \" +\n              \"useSpread (defaults to false)\",\n          );\n        }\n\n        if (typeof useBuiltIns !== \"boolean\") {\n          throw new Error(\n            \"transform-react-jsx currently only accepts a boolean option for \" +\n              \"useBuiltIns (defaults to false)\",\n          );\n        }\n\n        if (useSpread && useBuiltIns) {\n          throw new Error(\n            \"transform-react-jsx currently only accepts useBuiltIns or useSpread \" +\n              \"but not both\",\n          );\n        }\n      }\n    }\n\n    const injectMetaPropertiesVisitor: Visitor<PluginPass> = {\n      JSXOpeningElement(path, state) {\n        const attributes = [];\n        if (isThisAllowed(path.scope)) {\n          attributes.push(\n            t.jsxAttribute(\n              t.jsxIdentifier(\"__self\"),\n              t.jsxExpressionContainer(t.thisExpression()),\n            ),\n          );\n        }\n        attributes.push(\n          t.jsxAttribute(\n            t.jsxIdentifier(\"__source\"),\n            t.jsxExpressionContainer(makeSource(path, state)),\n          ),\n        );\n        path.pushContainer(\"attributes\", attributes);\n      },\n    };\n\n    return {\n      name,\n      inherits: jsx,\n      visitor: {\n        JSXNamespacedName(path) {\n          if (throwIfNamespace) {\n            throw path.buildCodeFrameError(\n              `Namespace tags are not supported by default. React's JSX doesn't support namespace tags. \\\nYou can set \\`throwIfNamespace: false\\` to bypass this warning.`,\n            );\n          }\n        },\n\n        JSXSpreadChild(path) {\n          throw path.buildCodeFrameError(\n            \"Spread children are not supported in React.\",\n          );\n        },\n\n        Program: {\n          enter(path, state) {\n            const { file } = state;\n            let runtime: string = RUNTIME_DEFAULT;\n\n            let source: string = IMPORT_SOURCE_DEFAULT;\n            let pragma: string = PRAGMA_DEFAULT;\n            let pragmaFrag: string = PRAGMA_FRAG_DEFAULT;\n\n            let sourceSet = !!options.importSource;\n            let pragmaSet = !!options.pragma;\n            let pragmaFragSet = !!options.pragmaFrag;\n\n            if (file.ast.comments) {\n              for (const comment of file.ast.comments) {\n                const sourceMatches = JSX_SOURCE_ANNOTATION_REGEX.exec(\n                  comment.value,\n                );\n                if (sourceMatches) {\n                  source = sourceMatches[1];\n                  sourceSet = true;\n                }\n\n                const runtimeMatches = JSX_RUNTIME_ANNOTATION_REGEX.exec(\n                  comment.value,\n                );\n                if (runtimeMatches) {\n                  runtime = runtimeMatches[1];\n                }\n\n                const jsxMatches = JSX_ANNOTATION_REGEX.exec(comment.value);\n                if (jsxMatches) {\n                  pragma = jsxMatches[1];\n                  pragmaSet = true;\n                }\n                const jsxFragMatches = JSX_FRAG_ANNOTATION_REGEX.exec(\n                  comment.value,\n                );\n                if (jsxFragMatches) {\n                  pragmaFrag = jsxFragMatches[1];\n                  pragmaFragSet = true;\n                }\n              }\n            }\n\n            set(state, \"runtime\", runtime);\n            if (runtime === \"classic\") {\n              if (sourceSet) {\n                throw path.buildCodeFrameError(\n                  `importSource cannot be set when runtime is classic.`,\n                );\n              }\n\n              const createElement = toMemberExpression(pragma);\n              const fragment = toMemberExpression(pragmaFrag);\n\n              set(state, \"id/createElement\", () => t.cloneNode(createElement));\n              set(state, \"id/fragment\", () => t.cloneNode(fragment));\n\n              set(state, \"defaultPure\", pragma === DEFAULT.pragma);\n            } else if (runtime === \"automatic\") {\n              if (pragmaSet || pragmaFragSet) {\n                throw path.buildCodeFrameError(\n                  `pragma and pragmaFrag cannot be set when runtime is automatic.`,\n                );\n              }\n\n              const define = (name: string, id: string) =>\n                set(state, name, createImportLazily(state, path, id, source));\n\n              define(\"id/jsx\", development ? \"jsxDEV\" : \"jsx\");\n              define(\"id/jsxs\", development ? \"jsxDEV\" : \"jsxs\");\n              define(\"id/createElement\", \"createElement\");\n              define(\"id/fragment\", \"Fragment\");\n\n              set(state, \"defaultPure\", source === DEFAULT.importSource);\n            } else {\n              throw path.buildCodeFrameError(\n                `Runtime must be either \"classic\" or \"automatic\".`,\n              );\n            }\n\n            if (development) {\n              path.traverse(injectMetaPropertiesVisitor, state);\n            }\n          },\n\n          // TODO(Babel 8): Decide if this should be removed or brought back.\n          // see: https://github.com/babel/babel/pull/12253#discussion_r513086528\n          //\n          // exit(path, state) {\n          //   if (\n          //     get(state, \"runtime\") === \"classic\" &&\n          //     get(state, \"pragmaSet\") &&\n          //     get(state, \"usedFragment\") &&\n          //     !get(state, \"pragmaFragSet\")\n          //   ) {\n          //     throw new Error(\n          //       \"transform-react-jsx: pragma has been set but \" +\n          //         \"pragmaFrag has not been set\",\n          //     );\n          //   }\n          // },\n        },\n\n        JSXFragment: {\n          exit(path, file) {\n            let callExpr;\n            if (get(file, \"runtime\") === \"classic\") {\n              callExpr = buildCreateElementFragmentCall(path, file);\n            } else {\n              callExpr = buildJSXFragmentCall(path, file);\n            }\n\n            path.replaceWith(t.inherits(callExpr, path.node));\n          },\n        },\n\n        JSXElement: {\n          exit(path, file) {\n            let callExpr;\n            if (\n              get(file, \"runtime\") === \"classic\" ||\n              shouldUseCreateElement(path)\n            ) {\n              callExpr = buildCreateElementCall(path, file);\n            } else {\n              callExpr = buildJSXElementCall(path, file);\n            }\n\n            path.replaceWith(t.inherits(callExpr, path.node));\n          },\n        },\n\n        JSXAttribute(path) {\n          if (t.isJSXElement(path.node.value)) {\n            path.node.value = t.jsxExpressionContainer(path.node.value);\n          }\n        },\n      },\n    };\n\n    // Returns whether the class has specified a superclass.\n    function isDerivedClass(classPath: NodePath<Class>) {\n      return classPath.node.superClass !== null;\n    }\n\n    // Returns whether `this` is allowed at given scope.\n    function isThisAllowed(scope: Scope) {\n      // This specifically skips arrow functions as they do not rewrite `this`.\n      do {\n        const { path } = scope;\n        if (path.isFunctionParent() && !path.isArrowFunctionExpression()) {\n          if (!path.isMethod()) {\n            // If the closest parent is a regular function, `this` will be rebound, therefore it is fine to use `this`.\n            return true;\n          }\n          // Current node is within a method, so we need to check if the method is a constructor.\n          if (path.node.kind !== \"constructor\") {\n            // We are not in a constructor, therefore it is always fine to use `this`.\n            return true;\n          }\n          // Now we are in a constructor. If it is a derived class, we do not reference `this`.\n          return !isDerivedClass(path.parentPath.parentPath as NodePath<Class>);\n        }\n        if (path.isTSModuleBlock()) {\n          // If the closest parent is a TS Module block, `this` will not be allowed.\n          return false;\n        }\n      } while ((scope = scope.parent));\n      // We are not in a method or function. It is fine to use `this`.\n      return true;\n    }\n\n    function call(\n      pass: PluginPass,\n      name: string,\n      args: CallExpression[\"arguments\"],\n    ) {\n      const node = t.callExpression(get(pass, `id/${name}`)(), args);\n      if (PURE_ANNOTATION ?? get(pass, \"defaultPure\")) annotateAsPure(node);\n      return node;\n    }\n\n    // We want to use React.createElement, even in the case of\n    // jsx, for <div {...props} key={key} /> to distinguish it\n    // from <div key={key} {...props} />. This is an intermediary\n    // step while we deprecate key spread from props. Afterwards,\n    // we will stop using createElement in the transform.\n    function shouldUseCreateElement(path: NodePath<JSXElement>) {\n      const openingPath = path.get(\"openingElement\");\n      const attributes = openingPath.node.attributes;\n\n      let seenPropsSpread = false;\n      for (let i = 0; i < attributes.length; i++) {\n        const attr = attributes[i];\n        if (\n          seenPropsSpread &&\n          t.isJSXAttribute(attr) &&\n          attr.name.name === \"key\"\n        ) {\n          return true;\n        } else if (t.isJSXSpreadAttribute(attr)) {\n          seenPropsSpread = true;\n        }\n      }\n      return false;\n    }\n\n    function convertJSXIdentifier(\n      node: t.JSXIdentifier | t.JSXMemberExpression | t.JSXNamespacedName,\n      parent: t.JSXOpeningElement | t.JSXMemberExpression,\n    ): t.ThisExpression | t.StringLiteral | t.MemberExpression | t.Identifier {\n      if (t.isJSXIdentifier(node)) {\n        if (node.name === \"this\" && t.isReferenced(node, parent)) {\n          return t.thisExpression();\n        } else if (t.isValidIdentifier(node.name, false)) {\n          // @ts-expect-error cast AST type to Identifier\n          node.type = \"Identifier\";\n          return node as unknown as t.Identifier;\n        } else {\n          return t.stringLiteral(node.name);\n        }\n      } else if (t.isJSXMemberExpression(node)) {\n        return t.memberExpression(\n          convertJSXIdentifier(node.object, node),\n          convertJSXIdentifier(node.property, node),\n        );\n      } else if (t.isJSXNamespacedName(node)) {\n        /**\n         * If the flag \"throwIfNamespace\" is false\n         * print XMLNamespace like string literal\n         */\n        return t.stringLiteral(`${node.namespace.name}:${node.name.name}`);\n      }\n\n      // todo: this branch should be unreachable\n      return node;\n    }\n\n    function convertAttributeValue(\n      node: t.JSXAttribute[\"value\"] | t.BooleanLiteral,\n    ) {\n      if (t.isJSXExpressionContainer(node)) {\n        return node.expression;\n      } else {\n        return node;\n      }\n    }\n\n    function accumulateAttribute(\n      array: ObjectExpression[\"properties\"],\n      attribute: NodePath<JSXAttribute | JSXSpreadAttribute>,\n    ) {\n      if (t.isJSXSpreadAttribute(attribute.node)) {\n        const arg = attribute.node.argument;\n        // Collect properties into props array if spreading object expression\n        if (t.isObjectExpression(arg) && !hasProto(arg)) {\n          array.push(...arg.properties);\n        } else {\n          array.push(t.spreadElement(arg));\n        }\n        return array;\n      }\n\n      const value = convertAttributeValue(\n        attribute.node.name.name !== \"key\"\n          ? attribute.node.value || t.booleanLiteral(true)\n          : attribute.node.value,\n      );\n\n      if (attribute.node.name.name === \"key\" && value === null) {\n        throw attribute.buildCodeFrameError(\n          'Please provide an explicit key value. Using \"key\" as a shorthand for \"key={true}\" is not allowed.',\n        );\n      }\n\n      if (\n        t.isStringLiteral(value) &&\n        !t.isJSXExpressionContainer(attribute.node.value)\n      ) {\n        value.value = value.value.replace(/\\n\\s+/g, \" \");\n\n        // \"raw\" JSXText should not be used from a StringLiteral because it needs to be escaped.\n        delete value.extra?.raw;\n      }\n\n      if (t.isJSXNamespacedName(attribute.node.name)) {\n        // @ts-expect-error mutating AST\n        attribute.node.name = t.stringLiteral(\n          attribute.node.name.namespace.name +\n            \":\" +\n            attribute.node.name.name.name,\n        );\n      } else if (t.isValidIdentifier(attribute.node.name.name, false)) {\n        // @ts-expect-error mutating AST\n        attribute.node.name.type = \"Identifier\";\n      } else {\n        // @ts-expect-error mutating AST\n        attribute.node.name = t.stringLiteral(attribute.node.name.name);\n      }\n\n      array.push(\n        t.inherits(\n          t.objectProperty(\n            // @ts-expect-error The attribute.node.name is an Identifier now\n            attribute.node.name,\n            value,\n          ),\n          attribute.node,\n        ),\n      );\n      return array;\n    }\n\n    function buildChildrenProperty(children: Expression[]) {\n      let childrenNode;\n      if (children.length === 1) {\n        childrenNode = children[0];\n      } else if (children.length > 1) {\n        childrenNode = t.arrayExpression(children);\n      } else {\n        return undefined;\n      }\n\n      return t.objectProperty(t.identifier(\"children\"), childrenNode);\n    }\n\n    // Builds JSX into:\n    // Production: React.jsx(type, arguments, key)\n    // Development: React.jsxDEV(type, arguments, key, isStaticChildren, source, self)\n    function buildJSXElementCall(path: NodePath<JSXElement>, file: PluginPass) {\n      const openingPath = path.get(\"openingElement\");\n      const args: t.Expression[] = [getTag(openingPath)];\n\n      const attribsArray = [];\n      const extracted = Object.create(null);\n\n      // for React.jsx, key, __source (dev), and __self (dev) is passed in as\n      // a separate argument rather than in the args object. We go through the\n      // props and filter out these three keywords so we can pass them in\n      // as separate arguments later\n      for (const attr of openingPath.get(\"attributes\")) {\n        if (attr.isJSXAttribute() && t.isJSXIdentifier(attr.node.name)) {\n          const { name } = attr.node.name;\n          switch (name) {\n            case \"__source\":\n            case \"__self\":\n              if (extracted[name]) throw sourceSelfError(path, name);\n            /* falls through */\n            case \"key\": {\n              const keyValue = convertAttributeValue(attr.node.value);\n              if (keyValue === null) {\n                throw attr.buildCodeFrameError(\n                  'Please provide an explicit key value. Using \"key\" as a shorthand for \"key={true}\" is not allowed.',\n                );\n              }\n\n              extracted[name] = keyValue;\n              break;\n            }\n            default:\n              attribsArray.push(attr);\n          }\n        } else {\n          attribsArray.push(attr);\n        }\n      }\n\n      const children = t.react.buildChildren(path.node);\n\n      let attribs: t.ObjectExpression;\n\n      if (attribsArray.length || children.length) {\n        attribs = buildJSXOpeningElementAttributes(\n          attribsArray,\n          //@ts-expect-error The children here contains JSXSpreadChild,\n          // which will be thrown later\n          children,\n        );\n      } else {\n        // attributes should never be null\n        attribs = t.objectExpression([]);\n      }\n\n      args.push(attribs);\n\n      if (development) {\n        // isStaticChildren, __source, and __self are only used in development\n        // automatically include __source and __self in this plugin\n        // so we can eliminate the need for separate Babel plugins in Babel 8\n        args.push(\n          extracted.key ?? path.scope.buildUndefinedNode(),\n          t.booleanLiteral(children.length > 1),\n        );\n        if (extracted.__source) {\n          args.push(extracted.__source);\n          if (extracted.__self) args.push(extracted.__self);\n        } else if (extracted.__self) {\n          args.push(path.scope.buildUndefinedNode(), extracted.__self);\n        }\n      } else if (extracted.key !== undefined) {\n        args.push(extracted.key);\n      }\n\n      return call(file, children.length > 1 ? \"jsxs\" : \"jsx\", args);\n    }\n\n    // Builds props for React.jsx. This function adds children into the props\n    // and ensures that props is always an object\n    function buildJSXOpeningElementAttributes(\n      attribs: NodePath<JSXAttribute | JSXSpreadAttribute>[],\n      children: Expression[],\n    ) {\n      const props = attribs.reduce(accumulateAttribute, []);\n\n      // In React.jsx, children is no longer a separate argument, but passed in\n      // through the argument object\n      if (children?.length > 0) {\n        props.push(buildChildrenProperty(children));\n      }\n\n      return t.objectExpression(props);\n    }\n\n    // Builds JSX Fragment <></> into\n    // Production: React.jsx(type, arguments)\n    // Development: React.jsxDEV(type, { children })\n    function buildJSXFragmentCall(\n      path: NodePath<JSXFragment>,\n      file: PluginPass,\n    ) {\n      const args = [get(file, \"id/fragment\")()];\n\n      const children = t.react.buildChildren(path.node);\n\n      args.push(\n        t.objectExpression(\n          children.length > 0\n            ? [\n                buildChildrenProperty(\n                  //@ts-expect-error The children here contains JSXSpreadChild,\n                  // which will be thrown later\n                  children,\n                ),\n              ]\n            : [],\n        ),\n      );\n\n      if (development) {\n        args.push(\n          path.scope.buildUndefinedNode(),\n          t.booleanLiteral(children.length > 1),\n        );\n      }\n\n      return call(file, children.length > 1 ? \"jsxs\" : \"jsx\", args);\n    }\n\n    // Builds JSX Fragment <></> into\n    // React.createElement(React.Fragment, null, ...children)\n    function buildCreateElementFragmentCall(\n      path: NodePath<JSXFragment>,\n      file: PluginPass,\n    ) {\n      if (filter && !filter(path.node, file)) return;\n\n      return call(file, \"createElement\", [\n        get(file, \"id/fragment\")(),\n        t.nullLiteral(),\n        ...t.react.buildChildren(path.node),\n      ]);\n    }\n\n    // Builds JSX into:\n    // Production: React.createElement(type, arguments, children)\n    // Development: React.createElement(type, arguments, children, source, self)\n    function buildCreateElementCall(\n      path: NodePath<JSXElement>,\n      file: PluginPass,\n    ) {\n      const openingPath = path.get(\"openingElement\");\n\n      return call(file, \"createElement\", [\n        getTag(openingPath),\n        buildCreateElementOpeningElementAttributes(\n          file,\n          path,\n          openingPath.get(\"attributes\"),\n        ),\n        // @ts-expect-error JSXSpreadChild has been transformed in convertAttributeValue\n        ...t.react.buildChildren(path.node),\n      ]);\n    }\n\n    function getTag(openingPath: NodePath<JSXOpeningElement>) {\n      const tagExpr = convertJSXIdentifier(\n        openingPath.node.name,\n        openingPath.node,\n      );\n\n      let tagName: string;\n      if (t.isIdentifier(tagExpr)) {\n        tagName = tagExpr.name;\n      } else if (t.isStringLiteral(tagExpr)) {\n        tagName = tagExpr.value;\n      }\n\n      if (t.react.isCompatTag(tagName)) {\n        return t.stringLiteral(tagName);\n      } else {\n        return tagExpr;\n      }\n    }\n\n    /**\n     * The logic for this is quite terse. It's because we need to\n     * support spread elements. We loop over all attributes,\n     * breaking on spreads, we then push a new object containing\n     * all prior attributes to an array for later processing.\n     */\n    function buildCreateElementOpeningElementAttributes(\n      file: PluginPass,\n      path: NodePath<JSXElement>,\n      attribs: NodePath<JSXAttribute | JSXSpreadAttribute>[],\n    ) {\n      const runtime = get(file, \"runtime\");\n      if (!process.env.BABEL_8_BREAKING) {\n        if (runtime !== \"automatic\") {\n          const objs = [];\n          const props = attribs.reduce(accumulateAttribute, []);\n\n          if (!useSpread) {\n            // Convert syntax to use multiple objects instead of spread\n            let start = 0;\n            props.forEach((prop, i) => {\n              if (t.isSpreadElement(prop)) {\n                if (i > start) {\n                  objs.push(t.objectExpression(props.slice(start, i)));\n                }\n                objs.push(prop.argument);\n                start = i + 1;\n              }\n            });\n            if (props.length > start) {\n              objs.push(t.objectExpression(props.slice(start)));\n            }\n          } else if (props.length) {\n            objs.push(t.objectExpression(props));\n          }\n\n          if (!objs.length) {\n            return t.nullLiteral();\n          }\n\n          if (objs.length === 1) {\n            if (\n              !(\n                t.isSpreadElement(props[0]) &&\n                // If an object expression is spread element's argument\n                // it is very likely to contain __proto__ and we should stop\n                // optimizing spread element\n                t.isObjectExpression(props[0].argument)\n              )\n            ) {\n              return objs[0];\n            }\n          }\n\n          // looks like we have multiple objects\n          if (!t.isObjectExpression(objs[0])) {\n            objs.unshift(t.objectExpression([]));\n          }\n\n          const helper = useBuiltIns\n            ? t.memberExpression(t.identifier(\"Object\"), t.identifier(\"assign\"))\n            : file.addHelper(\"extends\");\n\n          // spread it\n          return t.callExpression(helper, objs);\n        }\n      }\n\n      const props: ObjectExpression[\"properties\"] = [];\n      const found = Object.create(null);\n\n      for (const attr of attribs) {\n        const { node } = attr;\n        const name =\n          t.isJSXAttribute(node) &&\n          t.isJSXIdentifier(node.name) &&\n          node.name.name;\n\n        if (\n          runtime === \"automatic\" &&\n          (name === \"__source\" || name === \"__self\")\n        ) {\n          if (found[name]) throw sourceSelfError(path, name);\n          found[name] = true;\n        }\n\n        accumulateAttribute(props, attr);\n      }\n\n      return props.length === 1 &&\n        t.isSpreadElement(props[0]) &&\n        // If an object expression is spread element's argument\n        // it is very likely to contain __proto__ and we should stop\n        // optimizing spread element\n        !t.isObjectExpression(props[0].argument)\n        ? props[0].argument\n        : props.length > 0\n          ? t.objectExpression(props)\n          : t.nullLiteral();\n    }\n  });\n\n  function getSource(source: string, importName: string) {\n    switch (importName) {\n      case \"Fragment\":\n        return `${source}/${development ? \"jsx-dev-runtime\" : \"jsx-runtime\"}`;\n      case \"jsxDEV\":\n        return `${source}/jsx-dev-runtime`;\n      case \"jsx\":\n      case \"jsxs\":\n        return `${source}/jsx-runtime`;\n      case \"createElement\":\n        return source;\n    }\n  }\n\n  function createImportLazily(\n    pass: PluginPass,\n    path: NodePath<Program>,\n    importName: string,\n    source: string,\n  ): () => Identifier | MemberExpression {\n    return () => {\n      const actualSource = getSource(source, importName);\n      if (isModule(path)) {\n        let reference = get(pass, `imports/${importName}`);\n        if (reference) return t.cloneNode(reference);\n\n        reference = addNamed(path, importName, actualSource, {\n          importedInterop: \"uncompiled\",\n          importPosition: \"after\",\n        });\n        set(pass, `imports/${importName}`, reference);\n\n        return reference;\n      } else {\n        let reference = get(pass, `requires/${actualSource}`);\n        if (reference) {\n          reference = t.cloneNode(reference);\n        } else {\n          reference = addNamespace(path, actualSource, {\n            importedInterop: \"uncompiled\",\n          });\n          set(pass, `requires/${actualSource}`, reference);\n        }\n\n        return t.memberExpression(reference, t.identifier(importName));\n      }\n    };\n  }\n}\n\nfunction toMemberExpression(id: string): Identifier | MemberExpression {\n  return (\n    id\n      .split(\".\")\n      .map(name => t.identifier(name))\n      // @ts-expect-error - The Array#reduce does not have a signature\n      // where the type of initial value differs from callback return type\n      .reduce((object, property) => t.memberExpression(object, property))\n  );\n}\n\nfunction makeSource(path: NodePath, state: PluginPass) {\n  const location = path.node.loc;\n  if (!location) {\n    // the element was generated and doesn't have location information\n    return path.scope.buildUndefinedNode();\n  }\n\n  // @ts-expect-error todo: avoid mutating PluginPass\n  if (!state.fileNameIdentifier) {\n    const { filename = \"\" } = state;\n\n    const fileNameIdentifier = path.scope.generateUidIdentifier(\"_jsxFileName\");\n    path.scope.getProgramParent().push({\n      id: fileNameIdentifier,\n      init: t.stringLiteral(filename),\n    });\n    // @ts-expect-error todo: avoid mutating PluginPass\n    state.fileNameIdentifier = fileNameIdentifier;\n  }\n\n  return makeTrace(\n    t.cloneNode(\n      // @ts-expect-error todo: avoid mutating PluginPass\n      state.fileNameIdentifier,\n    ),\n    location.start.line,\n    location.start.column,\n  );\n}\n\nfunction makeTrace(\n  fileNameIdentifier: Identifier,\n  lineNumber?: number,\n  column0Based?: number,\n) {\n  const fileLineLiteral =\n    lineNumber != null ? t.numericLiteral(lineNumber) : t.nullLiteral();\n\n  const fileColumnLiteral =\n    column0Based != null ? t.numericLiteral(column0Based + 1) : t.nullLiteral();\n\n  return template.expression.ast`{\n    fileName: ${fileNameIdentifier},\n    lineNumber: ${fileLineLiteral},\n    columnNumber: ${fileColumnLiteral},\n  }`;\n}\n\nfunction sourceSelfError(path: NodePath, name: string) {\n  const pluginName = `transform-react-jsx-${name.slice(2)}`;\n\n  return path.buildCodeFrameError(\n    `Duplicate ${name} prop found. You are most likely using the deprecated ${pluginName} Babel plugin. Both __source and __self are automatically set when using the automatic runtime. Please remove transform-react-jsx-source and transform-react-jsx-self from your Babel config.`,\n  );\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,gBAAA,GAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAGA,IAAAG,oBAAA,GAAAH,OAAA;AACA,IAAAI,qBAAA,GAAAJ,OAAA;AAgBA,MAAMK,OAAO,GAAG;EACdC,YAAY,EAAE,OAAO;EACrBC,OAAO,EAAE,WAAW;EACpBC,MAAM,EAAE,qBAAqB;EAC7BC,UAAU,EAAE;AACd,CAAC;AAED,MAAMC,2BAA2B,GAC/B,4CAA4C;AAC9C,MAAMC,4BAA4B,GAAG,uCAAuC;AAE5E,MAAMC,oBAAoB,GAAG,gCAAgC;AAC7D,MAAMC,yBAAyB,GAAG,oCAAoC;AAEtE,MAAMC,GAAG,GAAGA,CAACC,IAAgB,EAAEC,IAAY,KACzCD,IAAI,CAACD,GAAG,CAAE,2BAA0BE,IAAK,EAAC,CAAC;AAC7C,MAAMC,GAAG,GAAGA,CAACF,IAAgB,EAAEC,IAAY,EAAEE,CAAM,KACjDH,IAAI,CAACE,GAAG,CAAE,2BAA0BD,IAAK,EAAC,EAAEE,CAAC,CAAC;AAEhD,SAASC,QAAQA,CAACC,IAAwB,EAAE;EAC1C,OAAOA,IAAI,CAACC,UAAU,CAACC,IAAI,CACzBC,KAAK,IACHC,WAAC,CAACC,gBAAgB,CAACF,KAAK,EAAE;IAAEG,QAAQ,EAAE,KAAK;IAAEC,SAAS,EAAE;EAAM,CAAC,CAAC,KAC/DH,WAAC,CAACI,YAAY,CAACL,KAAK,CAACM,GAAG,EAAE;IAAEb,IAAI,EAAE;EAAY,CAAC,CAAC,IAC/CQ,WAAC,CAACM,eAAe,CAACP,KAAK,CAACM,GAAG,EAAE;IAAEN,KAAK,EAAE;EAAY,CAAC,CAAC,CAC1D,CAAC;AACH;AAae,SAASQ,YAAYA,CAAC;EACnCf,IAAI;EACJgB;AAIF,CAAC,EAAE;EACD,OAAO,IAAAC,0BAAO,EAAC,CAACC,CAAC,EAAEC,OAAgB,KAAK;IACtC,MAAM;MACJC,IAAI,EAAEC,eAAe;MAErBC,gBAAgB,GAAG,IAAI;MAEvBC,MAAM;MAENhC,OAAO,EAAEiC,eAAe,GAEpBR,WAAW,GACT,WAAW,GACX,SAAS;MAEf1B,YAAY,EAAEmC,qBAAqB,GAAGpC,OAAO,CAACC,YAAY;MAC1DE,MAAM,EAAEkC,cAAc,GAAGrC,OAAO,CAACG,MAAM;MACvCC,UAAU,EAAEkC,mBAAmB,GAAGtC,OAAO,CAACI;IAC5C,CAAC,GAAG0B,OAAO;IA6BJ;MAEL,IAAI;QAAES,SAAS,GAAG,KAAK;QAAEC,WAAW,GAAG;MAAM,CAAC,GAAGV,OAAO;MAExD,IAAIK,eAAe,KAAK,SAAS,EAAE;QACjC,IAAI,OAAOI,SAAS,KAAK,SAAS,EAAE;UAClC,MAAM,IAAIE,KAAK,CACb,kEAAkE,GAChE,+BACJ,CAAC;QACH;QAEA,IAAI,OAAOD,WAAW,KAAK,SAAS,EAAE;UACpC,MAAM,IAAIC,KAAK,CACb,kEAAkE,GAChE,iCACJ,CAAC;QACH;QAEA,IAAIF,SAAS,IAAIC,WAAW,EAAE;UAC5B,MAAM,IAAIC,KAAK,CACb,sEAAsE,GACpE,cACJ,CAAC;QACH;MACF;IACF;IAEA,MAAMC,2BAAgD,GAAG;MACvDC,iBAAiBA,CAACC,IAAI,EAAEC,KAAK,EAAE;QAC7B,MAAMC,UAAU,GAAG,EAAE;QACrB,IAAIC,aAAa,CAACH,IAAI,CAACI,KAAK,CAAC,EAAE;UAC7BF,UAAU,CAACG,IAAI,CACb9B,WAAC,CAAC+B,YAAY,CACZ/B,WAAC,CAACgC,aAAa,CAAC,QAAQ,CAAC,EACzBhC,WAAC,CAACiC,sBAAsB,CAACjC,WAAC,CAACkC,cAAc,CAAC,CAAC,CAC7C,CACF,CAAC;QACH;QACAP,UAAU,CAACG,IAAI,CACb9B,WAAC,CAAC+B,YAAY,CACZ/B,WAAC,CAACgC,aAAa,CAAC,UAAU,CAAC,EAC3BhC,WAAC,CAACiC,sBAAsB,CAACE,UAAU,CAACV,IAAI,EAAEC,KAAK,CAAC,CAClD,CACF,CAAC;QACDD,IAAI,CAACW,aAAa,CAAC,YAAY,EAAET,UAAU,CAAC;MAC9C;IACF,CAAC;IAED,OAAO;MACLnC,IAAI;MACJ6C,QAAQ,EAAEC,wBAAG;MACbC,OAAO,EAAE;QACPC,iBAAiBA,CAACf,IAAI,EAAE;UACtB,IAAIX,gBAAgB,EAAE;YACpB,MAAMW,IAAI,CAACgB,mBAAmB,CAC3B;AACf,gEACY,CAAC;UACH;QACF,CAAC;QAEDC,cAAcA,CAACjB,IAAI,EAAE;UACnB,MAAMA,IAAI,CAACgB,mBAAmB,CAC5B,6CACF,CAAC;QACH,CAAC;QAEDE,OAAO,EAAE;UACPC,KAAKA,CAACnB,IAAI,EAAEC,KAAK,EAAE;YACjB,MAAM;cAAEmB;YAAK,CAAC,GAAGnB,KAAK;YACtB,IAAI3C,OAAe,GAAGiC,eAAe;YAErC,IAAI8B,MAAc,GAAG7B,qBAAqB;YAC1C,IAAIjC,MAAc,GAAGkC,cAAc;YACnC,IAAIjC,UAAkB,GAAGkC,mBAAmB;YAE5C,IAAI4B,SAAS,GAAG,CAAC,CAACpC,OAAO,CAAC7B,YAAY;YACtC,IAAIkE,SAAS,GAAG,CAAC,CAACrC,OAAO,CAAC3B,MAAM;YAChC,IAAIiE,aAAa,GAAG,CAAC,CAACtC,OAAO,CAAC1B,UAAU;YAExC,IAAI4D,IAAI,CAACK,GAAG,CAACC,QAAQ,EAAE;cACrB,KAAK,MAAMC,OAAO,IAAIP,IAAI,CAACK,GAAG,CAACC,QAAQ,EAAE;gBACvC,MAAME,aAAa,GAAGnE,2BAA2B,CAACoE,IAAI,CACpDF,OAAO,CAACrD,KACV,CAAC;gBACD,IAAIsD,aAAa,EAAE;kBACjBP,MAAM,GAAGO,aAAa,CAAC,CAAC,CAAC;kBACzBN,SAAS,GAAG,IAAI;gBAClB;gBAEA,MAAMQ,cAAc,GAAGpE,4BAA4B,CAACmE,IAAI,CACtDF,OAAO,CAACrD,KACV,CAAC;gBACD,IAAIwD,cAAc,EAAE;kBAClBxE,OAAO,GAAGwE,cAAc,CAAC,CAAC,CAAC;gBAC7B;gBAEA,MAAMC,UAAU,GAAGpE,oBAAoB,CAACkE,IAAI,CAACF,OAAO,CAACrD,KAAK,CAAC;gBAC3D,IAAIyD,UAAU,EAAE;kBACdxE,MAAM,GAAGwE,UAAU,CAAC,CAAC,CAAC;kBACtBR,SAAS,GAAG,IAAI;gBAClB;gBACA,MAAMS,cAAc,GAAGpE,yBAAyB,CAACiE,IAAI,CACnDF,OAAO,CAACrD,KACV,CAAC;gBACD,IAAI0D,cAAc,EAAE;kBAClBxE,UAAU,GAAGwE,cAAc,CAAC,CAAC,CAAC;kBAC9BR,aAAa,GAAG,IAAI;gBACtB;cACF;YACF;YAEAxD,GAAG,CAACiC,KAAK,EAAE,SAAS,EAAE3C,OAAO,CAAC;YAC9B,IAAIA,OAAO,KAAK,SAAS,EAAE;cACzB,IAAIgE,SAAS,EAAE;gBACb,MAAMtB,IAAI,CAACgB,mBAAmB,CAC3B,qDACH,CAAC;cACH;cAEA,MAAMiB,aAAa,GAAGC,kBAAkB,CAAC3E,MAAM,CAAC;cAChD,MAAM4E,QAAQ,GAAGD,kBAAkB,CAAC1E,UAAU,CAAC;cAE/CQ,GAAG,CAACiC,KAAK,EAAE,kBAAkB,EAAE,MAAM1B,WAAC,CAAC6D,SAAS,CAACH,aAAa,CAAC,CAAC;cAChEjE,GAAG,CAACiC,KAAK,EAAE,aAAa,EAAE,MAAM1B,WAAC,CAAC6D,SAAS,CAACD,QAAQ,CAAC,CAAC;cAEtDnE,GAAG,CAACiC,KAAK,EAAE,aAAa,EAAE1C,MAAM,KAAKH,OAAO,CAACG,MAAM,CAAC;YACtD,CAAC,MAAM,IAAID,OAAO,KAAK,WAAW,EAAE;cAClC,IAAIiE,SAAS,IAAIC,aAAa,EAAE;gBAC9B,MAAMxB,IAAI,CAACgB,mBAAmB,CAC3B,gEACH,CAAC;cACH;cAEA,MAAMqB,MAAM,GAAGA,CAACtE,IAAY,EAAEuE,EAAU,KACtCtE,GAAG,CAACiC,KAAK,EAAElC,IAAI,EAAEwE,kBAAkB,CAACtC,KAAK,EAAED,IAAI,EAAEsC,EAAE,EAAEjB,MAAM,CAAC,CAAC;cAE/DgB,MAAM,CAAC,QAAQ,EAAEtD,WAAW,GAAG,QAAQ,GAAG,KAAK,CAAC;cAChDsD,MAAM,CAAC,SAAS,EAAEtD,WAAW,GAAG,QAAQ,GAAG,MAAM,CAAC;cAClDsD,MAAM,CAAC,kBAAkB,EAAE,eAAe,CAAC;cAC3CA,MAAM,CAAC,aAAa,EAAE,UAAU,CAAC;cAEjCrE,GAAG,CAACiC,KAAK,EAAE,aAAa,EAAEoB,MAAM,KAAKjE,OAAO,CAACC,YAAY,CAAC;YAC5D,CAAC,MAAM;cACL,MAAM2C,IAAI,CAACgB,mBAAmB,CAC3B,kDACH,CAAC;YACH;YAEA,IAAIjC,WAAW,EAAE;cACfiB,IAAI,CAACwC,QAAQ,CAAC1C,2BAA2B,EAAEG,KAAK,CAAC;YACnD;UACF;QAkBF,CAAC;QAEDwC,WAAW,EAAE;UACXC,IAAIA,CAAC1C,IAAI,EAAEoB,IAAI,EAAE;YACf,IAAIuB,QAAQ;YACZ,IAAI9E,GAAG,CAACuD,IAAI,EAAE,SAAS,CAAC,KAAK,SAAS,EAAE;cACtCuB,QAAQ,GAAGC,8BAA8B,CAAC5C,IAAI,EAAEoB,IAAI,CAAC;YACvD,CAAC,MAAM;cACLuB,QAAQ,GAAGE,oBAAoB,CAAC7C,IAAI,EAAEoB,IAAI,CAAC;YAC7C;YAEApB,IAAI,CAAC8C,WAAW,CAACvE,WAAC,CAACqC,QAAQ,CAAC+B,QAAQ,EAAE3C,IAAI,CAAC7B,IAAI,CAAC,CAAC;UACnD;QACF,CAAC;QAED4E,UAAU,EAAE;UACVL,IAAIA,CAAC1C,IAAI,EAAEoB,IAAI,EAAE;YACf,IAAIuB,QAAQ;YACZ,IACE9E,GAAG,CAACuD,IAAI,EAAE,SAAS,CAAC,KAAK,SAAS,IAClC4B,sBAAsB,CAAChD,IAAI,CAAC,EAC5B;cACA2C,QAAQ,GAAGM,sBAAsB,CAACjD,IAAI,EAAEoB,IAAI,CAAC;YAC/C,CAAC,MAAM;cACLuB,QAAQ,GAAGO,mBAAmB,CAAClD,IAAI,EAAEoB,IAAI,CAAC;YAC5C;YAEApB,IAAI,CAAC8C,WAAW,CAACvE,WAAC,CAACqC,QAAQ,CAAC+B,QAAQ,EAAE3C,IAAI,CAAC7B,IAAI,CAAC,CAAC;UACnD;QACF,CAAC;QAEDgF,YAAYA,CAACnD,IAAI,EAAE;UACjB,IAAIzB,WAAC,CAAC6E,YAAY,CAACpD,IAAI,CAAC7B,IAAI,CAACG,KAAK,CAAC,EAAE;YACnC0B,IAAI,CAAC7B,IAAI,CAACG,KAAK,GAAGC,WAAC,CAACiC,sBAAsB,CAACR,IAAI,CAAC7B,IAAI,CAACG,KAAK,CAAC;UAC7D;QACF;MACF;IACF,CAAC;IAGD,SAAS+E,cAAcA,CAACC,SAA0B,EAAE;MAClD,OAAOA,SAAS,CAACnF,IAAI,CAACoF,UAAU,KAAK,IAAI;IAC3C;IAGA,SAASpD,aAAaA,CAACC,KAAY,EAAE;MAEnC,GAAG;QACD,MAAM;UAAEJ;QAAK,CAAC,GAAGI,KAAK;QACtB,IAAIJ,IAAI,CAACwD,gBAAgB,CAAC,CAAC,IAAI,CAACxD,IAAI,CAACyD,yBAAyB,CAAC,CAAC,EAAE;UAChE,IAAI,CAACzD,IAAI,CAAC0D,QAAQ,CAAC,CAAC,EAAE;YAEpB,OAAO,IAAI;UACb;UAEA,IAAI1D,IAAI,CAAC7B,IAAI,CAACwF,IAAI,KAAK,aAAa,EAAE;YAEpC,OAAO,IAAI;UACb;UAEA,OAAO,CAACN,cAAc,CAACrD,IAAI,CAAC4D,UAAU,CAACA,UAA6B,CAAC;QACvE;QACA,IAAI5D,IAAI,CAAC6D,eAAe,CAAC,CAAC,EAAE;UAE1B,OAAO,KAAK;QACd;MACF,CAAC,QAASzD,KAAK,GAAGA,KAAK,CAAC0D,MAAM;MAE9B,OAAO,IAAI;IACb;IAEA,SAASC,IAAIA,CACXjG,IAAgB,EAChBC,IAAY,EACZiG,IAAiC,EACjC;MACA,MAAM7F,IAAI,GAAGI,WAAC,CAAC0F,cAAc,CAACpG,GAAG,CAACC,IAAI,EAAG,MAAKC,IAAK,EAAC,CAAC,CAAC,CAAC,EAAEiG,IAAI,CAAC;MAC9D,IAAI5E,eAAe,WAAfA,eAAe,GAAIvB,GAAG,CAACC,IAAI,EAAE,aAAa,CAAC,EAAE,IAAAoG,6BAAc,EAAC/F,IAAI,CAAC;MACrE,OAAOA,IAAI;IACb;IAOA,SAAS6E,sBAAsBA,CAAChD,IAA0B,EAAE;MAC1D,MAAMmE,WAAW,GAAGnE,IAAI,CAACnC,GAAG,CAAC,gBAAgB,CAAC;MAC9C,MAAMqC,UAAU,GAAGiE,WAAW,CAAChG,IAAI,CAAC+B,UAAU;MAE9C,IAAIkE,eAAe,GAAG,KAAK;MAC3B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnE,UAAU,CAACoE,MAAM,EAAED,CAAC,EAAE,EAAE;QAC1C,MAAME,IAAI,GAAGrE,UAAU,CAACmE,CAAC,CAAC;QAC1B,IACED,eAAe,IACf7F,WAAC,CAACiG,cAAc,CAACD,IAAI,CAAC,IACtBA,IAAI,CAACxG,IAAI,CAACA,IAAI,KAAK,KAAK,EACxB;UACA,OAAO,IAAI;QACb,CAAC,MAAM,IAAIQ,WAAC,CAACkG,oBAAoB,CAACF,IAAI,CAAC,EAAE;UACvCH,eAAe,GAAG,IAAI;QACxB;MACF;MACA,OAAO,KAAK;IACd;IAEA,SAASM,oBAAoBA,CAC3BvG,IAAmE,EACnE2F,MAAmD,EACqB;MACxE,IAAIvF,WAAC,CAACoG,eAAe,CAACxG,IAAI,CAAC,EAAE;QAC3B,IAAIA,IAAI,CAACJ,IAAI,KAAK,MAAM,IAAIQ,WAAC,CAACqG,YAAY,CAACzG,IAAI,EAAE2F,MAAM,CAAC,EAAE;UACxD,OAAOvF,WAAC,CAACkC,cAAc,CAAC,CAAC;QAC3B,CAAC,MAAM,IAAIlC,WAAC,CAACsG,iBAAiB,CAAC1G,IAAI,CAACJ,IAAI,EAAE,KAAK,CAAC,EAAE;UAEhDI,IAAI,CAAC2G,IAAI,GAAG,YAAY;UACxB,OAAO3G,IAAI;QACb,CAAC,MAAM;UACL,OAAOI,WAAC,CAACwG,aAAa,CAAC5G,IAAI,CAACJ,IAAI,CAAC;QACnC;MACF,CAAC,MAAM,IAAIQ,WAAC,CAACyG,qBAAqB,CAAC7G,IAAI,CAAC,EAAE;QACxC,OAAOI,WAAC,CAAC0G,gBAAgB,CACvBP,oBAAoB,CAACvG,IAAI,CAAC+G,MAAM,EAAE/G,IAAI,CAAC,EACvCuG,oBAAoB,CAACvG,IAAI,CAACgH,QAAQ,EAAEhH,IAAI,CAC1C,CAAC;MACH,CAAC,MAAM,IAAII,WAAC,CAAC6G,mBAAmB,CAACjH,IAAI,CAAC,EAAE;QAKtC,OAAOI,WAAC,CAACwG,aAAa,CAAE,GAAE5G,IAAI,CAACkH,SAAS,CAACtH,IAAK,IAAGI,IAAI,CAACJ,IAAI,CAACA,IAAK,EAAC,CAAC;MACpE;MAGA,OAAOI,IAAI;IACb;IAEA,SAASmH,qBAAqBA,CAC5BnH,IAAgD,EAChD;MACA,IAAII,WAAC,CAACgH,wBAAwB,CAACpH,IAAI,CAAC,EAAE;QACpC,OAAOA,IAAI,CAACqH,UAAU;MACxB,CAAC,MAAM;QACL,OAAOrH,IAAI;MACb;IACF;IAEA,SAASsH,mBAAmBA,CAC1BC,KAAqC,EACrCC,SAAsD,EACtD;MACA,IAAIpH,WAAC,CAACkG,oBAAoB,CAACkB,SAAS,CAACxH,IAAI,CAAC,EAAE;QAC1C,MAAMyH,GAAG,GAAGD,SAAS,CAACxH,IAAI,CAAC0H,QAAQ;QAEnC,IAAItH,WAAC,CAACuH,kBAAkB,CAACF,GAAG,CAAC,IAAI,CAAC1H,QAAQ,CAAC0H,GAAG,CAAC,EAAE;UAC/CF,KAAK,CAACrF,IAAI,CAAC,GAAGuF,GAAG,CAACxH,UAAU,CAAC;QAC/B,CAAC,MAAM;UACLsH,KAAK,CAACrF,IAAI,CAAC9B,WAAC,CAACwH,aAAa,CAACH,GAAG,CAAC,CAAC;QAClC;QACA,OAAOF,KAAK;MACd;MAEA,MAAMpH,KAAK,GAAGgH,qBAAqB,CACjCK,SAAS,CAACxH,IAAI,CAACJ,IAAI,CAACA,IAAI,KAAK,KAAK,GAC9B4H,SAAS,CAACxH,IAAI,CAACG,KAAK,IAAIC,WAAC,CAACyH,cAAc,CAAC,IAAI,CAAC,GAC9CL,SAAS,CAACxH,IAAI,CAACG,KACrB,CAAC;MAED,IAAIqH,SAAS,CAACxH,IAAI,CAACJ,IAAI,CAACA,IAAI,KAAK,KAAK,IAAIO,KAAK,KAAK,IAAI,EAAE;QACxD,MAAMqH,SAAS,CAAC3E,mBAAmB,CACjC,mGACF,CAAC;MACH;MAEA,IACEzC,WAAC,CAACM,eAAe,CAACP,KAAK,CAAC,IACxB,CAACC,WAAC,CAACgH,wBAAwB,CAACI,SAAS,CAACxH,IAAI,CAACG,KAAK,CAAC,EACjD;QAAA,IAAA2H,YAAA;QACA3H,KAAK,CAACA,KAAK,GAAGA,KAAK,CAACA,KAAK,CAAC4H,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC;QAGhD,CAAAD,YAAA,GAAO3H,KAAK,CAAC6H,KAAK,aAAlB,OAAOF,YAAA,CAAaG,GAAG;MACzB;MAEA,IAAI7H,WAAC,CAAC6G,mBAAmB,CAACO,SAAS,CAACxH,IAAI,CAACJ,IAAI,CAAC,EAAE;QAE9C4H,SAAS,CAACxH,IAAI,CAACJ,IAAI,GAAGQ,WAAC,CAACwG,aAAa,CACnCY,SAAS,CAACxH,IAAI,CAACJ,IAAI,CAACsH,SAAS,CAACtH,IAAI,GAChC,GAAG,GACH4H,SAAS,CAACxH,IAAI,CAACJ,IAAI,CAACA,IAAI,CAACA,IAC7B,CAAC;MACH,CAAC,MAAM,IAAIQ,WAAC,CAACsG,iBAAiB,CAACc,SAAS,CAACxH,IAAI,CAACJ,IAAI,CAACA,IAAI,EAAE,KAAK,CAAC,EAAE;QAE/D4H,SAAS,CAACxH,IAAI,CAACJ,IAAI,CAAC+G,IAAI,GAAG,YAAY;MACzC,CAAC,MAAM;QAELa,SAAS,CAACxH,IAAI,CAACJ,IAAI,GAAGQ,WAAC,CAACwG,aAAa,CAACY,SAAS,CAACxH,IAAI,CAACJ,IAAI,CAACA,IAAI,CAAC;MACjE;MAEA2H,KAAK,CAACrF,IAAI,CACR9B,WAAC,CAACqC,QAAQ,CACRrC,WAAC,CAAC8H,cAAc,CAEdV,SAAS,CAACxH,IAAI,CAACJ,IAAI,EACnBO,KACF,CAAC,EACDqH,SAAS,CAACxH,IACZ,CACF,CAAC;MACD,OAAOuH,KAAK;IACd;IAEA,SAASY,qBAAqBA,CAACC,QAAsB,EAAE;MACrD,IAAIC,YAAY;MAChB,IAAID,QAAQ,CAACjC,MAAM,KAAK,CAAC,EAAE;QACzBkC,YAAY,GAAGD,QAAQ,CAAC,CAAC,CAAC;MAC5B,CAAC,MAAM,IAAIA,QAAQ,CAACjC,MAAM,GAAG,CAAC,EAAE;QAC9BkC,YAAY,GAAGjI,WAAC,CAACkI,eAAe,CAACF,QAAQ,CAAC;MAC5C,CAAC,MAAM;QACL,OAAOG,SAAS;MAClB;MAEA,OAAOnI,WAAC,CAAC8H,cAAc,CAAC9H,WAAC,CAACoI,UAAU,CAAC,UAAU,CAAC,EAAEH,YAAY,CAAC;IACjE;IAKA,SAAStD,mBAAmBA,CAAClD,IAA0B,EAAEoB,IAAgB,EAAE;MACzE,MAAM+C,WAAW,GAAGnE,IAAI,CAACnC,GAAG,CAAC,gBAAgB,CAAC;MAC9C,MAAMmG,IAAoB,GAAG,CAAC4C,MAAM,CAACzC,WAAW,CAAC,CAAC;MAElD,MAAM0C,YAAY,GAAG,EAAE;MACvB,MAAMC,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MAMrC,KAAK,MAAMzC,IAAI,IAAIJ,WAAW,CAACtG,GAAG,CAAC,YAAY,CAAC,EAAE;QAChD,IAAI0G,IAAI,CAACC,cAAc,CAAC,CAAC,IAAIjG,WAAC,CAACoG,eAAe,CAACJ,IAAI,CAACpG,IAAI,CAACJ,IAAI,CAAC,EAAE;UAC9D,MAAM;YAAEA;UAAK,CAAC,GAAGwG,IAAI,CAACpG,IAAI,CAACJ,IAAI;UAC/B,QAAQA,IAAI;YACV,KAAK,UAAU;YACf,KAAK,QAAQ;cACX,IAAI+I,SAAS,CAAC/I,IAAI,CAAC,EAAE,MAAMkJ,eAAe,CAACjH,IAAI,EAAEjC,IAAI,CAAC;YAExD,KAAK,KAAK;cAAE;gBACV,MAAMmJ,QAAQ,GAAG5B,qBAAqB,CAACf,IAAI,CAACpG,IAAI,CAACG,KAAK,CAAC;gBACvD,IAAI4I,QAAQ,KAAK,IAAI,EAAE;kBACrB,MAAM3C,IAAI,CAACvD,mBAAmB,CAC5B,mGACF,CAAC;gBACH;gBAEA8F,SAAS,CAAC/I,IAAI,CAAC,GAAGmJ,QAAQ;gBAC1B;cACF;YACA;cACEL,YAAY,CAACxG,IAAI,CAACkE,IAAI,CAAC;UAC3B;QACF,CAAC,MAAM;UACLsC,YAAY,CAACxG,IAAI,CAACkE,IAAI,CAAC;QACzB;MACF;MAEA,MAAMgC,QAAQ,GAAGhI,WAAC,CAAC4I,KAAK,CAACC,aAAa,CAACpH,IAAI,CAAC7B,IAAI,CAAC;MAEjD,IAAIkJ,OAA2B;MAE/B,IAAIR,YAAY,CAACvC,MAAM,IAAIiC,QAAQ,CAACjC,MAAM,EAAE;QAC1C+C,OAAO,GAAGC,gCAAgC,CACxCT,YAAY,EAGZN,QACF,CAAC;MACH,CAAC,MAAM;QAELc,OAAO,GAAG9I,WAAC,CAACgJ,gBAAgB,CAAC,EAAE,CAAC;MAClC;MAEAvD,IAAI,CAAC3D,IAAI,CAACgH,OAAO,CAAC;MAElB,IAAItI,WAAW,EAAE;QAAA,IAAAyI,cAAA;QAIfxD,IAAI,CAAC3D,IAAI,EAAAmH,cAAA,GACPV,SAAS,CAAClI,GAAG,YAAA4I,cAAA,GAAIxH,IAAI,CAACI,KAAK,CAACqH,kBAAkB,CAAC,CAAC,EAChDlJ,WAAC,CAACyH,cAAc,CAACO,QAAQ,CAACjC,MAAM,GAAG,CAAC,CACtC,CAAC;QACD,IAAIwC,SAAS,CAACY,QAAQ,EAAE;UACtB1D,IAAI,CAAC3D,IAAI,CAACyG,SAAS,CAACY,QAAQ,CAAC;UAC7B,IAAIZ,SAAS,CAACa,MAAM,EAAE3D,IAAI,CAAC3D,IAAI,CAACyG,SAAS,CAACa,MAAM,CAAC;QACnD,CAAC,MAAM,IAAIb,SAAS,CAACa,MAAM,EAAE;UAC3B3D,IAAI,CAAC3D,IAAI,CAACL,IAAI,CAACI,KAAK,CAACqH,kBAAkB,CAAC,CAAC,EAAEX,SAAS,CAACa,MAAM,CAAC;QAC9D;MACF,CAAC,MAAM,IAAIb,SAAS,CAAClI,GAAG,KAAK8H,SAAS,EAAE;QACtC1C,IAAI,CAAC3D,IAAI,CAACyG,SAAS,CAAClI,GAAG,CAAC;MAC1B;MAEA,OAAOmF,IAAI,CAAC3C,IAAI,EAAEmF,QAAQ,CAACjC,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK,EAAEN,IAAI,CAAC;IAC/D;IAIA,SAASsD,gCAAgCA,CACvCD,OAAsD,EACtDd,QAAsB,EACtB;MACA,MAAMqB,KAAK,GAAGP,OAAO,CAACQ,MAAM,CAACpC,mBAAmB,EAAE,EAAE,CAAC;MAIrD,IAAI,CAAAc,QAAQ,oBAARA,QAAQ,CAAEjC,MAAM,IAAG,CAAC,EAAE;QACxBsD,KAAK,CAACvH,IAAI,CAACiG,qBAAqB,CAACC,QAAQ,CAAC,CAAC;MAC7C;MAEA,OAAOhI,WAAC,CAACgJ,gBAAgB,CAACK,KAAK,CAAC;IAClC;IAKA,SAAS/E,oBAAoBA,CAC3B7C,IAA2B,EAC3BoB,IAAgB,EAChB;MACA,MAAM4C,IAAI,GAAG,CAACnG,GAAG,CAACuD,IAAI,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;MAEzC,MAAMmF,QAAQ,GAAGhI,WAAC,CAAC4I,KAAK,CAACC,aAAa,CAACpH,IAAI,CAAC7B,IAAI,CAAC;MAEjD6F,IAAI,CAAC3D,IAAI,CACP9B,WAAC,CAACgJ,gBAAgB,CAChBhB,QAAQ,CAACjC,MAAM,GAAG,CAAC,GACf,CACEgC,qBAAqB,CAGnBC,QACF,CAAC,CACF,GACD,EACN,CACF,CAAC;MAED,IAAIxH,WAAW,EAAE;QACfiF,IAAI,CAAC3D,IAAI,CACPL,IAAI,CAACI,KAAK,CAACqH,kBAAkB,CAAC,CAAC,EAC/BlJ,WAAC,CAACyH,cAAc,CAACO,QAAQ,CAACjC,MAAM,GAAG,CAAC,CACtC,CAAC;MACH;MAEA,OAAOP,IAAI,CAAC3C,IAAI,EAAEmF,QAAQ,CAACjC,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK,EAAEN,IAAI,CAAC;IAC/D;IAIA,SAASpB,8BAA8BA,CACrC5C,IAA2B,EAC3BoB,IAAgB,EAChB;MACA,IAAI9B,MAAM,IAAI,CAACA,MAAM,CAACU,IAAI,CAAC7B,IAAI,EAAEiD,IAAI,CAAC,EAAE;MAExC,OAAO2C,IAAI,CAAC3C,IAAI,EAAE,eAAe,EAAE,CACjCvD,GAAG,CAACuD,IAAI,EAAE,aAAa,CAAC,CAAC,CAAC,EAC1B7C,WAAC,CAACuJ,WAAW,CAAC,CAAC,EACf,GAAGvJ,WAAC,CAAC4I,KAAK,CAACC,aAAa,CAACpH,IAAI,CAAC7B,IAAI,CAAC,CACpC,CAAC;IACJ;IAKA,SAAS8E,sBAAsBA,CAC7BjD,IAA0B,EAC1BoB,IAAgB,EAChB;MACA,MAAM+C,WAAW,GAAGnE,IAAI,CAACnC,GAAG,CAAC,gBAAgB,CAAC;MAE9C,OAAOkG,IAAI,CAAC3C,IAAI,EAAE,eAAe,EAAE,CACjCwF,MAAM,CAACzC,WAAW,CAAC,EACnB4D,0CAA0C,CACxC3G,IAAI,EACJpB,IAAI,EACJmE,WAAW,CAACtG,GAAG,CAAC,YAAY,CAC9B,CAAC,EAED,GAAGU,WAAC,CAAC4I,KAAK,CAACC,aAAa,CAACpH,IAAI,CAAC7B,IAAI,CAAC,CACpC,CAAC;IACJ;IAEA,SAASyI,MAAMA,CAACzC,WAAwC,EAAE;MACxD,MAAM6D,OAAO,GAAGtD,oBAAoB,CAClCP,WAAW,CAAChG,IAAI,CAACJ,IAAI,EACrBoG,WAAW,CAAChG,IACd,CAAC;MAED,IAAI8J,OAAe;MACnB,IAAI1J,WAAC,CAACI,YAAY,CAACqJ,OAAO,CAAC,EAAE;QAC3BC,OAAO,GAAGD,OAAO,CAACjK,IAAI;MACxB,CAAC,MAAM,IAAIQ,WAAC,CAACM,eAAe,CAACmJ,OAAO,CAAC,EAAE;QACrCC,OAAO,GAAGD,OAAO,CAAC1J,KAAK;MACzB;MAEA,IAAIC,WAAC,CAAC4I,KAAK,CAACe,WAAW,CAACD,OAAO,CAAC,EAAE;QAChC,OAAO1J,WAAC,CAACwG,aAAa,CAACkD,OAAO,CAAC;MACjC,CAAC,MAAM;QACL,OAAOD,OAAO;MAChB;IACF;IAQA,SAASD,0CAA0CA,CACjD3G,IAAgB,EAChBpB,IAA0B,EAC1BqH,OAAsD,EACtD;MACA,MAAM/J,OAAO,GAAGO,GAAG,CAACuD,IAAI,EAAE,SAAS,CAAC;MACD;QACjC,IAAI9D,OAAO,KAAK,WAAW,EAAE;UAC3B,MAAM6K,IAAI,GAAG,EAAE;UACf,MAAMP,KAAK,GAAGP,OAAO,CAACQ,MAAM,CAACpC,mBAAmB,EAAE,EAAE,CAAC;UAErD,IAAI,CAAC9F,SAAS,EAAE;YAEd,IAAIyI,KAAK,GAAG,CAAC;YACbR,KAAK,CAACS,OAAO,CAAC,CAACC,IAAI,EAAEjE,CAAC,KAAK;cACzB,IAAI9F,WAAC,CAACgK,eAAe,CAACD,IAAI,CAAC,EAAE;gBAC3B,IAAIjE,CAAC,GAAG+D,KAAK,EAAE;kBACbD,IAAI,CAAC9H,IAAI,CAAC9B,WAAC,CAACgJ,gBAAgB,CAACK,KAAK,CAACY,KAAK,CAACJ,KAAK,EAAE/D,CAAC,CAAC,CAAC,CAAC;gBACtD;gBACA8D,IAAI,CAAC9H,IAAI,CAACiI,IAAI,CAACzC,QAAQ,CAAC;gBACxBuC,KAAK,GAAG/D,CAAC,GAAG,CAAC;cACf;YACF,CAAC,CAAC;YACF,IAAIuD,KAAK,CAACtD,MAAM,GAAG8D,KAAK,EAAE;cACxBD,IAAI,CAAC9H,IAAI,CAAC9B,WAAC,CAACgJ,gBAAgB,CAACK,KAAK,CAACY,KAAK,CAACJ,KAAK,CAAC,CAAC,CAAC;YACnD;UACF,CAAC,MAAM,IAAIR,KAAK,CAACtD,MAAM,EAAE;YACvB6D,IAAI,CAAC9H,IAAI,CAAC9B,WAAC,CAACgJ,gBAAgB,CAACK,KAAK,CAAC,CAAC;UACtC;UAEA,IAAI,CAACO,IAAI,CAAC7D,MAAM,EAAE;YAChB,OAAO/F,WAAC,CAACuJ,WAAW,CAAC,CAAC;UACxB;UAEA,IAAIK,IAAI,CAAC7D,MAAM,KAAK,CAAC,EAAE;YACrB,IACE,EACE/F,WAAC,CAACgK,eAAe,CAACX,KAAK,CAAC,CAAC,CAAC,CAAC,IAI3BrJ,WAAC,CAACuH,kBAAkB,CAAC8B,KAAK,CAAC,CAAC,CAAC,CAAC/B,QAAQ,CAAC,CACxC,EACD;cACA,OAAOsC,IAAI,CAAC,CAAC,CAAC;YAChB;UACF;UAGA,IAAI,CAAC5J,WAAC,CAACuH,kBAAkB,CAACqC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;YAClCA,IAAI,CAACM,OAAO,CAAClK,WAAC,CAACgJ,gBAAgB,CAAC,EAAE,CAAC,CAAC;UACtC;UAEA,MAAMmB,MAAM,GAAG9I,WAAW,GACtBrB,WAAC,CAAC0G,gBAAgB,CAAC1G,WAAC,CAACoI,UAAU,CAAC,QAAQ,CAAC,EAAEpI,WAAC,CAACoI,UAAU,CAAC,QAAQ,CAAC,CAAC,GAClEvF,IAAI,CAACuH,SAAS,CAAC,SAAS,CAAC;UAG7B,OAAOpK,WAAC,CAAC0F,cAAc,CAACyE,MAAM,EAAEP,IAAI,CAAC;QACvC;MACF;MAEA,MAAMP,KAAqC,GAAG,EAAE;MAChD,MAAMgB,KAAK,GAAG7B,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MAEjC,KAAK,MAAMzC,IAAI,IAAI8C,OAAO,EAAE;QAC1B,MAAM;UAAElJ;QAAK,CAAC,GAAGoG,IAAI;QACrB,MAAMxG,IAAI,GACRQ,WAAC,CAACiG,cAAc,CAACrG,IAAI,CAAC,IACtBI,WAAC,CAACoG,eAAe,CAACxG,IAAI,CAACJ,IAAI,CAAC,IAC5BI,IAAI,CAACJ,IAAI,CAACA,IAAI;QAEhB,IACET,OAAO,KAAK,WAAW,KACtBS,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,QAAQ,CAAC,EAC1C;UACA,IAAI6K,KAAK,CAAC7K,IAAI,CAAC,EAAE,MAAMkJ,eAAe,CAACjH,IAAI,EAAEjC,IAAI,CAAC;UAClD6K,KAAK,CAAC7K,IAAI,CAAC,GAAG,IAAI;QACpB;QAEA0H,mBAAmB,CAACmC,KAAK,EAAErD,IAAI,CAAC;MAClC;MAEA,OAAOqD,KAAK,CAACtD,MAAM,KAAK,CAAC,IACvB/F,WAAC,CAACgK,eAAe,CAACX,KAAK,CAAC,CAAC,CAAC,CAAC,IAI3B,CAACrJ,WAAC,CAACuH,kBAAkB,CAAC8B,KAAK,CAAC,CAAC,CAAC,CAAC/B,QAAQ,CAAC,GACtC+B,KAAK,CAAC,CAAC,CAAC,CAAC/B,QAAQ,GACjB+B,KAAK,CAACtD,MAAM,GAAG,CAAC,GACd/F,WAAC,CAACgJ,gBAAgB,CAACK,KAAK,CAAC,GACzBrJ,WAAC,CAACuJ,WAAW,CAAC,CAAC;IACvB;EACF,CAAC,CAAC;EAEF,SAASe,SAASA,CAACxH,MAAc,EAAEyH,UAAkB,EAAE;IACrD,QAAQA,UAAU;MAChB,KAAK,UAAU;QACb,OAAQ,GAAEzH,MAAO,IAAGtC,WAAW,GAAG,iBAAiB,GAAG,aAAc,EAAC;MACvE,KAAK,QAAQ;QACX,OAAQ,GAAEsC,MAAO,kBAAiB;MACpC,KAAK,KAAK;MACV,KAAK,MAAM;QACT,OAAQ,GAAEA,MAAO,cAAa;MAChC,KAAK,eAAe;QAClB,OAAOA,MAAM;IACjB;EACF;EAEA,SAASkB,kBAAkBA,CACzBzE,IAAgB,EAChBkC,IAAuB,EACvB8I,UAAkB,EAClBzH,MAAc,EACuB;IACrC,OAAO,MAAM;MACX,MAAM0H,YAAY,GAAGF,SAAS,CAACxH,MAAM,EAAEyH,UAAU,CAAC;MAClD,IAAI,IAAAE,6BAAQ,EAAChJ,IAAI,CAAC,EAAE;QAClB,IAAIiJ,SAAS,GAAGpL,GAAG,CAACC,IAAI,EAAG,WAAUgL,UAAW,EAAC,CAAC;QAClD,IAAIG,SAAS,EAAE,OAAO1K,WAAC,CAAC6D,SAAS,CAAC6G,SAAS,CAAC;QAE5CA,SAAS,GAAG,IAAAC,6BAAQ,EAAClJ,IAAI,EAAE8I,UAAU,EAAEC,YAAY,EAAE;UACnDI,eAAe,EAAE,YAAY;UAC7BC,cAAc,EAAE;QAClB,CAAC,CAAC;QACFpL,GAAG,CAACF,IAAI,EAAG,WAAUgL,UAAW,EAAC,EAAEG,SAAS,CAAC;QAE7C,OAAOA,SAAS;MAClB,CAAC,MAAM;QACL,IAAIA,SAAS,GAAGpL,GAAG,CAACC,IAAI,EAAG,YAAWiL,YAAa,EAAC,CAAC;QACrD,IAAIE,SAAS,EAAE;UACbA,SAAS,GAAG1K,WAAC,CAAC6D,SAAS,CAAC6G,SAAS,CAAC;QACpC,CAAC,MAAM;UACLA,SAAS,GAAG,IAAAI,iCAAY,EAACrJ,IAAI,EAAE+I,YAAY,EAAE;YAC3CI,eAAe,EAAE;UACnB,CAAC,CAAC;UACFnL,GAAG,CAACF,IAAI,EAAG,YAAWiL,YAAa,EAAC,EAAEE,SAAS,CAAC;QAClD;QAEA,OAAO1K,WAAC,CAAC0G,gBAAgB,CAACgE,SAAS,EAAE1K,WAAC,CAACoI,UAAU,CAACmC,UAAU,CAAC,CAAC;MAChE;IACF,CAAC;EACH;AACF;AAEA,SAAS5G,kBAAkBA,CAACI,EAAU,EAAiC;EACrE,OACEA,EAAE,CACCgH,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAACxL,IAAI,IAAIQ,WAAC,CAACoI,UAAU,CAAC5I,IAAI,CAAC,CAAC,CAG/B8J,MAAM,CAAC,CAAC3C,MAAM,EAAEC,QAAQ,KAAK5G,WAAC,CAAC0G,gBAAgB,CAACC,MAAM,EAAEC,QAAQ,CAAC,CAAC;AAEzE;AAEA,SAASzE,UAAUA,CAACV,IAAc,EAAEC,KAAiB,EAAE;EACrD,MAAMuJ,QAAQ,GAAGxJ,IAAI,CAAC7B,IAAI,CAACsL,GAAG;EAC9B,IAAI,CAACD,QAAQ,EAAE;IAEb,OAAOxJ,IAAI,CAACI,KAAK,CAACqH,kBAAkB,CAAC,CAAC;EACxC;EAGA,IAAI,CAACxH,KAAK,CAACyJ,kBAAkB,EAAE;IAC7B,MAAM;MAAEC,QAAQ,GAAG;IAAG,CAAC,GAAG1J,KAAK;IAE/B,MAAMyJ,kBAAkB,GAAG1J,IAAI,CAACI,KAAK,CAACwJ,qBAAqB,CAAC,cAAc,CAAC;IAC3E5J,IAAI,CAACI,KAAK,CAACyJ,gBAAgB,CAAC,CAAC,CAACxJ,IAAI,CAAC;MACjCiC,EAAE,EAAEoH,kBAAkB;MACtBI,IAAI,EAAEvL,WAAC,CAACwG,aAAa,CAAC4E,QAAQ;IAChC,CAAC,CAAC;IAEF1J,KAAK,CAACyJ,kBAAkB,GAAGA,kBAAkB;EAC/C;EAEA,OAAOK,SAAS,CACdxL,WAAC,CAAC6D,SAAS,CAETnC,KAAK,CAACyJ,kBACR,CAAC,EACDF,QAAQ,CAACpB,KAAK,CAAC4B,IAAI,EACnBR,QAAQ,CAACpB,KAAK,CAAC6B,MACjB,CAAC;AACH;AAEA,SAASF,SAASA,CAChBL,kBAA8B,EAC9BQ,UAAmB,EACnBC,YAAqB,EACrB;EACA,MAAMC,eAAe,GACnBF,UAAU,IAAI,IAAI,GAAG3L,WAAC,CAAC8L,cAAc,CAACH,UAAU,CAAC,GAAG3L,WAAC,CAACuJ,WAAW,CAAC,CAAC;EAErE,MAAMwC,iBAAiB,GACrBH,YAAY,IAAI,IAAI,GAAG5L,WAAC,CAAC8L,cAAc,CAACF,YAAY,GAAG,CAAC,CAAC,GAAG5L,WAAC,CAACuJ,WAAW,CAAC,CAAC;EAE7E,OAAOyC,cAAQ,CAAC/E,UAAU,CAAC/D,GAAI;AACjC,gBAAgBiI,kBAAmB;AACnC,kBAAkBU,eAAgB;AAClC,oBAAoBE,iBAAkB;AACtC,IAAI;AACJ;AAEA,SAASrD,eAAeA,CAACjH,IAAc,EAAEjC,IAAY,EAAE;EACrD,MAAMyM,UAAU,GAAI,uBAAsBzM,IAAI,CAACyK,KAAK,CAAC,CAAC,CAAE,EAAC;EAEzD,OAAOxI,IAAI,CAACgB,mBAAmB,CAC5B,aAAYjD,IAAK,yDAAwDyM,UAAW,+LACvF,CAAC;AACH"}
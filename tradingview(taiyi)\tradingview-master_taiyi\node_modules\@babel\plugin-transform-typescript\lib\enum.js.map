{"version": 3, "names": ["_core", "require", "_assert", "_helperAnnotateAsPure", "ENUMS", "WeakMap", "buildEnumWrapper", "template", "expression", "transpileEnum", "path", "t", "node", "parentPath", "declare", "remove", "name", "id", "fill", "data", "isPure", "enumFill", "type", "isGlobal", "isProgram", "parent", "isSeen", "seen", "init", "objectExpression", "logicalExpression", "cloneNode", "ID", "enumIIFE", "Object", "assign", "INIT", "annotateAsPure", "toReplace", "isExportDeclaration", "replaceWith", "expressionStatement", "assignmentExpression", "scope", "registerDeclaration", "variableDeclaration", "variableDeclarator", "set", "getBindingIdentifier", "Error", "getData", "setData", "buildStringAssignment", "buildNumericAssignment", "buildEnumMember", "isString", "options", "enum<PERSON><PERSON><PERSON>", "x", "translateEnumValues", "assignments", "map", "memberName", "memberValue", "isStringLiteral", "ENUM", "NAME", "VALUE", "ASSIGNMENTS", "ReferencedIdentifier", "expr", "state", "has", "hasOwnBinding", "memberExpression", "skip", "enumSelfReferenceVisitor", "_ENUMS$get", "bindingIdentifier", "get", "Map", "constV<PERSON>ue", "lastName", "memberPath", "member", "isIdentifier", "value", "initializerPath", "initializer", "computeConstantValue", "undefined", "assert", "Infinity", "Number", "isNaN", "identifier", "String", "unaryExpression", "valueToNode", "isReferencedIdentifier", "traverse", "numericLiteral", "buildCodeFrameError", "lastRef", "stringLiteral", "binaryExpression", "prevMembers", "Set", "evaluate", "evaluateRef", "evalUnaryExpression", "evalBinaryExpression", "quasis", "length", "cooked", "paths", "str", "i", "isMemberExpression", "obj", "object", "prop", "property", "computed", "includes", "add", "resolve", "operator", "left", "right", "Math", "pow"], "sources": ["../src/enum.ts"], "sourcesContent": ["import { template, types as t } from \"@babel/core\";\nimport type { NodePath } from \"@babel/traverse\";\nimport assert from \"assert\";\nimport annotateAsPure from \"@babel/helper-annotate-as-pure\";\n\ntype t = typeof t;\n\nconst ENUMS = new WeakMap<t.Identifier, PreviousEnumMembers>();\n\nconst buildEnumWrapper = template.expression(\n  `\n    (function (ID) {\n      ASSIGNMENTS;\n      return ID;\n    })(INIT)\n  `,\n);\n\nexport default function transpileEnum(\n  path: NodePath<t.TSEnumDeclaration>,\n  t: t,\n) {\n  const { node, parentPath } = path;\n\n  if (node.declare) {\n    path.remove();\n    return;\n  }\n\n  const name = node.id.name;\n  const { fill, data, isPure } = enumFill(path, t, node.id);\n\n  switch (parentPath.type) {\n    case \"BlockStatement\":\n    case \"ExportNamedDeclaration\":\n    case \"Program\": {\n      // todo: Consider exclude program with import/export\n      // && !path.parent.body.some(n => t.isImportDeclaration(n) || t.isExportDeclaration(n));\n      const isGlobal = t.isProgram(path.parent);\n      const isSeen = seen(parentPath);\n\n      let init: t.Expression = t.objectExpression([]);\n      if (isSeen || isGlobal) {\n        init = t.logicalExpression(\"||\", t.cloneNode(fill.ID), init);\n      }\n      const enumIIFE = buildEnumWrapper({ ...fill, INIT: init });\n      if (isPure) annotateAsPure(enumIIFE);\n\n      if (isSeen) {\n        const toReplace = parentPath.isExportDeclaration() ? parentPath : path;\n        toReplace.replaceWith(\n          t.expressionStatement(\n            t.assignmentExpression(\"=\", t.cloneNode(node.id), enumIIFE),\n          ),\n        );\n      } else {\n        path.scope.registerDeclaration(\n          path.replaceWith(\n            t.variableDeclaration(isGlobal ? \"var\" : \"let\", [\n              t.variableDeclarator(node.id, enumIIFE),\n            ]),\n          )[0],\n        );\n      }\n      ENUMS.set(path.scope.getBindingIdentifier(name), data);\n      break;\n    }\n\n    default:\n      throw new Error(`Unexpected enum parent '${path.parent.type}`);\n  }\n\n  function seen(parentPath: NodePath<t.Node>): boolean {\n    if (parentPath.isExportDeclaration()) {\n      return seen(parentPath.parentPath);\n    }\n\n    if (parentPath.getData(name)) {\n      return true;\n    } else {\n      parentPath.setData(name, true);\n      return false;\n    }\n  }\n}\n\nconst buildStringAssignment = template(`\n  ENUM[\"NAME\"] = VALUE;\n`);\n\nconst buildNumericAssignment = template(`\n  ENUM[ENUM[\"NAME\"] = VALUE] = \"NAME\";\n`);\n\nconst buildEnumMember = (isString: boolean, options: Record<string, unknown>) =>\n  (isString ? buildStringAssignment : buildNumericAssignment)(options);\n\n/**\n * Generates the statement that fills in the variable declared by the enum.\n * `(function (E) { ... assignments ... })(E || (E = {}));`\n */\nfunction enumFill(path: NodePath<t.TSEnumDeclaration>, t: t, id: t.Identifier) {\n  const { enumValues: x, data, isPure } = translateEnumValues(path, t);\n  const assignments = x.map(([memberName, memberValue]) =>\n    buildEnumMember(t.isStringLiteral(memberValue), {\n      ENUM: t.cloneNode(id),\n      NAME: memberName,\n      VALUE: memberValue,\n    }),\n  );\n\n  return {\n    fill: {\n      ID: t.cloneNode(id),\n      ASSIGNMENTS: assignments,\n    },\n    data,\n    isPure,\n  };\n}\n\n/**\n * Maps the name of an enum member to its value.\n * We keep track of the previous enum members so you can write code like:\n *   enum E {\n *     X = 1 << 0,\n *     Y = 1 << 1,\n *     Z = X | Y,\n *   }\n */\ntype PreviousEnumMembers = Map<string, number | string>;\n\ntype EnumSelfReferenceVisitorState = {\n  seen: PreviousEnumMembers;\n  path: NodePath<t.TSEnumDeclaration>;\n  t: t;\n};\n\nfunction ReferencedIdentifier(\n  expr: NodePath<t.Identifier>,\n  state: EnumSelfReferenceVisitorState,\n) {\n  const { seen, path, t } = state;\n  const name = expr.node.name;\n  if (seen.has(name) && !expr.scope.hasOwnBinding(name)) {\n    expr.replaceWith(\n      t.memberExpression(t.cloneNode(path.node.id), t.cloneNode(expr.node)),\n    );\n    expr.skip();\n  }\n}\n\nconst enumSelfReferenceVisitor = {\n  ReferencedIdentifier,\n};\n\nexport function translateEnumValues(path: NodePath<t.TSEnumDeclaration>, t: t) {\n  const bindingIdentifier = path.scope.getBindingIdentifier(path.node.id.name);\n  const seen: PreviousEnumMembers = ENUMS.get(bindingIdentifier) ?? new Map();\n\n  // Start at -1 so the first enum member is its increment, 0.\n  let constValue: number | string | undefined = -1;\n  let lastName: string;\n  let isPure = true;\n\n  const enumValues: Array<[name: string, value: t.Expression]> = path\n    .get(\"members\")\n    .map(memberPath => {\n      const member = memberPath.node;\n      const name = t.isIdentifier(member.id) ? member.id.name : member.id.value;\n      const initializerPath = memberPath.get(\"initializer\");\n      const initializer = member.initializer;\n      let value: t.Expression;\n      if (initializer) {\n        constValue = computeConstantValue(initializerPath, seen);\n        if (constValue !== undefined) {\n          seen.set(name, constValue);\n          assert(\n            typeof constValue === \"number\" || typeof constValue === \"string\",\n          );\n          // We do not use `t.valueToNode` because `Infinity`/`NaN` might refer\n          // to a local variable. Even 1/0\n          // Revisit once https://github.com/microsoft/TypeScript/issues/55091\n          // is fixed. Note: we will have to distinguish between actual\n          // infinities and reference  to non-infinite variables names Infinity.\n          if (constValue === Infinity || Number.isNaN(constValue)) {\n            value = t.identifier(String(constValue));\n          } else if (constValue === -Infinity) {\n            value = t.unaryExpression(\"-\", t.identifier(\"Infinity\"));\n          } else {\n            value = t.valueToNode(constValue);\n          }\n        } else {\n          isPure &&= initializerPath.isPure();\n\n          if (initializerPath.isReferencedIdentifier()) {\n            ReferencedIdentifier(initializerPath, {\n              t,\n              seen,\n              path,\n            });\n          } else {\n            initializerPath.traverse(enumSelfReferenceVisitor, {\n              t,\n              seen,\n              path,\n            });\n          }\n\n          value = initializerPath.node;\n          seen.set(name, undefined);\n        }\n      } else if (typeof constValue === \"number\") {\n        constValue += 1;\n        value = t.numericLiteral(constValue);\n        seen.set(name, constValue);\n      } else if (typeof constValue === \"string\") {\n        throw path.buildCodeFrameError(\"Enum member must have initializer.\");\n      } else {\n        // create dynamic initializer: 1 + ENUM[\"PREVIOUS\"]\n        const lastRef = t.memberExpression(\n          t.cloneNode(path.node.id),\n          t.stringLiteral(lastName),\n          true,\n        );\n        value = t.binaryExpression(\"+\", t.numericLiteral(1), lastRef);\n        seen.set(name, undefined);\n      }\n\n      lastName = name;\n      return [name, value];\n    });\n\n  return {\n    isPure,\n    data: seen,\n    enumValues,\n  };\n}\n\n// Based on the TypeScript repository's `computeConstantValue` in `checker.ts`.\nfunction computeConstantValue(\n  path: NodePath,\n  prevMembers?: PreviousEnumMembers,\n  seen: Set<t.Identifier> = new Set(),\n): number | string | undefined {\n  return evaluate(path);\n\n  function evaluate(path: NodePath): number | string | undefined {\n    const expr = path.node;\n    switch (expr.type) {\n      case \"MemberExpression\":\n        return evaluateRef(path, prevMembers, seen);\n      case \"StringLiteral\":\n        return expr.value;\n      case \"UnaryExpression\":\n        return evalUnaryExpression(path as NodePath<t.UnaryExpression>);\n      case \"BinaryExpression\":\n        return evalBinaryExpression(path as NodePath<t.BinaryExpression>);\n      case \"NumericLiteral\":\n        return expr.value;\n      case \"ParenthesizedExpression\":\n        return evaluate(path.get(\"expression\"));\n      case \"Identifier\":\n        return evaluateRef(path, prevMembers, seen);\n      case \"TemplateLiteral\": {\n        if (expr.quasis.length === 1) {\n          return expr.quasis[0].value.cooked;\n        }\n\n        const paths = (path as NodePath<t.TemplateLiteral>).get(\"expressions\");\n        const quasis = expr.quasis;\n        let str = \"\";\n\n        for (let i = 0; i < quasis.length; i++) {\n          str += quasis[i].value.cooked;\n\n          if (i + 1 < quasis.length) {\n            const value = evaluateRef(paths[i], prevMembers, seen);\n            if (value === undefined) return undefined;\n            str += value;\n          }\n        }\n        return str;\n      }\n      default:\n        return undefined;\n    }\n  }\n\n  function evaluateRef(\n    path: NodePath,\n    prevMembers: PreviousEnumMembers,\n    seen: Set<t.Identifier>,\n  ): number | string | undefined {\n    if (path.isMemberExpression()) {\n      const expr = path.node;\n\n      const obj = expr.object;\n      const prop = expr.property;\n      if (\n        !t.isIdentifier(obj) ||\n        (expr.computed ? !t.isStringLiteral(prop) : !t.isIdentifier(prop))\n      ) {\n        return;\n      }\n      const bindingIdentifier = path.scope.getBindingIdentifier(obj.name);\n      const data = ENUMS.get(bindingIdentifier);\n      if (!data) return;\n      // @ts-expect-error checked above\n      return data.get(prop.computed ? prop.value : prop.name);\n    } else if (path.isIdentifier()) {\n      const name = path.node.name;\n\n      if ([\"Infinity\", \"NaN\"].includes(name)) {\n        return Number(name);\n      }\n\n      let value = prevMembers?.get(name);\n      if (value !== undefined) {\n        return value;\n      }\n\n      if (seen.has(path.node)) return;\n      seen.add(path.node);\n\n      value = computeConstantValue(path.resolve(), prevMembers, seen);\n      prevMembers?.set(name, value);\n      return value;\n    }\n  }\n\n  function evalUnaryExpression(\n    path: NodePath<t.UnaryExpression>,\n  ): number | string | undefined {\n    const value = evaluate(path.get(\"argument\"));\n    if (value === undefined) {\n      return undefined;\n    }\n\n    switch (path.node.operator) {\n      case \"+\":\n        return value;\n      case \"-\":\n        return -value;\n      case \"~\":\n        return ~value;\n      default:\n        return undefined;\n    }\n  }\n\n  function evalBinaryExpression(\n    path: NodePath<t.BinaryExpression>,\n  ): number | string | undefined {\n    const left = evaluate(path.get(\"left\")) as any;\n    if (left === undefined) {\n      return undefined;\n    }\n    const right = evaluate(path.get(\"right\")) as any;\n    if (right === undefined) {\n      return undefined;\n    }\n\n    switch (path.node.operator) {\n      case \"|\":\n        return left | right;\n      case \"&\":\n        return left & right;\n      case \">>\":\n        return left >> right;\n      case \">>>\":\n        return left >>> right;\n      case \"<<\":\n        return left << right;\n      case \"^\":\n        return left ^ right;\n      case \"*\":\n        return left * right;\n      case \"/\":\n        return left / right;\n      case \"+\":\n        return left + right;\n      case \"-\":\n        return left - right;\n      case \"%\":\n        return left % right;\n      case \"**\":\n        return left ** right;\n      default:\n        return undefined;\n    }\n  }\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAEA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,qBAAA,GAAAF,OAAA;AAIA,MAAMG,KAAK,GAAG,IAAIC,OAAO,CAAoC,CAAC;AAE9D,MAAMC,gBAAgB,GAAGC,cAAQ,CAACC,UAAU,CACzC;AACH;AACA;AACA;AACA;AACA,GACA,CAAC;AAEc,SAASC,aAAaA,CACnCC,IAAmC,EACnCC,CAAI,EACJ;EACA,MAAM;IAAEC,IAAI;IAAEC;EAAW,CAAC,GAAGH,IAAI;EAEjC,IAAIE,IAAI,CAACE,OAAO,EAAE;IAChBJ,IAAI,CAACK,MAAM,CAAC,CAAC;IACb;EACF;EAEA,MAAMC,IAAI,GAAGJ,IAAI,CAACK,EAAE,CAACD,IAAI;EACzB,MAAM;IAAEE,IAAI;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGC,QAAQ,CAACX,IAAI,EAAEC,CAAC,EAAEC,IAAI,CAACK,EAAE,CAAC;EAEzD,QAAQJ,UAAU,CAACS,IAAI;IACrB,KAAK,gBAAgB;IACrB,KAAK,wBAAwB;IAC7B,KAAK,SAAS;MAAE;QAGd,MAAMC,QAAQ,GAAGZ,CAAC,CAACa,SAAS,CAACd,IAAI,CAACe,MAAM,CAAC;QACzC,MAAMC,MAAM,GAAGC,IAAI,CAACd,UAAU,CAAC;QAE/B,IAAIe,IAAkB,GAAGjB,CAAC,CAACkB,gBAAgB,CAAC,EAAE,CAAC;QAC/C,IAAIH,MAAM,IAAIH,QAAQ,EAAE;UACtBK,IAAI,GAAGjB,CAAC,CAACmB,iBAAiB,CAAC,IAAI,EAAEnB,CAAC,CAACoB,SAAS,CAACb,IAAI,CAACc,EAAE,CAAC,EAAEJ,IAAI,CAAC;QAC9D;QACA,MAAMK,QAAQ,GAAG3B,gBAAgB,CAAA4B,MAAA,CAAAC,MAAA,KAAMjB,IAAI;UAAEkB,IAAI,EAAER;QAAI,EAAE,CAAC;QAC1D,IAAIR,MAAM,EAAE,IAAAiB,6BAAc,EAACJ,QAAQ,CAAC;QAEpC,IAAIP,MAAM,EAAE;UACV,MAAMY,SAAS,GAAGzB,UAAU,CAAC0B,mBAAmB,CAAC,CAAC,GAAG1B,UAAU,GAAGH,IAAI;UACtE4B,SAAS,CAACE,WAAW,CACnB7B,CAAC,CAAC8B,mBAAmB,CACnB9B,CAAC,CAAC+B,oBAAoB,CAAC,GAAG,EAAE/B,CAAC,CAACoB,SAAS,CAACnB,IAAI,CAACK,EAAE,CAAC,EAAEgB,QAAQ,CAC5D,CACF,CAAC;QACH,CAAC,MAAM;UACLvB,IAAI,CAACiC,KAAK,CAACC,mBAAmB,CAC5BlC,IAAI,CAAC8B,WAAW,CACd7B,CAAC,CAACkC,mBAAmB,CAACtB,QAAQ,GAAG,KAAK,GAAG,KAAK,EAAE,CAC9CZ,CAAC,CAACmC,kBAAkB,CAAClC,IAAI,CAACK,EAAE,EAAEgB,QAAQ,CAAC,CACxC,CACH,CAAC,CAAC,CAAC,CACL,CAAC;QACH;QACA7B,KAAK,CAAC2C,GAAG,CAACrC,IAAI,CAACiC,KAAK,CAACK,oBAAoB,CAAChC,IAAI,CAAC,EAAEG,IAAI,CAAC;QACtD;MACF;IAEA;MACE,MAAM,IAAI8B,KAAK,CAAE,2BAA0BvC,IAAI,CAACe,MAAM,CAACH,IAAK,EAAC,CAAC;EAClE;EAEA,SAASK,IAAIA,CAACd,UAA4B,EAAW;IACnD,IAAIA,UAAU,CAAC0B,mBAAmB,CAAC,CAAC,EAAE;MACpC,OAAOZ,IAAI,CAACd,UAAU,CAACA,UAAU,CAAC;IACpC;IAEA,IAAIA,UAAU,CAACqC,OAAO,CAAClC,IAAI,CAAC,EAAE;MAC5B,OAAO,IAAI;IACb,CAAC,MAAM;MACLH,UAAU,CAACsC,OAAO,CAACnC,IAAI,EAAE,IAAI,CAAC;MAC9B,OAAO,KAAK;IACd;EACF;AACF;AAEA,MAAMoC,qBAAqB,GAAG,IAAA7C,cAAQ,EAAE;AACxC;AACA,CAAC,CAAC;AAEF,MAAM8C,sBAAsB,GAAG,IAAA9C,cAAQ,EAAE;AACzC;AACA,CAAC,CAAC;AAEF,MAAM+C,eAAe,GAAGA,CAACC,QAAiB,EAAEC,OAAgC,KAC1E,CAACD,QAAQ,GAAGH,qBAAqB,GAAGC,sBAAsB,EAAEG,OAAO,CAAC;AAMtE,SAASnC,QAAQA,CAACX,IAAmC,EAAEC,CAAI,EAAEM,EAAgB,EAAE;EAC7E,MAAM;IAAEwC,UAAU,EAAEC,CAAC;IAAEvC,IAAI;IAAEC;EAAO,CAAC,GAAGuC,mBAAmB,CAACjD,IAAI,EAAEC,CAAC,CAAC;EACpE,MAAMiD,WAAW,GAAGF,CAAC,CAACG,GAAG,CAAC,CAAC,CAACC,UAAU,EAAEC,WAAW,CAAC,KAClDT,eAAe,CAAC3C,CAAC,CAACqD,eAAe,CAACD,WAAW,CAAC,EAAE;IAC9CE,IAAI,EAAEtD,CAAC,CAACoB,SAAS,CAACd,EAAE,CAAC;IACrBiD,IAAI,EAAEJ,UAAU;IAChBK,KAAK,EAAEJ;EACT,CAAC,CACH,CAAC;EAED,OAAO;IACL7C,IAAI,EAAE;MACJc,EAAE,EAAErB,CAAC,CAACoB,SAAS,CAACd,EAAE,CAAC;MACnBmD,WAAW,EAAER;IACf,CAAC;IACDzC,IAAI;IACJC;EACF,CAAC;AACH;AAmBA,SAASiD,oBAAoBA,CAC3BC,IAA4B,EAC5BC,KAAoC,EACpC;EACA,MAAM;IAAE5C,IAAI;IAAEjB,IAAI;IAAEC;EAAE,CAAC,GAAG4D,KAAK;EAC/B,MAAMvD,IAAI,GAAGsD,IAAI,CAAC1D,IAAI,CAACI,IAAI;EAC3B,IAAIW,IAAI,CAAC6C,GAAG,CAACxD,IAAI,CAAC,IAAI,CAACsD,IAAI,CAAC3B,KAAK,CAAC8B,aAAa,CAACzD,IAAI,CAAC,EAAE;IACrDsD,IAAI,CAAC9B,WAAW,CACd7B,CAAC,CAAC+D,gBAAgB,CAAC/D,CAAC,CAACoB,SAAS,CAACrB,IAAI,CAACE,IAAI,CAACK,EAAE,CAAC,EAAEN,CAAC,CAACoB,SAAS,CAACuC,IAAI,CAAC1D,IAAI,CAAC,CACtE,CAAC;IACD0D,IAAI,CAACK,IAAI,CAAC,CAAC;EACb;AACF;AAEA,MAAMC,wBAAwB,GAAG;EAC/BP;AACF,CAAC;AAEM,SAASV,mBAAmBA,CAACjD,IAAmC,EAAEC,CAAI,EAAE;EAAA,IAAAkE,UAAA;EAC7E,MAAMC,iBAAiB,GAAGpE,IAAI,CAACiC,KAAK,CAACK,oBAAoB,CAACtC,IAAI,CAACE,IAAI,CAACK,EAAE,CAACD,IAAI,CAAC;EAC5E,MAAMW,IAAyB,IAAAkD,UAAA,GAAGzE,KAAK,CAAC2E,GAAG,CAACD,iBAAiB,CAAC,YAAAD,UAAA,GAAI,IAAIG,GAAG,CAAC,CAAC;EAG3E,IAAIC,UAAuC,GAAG,CAAC,CAAC;EAChD,IAAIC,QAAgB;EACpB,IAAI9D,MAAM,GAAG,IAAI;EAEjB,MAAMqC,UAAsD,GAAG/C,IAAI,CAChEqE,GAAG,CAAC,SAAS,CAAC,CACdlB,GAAG,CAACsB,UAAU,IAAI;IACjB,MAAMC,MAAM,GAAGD,UAAU,CAACvE,IAAI;IAC9B,MAAMI,IAAI,GAAGL,CAAC,CAAC0E,YAAY,CAACD,MAAM,CAACnE,EAAE,CAAC,GAAGmE,MAAM,CAACnE,EAAE,CAACD,IAAI,GAAGoE,MAAM,CAACnE,EAAE,CAACqE,KAAK;IACzE,MAAMC,eAAe,GAAGJ,UAAU,CAACJ,GAAG,CAAC,aAAa,CAAC;IACrD,MAAMS,WAAW,GAAGJ,MAAM,CAACI,WAAW;IACtC,IAAIF,KAAmB;IACvB,IAAIE,WAAW,EAAE;MACfP,UAAU,GAAGQ,oBAAoB,CAACF,eAAe,EAAE5D,IAAI,CAAC;MACxD,IAAIsD,UAAU,KAAKS,SAAS,EAAE;QAC5B/D,IAAI,CAACoB,GAAG,CAAC/B,IAAI,EAAEiE,UAAU,CAAC;QAC1BU,OAAM,CACJ,OAAOV,UAAU,KAAK,QAAQ,IAAI,OAAOA,UAAU,KAAK,QAC1D,CAAC;QAMD,IAAIA,UAAU,KAAKW,QAAQ,IAAIC,MAAM,CAACC,KAAK,CAACb,UAAU,CAAC,EAAE;UACvDK,KAAK,GAAG3E,CAAC,CAACoF,UAAU,CAACC,MAAM,CAACf,UAAU,CAAC,CAAC;QAC1C,CAAC,MAAM,IAAIA,UAAU,KAAK,CAACW,QAAQ,EAAE;UACnCN,KAAK,GAAG3E,CAAC,CAACsF,eAAe,CAAC,GAAG,EAAEtF,CAAC,CAACoF,UAAU,CAAC,UAAU,CAAC,CAAC;QAC1D,CAAC,MAAM;UACLT,KAAK,GAAG3E,CAAC,CAACuF,WAAW,CAACjB,UAAU,CAAC;QACnC;MACF,CAAC,MAAM;QACL7D,MAAM,KAANA,MAAM,GAAKmE,eAAe,CAACnE,MAAM,CAAC,CAAC;QAEnC,IAAImE,eAAe,CAACY,sBAAsB,CAAC,CAAC,EAAE;UAC5C9B,oBAAoB,CAACkB,eAAe,EAAE;YACpC5E,CAAC;YACDgB,IAAI;YACJjB;UACF,CAAC,CAAC;QACJ,CAAC,MAAM;UACL6E,eAAe,CAACa,QAAQ,CAACxB,wBAAwB,EAAE;YACjDjE,CAAC;YACDgB,IAAI;YACJjB;UACF,CAAC,CAAC;QACJ;QAEA4E,KAAK,GAAGC,eAAe,CAAC3E,IAAI;QAC5Be,IAAI,CAACoB,GAAG,CAAC/B,IAAI,EAAE0E,SAAS,CAAC;MAC3B;IACF,CAAC,MAAM,IAAI,OAAOT,UAAU,KAAK,QAAQ,EAAE;MACzCA,UAAU,IAAI,CAAC;MACfK,KAAK,GAAG3E,CAAC,CAAC0F,cAAc,CAACpB,UAAU,CAAC;MACpCtD,IAAI,CAACoB,GAAG,CAAC/B,IAAI,EAAEiE,UAAU,CAAC;IAC5B,CAAC,MAAM,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;MACzC,MAAMvE,IAAI,CAAC4F,mBAAmB,CAAC,oCAAoC,CAAC;IACtE,CAAC,MAAM;MAEL,MAAMC,OAAO,GAAG5F,CAAC,CAAC+D,gBAAgB,CAChC/D,CAAC,CAACoB,SAAS,CAACrB,IAAI,CAACE,IAAI,CAACK,EAAE,CAAC,EACzBN,CAAC,CAAC6F,aAAa,CAACtB,QAAQ,CAAC,EACzB,IACF,CAAC;MACDI,KAAK,GAAG3E,CAAC,CAAC8F,gBAAgB,CAAC,GAAG,EAAE9F,CAAC,CAAC0F,cAAc,CAAC,CAAC,CAAC,EAAEE,OAAO,CAAC;MAC7D5E,IAAI,CAACoB,GAAG,CAAC/B,IAAI,EAAE0E,SAAS,CAAC;IAC3B;IAEAR,QAAQ,GAAGlE,IAAI;IACf,OAAO,CAACA,IAAI,EAAEsE,KAAK,CAAC;EACtB,CAAC,CAAC;EAEJ,OAAO;IACLlE,MAAM;IACND,IAAI,EAAEQ,IAAI;IACV8B;EACF,CAAC;AACH;AAGA,SAASgC,oBAAoBA,CAC3B/E,IAAc,EACdgG,WAAiC,EACjC/E,IAAuB,GAAG,IAAIgF,GAAG,CAAC,CAAC,EACN;EAC7B,OAAOC,QAAQ,CAAClG,IAAI,CAAC;EAErB,SAASkG,QAAQA,CAAClG,IAAc,EAA+B;IAC7D,MAAM4D,IAAI,GAAG5D,IAAI,CAACE,IAAI;IACtB,QAAQ0D,IAAI,CAAChD,IAAI;MACf,KAAK,kBAAkB;QACrB,OAAOuF,WAAW,CAACnG,IAAI,EAAEgG,WAAW,EAAE/E,IAAI,CAAC;MAC7C,KAAK,eAAe;QAClB,OAAO2C,IAAI,CAACgB,KAAK;MACnB,KAAK,iBAAiB;QACpB,OAAOwB,mBAAmB,CAACpG,IAAmC,CAAC;MACjE,KAAK,kBAAkB;QACrB,OAAOqG,oBAAoB,CAACrG,IAAoC,CAAC;MACnE,KAAK,gBAAgB;QACnB,OAAO4D,IAAI,CAACgB,KAAK;MACnB,KAAK,yBAAyB;QAC5B,OAAOsB,QAAQ,CAAClG,IAAI,CAACqE,GAAG,CAAC,YAAY,CAAC,CAAC;MACzC,KAAK,YAAY;QACf,OAAO8B,WAAW,CAACnG,IAAI,EAAEgG,WAAW,EAAE/E,IAAI,CAAC;MAC7C,KAAK,iBAAiB;QAAE;UACtB,IAAI2C,IAAI,CAAC0C,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE;YAC5B,OAAO3C,IAAI,CAAC0C,MAAM,CAAC,CAAC,CAAC,CAAC1B,KAAK,CAAC4B,MAAM;UACpC;UAEA,MAAMC,KAAK,GAAIzG,IAAI,CAAiCqE,GAAG,CAAC,aAAa,CAAC;UACtE,MAAMiC,MAAM,GAAG1C,IAAI,CAAC0C,MAAM;UAC1B,IAAII,GAAG,GAAG,EAAE;UAEZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,MAAM,CAACC,MAAM,EAAEI,CAAC,EAAE,EAAE;YACtCD,GAAG,IAAIJ,MAAM,CAACK,CAAC,CAAC,CAAC/B,KAAK,CAAC4B,MAAM;YAE7B,IAAIG,CAAC,GAAG,CAAC,GAAGL,MAAM,CAACC,MAAM,EAAE;cACzB,MAAM3B,KAAK,GAAGuB,WAAW,CAACM,KAAK,CAACE,CAAC,CAAC,EAAEX,WAAW,EAAE/E,IAAI,CAAC;cACtD,IAAI2D,KAAK,KAAKI,SAAS,EAAE,OAAOA,SAAS;cACzC0B,GAAG,IAAI9B,KAAK;YACd;UACF;UACA,OAAO8B,GAAG;QACZ;MACA;QACE,OAAO1B,SAAS;IACpB;EACF;EAEA,SAASmB,WAAWA,CAClBnG,IAAc,EACdgG,WAAgC,EAChC/E,IAAuB,EACM;IAC7B,IAAIjB,IAAI,CAAC4G,kBAAkB,CAAC,CAAC,EAAE;MAC7B,MAAMhD,IAAI,GAAG5D,IAAI,CAACE,IAAI;MAEtB,MAAM2G,GAAG,GAAGjD,IAAI,CAACkD,MAAM;MACvB,MAAMC,IAAI,GAAGnD,IAAI,CAACoD,QAAQ;MAC1B,IACE,CAAC/G,WAAC,CAAC0E,YAAY,CAACkC,GAAG,CAAC,KACnBjD,IAAI,CAACqD,QAAQ,GAAG,CAAChH,WAAC,CAACqD,eAAe,CAACyD,IAAI,CAAC,GAAG,CAAC9G,WAAC,CAAC0E,YAAY,CAACoC,IAAI,CAAC,CAAC,EAClE;QACA;MACF;MACA,MAAM3C,iBAAiB,GAAGpE,IAAI,CAACiC,KAAK,CAACK,oBAAoB,CAACuE,GAAG,CAACvG,IAAI,CAAC;MACnE,MAAMG,IAAI,GAAGf,KAAK,CAAC2E,GAAG,CAACD,iBAAiB,CAAC;MACzC,IAAI,CAAC3D,IAAI,EAAE;MAEX,OAAOA,IAAI,CAAC4D,GAAG,CAAC0C,IAAI,CAACE,QAAQ,GAAGF,IAAI,CAACnC,KAAK,GAAGmC,IAAI,CAACzG,IAAI,CAAC;IACzD,CAAC,MAAM,IAAIN,IAAI,CAAC2E,YAAY,CAAC,CAAC,EAAE;MAC9B,MAAMrE,IAAI,GAAGN,IAAI,CAACE,IAAI,CAACI,IAAI;MAE3B,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC4G,QAAQ,CAAC5G,IAAI,CAAC,EAAE;QACtC,OAAO6E,MAAM,CAAC7E,IAAI,CAAC;MACrB;MAEA,IAAIsE,KAAK,GAAGoB,WAAW,oBAAXA,WAAW,CAAE3B,GAAG,CAAC/D,IAAI,CAAC;MAClC,IAAIsE,KAAK,KAAKI,SAAS,EAAE;QACvB,OAAOJ,KAAK;MACd;MAEA,IAAI3D,IAAI,CAAC6C,GAAG,CAAC9D,IAAI,CAACE,IAAI,CAAC,EAAE;MACzBe,IAAI,CAACkG,GAAG,CAACnH,IAAI,CAACE,IAAI,CAAC;MAEnB0E,KAAK,GAAGG,oBAAoB,CAAC/E,IAAI,CAACoH,OAAO,CAAC,CAAC,EAAEpB,WAAW,EAAE/E,IAAI,CAAC;MAC/D+E,WAAW,YAAXA,WAAW,CAAE3D,GAAG,CAAC/B,IAAI,EAAEsE,KAAK,CAAC;MAC7B,OAAOA,KAAK;IACd;EACF;EAEA,SAASwB,mBAAmBA,CAC1BpG,IAAiC,EACJ;IAC7B,MAAM4E,KAAK,GAAGsB,QAAQ,CAAClG,IAAI,CAACqE,GAAG,CAAC,UAAU,CAAC,CAAC;IAC5C,IAAIO,KAAK,KAAKI,SAAS,EAAE;MACvB,OAAOA,SAAS;IAClB;IAEA,QAAQhF,IAAI,CAACE,IAAI,CAACmH,QAAQ;MACxB,KAAK,GAAG;QACN,OAAOzC,KAAK;MACd,KAAK,GAAG;QACN,OAAO,CAACA,KAAK;MACf,KAAK,GAAG;QACN,OAAO,CAACA,KAAK;MACf;QACE,OAAOI,SAAS;IACpB;EACF;EAEA,SAASqB,oBAAoBA,CAC3BrG,IAAkC,EACL;IAC7B,MAAMsH,IAAI,GAAGpB,QAAQ,CAAClG,IAAI,CAACqE,GAAG,CAAC,MAAM,CAAC,CAAQ;IAC9C,IAAIiD,IAAI,KAAKtC,SAAS,EAAE;MACtB,OAAOA,SAAS;IAClB;IACA,MAAMuC,KAAK,GAAGrB,QAAQ,CAAClG,IAAI,CAACqE,GAAG,CAAC,OAAO,CAAC,CAAQ;IAChD,IAAIkD,KAAK,KAAKvC,SAAS,EAAE;MACvB,OAAOA,SAAS;IAClB;IAEA,QAAQhF,IAAI,CAACE,IAAI,CAACmH,QAAQ;MACxB,KAAK,GAAG;QACN,OAAOC,IAAI,GAAGC,KAAK;MACrB,KAAK,GAAG;QACN,OAAOD,IAAI,GAAGC,KAAK;MACrB,KAAK,IAAI;QACP,OAAOD,IAAI,IAAIC,KAAK;MACtB,KAAK,KAAK;QACR,OAAOD,IAAI,KAAKC,KAAK;MACvB,KAAK,IAAI;QACP,OAAOD,IAAI,IAAIC,KAAK;MACtB,KAAK,GAAG;QACN,OAAOD,IAAI,GAAGC,KAAK;MACrB,KAAK,GAAG;QACN,OAAOD,IAAI,GAAGC,KAAK;MACrB,KAAK,GAAG;QACN,OAAOD,IAAI,GAAGC,KAAK;MACrB,KAAK,GAAG;QACN,OAAOD,IAAI,GAAGC,KAAK;MACrB,KAAK,GAAG;QACN,OAAOD,IAAI,GAAGC,KAAK;MACrB,KAAK,GAAG;QACN,OAAOD,IAAI,GAAGC,KAAK;MACrB,KAAK,IAAI;QACP,OAAAC,IAAA,CAAAC,GAAA,CAAOH,IAAI,EAAIC,KAAK;MACtB;QACE,OAAOvC,SAAS;IACpB;EACF;AACF", "ignoreList": []}
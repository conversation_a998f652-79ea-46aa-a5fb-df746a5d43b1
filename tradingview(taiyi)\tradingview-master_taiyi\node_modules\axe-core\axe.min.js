/*! axe v4.7.0
 * Copyright (c) 2023 Deque Systems, Inc.
 *
 * Your use of this Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 *
 * This entire copyright notice must appear in every copy of this file you
 * distribute or in any file that contains substantial portions of this source
 * code.
 */
!function i(window){var q=window,document=window.document;function te(e){return(te="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var axe=axe||{};function S(e){this.name="SupportError",this.cause=e.cause,this.message="`".concat(e.cause,"` - feature unsupported in your environment."),e.ruleId&&(this.ruleId=e.ruleId,this.message+=" Skipping ".concat(this.ruleId," rule.")),this.stack=(new Error).stack}axe.version="4.7.0","function"==typeof define&&define.amd&&define("axe-core",[],function(){return axe}),"object"===("undefined"==typeof module?"undefined":te(module))&&module.exports&&"function"==typeof i.toString&&(axe.source="("+i.toString()+')(typeof window === "object" ? window : this);',module.exports=axe),"function"==typeof window.getComputedStyle&&(window.axe=axe),(S.prototype=Object.create(Error.prototype)).constructor=S;var I=["node"],P=["variant"],M=["matches"],B=["chromium"],L=["noImplicit"],j=["noPresentational"],V=["node"],z=["nodes"],$=["node"],U=["relatedNodes"],H=["environmentData"],G=["environmentData"],W=["node"],K=["environmentData"],Y=["environmentData"],J=["environmentData"];function X(e){return ie(e)||re(e)||de(e)||oe()}function Q(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Z(e,t)}function Z(e,t){return(Z=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function ee(n){var a=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var e,t=ae(n),t=(e=a?(e=ae(this).constructor,Reflect.construct(t,arguments,e)):t.apply(this,arguments),this);if(e&&("object"===te(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return ne(t)}}function ne(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ae(e){return(ae=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function m(e,t){if(null==e)return{};var n,a=function(e,t){if(null==e)return{};var n,a,r={},o=Object.keys(e);for(a=0;a<o.length;a++)n=o[a],0<=t.indexOf(n)||(r[n]=e[n]);return r}(e,t);if(Object.getOwnPropertySymbols)for(var r=Object.getOwnPropertySymbols(e),o=0;o<r.length;o++)n=r[o],0<=t.indexOf(n)||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n]);return a}function v(e){return function(e){if(Array.isArray(e))return pe(e)}(e)||re(e)||de(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function re(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function p(){return(p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n,a=arguments[t];for(n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e}).apply(this,arguments)}function h(e,t){return ie(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,o,i,l=[],s=!0,u=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(a=o.call(n)).done)&&(l.push(a.value),l.length!==t);s=!0);}catch(e){u=!0,r=e}finally{try{if(!s&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(u)throw r}}return l}}(e,t)||de(e,t)||oe()}function oe(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ie(e){if(Array.isArray(e))return e}function le(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function se(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,ce(a.key),a)}}function ue(e,t,n){t&&se(e.prototype,t),n&&se(e,n),Object.defineProperty(e,"prototype",{writable:!1})}function ce(e){e=function(e,t){if("object"!==te(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"===te(n))throw new TypeError("@@toPrimitive must return a primitive value.");return n}(e,"string");return"symbol"===te(e)?e:String(e)}function f(e,t){var n,a,r,o,i="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(i)return a=!(n=!0),{s:function(){i=i.call(e)},n:function(){var e=i.next();return n=e.done,e},e:function(e){a=!0,r=e},f:function(){try{n||null==i.return||i.return()}finally{if(a)throw r}}};if(Array.isArray(e)||(i=de(e))||t&&e&&"number"==typeof e.length)return i&&(e=i),o=0,{s:t=function(){},n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:t};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function de(e,t){var n;if(e)return"string"==typeof e?pe(e,t):"Map"===(n="Object"===(n=Object.prototype.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:n)||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?pe(e,t):void 0}function pe(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}function te(e){return(te="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function e(e,t){return function(){return t||e((t={exports:{}}).exports,t),t.exports}}function fe(e,t){for(var n in t)be(e,n,{get:t[n],enumerable:!0})}function me(t,n,a){if(n&&"object"===te(n)||"function"==typeof n){var r,o=f(we(n));try{for(o.s();!(r=o.n()).done;)!function(){var e=r.value;ve.call(t,e)||"default"===e||be(t,e,{get:function(){return n[e]},enumerable:!(a=De(n,e))||a.enumerable})}()}catch(e){o.e(e)}finally{o.f()}}return t}function he(e){return me((t=be(null!=e?ge(ye(e)):{},"default",e&&e.__esModule&&"default"in e?{get:function(){return e.default},enumerable:!0}:{value:e,enumerable:!0}),be(t,"__esModule",{value:!0})),e);var t}var ge=Object.create,be=Object.defineProperty,ye=Object.getPrototypeOf,ve=Object.prototype.hasOwnProperty,we=Object.getOwnPropertyNames,De=Object.getOwnPropertyDescriptor,xe=e(function(i){"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i.isIdentStart=function(e){return"a"<=e&&e<="z"||"A"<=e&&e<="Z"||"-"===e||"_"===e},i.isIdent=function(e){return"a"<=e&&e<="z"||"A"<=e&&e<="Z"||"0"<=e&&e<="9"||"-"===e||"_"===e},i.isHex=function(e){return"a"<=e&&e<="f"||"A"<=e&&e<="F"||"0"<=e&&e<="9"},i.escapeIdentifier=function(e){for(var t=e.length,n="",a=0;a<t;){var r=e.charAt(a);if(i.identSpecialChars[r])n+="\\"+r;else if("_"===r||"-"===r||"A"<=r&&r<="Z"||"a"<=r&&r<="z"||0!==a&&"0"<=r&&r<="9")n+=r;else{r=r.charCodeAt(0);if(55296==(63488&r)){var o=e.charCodeAt(a++);if(55296!=(64512&r)||56320!=(64512&o))throw Error("UCS-2(decode): illegal sequence");r=((1023&r)<<10)+(1023&o)+65536}n+="\\"+r.toString(16)+" "}a++}return n},i.escapeStr=function(e){for(var t,n=e.length,a="",r=0;r<n;){var o=e.charAt(r);'"'===o?o='\\"':"\\"===o?o="\\\\":void 0!==(t=i.strReplacementsRev[o])&&(o=t),a+=o,r++}return'"'+a+'"'},i.identSpecialChars={"!":!0,'"':!0,"#":!0,$:!0,"%":!0,"&":!0,"'":!0,"(":!0,")":!0,"*":!0,"+":!0,",":!0,".":!0,"/":!0,";":!0,"<":!0,"=":!0,">":!0,"?":!0,"@":!0,"[":!0,"\\":!0,"]":!0,"^":!0,"`":!0,"{":!0,"|":!0,"}":!0,"~":!0},i.strReplacementsRev={"\n":"\\n","\r":"\\r","\t":"\\t","\f":"\\f","\v":"\\v"},i.singleQuoteEscapeChars={n:"\n",r:"\r",t:"\t",f:"\f","\\":"\\","'":"'"},i.doubleQuotesEscapeChars={n:"\n",r:"\r",t:"\t",f:"\f","\\":"\\",'"':'"'}}),Ee=e(function(e){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var b=xe();e.parseCssSelector=function(o,i,l,s,r,u){var c=o.length,d="";function p(e,t){var n="";for(i++,d=o.charAt(i);i<c;){if(d===e)return i++,n;if("\\"===d){i++;var a;if((d=o.charAt(i))===e)n+=e;else if(void 0!==(a=t[d]))n+=a;else{if(b.isHex(d)){var r=d;for(i++,d=o.charAt(i);b.isHex(d);)r+=d,i++,d=o.charAt(i);" "===d&&(i++,d=o.charAt(i)),n+=String.fromCharCode(parseInt(r,16));continue}n+=d}}else n+=d;i++,d=o.charAt(i)}return n}function f(){var e="";for(d=o.charAt(i);i<c;){if(!b.isIdent(d)){if("\\"!==d)return e;if(c<=++i)throw Error("Expected symbol but end of file reached.");if(d=o.charAt(i),!b.identSpecialChars[d]&&b.isHex(d)){var t=d;for(i++,d=o.charAt(i);b.isHex(d);)t+=d,i++,d=o.charAt(i);" "===d&&(i++,d=o.charAt(i)),e+=String.fromCharCode(parseInt(t,16));continue}}e+=d,i++,d=o.charAt(i)}return e}function m(){d=o.charAt(i);for(;" "===d||"\t"===d||"\n"===d||"\r"===d||"\f"===d;)i++,d=o.charAt(i)}function h(){var e=n();if(!e)return null;var t=e;for(d=o.charAt(i);","===d;){if(i++,m(),"selectors"!==t.type&&(t={type:"selectors",selectors:[e]}),!(e=n()))throw Error('Rule expected after ",".');t.selectors.push(e)}return t}function n(){m();var e={type:"ruleSet"},t=g();if(!t)return null;for(var n=e;t&&(t.type="rule",n.rule=t,n=t,m(),d=o.charAt(i),!(c<=i||","===d||")"===d));)if(r[d]){var a=d;if(i++,m(),!(t=g()))throw Error('Rule expected after "'+a+'".');t.nestingOperator=a}else(t=g())&&(t.nestingOperator=null);return e}function g(){for(var e=null;i<c;)if("*"===(d=o.charAt(i)))i++,(e=e||{}).tagName="*";else if(b.isIdentStart(d)||"\\"===d)(e=e||{}).tagName=f();else if("."===d)i++,((e=e||{}).classNames=e.classNames||[]).push(f());else if("#"===d)i++,(e=e||{}).id=f();else if("["===d){i++,m();var t={name:f()};if(m(),"]"===d)i++;else{var n="";if(s[d]&&(n=d,i++,d=o.charAt(i)),c<=i)throw Error('Expected "=" but end of file reached.');if("="!==d)throw Error('Expected "=" but "'+d+'" found.');t.operator=n+"=",i++,m();var a="";if(t.valueType="string",'"'===d)a=p('"',b.doubleQuotesEscapeChars);else if("'"===d)a=p("'",b.singleQuoteEscapeChars);else if(u&&"$"===d)i++,a=f(),t.valueType="substitute";else{for(;i<c&&"]"!==d;)a+=d,i++,d=o.charAt(i);a=a.trim()}if(m(),c<=i)throw Error('Expected "]" but end of file reached.');if("]"!==d)throw Error('Expected "]" but "'+d+'" found.');i++,t.value=a}((e=e||{}).attrs=e.attrs||[]).push(t)}else{if(":"!==d)break;i++;n=f(),t={name:n};if("("===d){i++;var r="";if(m(),"selector"===l[n])t.valueType="selector",r=h();else{if(t.valueType=l[n]||"string",'"'===d)r=p('"',b.doubleQuotesEscapeChars);else if("'"===d)r=p("'",b.singleQuoteEscapeChars);else if(u&&"$"===d)i++,r=f(),t.valueType="substitute";else{for(;i<c&&")"!==d;)r+=d,i++,d=o.charAt(i);r=r.trim()}m()}if(c<=i)throw Error('Expected ")" but end of file reached.');if(")"!==d)throw Error('Expected ")" but "'+d+'" found.');i++,t.value=r}((e=e||{}).pseudos=e.pseudos||[]).push(t)}return e}var e=h();if(i<c)throw Error('Rule expected but "'+o.charAt(i)+'" found.');return e}}),Fe=e(function(e){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o=xe();e.renderEntity=function t(e){var n="";switch(e.type){case"ruleSet":for(var a=e.rule,r=[];a;)a.nestingOperator&&r.push(a.nestingOperator),r.push(t(a)),a=a.rule;n=r.join(" ");break;case"selectors":n=e.selectors.map(t).join(", ");break;case"rule":e.tagName&&(n="*"===e.tagName?"*":o.escapeIdentifier(e.tagName)),e.id&&(n+="#"+o.escapeIdentifier(e.id)),e.classNames&&(n+=e.classNames.map(function(e){return"."+o.escapeIdentifier(e)}).join("")),e.attrs&&(n+=e.attrs.map(function(e){return"operator"in e?"substitute"===e.valueType?"["+o.escapeIdentifier(e.name)+e.operator+"$"+e.value+"]":"["+o.escapeIdentifier(e.name)+e.operator+o.escapeStr(e.value)+"]":"["+o.escapeIdentifier(e.name)+"]"}).join("")),e.pseudos&&(n+=e.pseudos.map(function(e){return e.valueType?"selector"===e.valueType?":"+o.escapeIdentifier(e.name)+"("+t(e.value)+")":"substitute"===e.valueType?":"+o.escapeIdentifier(e.name)+"($"+e.value+")":"numeric"===e.valueType?":"+o.escapeIdentifier(e.name)+"("+e.value+")":":"+o.escapeIdentifier(e.name)+"("+o.escapeIdentifier(e.value)+")":":"+o.escapeIdentifier(e.name)}).join(""));break;default:throw Error('Unknown entity type: "'+e.type+'".')}return n}}),Ae=e(function(e){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var t=Ee(),n=Fe();function a(){this.pseudos={},this.attrEqualityMods={},this.ruleNestingOperators={},this.substitutesEnabled=!1}a.prototype.registerSelectorPseudos=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,a=e;n<a.length;n++)this.pseudos[a[n]]="selector";return this},a.prototype.unregisterSelectorPseudos=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,a=e;n<a.length;n++)delete this.pseudos[a[n]];return this},a.prototype.registerNumericPseudos=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,a=e;n<a.length;n++)this.pseudos[a[n]]="numeric";return this},a.prototype.unregisterNumericPseudos=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,a=e;n<a.length;n++)delete this.pseudos[a[n]];return this},a.prototype.registerNestingOperators=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,a=e;n<a.length;n++)this.ruleNestingOperators[a[n]]=!0;return this},a.prototype.unregisterNestingOperators=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,a=e;n<a.length;n++)delete this.ruleNestingOperators[a[n]];return this},a.prototype.registerAttrEqualityMods=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,a=e;n<a.length;n++)this.attrEqualityMods[a[n]]=!0;return this},a.prototype.unregisterAttrEqualityMods=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,a=e;n<a.length;n++)delete this.attrEqualityMods[a[n]];return this},a.prototype.enableSubstitutes=function(){return this.substitutesEnabled=!0,this},a.prototype.disableSubstitutes=function(){return this.substitutesEnabled=!1,this},a.prototype.parse=function(e){return t.parseCssSelector(e,0,this.pseudos,this.attrEqualityMods,this.ruleNestingOperators,this.substitutesEnabled)},a.prototype.render=function(e){return n.renderEntity(e).trim()},e.CssSelectorParser=a}),Ce=e(function(e,t){"use strict";t.exports=function(){}}),ke=e(function(e,t){"use strict";var n=Ce()();t.exports=function(e){return e!==n&&null!==e}}),Te=e(function(e,t){"use strict";var o=ke(),n=Array.prototype.forEach,a=Object.create;t.exports=function(e){var r=a(null);return n.call(arguments,function(e){if(o(e)){var t,n=Object(e),a=r;for(t in n)a[t]=n[t]}}),r}}),Ne=e(function(e,t){"use strict";t.exports=function(){var e=Math.sign;return"function"==typeof e&&1===e(10)&&-1===e(-20)}}),Re=e(function(e,t){"use strict";t.exports=function(e){return e=Number(e),isNaN(e)||0===e?e:0<e?1:-1}}),Oe=e(function(e,t){"use strict";t.exports=Ne()()?Math.sign:Re()}),_e=e(function(e,t){"use strict";var n=Oe(),a=Math.abs,r=Math.floor;t.exports=function(e){return isNaN(e)?0:0!==(e=Number(e))&&isFinite(e)?n(e)*r(a(e)):e}}),Se=e(function(e,t){"use strict";var n=_e(),a=Math.max;t.exports=function(e){return a(0,n(e))}}),Ie=e(function(e,t){"use strict";var a=Se();t.exports=function(e,t,n){return isNaN(e)?0<=t?n&&t?t-1:t:1:!1!==e&&a(e)}}),Pe=e(function(e,t){"use strict";t.exports=function(e){if("function"!=typeof e)throw new TypeError(e+" is not a function");return e}}),Me=e(function(e,t){"use strict";var n=ke();t.exports=function(e){if(n(e))return e;throw new TypeError("Cannot use null or undefined")}}),Be=e(function(e,t){"use strict";var l=Pe(),s=Me(),u=Function.prototype.bind,c=Function.prototype.call,d=Object.keys,p=Object.prototype.propertyIsEnumerable;t.exports=function(o,i){return function(n,a){var e,r=arguments[2],t=arguments[3];return n=Object(s(n)),l(a),e=d(n),t&&e.sort("function"==typeof t?u.call(t,n):void 0),"function"!=typeof o&&(o=e[o]),c.call(o,e,function(e,t){return p.call(n,e)?c.call(a,r,n[e],e,n,t):i})}}}),Le=e(function(e,t){"use strict";t.exports=Be()("forEach")}),je=e(function(){}),qe=e(function(e,t){"use strict";t.exports=function(){var e=Object.assign;return"function"==typeof e&&(e(e={foo:"raz"},{bar:"dwa"},{trzy:"trzy"}),e.foo+e.bar+e.trzy==="razdwatrzy")}}),Ve=e(function(e,t){"use strict";t.exports=function(){try{return Object.keys("primitive"),!0}catch(e){return!1}}}),ze=e(function(e,t){"use strict";var n=ke(),a=Object.keys;t.exports=function(e){return a(n(e)?Object(e):e)}}),$e=e(function(e,t){"use strict";t.exports=Ve()()?Object.keys:ze()}),Ue=e(function(e,t){"use strict";var i=$e(),l=Me(),s=Math.max;t.exports=function(t,n){var a,e,r,o=s(arguments.length,2);for(t=Object(l(t)),r=function(e){try{t[e]=n[e]}catch(e){a=a||e}},e=1;e<o;++e)i(n=arguments[e]).forEach(r);if(void 0!==a)throw a;return t}}),He=e(function(e,t){"use strict";t.exports=qe()()?Object.assign:Ue()}),Ge=e(function(e,t){"use strict";var n=ke(),a={function:!0,object:!0};t.exports=function(e){return n(e)&&a[te(e)]||!1}}),We=e(function(e,a){"use strict";var r=He(),o=Ge(),i=ke(),l=Error.captureStackTrace;a.exports=function(e){var e=new Error(e),t=arguments[1],n=arguments[2];return i(n)||o(t)&&(n=t,t=null),i(n)&&r(e,n),i(t)&&(e.code=t),l&&l(e,a.exports),e}}),Ke=e(function(e,t){"use strict";var r=Me(),o=Object.defineProperty,i=Object.getOwnPropertyDescriptor,l=Object.getOwnPropertyNames,s=Object.getOwnPropertySymbols;t.exports=function(t,n){var a,e=Object(r(n));if(t=Object(r(t)),l(e).forEach(function(e){try{o(t,e,i(n,e))}catch(e){a=e}}),"function"==typeof s&&s(e).forEach(function(e){try{o(t,e,i(n,e))}catch(e){a=e}}),void 0!==a)throw a;return t}}),Ye=e(function(e,t){"use strict";function n(e,t){return t}var a,r,o,i,l,s=Se();try{Object.defineProperty(n,"length",{configurable:!0,writable:!1,enumerable:!1,value:1})}catch(e){}1===n.length?(a={configurable:!0,writable:!1,enumerable:!1},r=Object.defineProperty,t.exports=function(e,t){return t=s(t),e.length===t?e:(a.value=t,r(e,"length",a))}):(i=Ke(),l=[],o=function(e){var t,n=0;if(l[e])return l[e];for(t=[];e--;)t.push("a"+(++n).toString(36));return new Function("fn","return function ("+t.join(", ")+") { return fn.apply(this, arguments); };")},t.exports=function(e,t){if(t=s(t),e.length===t)return e;t=o(t)(e);try{i(t,e)}catch(e){}return t})}),Je=e(function(e,t){"use strict";t.exports=function(e){return null!=e}}),Xe=e(function(e,t){"use strict";var n=Je(),a={object:!0,function:!0,undefined:!0};t.exports=function(e){return!!n(e)&&hasOwnProperty.call(a,te(e))}}),Qe=e(function(e,t){"use strict";var n=Xe();t.exports=function(e){if(!n(e))return!1;try{return e.constructor?e.constructor.prototype===e:!1}catch(e){return!1}}}),Ze=e(function(e,t){"use strict";var n=Qe();t.exports=function(e){if("function"!=typeof e)return!1;if(!hasOwnProperty.call(e,"length"))return!1;try{if("number"!=typeof e.length)return!1;if("function"!=typeof e.call)return!1;if("function"!=typeof e.apply)return!1}catch(e){return!1}return!n(e)}}),et=e(function(e,t){"use strict";var n=Ze(),a=/^\s*class[\s{/}]/,r=Function.prototype.toString;t.exports=function(e){return!!n(e)&&!a.test(r.call(e))}}),tt=e(function(e,t){"use strict";var n="razdwatrzy";t.exports=function(){return"function"==typeof n.contains&&!0===n.contains("dwa")&&!1===n.contains("foo")}}),nt=e(function(e,t){"use strict";var n=String.prototype.indexOf;t.exports=function(e){return-1<n.call(this,e,arguments[1])}}),at=e(function(e,t){"use strict";t.exports=tt()()?String.prototype.contains:nt()}),rt=e(function(e,t){"use strict";var i=Je(),o=et(),l=He(),s=Te(),u=at();(t.exports=function(e,t){var n,a,r,o;return arguments.length<2||"string"!=typeof e?(o=t,t=e,e=null):o=arguments[2],i(e)?(n=u.call(e,"c"),a=u.call(e,"e"),r=u.call(e,"w")):a=!(n=r=!0),e={value:t,configurable:n,enumerable:a,writable:r},o?l(s(o),e):e}).gs=function(e,t,n){var a,r;return"string"!=typeof e?(r=n,n=t,t=e,e=null):r=arguments[3],i(t)?o(t)?i(n)?o(n)||(r=n,n=void 0):n=void 0:(r=t,t=n=void 0):t=void 0,e=i(e)?(a=u.call(e,"c"),u.call(e,"e")):!(a=!0),t={get:t,set:n,configurable:a,enumerable:e},r?l(s(r),t):t}}),ot=e(function(e,t){"use strict";var n=rt(),i=Pe(),l=Function.prototype.apply,s=Function.prototype.call,a=Object.create,r=Object.defineProperty,o=Object.defineProperties,u=Object.prototype.hasOwnProperty,c={configurable:!0,enumerable:!1,writable:!0},d=function(e,t){var n;return i(t),u.call(this,"__ee__")?n=this.__ee__:(n=c.value=a(null),r(this,"__ee__",c),c.value=null),n[e]?"object"===te(n[e])?n[e].push(t):n[e]=[n[e],t]:n[e]=t,this},p=function(e,t){var n,a;return i(t),a=this,d.call(this,e,n=function(){f.call(a,e,n),l.call(t,this,arguments)}),n.__eeOnceListener__=t,this},f=function(e,t){var n,a,r,o;if(i(t),u.call(this,"__ee__")&&(n=this.__ee__)[e])if(a=n[e],"object"===te(a))for(o=0;r=a[o];++o)r!==t&&r.__eeOnceListener__!==t||(2===a.length?n[e]=a[o?0:1]:a.splice(o,1));else a!==t&&a.__eeOnceListener__!==t||delete n[e];return this},m=function(e){var t,n,a,r,o;if(u.call(this,"__ee__")&&(r=this.__ee__[e]))if("object"===te(r)){for(n=arguments.length,o=new Array(n-1),t=1;t<n;++t)o[t-1]=arguments[t];for(r=r.slice(),t=0;a=r[t];++t)l.call(a,this,o)}else switch(arguments.length){case 1:s.call(r,this);break;case 2:s.call(r,this,arguments[1]);break;case 3:s.call(r,this,arguments[1],arguments[2]);break;default:for(n=arguments.length,o=new Array(n-1),t=1;t<n;++t)o[t-1]=arguments[t];l.call(r,this,o)}},h={on:d,once:p,off:f,emit:m},g={on:n(d),once:n(p),off:n(f),emit:n(m)},b=o({},g);t.exports=e=function(e){return null==e?a(b):o(Object(e),g)},e.methods=h}),it=e(function(e,t){"use strict";t.exports=function(){var e,t=Array.from;return"function"==typeof t&&(e=t(t=["raz","dwa"]),Boolean(e&&e!==t&&"dwa"===e[1]))}}),lt=e(function(e,t){"use strict";t.exports=function(){return"object"===("undefined"==typeof globalThis?"undefined":te(globalThis))&&!!globalThis&&globalThis.Array===Array}}),st=e(function(e,t){function n(){if("object"===("undefined"==typeof self?"undefined":te(self))&&self)return self;if("object"===(void 0===window?"undefined":te(window))&&window)return window;throw new Error("Unable to resolve global `this`")}t.exports=function(){if(this)return this;try{Object.defineProperty(Object.prototype,"__global__",{get:function(){return this},configurable:!0})}catch(e){return n()}try{return __global__?__global__:n()}finally{delete Object.prototype.__global__}}()}),ut=e(function(e,t){"use strict";t.exports=lt()()?globalThis:st()}),ct=e(function(e,t){"use strict";var n=ut(),a={object:!0,symbol:!0};t.exports=function(){var e,t=n.Symbol;if("function"!=typeof t)return!1;e=t("test symbol");try{String(e)}catch(e){return!1}return!!a[te(t.iterator)]&&!!a[te(t.toPrimitive)]&&!!a[te(t.toStringTag)]}}),dt=e(function(e,t){"use strict";t.exports=function(e){return!!e&&("symbol"===te(e)||!!e.constructor&&"Symbol"===e.constructor.name&&"Symbol"===e[e.constructor.toStringTag])}}),pt=e(function(e,t){"use strict";var n=dt();t.exports=function(e){if(n(e))return e;throw new TypeError(e+" is not a symbol")}}),ft=e(function(e,t){"use strict";var r=rt(),n=Object.create,o=Object.defineProperty,i=Object.prototype,l=n(null);t.exports=function(e){for(var t,n,a=0;l[e+(a||"")];)++a;return l[e+=a||""]=!0,o(i,t="@@"+e,r.gs(null,function(e){n||(n=!0,o(this,t,r(e)),n=!1)})),t}}),mt=e(function(e,t){"use strict";var n=rt(),a=ut().Symbol;t.exports=function(e){return Object.defineProperties(e,{hasInstance:n("",a&&a.hasInstance||e("hasInstance")),isConcatSpreadable:n("",a&&a.isConcatSpreadable||e("isConcatSpreadable")),iterator:n("",a&&a.iterator||e("iterator")),match:n("",a&&a.match||e("match")),replace:n("",a&&a.replace||e("replace")),search:n("",a&&a.search||e("search")),species:n("",a&&a.species||e("species")),split:n("",a&&a.split||e("split")),toPrimitive:n("",a&&a.toPrimitive||e("toPrimitive")),toStringTag:n("",a&&a.toStringTag||e("toStringTag")),unscopables:n("",a&&a.unscopables||e("unscopables"))})}}),ht=e(function(e,t){"use strict";var n=rt(),a=pt(),r=Object.create(null);t.exports=function(t){return Object.defineProperties(t,{for:n(function(e){return r[e]||(r[e]=t(String(e)))}),keyFor:n(function(e){for(var t in a(e),r)if(r[t]===e)return t})})}}),gt=e(function(e,t){"use strict";var n,a,r,o=rt(),i=pt(),l=ut().Symbol,s=ft(),u=mt(),c=ht(),d=Object.create,p=Object.defineProperties,f=Object.defineProperty;if("function"==typeof l)try{String(l()),r=!0}catch(e){}else l=null;a=function(e){if(this instanceof a)throw new TypeError("Symbol is not a constructor");return n(e)},t.exports=n=function e(t){var n;if(this instanceof e)throw new TypeError("Symbol is not a constructor");return r?l(t):(n=d(a.prototype),t=void 0===t?"":String(t),p(n,{__description__:o("",t),__name__:o("",s(t))}))},u(n),c(n),p(a.prototype,{constructor:o(n),toString:o("",function(){return this.__name__})}),p(n.prototype,{toString:o(function(){return"Symbol ("+i(this).__description__+")"}),valueOf:o(function(){return i(this)})}),f(n.prototype,n.toPrimitive,o("",function(){var e=i(this);return"symbol"===te(e)?e:e.toString()})),f(n.prototype,n.toStringTag,o("c","Symbol")),f(a.prototype,n.toStringTag,o("c",n.prototype[n.toStringTag])),f(a.prototype,n.toPrimitive,o("c",n.prototype[n.toPrimitive]))}),bt=e(function(e,t){"use strict";t.exports=ct()()?ut().Symbol:gt()}),yt=e(function(e,t){"use strict";var n=Object.prototype.toString,a=n.call(function(){return arguments}());t.exports=function(e){return n.call(e)===a}}),vt=e(function(e,t){"use strict";var n=Object.prototype.toString,a=RegExp.prototype.test.bind(/^[object [A-Za-z0-9]*Function]$/);t.exports=function(e){return"function"==typeof e&&a(n.call(e))}}),wt=e(function(e,t){"use strict";var n=Object.prototype.toString,a=n.call("");t.exports=function(e){return"string"==typeof e||e&&"object"===te(e)&&(e instanceof String||n.call(e)===a)||!1}}),Dt=e(function(e,t){"use strict";var f=bt().iterator,m=yt(),h=vt(),g=Se(),b=Pe(),y=Me(),v=ke(),w=wt(),D=Array.isArray,x=Function.prototype.call,E={configurable:!0,enumerable:!0,writable:!0,value:null},F=Object.defineProperty;t.exports=function(e){var t,n,a,r,o,i,l,s,u,c,d=arguments[1],p=arguments[2];if(e=Object(y(e)),v(d)&&b(d),this&&this!==Array&&h(this))t=this;else{if(!d){if(m(e))return 1!==(o=e.length)?Array.apply(null,e):((r=new Array(1))[0]=e[0],r);if(D(e)){for(r=new Array(o=e.length),n=0;n<o;++n)r[n]=e[n];return r}}r=[]}if(!D(e))if(void 0!==(u=e[f])){for(l=b(u).call(e),t&&(r=new t),s=l.next(),n=0;!s.done;)c=d?x.call(d,p,s.value,n):s.value,t?(E.value=c,F(r,n,E)):r[n]=c,s=l.next(),++n;o=n}else if(w(e)){for(o=e.length,t&&(r=new t),a=n=0;n<o;++n)c=e[n],n+1<o&&55296<=(i=c.charCodeAt(0))&&i<=56319&&(c+=e[++n]),c=d?x.call(d,p,c,a):c,t?(E.value=c,F(r,a,E)):r[a]=c,++a;o=a}if(void 0===o)for(o=g(e.length),t&&(r=new t(o)),n=0;n<o;++n)c=d?x.call(d,p,e[n],n):e[n],t?(E.value=c,F(r,n,E)):r[n]=c;return t&&(E.value=null,r.length=o),r}}),xt=e(function(e,t){"use strict";t.exports=it()()?Array.from:Dt()}),Et=e(function(e,t){"use strict";var n=xt(),a=Array.isArray;t.exports=function(e){return a(e)?e:n(e)}}),Ft=e(function(e,t){"use strict";var n=Et(),a=ke(),r=Pe(),o=Array.prototype.slice,i=function(n){return this.map(function(e,t){return e?e(n[t]):n[t]}).concat(o.call(n,this.length))};t.exports=function(e){return(e=n(e)).forEach(function(e){a(e)&&r(e)}),i.bind(e)}}),At=e(function(e,t){"use strict";var n=Pe();t.exports=function(e){var t;return"function"==typeof e?{set:e,get:e}:(t={get:n(e.get)},void 0!==e.set?(t.set=n(e.set),e.delete&&(t.delete=n(e.delete)),e.clear&&(t.clear=n(e.clear))):t.set=t.get,t)}}),Ct=e(function(e,t){"use strict";var g=We(),b=Ye(),y=rt(),n=ot().methods,v=Ft(),w=At(),D=Function.prototype.apply,x=Function.prototype.call,E=Object.create,F=Object.defineProperties,A=n.on,C=n.emit;t.exports=function(r,t,e){var o,i,l,n,a,s,u,c,d,p,f,m=E(null),h=!1!==t?t:isNaN(r.length)?1:r.length;return e.normalizer&&(p=w(e.normalizer),i=p.get,l=p.set,n=p.delete,a=p.clear),null!=e.resolvers&&(f=v(e.resolvers)),p=i?b(function(e){var t,n,a=arguments;if(f&&(a=f(a)),null!==(t=i(a))&&hasOwnProperty.call(m,t))return u&&o.emit("get",t,a,this),m[t];if(n=1===a.length?x.call(r,this,a[0]):D.call(r,this,a),null===t){if(null!==(t=i(a)))throw g("Circular invocation","CIRCULAR_INVOCATION");t=l(a)}else if(hasOwnProperty.call(m,t))throw g("Circular invocation","CIRCULAR_INVOCATION");return m[t]=n,c&&o.emit("set",t,null,n),n},h):0===t?function(){var e;if(hasOwnProperty.call(m,"data"))return u&&o.emit("get","data",arguments,this),m.data;if(e=arguments.length?D.call(r,this,arguments):x.call(r,this),hasOwnProperty.call(m,"data"))throw g("Circular invocation","CIRCULAR_INVOCATION");return m.data=e,c&&o.emit("set","data",null,e),e}:function(e){var t,n=arguments;if(f&&(n=f(arguments)),t=String(n[0]),hasOwnProperty.call(m,t))return u&&o.emit("get",t,n,this),m[t];if(n=1===n.length?x.call(r,this,n[0]):D.call(r,this,n),hasOwnProperty.call(m,t))throw g("Circular invocation","CIRCULAR_INVOCATION");return m[t]=n,c&&o.emit("set",t,null,n),n},o={original:r,memoized:p,profileName:e.profileName,get:function(e){return f&&(e=f(e)),i?i(e):String(e[0])},has:function(e){return hasOwnProperty.call(m,e)},delete:function(e){var t;hasOwnProperty.call(m,e)&&(n&&n(e),t=m[e],delete m[e],d)&&o.emit("delete",e,t)},clear:function(){var e=m;a&&a(),m=E(null),o.emit("clear",e)},on:function(e,t){return"get"===e?u=!0:"set"===e?c=!0:"delete"===e&&(d=!0),A.call(this,e,t)},emit:C,updateEnv:function(){r=o.original}},e=i?b(function(e){var t=arguments;f&&(t=f(t)),null!==(t=i(t))&&o.delete(t)},h):0===t?function(){return o.delete("data")}:function(e){return f&&(e=f(arguments)[0]),o.delete(e)},h=b(function(){var e=arguments;return 0===t?m.data:(f&&(e=f(e)),e=i?i(e):String(e[0]),m[e])}),s=b(function(){var e=arguments;return 0===t?o.has("data"):(f&&(e=f(e)),null!==(e=i?i(e):String(e[0]))&&o.has(e))}),F(p,{__memoized__:y(!0),delete:y(e),clear:y(o.clear),_get:y(h),_has:y(s)}),o}}),kt=e(function(e,t){"use strict";var o=Pe(),i=Le(),l=je(),s=Ct(),u=Ie();t.exports=function e(t){var n,a,r;if(o(t),(n=Object(arguments[1])).async&&n.promise)throw new Error("Options 'async' and 'promise' cannot be used together");return hasOwnProperty.call(t,"__memoized__")&&!n.force?t:(a=u(n.length,t.length,n.async&&l.async),r=s(t,a,n),i(l,function(e,t){n[t]&&e(n[t],r,n)}),e.__profiler__&&e.__profiler__(r),r.updateEnv(),r.memoized)}}),Tt=e(function(e,t){"use strict";t.exports=function(e){var t,n,a=e.length;if(!a)return"";for(t=String(e[n=0]);--a;)t+=""+e[++n];return t}}),Nt=e(function(e,t){"use strict";t.exports=function(r){return r?function(e){for(var t=String(e[0]),n=0,a=r;--a;)t+=""+e[++n];return t}:function(){return""}}}),Rt=e(function(e,t){"use strict";t.exports=function(){var e=Number.isNaN;return"function"==typeof e&&!e({})&&e(NaN)&&!e(34)}}),Ot=e(function(e,t){"use strict";t.exports=function(e){return e!=e}}),_t=e(function(e,t){"use strict";t.exports=Rt()()?Number.isNaN:Ot()}),St=e(function(e,t){"use strict";var r=_t(),o=Se(),i=Me(),l=Array.prototype.indexOf,s=Object.prototype.hasOwnProperty,u=Math.abs,c=Math.floor;t.exports=function(e){var t,n,a;if(!r(e))return l.apply(this,arguments);for(n=o(i(this).length),e=arguments[1],t=e=isNaN(e)?0:0<=e?c(e):o(this.length)-c(u(e));t<n;++t)if(s.call(this,t)&&(a=this[t],r(a)))return t;return-1}}),It=e(function(e,t){"use strict";var u=St(),n=Object.create;t.exports=function(){var o=0,l=[],s=n(null);return{get:function(e){var t,n=0,a=l,r=e.length;if(0===r)return a[r]||null;if(a=a[r]){for(;n<r-1;){if(-1===(t=u.call(a[0],e[n])))return null;a=a[1][t],++n}return-1===(t=u.call(a[0],e[n]))?null:a[1][t]||null}return null},set:function(e){var t,n=0,a=l,r=e.length;if(0===r)a[r]=++o;else{for(a[r]||(a[r]=[[],[]]),a=a[r];n<r-1;)-1===(t=u.call(a[0],e[n]))&&(t=a[0].push(e[n])-1,a[1].push([[],[]])),a=a[1][t],++n;-1===(t=u.call(a[0],e[n]))&&(t=a[0].push(e[n])-1),a[1][t]=++o}return s[o]=e,o},delete:function(e){var t,n=0,a=l,r=s[e],o=r.length,i=[];if(0===o)delete a[o];else if(a=a[o]){for(;n<o-1;){if(-1===(t=u.call(a[0],r[n])))return;i.push(a,t),a=a[1][t],++n}if(-1===(t=u.call(a[0],r[n])))return;for(e=a[1][t],a[0].splice(t,1),a[1].splice(t,1);!a[0].length&&i.length;)t=i.pop(),(a=i.pop())[0].splice(t,1),a[1].splice(t,1)}delete s[e]},clear:function(){l=[],s=n(null)}}}}),Pt=e(function(e,t){"use strict";var r=St();t.exports=function(){var t=0,n=[],a=[];return{get:function(e){e=r.call(n,e[0]);return-1===e?null:a[e]},set:function(e){return n.push(e[0]),a.push(++t),t},delete:function(e){e=r.call(a,e);-1!==e&&(n.splice(e,1),a.splice(e,1))},clear:function(){n=[],a=[]}}}}),Mt=e(function(e,t){"use strict";var u=St(),n=Object.create;t.exports=function(i){var r=0,l=[[],[]],s=n(null);return{get:function(e){for(var t,n=0,a=l;n<i-1;){if(-1===(t=u.call(a[0],e[n])))return null;a=a[1][t],++n}return-1!==(t=u.call(a[0],e[n]))&&a[1][t]||null},set:function(e){for(var t,n=0,a=l;n<i-1;)-1===(t=u.call(a[0],e[n]))&&(t=a[0].push(e[n])-1,a[1].push([[],[]])),a=a[1][t],++n;return-1===(t=u.call(a[0],e[n]))&&(t=a[0].push(e[n])-1),a[1][t]=++r,s[r]=e,r},delete:function(e){for(var t,n=0,a=l,r=[],o=s[e];n<i-1;){if(-1===(t=u.call(a[0],o[n])))return;r.push(a,t),a=a[1][t],++n}if(-1!==(t=u.call(a[0],o[n]))){for(e=a[1][t],a[0].splice(t,1),a[1].splice(t,1);!a[0].length&&r.length;)t=r.pop(),(a=r.pop())[0].splice(t,1),a[1].splice(t,1);delete s[e]}},clear:function(){l=[[],[]],s=n(null)}}}}),Bt=e(function(e,t){"use strict";var n=Pe(),a=Le(),l=Function.prototype.call;t.exports=function(e,r){var o={},i=arguments[2];return n(r),a(e,function(e,t,n,a){o[t]=l.call(r,i,e,t,n,a)}),o}}),Lt=e(function(e,t){"use strict";function o(e){if("function"!=typeof e)throw new TypeError(e+" is not a function");return e}function n(e){var t,n,a=document.createTextNode(""),r=0;return new e(function(){var e;if(t)n&&(t=n.concat(t));else{if(!n)return;t=n}if(n=t,t=null,"function"==typeof n)e=n,n=null,e();else for(a.data=r=++r%2;n;)e=n.shift(),n.length||(n=null),e()}).observe(a,{characterData:!0}),function(e){o(e),t?"function"==typeof t?t=[t,e]:t.push(e):(t=e,a.data=r=++r%2)}}t.exports=function(){if("object"===("undefined"==typeof process?"undefined":te(process))&&process&&"function"==typeof process.nextTick)return process.nextTick;if("function"==typeof queueMicrotask)return function(e){queueMicrotask(o(e))};if("object"===(void 0===document?"undefined":te(document))&&document){if("function"==typeof MutationObserver)return n(MutationObserver);if("function"==typeof WebKitMutationObserver)return n(WebKitMutationObserver)}return"function"==typeof setImmediate?function(e){setImmediate(o(e))}:"function"==typeof setTimeout||"object"===("undefined"==typeof setTimeout?"undefined":te(setTimeout))?function(e){setTimeout(o(e),0)}:null}()}),jt=e(function(){"use strict";var p=xt(),t=Bt(),n=Ke(),r=Ye(),f=Lt(),m=Array.prototype.slice,h=Function.prototype.apply,g=Object.create;je().async=function(e,i){var l,s,u,c=g(null),d=g(null),o=i.memoized,a=i.original;i.memoized=r(function(e){var t=arguments,n=t[t.length-1];return"function"==typeof n&&(l=n,t=m.call(t,0,-1)),o.apply(s=this,u=t)},o);try{n(i.memoized,o)}catch(e){}i.on("get",function(t){var n,a,r;l&&(c[t]?("function"==typeof c[t]?c[t]=[c[t],l]:c[t].push(l),l=null):(n=l,a=s,r=u,l=s=u=null,f(function(){var e;hasOwnProperty.call(d,t)?(e=d[t],i.emit("getasync",t,r,a),h.call(n,e.context,e.args)):(l=n,s=a,u=r,o.apply(a,r))})))}),i.original=function(){var e,t,n,o;return l?(e=p(arguments),n=l,l=s=u=null,e.push(t=function e(t){var n,a,r=e.id;if(null==r)f(h.bind(e,this,arguments));else if(delete e.id,n=c[r],delete c[r],n)return a=p(arguments),i.has(r)&&(t?i.delete(r):(d[r]={context:this,args:a},i.emit("setasync",r,"function"==typeof n?1:n.length))),"function"==typeof n?o=h.call(n,this,a):n.forEach(function(e){o=h.call(e,this,a)},this),o}),o=h.call(a,this,e),t.cb=n,l=t,o):h.call(a,this,arguments)},i.on("set",function(e){l?(c[e]?"function"==typeof c[e]?c[e]=[c[e],l.cb]:c[e].push(l.cb):c[e]=l.cb,delete l.cb,l.id=e,l=null):i.delete(e)}),i.on("delete",function(e){var t;hasOwnProperty.call(c,e)||d[e]&&(t=d[e],delete d[e],i.emit("deleteasync",e,m.call(t.args,1)))}),i.on("clear",function(){var e=d;d=g(null),i.emit("clearasync",t(e,function(e){return m.call(e.args,1)}))})}}),qt=e(function(e,t){"use strict";var n=Array.prototype.forEach,a=Object.create;t.exports=function(e){var t=a(null);return n.call(arguments,function(e){t[e]=!0}),t}}),Vt=e(function(e,t){"use strict";t.exports=function(e){return"function"==typeof e}}),zt=e(function(e,t){"use strict";var n=Vt();t.exports=function(e){try{return e&&n(e.toString)?e.toString():String(e)}catch(e){throw new TypeError("Passed argument cannot be stringifed")}}}),$t=e(function(e,t){"use strict";var n=Me(),a=zt();t.exports=function(e){return a(n(e))}}),Ut=e(function(e,t){"use strict";var n=Vt();t.exports=function(e){try{return e&&n(e.toString)?e.toString():String(e)}catch(e){return"<Non-coercible to string value>"}}}),Ht=e(function(e,t){"use strict";var n=Ut(),a=/[\n\r\u2028\u2029]/g;t.exports=function(e){e=n(e);return e=(e=100<e.length?e.slice(0,99)+"…":e).replace(a,function(e){return JSON.stringify(e).slice(1,-1)})}}),Gt=e(function(e,t){function n(e){return!!e&&("object"===te(e)||"function"==typeof e)&&"function"==typeof e.then}t.exports=n,t.exports.default=n}),Wt=e(function(){"use strict";var t=Bt(),e=qt(),n=$t(),a=Ht(),f=Gt(),m=Lt(),r=Object.create,o=e("then","then:finally","done","done:finally");je().promise=function(s,u){var c=r(null),d=r(null),p=r(null);if(!0===s)s=null;else if(s=n(s),!o[s])throw new TypeError("'"+a(s)+"' is not valid promise mode");u.on("set",function(n,e,t){var a=!1;if(f(t)){c[n]=1,p[n]=t;var r=function(e){var t=c[n];if(a)throw new Error("Memoizee error: Detected unordered then|done & finally resolution, which in turn makes proper detection of success/failure impossible (when in 'done:finally' mode)\nConsider to rely on 'then' or 'done' mode instead.");t&&(delete c[n],d[n]=e,u.emit("setasync",n,t))},o=function(){a=!0,c[n]&&(delete c[n],delete p[n],u.delete(n))},i=s;if("then"===(i=i||"then")){var l=function(){m(o)};"function"==typeof(t=t.then(function(e){m(r.bind(this,e))},l)).finally&&t.finally(l)}else if("done"===i){if("function"!=typeof t.done)throw new Error("Memoizee error: Retrieved promise does not implement 'done' in 'done' mode");t.done(r,o)}else if("done:finally"===i){if("function"!=typeof t.done)throw new Error("Memoizee error: Retrieved promise does not implement 'done' in 'done:finally' mode");if("function"!=typeof t.finally)throw new Error("Memoizee error: Retrieved promise does not implement 'finally' in 'done:finally' mode");t.done(r),t.finally(o)}}else d[n]=t,u.emit("setasync",n,1)}),u.on("get",function(e,t,n){var a,r;c[e]?++c[e]:(a=p[e],r=function(){u.emit("getasync",e,t,n)},f(a)?"function"==typeof a.done?a.done(r):a.then(function(){m(r)}):r())}),u.on("delete",function(e){var t;delete p[e],c[e]?delete c[e]:hasOwnProperty.call(d,e)&&(t=d[e],delete d[e],u.emit("deleteasync",e,[t]))}),u.on("clear",function(){var e=d;d=r(null),c=r(null),p=r(null),u.emit("clearasync",t(e,function(e){return[e]}))})}}),Kt=e(function(){"use strict";var r=Pe(),o=Le(),i=je(),l=Function.prototype.apply;i.dispose=function(n,e,t){var a;r(n),t.async&&i.async||t.promise&&i.promise?(e.on("deleteasync",a=function(e,t){l.call(n,null,t)}),e.on("clearasync",function(e){o(e,function(e,t){a(t,e)})})):(e.on("delete",a=function(e,t){n(t)}),e.on("clear",function(e){o(e,function(e,t){a(t,e)})}))}}),Yt=e(function(e,t){"use strict";t.exports=2147483647}),Jt=e(function(e,t){"use strict";var n=Se(),a=Yt();t.exports=function(e){if(e=n(e),a<e)throw new TypeError(e+" exceeds maximum possible timeout");return e}}),Xt=e(function(){"use strict";var l=xt(),s=Le(),u=Lt(),c=Gt(),d=Jt(),p=je(),f=Function.prototype,m=Math.max,h=Math.min,g=Object.create;p.maxAge=function(t,r,o){var n,e,a,i;(t=d(t))&&(n=g(null),e=o.async&&p.async||o.promise&&p.promise?"async":"",r.on("set"+e,function(e){n[e]=setTimeout(function(){r.delete(e)},t),"function"==typeof n[e].unref&&n[e].unref(),i&&(i[e]&&"nextTick"!==i[e]&&clearTimeout(i[e]),i[e]=setTimeout(function(){delete i[e]},a),"function"==typeof i[e].unref)&&i[e].unref()}),r.on("delete"+e,function(e){clearTimeout(n[e]),delete n[e],i&&("nextTick"!==i[e]&&clearTimeout(i[e]),delete i[e])}),o.preFetch&&(a=!0===o.preFetch||isNaN(o.preFetch)?.333:m(h(Number(o.preFetch),1),0))&&(i={},a=(1-a)*t,r.on("get"+e,function(t,n,a){i[t]||(i[t]="nextTick",u(function(){var e;"nextTick"===i[t]&&(delete i[t],r.delete(t),o.async&&(n=l(n)).push(f),e=r.memoized.apply(a,n),o.promise)&&c(e)&&("function"==typeof e.done?e.done(f,f):e.then(f,f))}))})),r.on("clear"+e,function(){s(n,function(e){clearTimeout(e)}),n={},i&&(s(i,function(e){"nextTick"!==e&&clearTimeout(e)}),i={})}))}}),Qt=e(function(e,t){"use strict";var n=Se(),c=Object.create,d=Object.prototype.hasOwnProperty;t.exports=function(a){var r,o=0,i=1,l=c(null),s=c(null),u=0;return a=n(a),{hit:function(e){var t=s[e],n=++u;if(l[n]=e,s[e]=n,!t)return++o<=a?void 0:(e=l[i],r(e),e);if(delete l[t],i===t)for(;!d.call(l,++i););},delete:r=function(e){var t=s[e];if(t&&(delete l[t],delete s[e],--o,i===t))if(o)for(;!d.call(l,++i););else u=0,i=1},clear:function(){o=0,i=1,l=c(null),s=c(null),u=0}}}}),Zt=e(function(){"use strict";var r=Se(),o=Qt(),i=je();i.max=function(e,t,n){var a;(e=r(e))&&(a=o(e),e=n.async&&i.async||n.promise&&i.promise?"async":"",t.on("set"+e,n=function(e){void 0!==(e=a.hit(e))&&t.delete(e)}),t.on("get"+e,n),t.on("delete"+e,a.delete),t.on("clear"+e,a.clear))}}),en=e(function(){"use strict";var r=rt(),o=je(),i=Object.create,l=Object.defineProperties;o.refCounter=function(e,t,n){var a=i(null),n=n.async&&o.async||n.promise&&o.promise?"async":"";t.on("set"+n,function(e,t){a[e]=t||1}),t.on("get"+n,function(e){++a[e]}),t.on("delete"+n,function(e){delete a[e]}),t.on("clear"+n,function(){a={}}),l(t.memoized,{deleteRef:r(function(){var e=t.get(arguments);return null!==e&&a[e]?!--a[e]&&(t.delete(e),!0):null}),getRefCount:r(function(){var e=t.get(arguments);return null!==e&&a[e]||0})})}}),tn=e(function(e,t){"use strict";var a=Te(),r=Ie(),o=kt();t.exports=function(e){var t,n=a(arguments[1]);return n.normalizer||0!==(t=n.length=r(n.length,e.length,n.async))&&(n.primitive?!1===t?n.normalizer=Tt():1<t&&(n.normalizer=Nt()(t)):n.normalizer=!1===t?It()():1===t?Pt()():Mt()(t)),n.async&&jt(),n.promise&&Wt(),n.dispose&&Kt(),n.maxAge&&Xt(),n.max&&Zt(),n.refCounter&&en(),o(e,n)}}),nn=e(function(e,t){!function(){"use strict";var l={name:"doT",version:"1.1.1",templateSettings:{evaluate:/\{\{([\s\S]+?(\}?)+)\}\}/g,interpolate:/\{\{=([\s\S]+?)\}\}/g,encode:/\{\{!([\s\S]+?)\}\}/g,use:/\{\{#([\s\S]+?)\}\}/g,useParams:/(^|[^\w$])def(?:\.|\[[\'\"])([\w$\.]+)(?:[\'\"]\])?\s*\:\s*([\w$\.]+|\"[^\"]+\"|\'[^\']+\'|\{[^\}]+\})/g,define:/\{\{##\s*([\w\.$]+)\s*(\:|=)([\s\S]+?)#\}\}/g,defineParams:/^\s*([\w$]+):([\s\S]+)/,conditional:/\{\{\?(\?)?\s*([\s\S]*?)\s*\}\}/g,iterate:/\{\{~\s*(?:\}\}|([\s\S]+?)\s*\:\s*([\w$]+)\s*(?:\:\s*([\w$]+))?\s*\}\})/g,varname:"it",strip:!0,append:!0,selfcontained:!1,doNotSkipEncoded:!1},template:void 0,compile:void 0,log:!0};if("object"!==("undefined"==typeof globalThis?"undefined":te(globalThis)))try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){window.globalThis=function(){if("undefined"!=typeof self)return self;if(void 0!==window)return window;if(void 0!==q)return q;if(void 0!==this)return this;throw new Error("Unable to locate global `this`")}()}l.encodeHTMLSource=function(e){var t={"&":"&#38;","<":"&#60;",">":"&#62;",'"':"&#34;","'":"&#39;","/":"&#47;"},n=e?/[&<>"'\/]/g:/&(?!#?\w+;)|<|>|"|'|\//g;return function(e){return e?e.toString().replace(n,function(e){return t[e]||e}):""}},void 0!==t&&t.exports?t.exports=l:"function"==typeof define&&define.amd?define(function(){return l}):globalThis.doT=l;var s={append:{start:"'+(",end:")+'",startencode:"'+encodeHTML("},split:{start:"';out+=(",end:");out+='",startencode:"';out+=encodeHTML("}},u=/$^/;function c(e){return e.replace(/\\('|\\)/g,"$1").replace(/[\r\t\n]/g," ")}l.template=function(e,t,n){var a,r,o=(t=t||l.templateSettings).append?s.append:s.split,i=0,n=t.use||t.define?function n(r,e,o){return("string"==typeof e?e:e.toString()).replace(r.define||u,function(e,a,t,n){return(a=0===a.indexOf("def.")?a.substring(4):a)in o||(":"===t?(r.defineParams&&n.replace(r.defineParams,function(e,t,n){o[a]={arg:t,text:n}}),a in o||(o[a]=n)):new Function("def","def['"+a+"']="+n)(o)),""}).replace(r.use||u,function(e,t){return r.useParams&&(t=t.replace(r.useParams,function(e,t,n,a){var r;if(o[n]&&o[n].arg&&a)return r=(n+":"+a).replace(/'|\\/g,"_"),o.__exp=o.__exp||{},o.__exp[r]=o[n].text.replace(new RegExp("(^|[^\\w$])"+o[n].arg+"([^\\w$])","g"),"$1"+a+"$2"),t+"def.__exp['"+r+"']"})),(t=new Function("def","return "+t)(o))&&n(r,t,o)})}(t,e,n||{}):e,n=("var out='"+(t.strip?n.replace(/(^|\r|\n)\t* +| +\t*(\r|\n|$)/g," ").replace(/\r|\n|\t|\/\*[\s\S]*?\*\//g,""):n).replace(/'|\\/g,"\\$&").replace(t.interpolate||u,function(e,t){return o.start+c(t)+o.end}).replace(t.encode||u,function(e,t){return a=!0,o.startencode+c(t)+o.end}).replace(t.conditional||u,function(e,t,n){return t?n?"';}else if("+c(n)+"){out+='":"';}else{out+='":n?"';if("+c(n)+"){out+='":"';}out+='"}).replace(t.iterate||u,function(e,t,n,a){return t?(i+=1,r=a||"i"+i,t=c(t),"';var arr"+i+"="+t+";if(arr"+i+"){var "+n+","+r+"=-1,l"+i+"=arr"+i+".length-1;while("+r+"<l"+i+"){"+n+"=arr"+i+"["+r+"+=1];out+='"):"';} } out+='"}).replace(t.evaluate||u,function(e,t){return"';"+c(t)+"out+='"})+"';return out;").replace(/\n/g,"\\n").replace(/\t/g,"\\t").replace(/\r/g,"\\r").replace(/(\s|;|\}|^|\{)out\+='';/g,"$1").replace(/\+''/g,"");a&&(t.selfcontained||!globalThis||globalThis._encodeHTML||(globalThis._encodeHTML=l.encodeHTMLSource(t.doNotSkipEncoded)),n="var encodeHTML = typeof _encodeHTML !== 'undefined' ? _encodeHTML : ("+l.encodeHTMLSource.toString()+"("+(t.doNotSkipEncoded||"")+"));"+n);try{return new Function(t.varname,n)}catch(e){throw"undefined"!=typeof console&&console.log("Could not create a template function: "+n),e}},l.compile=function(e,t){return l.template(e,null,t)}}()}),an=e(function(e,t){var n;n=function(){"use strict";function s(e){return"function"==typeof e}var n=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)},a=0,t=void 0,r=void 0,i=function(e,t){d[a]=e,d[a+1]=t,2===(a+=2)&&(r?r(p):M())};var e=void 0!==window?window:void 0,o=e||{},o=o.MutationObserver||o.WebKitMutationObserver,l="undefined"==typeof self&&"undefined"!=typeof process&&"[object process]"==={}.toString.call(process),u="undefined"!=typeof Uint8ClampedArray&&"undefined"!=typeof importScripts&&"undefined"!=typeof MessageChannel;function c(){var e=setTimeout;return function(){return e(p,1)}}var d=new Array(1e3);function p(){for(var e=0;e<a;e+=2)(0,d[e])(d[e+1]),d[e]=void 0,d[e+1]=void 0;a=0}function f(){try{var e=Function("return this")().require("vertx");return void 0!==(t=e.runOnLoop||e.runOnContext)?function(){t(p)}:c()}catch(e){return c()}}var m,h,g,M=void 0;function b(e,t){var n,a=this,r=new this.constructor(w),o=(void 0===r[v]&&_(r),a._state);return o?(n=arguments[o-1],i(function(){return R(o,r,n,a._result)})):T(a,r,e,t),r}function y(e){var t;return e&&"object"===te(e)&&e.constructor===this?e:(A(t=new this(w),e),t)}var M=l?function(){return process.nextTick(p)}:o?(h=0,l=new o(p),g=document.createTextNode(""),l.observe(g,{characterData:!0}),function(){g.data=h=++h%2}):u?((m=new MessageChannel).port1.onmessage=p,function(){return m.port2.postMessage(0)}):(void 0===e?f:c)(),v=Math.random().toString(36).substring(2);function w(){}var D=void 0,x=1,E=2;function B(e,a,r){i(function(t){var n=!1,e=function(e,t,n,a){try{e.call(t,n,a)}catch(e){return e}}(r,a,function(e){n||(n=!0,(a!==e?A:C)(t,e))},function(e){n||(n=!0,k(t,e))},t._label);!n&&e&&(n=!0,k(t,e))},e)}function F(e,t,n){var a,r;t.constructor===e.constructor&&n===b&&t.constructor.resolve===y?(a=e,(r=t)._state===x?C(a,r._result):r._state===E?k(a,r._result):T(r,void 0,function(e){return A(a,e)},function(e){return k(a,e)})):void 0!==n&&s(n)?B(e,t,n):C(e,t)}function A(t,e){if(t===e)k(t,new TypeError("You cannot resolve a promise with itself"));else if(a=te(n=e),null===n||"object"!==a&&"function"!==a)C(t,e);else{n=void 0;try{n=e.then}catch(e){return void k(t,e)}F(t,e,n)}var n,a}function L(e){e._onerror&&e._onerror(e._result),N(e)}function C(e,t){e._state===D&&(e._result=t,e._state=x,0!==e._subscribers.length)&&i(N,e)}function k(e,t){e._state===D&&(e._state=E,e._result=t,i(L,e))}function T(e,t,n,a){var r=e._subscribers,o=r.length;e._onerror=null,r[o]=t,r[o+x]=n,r[o+E]=a,0===o&&e._state&&i(N,e)}function N(e){var t=e._subscribers,n=e._state;if(0!==t.length){for(var a,r=void 0,o=e._result,i=0;i<t.length;i+=3)a=t[i],r=t[i+n],a?R(n,a,r,o):r(o);e._subscribers.length=0}}function R(e,t,n,a){var r=s(n),o=void 0,i=void 0,l=!0;if(r){try{o=n(a)}catch(e){l=!1,i=e}if(t===o)return void k(t,new TypeError("A promises callback cannot return that same promise."))}else o=a;t._state===D&&(r&&l?A(t,o):!1===l?k(t,i):e===x?C(t,o):e===E&&k(t,o))}var O=0;function _(e){e[v]=O++,e._state=void 0,e._result=void 0,e._subscribers=[]}S.prototype._enumerate=function(e){for(var t=0;this._state===D&&t<e.length;t++)this._eachEntry(e[t],t)},S.prototype._eachEntry=function(t,e){var n=this._instanceConstructor,a=n.resolve;if(a===y){var r,o=void 0,i=void 0,l=!1;try{o=t.then}catch(e){l=!0,i=e}o===b&&t._state!==D?this._settledAt(t._state,e,t._result):"function"!=typeof o?(this._remaining--,this._result[e]=t):n===I?(r=new n(w),l?k(r,i):F(r,t,o),this._willSettleAt(r,e)):this._willSettleAt(new n(function(e){return e(t)}),e)}else this._willSettleAt(a(t),e)},S.prototype._settledAt=function(e,t,n){var a=this.promise;a._state===D&&(this._remaining--,e===E?k(a,n):this._result[t]=n),0===this._remaining&&C(a,this._result)},S.prototype._willSettleAt=function(e,t){var n=this;T(e,void 0,function(e){return n._settledAt(x,t,e)},function(e){return n._settledAt(E,t,e)})};var j=S;function S(e,t){this._instanceConstructor=e,this.promise=new e(w),this.promise[v]||_(this.promise),n(t)?(this.length=t.length,this._remaining=t.length,this._result=new Array(this.length),0!==this.length&&(this.length=this.length||0,this._enumerate(t),0!==this._remaining)||C(this.promise,this._result)):k(this.promise,new Error("Array Methods must be provided an Array"))}P.prototype.catch=function(e){return this.then(null,e)},P.prototype.finally=function(t){var n=this.constructor;return s(t)?this.then(function(e){return n.resolve(t()).then(function(){return e})},function(e){return n.resolve(t()).then(function(){throw e})}):this.then(t,t)};var I=P;function P(e){if(this[v]=O++,this._result=this._state=void 0,this._subscribers=[],w!==e){if("function"!=typeof e)throw new TypeError("You must pass a resolver function as the first argument to the promise constructor");if(!(this instanceof P))throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.");var t=this;try{e(function(e){A(t,e)},function(e){k(t,e)})}catch(e){k(t,e)}}}return I.prototype.then=b,I.all=function(e){return new j(this,e).promise},I.race=function(r){var o=this;return n(r)?new o(function(e,t){for(var n=r.length,a=0;a<n;a++)o.resolve(r[a]).then(e,t)}):new o(function(e,t){return t(new TypeError("You must pass an array to race."))})},I.resolve=y,I.reject=function(e){var t=new this(w);return k(t,e),t},I._setScheduler=function(e){r=e},I._setAsap=function(e){i=e},I._asap=i,I.polyfill=function(){var e=void 0;if(void 0!==q)e=q;else if("undefined"!=typeof self)e=self;else try{e=Function("return this")()}catch(e){throw new Error("polyfill failed because global object is unavailable in this environment")}var t=e.Promise;if(t){var n=null;try{n=Object.prototype.toString.call(t.resolve())}catch(e){}if("[object Promise]"===n&&!t.cast)return}e.Promise=I},I.Promise=I},"object"===te(e=e)&&void 0!==t?t.exports=n():"function"==typeof define&&define.amd?define(n):e.ES6Promise=n()}),rn=e(function(l){var t,n,a=1e5,p=(t=Object.prototype.toString,n=Object.prototype.hasOwnProperty,{Class:function(e){return t.call(e).replace(/^\[object *|\]$/g,"")},HasProperty:function(e,t){return t in e},HasOwnProperty:function(e,t){return n.call(e,t)},IsCallable:function(e){return"function"==typeof e},ToInt32:function(e){return e>>0},ToUint32:function(e){return e>>>0}}),f=Math.LN2,m=Math.abs,h=Math.floor,g=Math.log,b=Math.min,y=Math.pow,M=Math.round;function r(e,t,n){return e<t?t:n<e?n:e}var o,e,i,s,u,c,d,v,w,D,x,E=Object.getOwnPropertyNames||function(e){if(e!==Object(e))throw new TypeError("Object.getOwnPropertyNames called on non-object");var t,n=[];for(t in e)p.HasOwnProperty(e,t)&&n.push(t);return n};function F(e){if(E&&o)for(var t=E(e),n=0;n<t.length;n+=1)o(e,t[n],{value:e[t[n]],writable:!1,enumerable:!1,configurable:!1})}function B(n){if(o){if(n.length>a)throw new RangeError("Array too large for polyfill");for(var e=0;e<n.length;e+=1)!function(t){o(n,t,{get:function(){return n._getter(t)},set:function(e){n._setter(t,e)},enumerable:!0,configurable:!1})}(e)}}function A(e,t){t=32-t;return e<<t>>t}function C(e,t){t=32-t;return e<<t>>>t}function L(e){return[255&e]}function j(e){return A(e[0],8)}function q(e){return[255&e]}function k(e){return C(e[0],8)}function V(e){return[(e=M(Number(e)))<0?0:255<e?255:255&e]}function z(e){return[e>>8&255,255&e]}function $(e){return A(e[0]<<8|e[1],16)}function U(e){return[e>>8&255,255&e]}function H(e){return C(e[0]<<8|e[1],16)}function G(e){return[e>>24&255,e>>16&255,e>>8&255,255&e]}function W(e){return A(e[0]<<24|e[1]<<16|e[2]<<8|e[3],32)}function K(e){return[e>>24&255,e>>16&255,e>>8&255,255&e]}function Y(e){return C(e[0]<<24|e[1]<<16|e[2]<<8|e[3],32)}function T(e,t,n){var a,r,o,i,l,s,u,c=(1<<t-1)-1;function d(e){var t=h(e),e=e-t;return!(e<.5)&&(.5<e||t%2)?t+1:t}for(e!=e?(r=(1<<t)-1,o=y(2,n-1),a=0):e===1/0||e===-1/0?(r=(1<<t)-1,a=e<(o=0)?1:0):0===e?a=1/e==-1/(o=r=0)?1:0:(a=e<0,(e=m(e))>=y(2,1-c)?(r=b(h(g(e)/f),1023),2<=(o=d(e/y(2,r)*y(2,n)))/y(2,n)&&(r+=1,o=1),c<r?(r=(1<<t)-1,o=0):(r+=c,o-=y(2,n))):(r=0,o=d(e/y(2,1-c-n)))),l=[],i=n;i;--i)l.push(o%2?1:0),o=h(o/2);for(i=t;i;--i)l.push(r%2?1:0),r=h(r/2);for(l.push(a?1:0),l.reverse(),s=l.join(""),u=[];s.length;)u.push(parseInt(s.substring(0,8),2)),s=s.substring(8);return u}function N(e,t,n){for(var a,r,o,i,l,s,u=[],c=e.length;c;--c)for(r=e[c-1],a=8;a;--a)u.push(r%2?1:0),r>>=1;return u.reverse(),s=u.join(""),o=(1<<t-1)-1,i=parseInt(s.substring(0,1),2)?-1:1,l=parseInt(s.substring(1,1+t),2),s=parseInt(s.substring(1+t),2),l===(1<<t)-1?0===s?1/0*i:NaN:0<l?i*y(2,l-o)*(1+s/y(2,n)):0!==s?i*y(2,-(o-1))*(s/y(2,n)):i<0?-0:0}function J(e){return N(e,11,52)}function X(e){return T(e,11,52)}function Q(e){return N(e,8,23)}function Z(e){return T(e,8,23)}function R(e){if((e=p.ToInt32(e))<0)throw new RangeError("ArrayBuffer size is not a small enough positive integer");var t;for(this.byteLength=e,this._bytes=[],this._bytes.length=e,t=0;t<this.byteLength;t+=1)this._bytes[t]=0;F(this)}function ee(){}function O(e,t,n){var l=function(e,t,n){var a,r,o,i;if(arguments.length&&"number"!=typeof e)if("object"===te(e)&&e.constructor===l)for(this.length=(a=e).length,this.byteLength=this.length*this.BYTES_PER_ELEMENT,this.buffer=new R(this.byteLength),o=this.byteOffset=0;o<this.length;o+=1)this._setter(o,a._getter(o));else if("object"!==te(e)||(e instanceof R||"ArrayBuffer"===p.Class(e))){if("object"!==te(e)||!(e instanceof R||"ArrayBuffer"===p.Class(e)))throw new TypeError("Unexpected argument type(s)");if(this.buffer=e,this.byteOffset=p.ToUint32(t),this.byteOffset>this.buffer.byteLength)throw new RangeError("byteOffset out of range");if(this.byteOffset%this.BYTES_PER_ELEMENT)throw new RangeError("ArrayBuffer length minus the byteOffset is not a multiple of the element size.");if(arguments.length<3){if(this.byteLength=this.buffer.byteLength-this.byteOffset,this.byteLength%this.BYTES_PER_ELEMENT)throw new RangeError("length of buffer minus byteOffset not a multiple of the element size");this.length=this.byteLength/this.BYTES_PER_ELEMENT}else this.length=p.ToUint32(n),this.byteLength=this.length*this.BYTES_PER_ELEMENT;if(this.byteOffset+this.byteLength>this.buffer.byteLength)throw new RangeError("byteOffset and length reference an area beyond the end of the buffer")}else for(this.length=p.ToUint32((r=e).length),this.byteLength=this.length*this.BYTES_PER_ELEMENT,this.buffer=new R(this.byteLength),o=this.byteOffset=0;o<this.length;o+=1)i=r[o],this._setter(o,Number(i));else{if(this.length=p.ToInt32(e),n<0)throw new RangeError("ArrayBufferView size is not a small enough positive integer");this.byteLength=this.length*this.BYTES_PER_ELEMENT,this.buffer=new R(this.byteLength),this.byteOffset=0}this.constructor=l,F(this),B(this)};return(l.prototype=new ee).BYTES_PER_ELEMENT=e,l.prototype._pack=t,l.prototype._unpack=n,l.BYTES_PER_ELEMENT=e,l.prototype.get=l.prototype._getter=function(e){if(arguments.length<1)throw new SyntaxError("Not enough arguments");if(!((e=p.ToUint32(e))>=this.length)){for(var t=[],n=0,a=this.byteOffset+e*this.BYTES_PER_ELEMENT;n<this.BYTES_PER_ELEMENT;n+=1,a+=1)t.push(this.buffer._bytes[a]);return this._unpack(t)}},l.prototype._setter=function(e,t){if(arguments.length<2)throw new SyntaxError("Not enough arguments");if((e=p.ToUint32(e))<this.length)for(var n=this._pack(t),a=0,r=this.byteOffset+e*this.BYTES_PER_ELEMENT;a<this.BYTES_PER_ELEMENT;a+=1,r+=1)this.buffer._bytes[r]=n[a]},l.prototype.set=function(e,t){if(arguments.length<1)throw new SyntaxError("Not enough arguments");var n,a,r,o,i,l,s,u,c,d;if("object"===te(e)&&e.constructor===this.constructor){if(n=e,(r=p.ToUint32(t))+n.length>this.length)throw new RangeError("Offset plus length of array is out of range");if(u=this.byteOffset+r*this.BYTES_PER_ELEMENT,c=n.length*this.BYTES_PER_ELEMENT,n.buffer===this.buffer){for(d=[],i=0,l=n.byteOffset;i<c;i+=1,l+=1)d[i]=n.buffer._bytes[l];for(i=0,s=u;i<c;i+=1,s+=1)this.buffer._bytes[s]=d[i]}else for(i=0,l=n.byteOffset,s=u;i<c;i+=1,l+=1,s+=1)this.buffer._bytes[s]=n.buffer._bytes[l]}else{if("object"!==te(e)||void 0===e.length)throw new TypeError("Unexpected argument type(s)");if(o=p.ToUint32((a=e).length),(r=p.ToUint32(t))+o>this.length)throw new RangeError("Offset plus length of array is out of range");for(i=0;i<o;i+=1)l=a[i],this._setter(r+i,Number(l))}},l.prototype.subarray=function(e,t){e=p.ToInt32(e),t=p.ToInt32(t),arguments.length<1&&(e=0),arguments.length<2&&(t=this.length),e<0&&(e=this.length+e),t<0&&(t=this.length+t),e=r(e,0,this.length);var n=(t=r(t,0,this.length))-e;return new this.constructor(this.buffer,this.byteOffset+e*this.BYTES_PER_ELEMENT,n=n<0?0:n)},l}function _(e,t){return p.IsCallable(e.get)?e.get(t):e[t]}function S(e,t,n){if(0===arguments.length)e=new l.ArrayBuffer(0);else if(!(e instanceof l.ArrayBuffer||"ArrayBuffer"===p.Class(e)))throw new TypeError("TypeError");if(this.buffer=e||new l.ArrayBuffer(0),this.byteOffset=p.ToUint32(t),this.byteOffset>this.buffer.byteLength)throw new RangeError("byteOffset out of range");if(this.byteLength=arguments.length<3?this.buffer.byteLength-this.byteOffset:p.ToUint32(n),this.byteOffset+this.byteLength>this.buffer.byteLength)throw new RangeError("byteOffset and length reference an area beyond the end of the buffer");F(this)}function I(o){return function(e,t){if((e=p.ToUint32(e))+o.BYTES_PER_ELEMENT>this.byteLength)throw new RangeError("Array index out of range");e+=this.byteOffset;for(var n=new l.Uint8Array(this.buffer,e,o.BYTES_PER_ELEMENT),a=[],r=0;r<o.BYTES_PER_ELEMENT;r+=1)a.push(_(n,r));return Boolean(t)===Boolean(x)&&a.reverse(),_(new o(new l.Uint8Array(a).buffer),0)}}function P(i){return function(e,t,n){if((e=p.ToUint32(e))+i.BYTES_PER_ELEMENT>this.byteLength)throw new RangeError("Array index out of range");for(var t=new i([t]),a=new l.Uint8Array(t.buffer),r=[],o=0;o<i.BYTES_PER_ELEMENT;o+=1)r.push(_(a,o));Boolean(n)===Boolean(x)&&r.reverse(),new l.Uint8Array(this.buffer,e,i.BYTES_PER_ELEMENT).set(r)}}o=Object.defineProperty&&function(){try{return Object.defineProperty({},"x",{}),1}catch(e){}}()?Object.defineProperty:function(e,t,n){if(!e===Object(e))throw new TypeError("Object.defineProperty called on non-object");return p.HasProperty(n,"get")&&Object.prototype.__defineGetter__&&Object.prototype.__defineGetter__.call(e,t,n.get),p.HasProperty(n,"set")&&Object.prototype.__defineSetter__&&Object.prototype.__defineSetter__.call(e,t,n.set),p.HasProperty(n,"value")&&(e[t]=n.value),e},l.ArrayBuffer=l.ArrayBuffer||R,D=O(1,L,j),e=O(1,q,k),i=O(1,V,k),s=O(2,z,$),u=O(2,U,H),c=O(4,G,W),d=O(4,K,Y),v=O(4,Z,Q),w=O(8,X,J),l.Int8Array=l.Int8Array||D,l.Uint8Array=l.Uint8Array||e,l.Uint8ClampedArray=l.Uint8ClampedArray||i,l.Int16Array=l.Int16Array||s,l.Uint16Array=l.Uint16Array||u,l.Int32Array=l.Int32Array||c,l.Uint32Array=l.Uint32Array||d,l.Float32Array=l.Float32Array||v,l.Float64Array=l.Float64Array||w,D=new l.Uint16Array([4660]),x=18===_(new l.Uint8Array(D.buffer),0),S.prototype.getUint8=I(l.Uint8Array),S.prototype.getInt8=I(l.Int8Array),S.prototype.getUint16=I(l.Uint16Array),S.prototype.getInt16=I(l.Int16Array),S.prototype.getUint32=I(l.Uint32Array),S.prototype.getInt32=I(l.Int32Array),S.prototype.getFloat32=I(l.Float32Array),S.prototype.getFloat64=I(l.Float64Array),S.prototype.setUint8=P(l.Uint8Array),S.prototype.setInt8=P(l.Int8Array),S.prototype.setUint16=P(l.Uint16Array),S.prototype.setInt16=P(l.Int16Array),S.prototype.setUint32=P(l.Uint32Array),S.prototype.setInt32=P(l.Int32Array),S.prototype.setFloat32=P(l.Float32Array),S.prototype.setFloat64=P(l.Float64Array),l.DataView=l.DataView||S}),on=e(function(e){!function(e){"use strict";var n,a,r;function t(){if(void 0===this)throw new TypeError("Constructor WeakMap requires 'new'");if(r(this,"_id","_WeakMap_"+i()+"."+i()),0<arguments.length)throw new TypeError("WeakMap iterable is not supported")}function o(e,t){if(!l(e)||!n.call(e,"_id"))throw new TypeError(t+" method called on incompatible receiver "+te(e))}function i(){return Math.random().toString().substring(2)}function l(e){return Object(e)===e}e.WeakMap||(n=Object.prototype.hasOwnProperty,a=Object.defineProperty&&function(){try{return 1===Object.defineProperty({},"x",{value:1}).x}catch(e){}}(),e.WeakMap=((r=function(e,t,n){a?Object.defineProperty(e,t,{configurable:!0,writable:!0,value:n}):e[t]=n})(t.prototype,"delete",function(e){var t;return o(this,"delete"),!!l(e)&&!(!(t=e[this._id])||t[0]!==e||(delete e[this._id],0))}),r(t.prototype,"get",function(e){var t;return o(this,"get"),l(e)&&(t=e[this._id])&&t[0]===e?t[1]:void 0}),r(t.prototype,"has",function(e){var t;return o(this,"has"),!!l(e)&&!(!(t=e[this._id])||t[0]!==e)}),r(t.prototype,"set",function(e,t){var n;if(o(this,"set"),l(e))return(n=e[this._id])&&n[0]===e?n[1]=t:r(e,this._id,[e,t]),this;throw new TypeError("Invalid value used as weak map key")}),r(t,"_polyfill",!0),t))}("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:void 0!==window?window:void 0!==q?q:e)}),ln={helpUrlBase:"https://dequeuniversity.com/rules/",gridSize:200,results:[],resultGroups:[],resultGroupMap:{},impact:Object.freeze(["minor","moderate","serious","critical"]),preload:Object.freeze({assets:["cssom","media"],timeout:1e4}),allOrigins:"<unsafe_all_origins>",sameOrigin:"<same_origin>"},g=([{name:"NA",value:"inapplicable",priority:0,group:"inapplicable"},{name:"PASS",value:"passed",priority:1,group:"passes"},{name:"CANTTELL",value:"cantTell",priority:2,group:"incomplete"},{name:"FAIL",value:"failed",priority:3,group:"violations"}].forEach(function(e){var t=e.name,n=e.value,a=e.priority,e=e.group;ln[t]=n,ln[t+"_PRIO"]=a,ln[t+"_GROUP"]=e,ln.results[a]=n,ln.resultGroups[a]=e,ln.resultGroupMap[n]=e}),Object.freeze(ln.results),Object.freeze(ln.resultGroups),Object.freeze(ln.resultGroupMap),Object.freeze(ln),ln),sn=function(){"object"===("undefined"==typeof console?"undefined":te(console))&&console.log&&Function.prototype.apply.call(console.log,console,arguments)},un=/[\t\r\n\f]/g;function cn(){le(this,cn),this.parent=void 0}ue(cn,[{key:"props",get:function(){throw new Error('VirtualNode class must have a "props" object consisting of "nodeType" and "nodeName" properties')}},{key:"attrNames",get:function(){throw new Error('VirtualNode class must have an "attrNames" property')}},{key:"attr",value:function(){throw new Error('VirtualNode class must have an "attr" function')}},{key:"hasAttr",value:function(){throw new Error('VirtualNode class must have a "hasAttr" function')}},{key:"hasClass",value:function(e){var t=this.attr("class");return!!t&&(e=" "+e+" ",0<=(" "+t+" ").replace(un," ").indexOf(e))}}]);var b=cn,dn={},pn=(fe(dn,{DqElement:function(){return Qn},aggregate:function(){return pn},aggregateChecks:function(){return yn},aggregateNodeResults:function(){return wn},aggregateResult:function(){return xn},areStylesSet:function(){return En},assert:function(){return d},checkHelper:function(){return Zn},clone:function(){return ea},closest:function(){return x},collectResultsFromFrames:function(){return er},contains:function(){return tr},convertSelector:function(){return la},cssParser:function(){return ta},deepMerge:function(){return nr},escapeSelector:function(){return y},extendMetaData:function(){return ar},filterHtmlAttrs:function(){return mu},finalizeRuleResult:function(){return vn},findBy:function(){return Xa},getAllChecks:function(){return Ja},getAncestry:function(){return Kn},getBaseLang:function(){return rs},getCheckMessage:function(){return ps},getCheckOption:function(){return fs},getEnvironmentData:function(){return ms},getFlattenedTree:function(){return as},getFrameContexts:function(){return Cs},getFriendlyUriEnd:function(){return Nn},getNodeAttributes:function(){return Rn},getNodeFromTree:function(){return D},getPreloadConfig:function(){return lu},getRootNode:function(){return lr},getRule:function(){return ks},getScroll:function(){return Ts},getScrollState:function(){return Rs},getSelector:function(){return Gn},getSelectorData:function(){return zn},getShadowSelector:function(){return Sn},getStandards:function(){return Os},getStyleSheetFactory:function(){return Ss},getXpath:function(){return Yn},injectStyle:function(){return Is},isHidden:function(){return Ps},isHtmlElement:function(){return Ms},isNodeInContext:function(){return Bs},isShadowRoot:function(){return or},isValidLang:function(){return Fu},isXHTML:function(){return _n},matchAncestry:function(){return Vs},matches:function(){return ua},matchesExpression:function(){return sa},matchesSelector:function(){return On},memoize:function(){return t},mergeResults:function(){return Za},nodeSorter:function(){return zs},parseCrossOriginStylesheet:function(){return Hs},parseSameOriginStylesheet:function(){return $s},parseStylesheet:function(){return Us},performanceTimer:function(){return s},pollyfillElementsFromPoint:function(){return Ys},preload:function(){return su},preloadCssom:function(){return nu},preloadMedia:function(){return ou},processMessage:function(){return ds},publishMetaData:function(){return cu},querySelectorAll:function(){return du},querySelectorAllFilter:function(){return tu},queue:function(){return ha},respondable:function(){return Ha},ruleShouldRun:function(){return fu},select:function(){return gu},sendCommandToFrame:function(){return Wa},setScrollState:function(){return bu},shadowSelect:function(){return yu},shadowSelectAll:function(){return vu},shouldPreload:function(){return iu},toArray:function(){return Fn},tokenList:function(){return _},uniqueArray:function(){return Qs},uuid:function(){return ka},validInputTypes:function(){return wu},validLangs:function(){return xu}}),function(t,e,n){return e=e.slice(),n&&e.push(n),n=e.map(function(e){return t.indexOf(e)}).sort(),t[n.pop()]}),fn=g.CANTTELL_PRIO,mn=g.FAIL_PRIO,hn=[],gn=(hn[g.PASS_PRIO]=!0,hn[g.CANTTELL_PRIO]=null,hn[g.FAIL_PRIO]=!1,["any","all","none"]);function bn(n,a){gn.reduce(function(e,t){return e[t]=(n[t]||[]).map(function(e){return a(e,t)}),e},{})}var yn=function(e){var n=Object.assign({},e),a=(bn(n,function(e,t){var n=void 0===e.result?-1:hn.indexOf(e.result);e.priority=-1!==n?n:g.CANTTELL_PRIO,"none"===t&&(e.priority===g.PASS_PRIO?e.priority=g.FAIL_PRIO:e.priority===g.FAIL_PRIO&&(e.priority=g.PASS_PRIO))}),{all:n.all.reduce(function(e,t){return Math.max(e,t.priority)},0),none:n.none.reduce(function(e,t){return Math.max(e,t.priority)},0),any:n.any.reduce(function(e,t){return Math.min(e,t.priority)},4)%4}),r=(n.priority=Math.max(a.all,a.none,a.any),[]);return gn.forEach(function(t){n[t]=n[t].filter(function(e){return e.priority===n.priority&&e.priority===a[t]}),n[t].forEach(function(e){return r.push(e.impact)})}),[fn,mn].includes(n.priority)?n.impact=pn(g.impact,r):n.impact=null,bn(n,function(e){delete e.result,delete e.priority}),n.result=g.results[n.priority],delete n.priority,n},vn=function(t){var n=axe._audit.rules.find(function(e){return e.id===t.id});return n&&n.impact&&t.nodes.forEach(function(t){["any","all","none"].forEach(function(e){(t[e]||[]).forEach(function(e){e.impact=n.impact})})}),Object.assign(t,wn(t.nodes)),delete t.nodes,t},wn=function(e){var n={},t=((e=e.map(function(e){if(e.any&&e.all&&e.none)return yn(e);if(Array.isArray(e.node))return vn(e);throw new TypeError("Invalid Result type")}))&&e.length?(t=e.map(function(e){return e.result}),n.result=pn(g.results,t,n.result)):n.result="inapplicable",g.resultGroups.forEach(function(e){return n[e]=[]}),e.forEach(function(e){var t=g.resultGroupMap[e.result];n[t].push(e)}),g.FAIL_GROUP);return 0===n[t].length&&(t=g.CANTTELL_GROUP),0<n[t].length?(e=n[t].map(function(e){return e.impact}),n.impact=pn(g.impact,e)||null):n.impact=null,n};function Dn(e,t,n){var a=Object.assign({},t);a.nodes=(a[n]||[]).concat(),g.resultGroups.forEach(function(e){delete a[e]}),e[n].push(a)}var xn=function(e){var n={};return g.resultGroups.forEach(function(e){return n[e]=[]}),e.forEach(function(t){t.error?Dn(n,t,g.CANTTELL_GROUP):t.result===g.NA?Dn(n,t,g.NA_GROUP):g.resultGroups.forEach(function(e){Array.isArray(t[e])&&0<t[e].length&&Dn(n,t,e)})}),n},En=function e(t,n,a){var r=window.getComputedStyle(t,null);if(!r)return!1;for(var o=0;o<n.length;++o){var i=n[o];if(r.getPropertyValue(i.property)===i.value)return!0}return!(!t.parentNode||t.nodeName.toUpperCase()===a.toUpperCase())&&e(t.parentNode,n,a)},d=function(e,t){if(!e)throw new Error(t)},Fn=function(e){return Array.prototype.slice.call(e)},y=function(e){for(var t,n=String(e),a=n.length,r=-1,o="",i=n.charCodeAt(0);++r<a;)0==(t=n.charCodeAt(r))?o+="�":o+=1<=t&&t<=31||127==t||0==r&&48<=t&&t<=57||1==r&&48<=t&&t<=57&&45==i?"\\"+t.toString(16)+" ":(0!=r||1!=a||45!=t)&&(128<=t||45==t||95==t||48<=t&&t<=57||65<=t&&t<=90||97<=t&&t<=122)?n.charAt(r):"\\"+n.charAt(r);return o};function An(e,t){return[e.substring(0,t),e.substring(t)]}function Cn(e){return e.replace(/\s+$/,"")}var kn,Tn,Nn=function(){var e,t,n,a,r,o,i,l,s=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"",u=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};if(!(s.length<=1||"data:"===s.substr(0,5)||"javascript:"===s.substr(0,11)||s.includes("?")))return e=u.currentDomain,u=void 0===(u=u.maxLength)?25:u,i=o=l=r=a="",(n=s=s).includes("#")&&(s=(t=h(An(s,s.indexOf("#")),2))[0],i=t[1]),s.includes("?")&&(s=(t=h(An(s,s.indexOf("?")),2))[0],o=t[1]),s.includes("://")?(a=(t=h(s.split("://"),2))[0],r=(t=h(An(s=t[1],s.indexOf("/")),2))[0],s=t[1]):"//"===s.substr(0,2)&&(r=(t=h(An(s=s.substr(2),s.indexOf("/")),2))[0],s=t[1]),(r="www."===r.substr(0,4)?r.substr(4):r)&&r.includes(":")&&(r=(t=h(An(r,r.indexOf(":")),2))[0],l=t[1]),n=(t={original:n,protocol:a,domain:r,port:l,path:s,query:o,hash:i}).domain,a=t.hash,l=(r=t.path).substr(r.substr(0,r.length-2).lastIndexOf("/")+1),a?l&&(l+a).length<=u?Cn(l+a):l.length<2&&2<a.length&&a.length<=u?Cn(a):void 0:n&&n.length<u&&r.length<=1||r==="/"+l&&n&&e&&n!==e&&(n+r).length<=u?Cn(n+r):(-1===(s=l.lastIndexOf("."))||1<s)&&(-1!==s||2<l.length)&&l.length<=u&&!l.match(/index(\.[a-zA-Z]{2-4})?/)&&!function(e){var t=0<arguments.length&&void 0!==e?e:"";return 0!==t.length&&(t.match(/[0-9]/g)||"").length>=t.length/2}(l)?Cn(l):void 0},Rn=function(e){return(e.attributes instanceof window.NamedNodeMap?e:e.cloneNode(!1)).attributes},On=function(e,t){return!!e[kn=kn&&e[kn]?kn:function(e){for(var t,n=["matches","matchesSelector","mozMatchesSelector","webkitMatchesSelector","msMatchesSelector"],a=n.length,r=0;r<a;r++)if(e[t=n[r]])return t}(e)]&&e[kn](t)},_n=function(e){return!!e.createElement&&"A"===e.createElement("A").localName},Sn=function(n,e){var a=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};if(!e)return"";var t=e.getRootNode&&e.getRootNode()||document;if(11!==t.nodeType)return n(e,a,t);for(var r=[];11===t.nodeType;){if(!t.host)return"";r.unshift({elm:e,doc:t}),t=(e=t.host).getRootNode()}return r.unshift({elm:e,doc:t}),r.map(function(e){var t=e.elm,e=e.doc;return n(t,a,e)})},In=["class","style","id","selected","checked","disabled","tabindex","aria-checked","aria-selected","aria-invalid","aria-activedescendant","aria-busy","aria-disabled","aria-expanded","aria-grabbed","aria-pressed","aria-valuenow"],Pn=31,Mn=/([\\"])/g,Bn=/(\r\n|\r|\n)/g;function Ln(e){return e.replace(Mn,"\\$1").replace(Bn,"\\a ")}function jn(e,t){var n,a=t.name;return-1!==a.indexOf("href")||-1!==a.indexOf("src")?(n=Nn(e.getAttribute(a)))?y(t.name)+'$="'+Ln(n)+'"':y(t.name)+'="'+Ln(e.getAttribute(a))+'"':y(a)+'="'+Ln(t.value)+'"'}function qn(e,t){return e.count<t.count?-1:e.count===t.count?0:1}function Vn(e){return!In.includes(e.name)&&-1===e.name.indexOf(":")&&(!e.value||e.value.length<Pn)}function zn(e){for(var a={classes:{},tags:{},attributes:{}},r=(e=Array.isArray(e)?e:[e]).slice(),o=[];r.length;)!function(){var e,t=r.pop(),n=t.actualNode;for(n.querySelectorAll&&(e=n.nodeName,a.tags[e]?a.tags[e]++:a.tags[e]=1,n.classList&&Array.from(n.classList).forEach(function(e){e=y(e);a.classes[e]?a.classes[e]++:a.classes[e]=1}),n.hasAttributes())&&Array.from(Rn(n)).filter(Vn).forEach(function(e){e=jn(n,e);e&&(a.attributes[e]?a.attributes[e]++:a.attributes[e]=1)}),t.children.length&&(o.push(r),r=t.children.slice());!r.length&&o.length;)r=o.pop()}();return a}function $n(e){return void 0===Tn&&(Tn=_n(document)),y(Tn?e.localName:e.nodeName.toLowerCase())}function Un(e,t){var n,a,r,o,i,l,s,u,c,d="",p=(a=e,r=[],o=t.classes,i=t.tags,a.classList&&Array.from(a.classList).forEach(function(e){e=y(e);o[e]<i[a.nodeName]&&r.push({name:e,count:o[e],species:"class"})}),r.sort(qn)),t=(l=e,s=[],u=t.attributes,c=t.tags,l.hasAttributes()&&Array.from(Rn(l)).filter(Vn).forEach(function(e){e=jn(l,e);e&&u[e]<c[l.nodeName]&&s.push({name:e,count:u[e],species:"attribute"})}),s.sort(qn));return p.length&&1===p[0].count?n=[p[0]]:t.length&&1===t[0].count?(n=[t[0]],d=$n(e)):((n=p.concat(t)).sort(qn),(n=n.slice(0,3)).some(function(e){return"class"===e.species})?n.sort(function(e,t){return e.species!==t.species&&"class"===e.species?-1:e.species===t.species?0:1}):d=$n(e)),d+n.reduce(function(e,t){switch(t.species){case"class":return e+"."+t.name;case"attribute":return e+"["+t.name+"]"}return e},"")}function Hn(e,t,n){if(!axe._selectorData)throw new Error("Expect axe._selectorData to be set up");var a,r,t=t.toRoot,o=void 0!==t&&t;do{var i=function(e){var t;if(e.getAttribute("id"))return t=e.getRootNode&&e.getRootNode()||document,(e="#"+y(e.getAttribute("id")||"")).match(/player_uid_/)||1!==t.querySelectorAll(e).length?void 0:e}(e);i||(i=Un(e,axe._selectorData),i+=function(t,n){var e=t.parentNode&&Array.from(t.parentNode.children||"")||[];return e.find(function(e){return e!==t&&On(e,n)})?":nth-child("+(1+e.indexOf(t))+")":""}(e,i)),a=a?i+" > "+a:i,r=r?r.filter(function(e){return On(e,a)}):Array.from(n.querySelectorAll(a)),e=e.parentElement}while((1<r.length||o)&&e&&11!==e.nodeType);return 1===r.length?a:-1!==a.indexOf(" > ")?":root"+a.substring(a.indexOf(" > ")):":root"}function Gn(e,t){return Sn(Hn,e,t)}function Wn(e){var t,n=e.nodeName.toLowerCase(),a=e.parentElement;return a?(t="","head"!==n&&"body"!==n&&1<a.children.length&&(e=Array.prototype.indexOf.call(a.children,e)+1,t=":nth-child(".concat(e,")")),Wn(a)+" > "+n+t):n}function Kn(e,t){return Sn(Wn,e,t)}var Yn=function(e){return function e(t,n){var a,r,o,i;if(!t)return[];if(!n&&9===t.nodeType)return n=[{str:"html"}];if(n=n||[],t.parentNode&&t.parentNode!==t&&(n=e(t.parentNode,n)),t.previousSibling){for(r=1,a=t.previousSibling;1===a.nodeType&&a.nodeName===t.nodeName&&r++,a=a.previousSibling;);1===r&&(r=null)}else if(t.nextSibling)for(a=t.nextSibling;a=1===a.nodeType&&a.nodeName===t.nodeName?(r=1,null):(r=null,a.previousSibling););return 1===t.nodeType&&((o={}).str=t.nodeName.toLowerCase(),(i=t.getAttribute&&y(t.getAttribute("id")))&&1===t.ownerDocument.querySelectorAll("#"+i).length&&(o.id=t.getAttribute("id")),1<r&&(o.count=r),n.push(o)),n}(e).reduce(function(e,t){return t.id?"/".concat(t.str,"[@id='").concat(t.id,"']"):e+"/".concat(t.str)+(0<t.count?"[".concat(t.count,"]"):"")},"")},Jn={},w={set:function(e,t){var n;d("string"==typeof(n=e),"key must be a string, "+te(n)+" given"),d(""!==n,"key must not be empty"),Jn[e]=t},get:function(e,t){var n;return d("function"==typeof(n=t)||void 0===n,"creator must be a function or undefined, "+te(n)+" given"),e in Jn?Jn[e]:"function"==typeof t?(n=t(),d(void 0!==n,"Cache creator function should not return undefined"),this.set(e,n),Jn[e]):void 0},clear:function(){Jn={}}},D=function(e,t){return t=t||e,w.get("nodeMap")?w.get("nodeMap").get(t):null};function Xn(e){var t,n,a=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};this.spec=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{},e instanceof b?(this._virtualNode=e,this._element=e.actualNode):(this._element=e,this._virtualNode=D(e)),this.fromFrame=1<(null==(t=this.spec.selector)?void 0:t.length),a.absolutePaths&&(this._options={toRoot:!0}),this.nodeIndexes=[],Array.isArray(this.spec.nodeIndexes)?this.nodeIndexes=this.spec.nodeIndexes:"number"==typeof(null==(t=this._virtualNode)?void 0:t.nodeIndex)&&(this.nodeIndexes=[this._virtualNode.nodeIndex]),this.source=null,axe._audit.noHtml||(this.source=null!=(a=this.spec.source)?a:null!=(t=this._element)&&t.outerHTML?((a=t.outerHTML)||"function"!=typeof window.XMLSerializer||(a=(new window.XMLSerializer).serializeToString(t)),(t=a||"").length>(n=n||300)&&(n=t.indexOf(">"),t=t.substring(0,n+1)),t):"")}Xn.prototype={get selector(){return this.spec.selector||[Gn(this.element,this._options)]},get ancestry(){return this.spec.ancestry||[Kn(this.element)]},get xpath(){return this.spec.xpath||[Yn(this.element)]},get element(){return this._element},toJSON:function(){return{selector:this.selector,source:this.source,xpath:this.xpath,ancestry:this.ancestry,nodeIndexes:this.nodeIndexes}}},Xn.fromFrame=function(e,t,n){e=Xn.mergeSpecs(e,n);return new Xn(n.element,t,e)},Xn.mergeSpecs=function(e,t){return p({},e,{selector:[].concat(v(t.selector),v(e.selector)),ancestry:[].concat(v(t.ancestry),v(e.ancestry)),xpath:[].concat(v(t.xpath),v(e.xpath)),nodeIndexes:[].concat(v(t.nodeIndexes),v(e.nodeIndexes))})};var Qn=Xn,Zn=function(t,n,a,r){return{isAsync:!1,async:function(){return this.isAsync=!0,function(e){e instanceof Error==!1?(t.result=e,a(t)):r(e)}},data:function(e){t.data=e},relatedNodes:function(e){window.Node&&(e=e instanceof window.Node?[e]:Fn(e)).every(function(e){return e instanceof window.Node||e.actualNode})&&(t.relatedNodes=e.map(function(e){return new Qn(e,n)}))}}},ea=function e(t){var n,a,r,o=t;if(null!=(n=window)&&n.Node&&t instanceof window.Node||null!=(n=window)&&n.HTMLCollection&&t instanceof window.HTMLCollection)return t;if(null!==t&&"object"===te(t))if(Array.isArray(t))for(o=[],a=0,r=t.length;a<r;a++)o[a]=e(t[a]);else for(a in o={},t)o[a]=e(t[a]);return o},ta=((a=new(he(Ae()).CssSelectorParser)).registerSelectorPseudos("not"),a.registerSelectorPseudos("is"),a.registerNestingOperators(">"),a.registerAttrEqualityMods("^","$","*","~"),a);function na(e,t){return i=t,1===(o=e).props.nodeType&&("*"===i.tag||o.props.nodeName===i.tag)&&(r=e,!(o=t).classes||o.classes.every(function(e){return r.hasClass(e.value)}))&&(a=e,!(i=t).attributes||i.attributes.every(function(e){var t=a.attr(e.key);return null!==t&&e.test(t)}))&&(o=e,!(i=t).id||o.props.id===i.id)&&(n=e,!((o=t).pseudos&&!o.pseudos.every(function(e){if("not"===e.name)return!e.expressions.some(function(e){return sa(n,e)});if("is"===e.name)return e.expressions.some(function(e){return sa(n,e)});throw new Error("the pseudo selector "+e.name+" has not yet been implemented")})));var n,a,r,o,i}aa=/(?=[\-\[\]{}()*+?.\\\^$|,#\s])/g;var aa,ra=function(e){return e.replace(aa,"\\")},oa=/\\/g;function ia(e){return e.map(function(e){for(var t=[],n=e.rule;n;)t.push({tag:n.tagName?n.tagName.toLowerCase():"*",combinator:n.nestingOperator||" ",id:n.id,attributes:function(e){if(e)return e.map(function(e){var t,n,a=e.name.replace(oa,""),r=(e.value||"").replace(oa,"");switch(e.operator){case"^=":n=new RegExp("^"+ra(r));break;case"$=":n=new RegExp(ra(r)+"$");break;case"~=":n=new RegExp("(^|\\s)"+ra(r)+"(\\s|$)");break;case"|=":n=new RegExp("^"+ra(r)+"(-|$)");break;case"=":t=function(e){return r===e};break;case"*=":t=function(e){return e&&e.includes(r)};break;case"!=":t=function(e){return r!==e};break;default:t=function(e){return null!==e}}return""===r&&/^[*$^]=$/.test(e.operator)&&(t=function(){return!1}),{key:a,value:r,type:void 0===e.value?"attrExist":"attrValue",test:t=t||function(e){return e&&n.test(e)}}})}(n.attrs),classes:function(e){if(e)return e.map(function(e){return{value:e=e.replace(oa,""),regexp:new RegExp("(^|\\s)"+ra(e)+"(\\s|$)")}})}(n.classNames),pseudos:function(e){if(e)return e.map(function(e){var t;return["is","not"].includes(e.name)&&(t=ia(t=(t=e.value).selectors||[t])),{name:e.name,expressions:t,value:e.value}})}(n.pseudos)}),n=n.rule;return t})}function la(e){e=ta.parse(e);return ia(e.selectors||[e])}function sa(e,t,n){return function e(t,n,a,r){if(!t)return!1;for(var o=Array.isArray(n)?n[a]:n,i=na(t,o);!i&&r&&t.parent;)i=na(t=t.parent,o);if(0<a){if(!1===[" ",">"].includes(o.combinator))throw new Error("axe.utils.matchesExpression does not support the combinator: "+o.combinator);i=i&&e(t.parent,n,a-1," "===o.combinator)}return i}(e,t,t.length-1,n)}var ua=function(t,e){return la(e).some(function(e){return sa(t,e)})},x=function(e,t){for(;e;){if(ua(e,t))return e;if(void 0===e.parent)throw new TypeError("Cannot resolve parent for non-DOM nodes");e=e.parent}return null};function ca(){}function da(e){if("function"!=typeof e)throw new TypeError("Queue methods require functions as arguments")}for(var pa,fa,ma,ha=function(){function t(e){a=e,setTimeout(function(){null!=a&&sn("Uncaught error (of queue)",a)},1)}var a,r=[],n=0,o=0,i=ca,l=!1,s=t;function u(e){return i=ca,s(e),r}function c(){for(var e=r.length;n<e;n++){var t=r[n];try{t.call(null,function(t){return function(e){r[t]=e,--o||i===ca||(l=!0,i(r))}}(n),u)}catch(e){u(e)}}}var d={defer:function(e){var n;if("object"===te(e)&&e.then&&e.catch&&(n=e,e=function(e,t){n.then(e).catch(t)}),da(e),void 0===a){if(l)throw new Error("Queue already completed");return r.push(e),++o,c(),d}},then:function(e){if(da(e),i!==ca)throw new Error("queue `then` already set");return a||(i=e,o)||(l=!0,i(r)),d},catch:function(e){if(da(e),s!==t)throw new Error("queue `catch` already set");return a?(e(a),a=null):s=e,d},abort:u};return d},ga=window.crypto||window.msCrypto,ba=(!pa&&ga&&ga.getRandomValues&&(fa=new Uint8Array(16),pa=function(){return ga.getRandomValues(fa),fa}),pa||(ma=new Array(16),pa=function(){for(var e,t=0;t<16;t++)0==(3&t)&&(e=4294967296*Math.random()),ma[t]=e>>>((3&t)<<3)&255;return ma}),"function"==typeof window.Buffer?window.Buffer:Array),n=[],ya={},va=0;va<256;va++)n[va]=(va+256).toString(16).substr(1),ya[n[va]]=va;function wa(e,t){t=t||0;return n[e[t++]]+n[e[t++]]+n[e[t++]]+n[e[t++]]+"-"+n[e[t++]]+n[e[t++]]+"-"+n[e[t++]]+n[e[t++]]+"-"+n[e[t++]]+n[e[t++]]+"-"+n[e[t++]]+n[e[t++]]+n[e[t++]]+n[e[t++]]+n[e[t++]]+n[e[+t]]}var Da=[1|(a=pa())[0],a[1],a[2],a[3],a[4],a[5]],xa=16383&(a[6]<<8|a[7]),Ea=0,Fa=0;function Aa(e,t,n){var a=t&&n||0,r=t||[],n=null!=(e=e||{}).clockseq?e.clockseq:xa,o=null!=e.msecs?e.msecs:(new Date).getTime(),i=null!=e.nsecs?e.nsecs:Fa+1,l=o-Ea+(i-Fa)/1e4;if(l<0&&null==e.clockseq&&(n=n+1&16383),1e4<=(i=(l<0||Ea<o)&&null==e.nsecs?0:i))throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");Ea=o,xa=n;for(var l=(1e4*(268435455&(o+=122192928e5))+(Fa=i))%4294967296,i=(r[a++]=l>>>24&255,r[a++]=l>>>16&255,r[a++]=l>>>8&255,r[a++]=255&l,o/4294967296*1e4&268435455),s=(r[a++]=i>>>8&255,r[a++]=255&i,r[a++]=i>>>24&15|16,r[a++]=i>>>16&255,r[a++]=n>>>8|128,r[a++]=255&n,e.node||Da),u=0;u<6;u++)r[a+u]=s[u];return t||wa(r)}function Ca(e,t,n){var a=t&&n||0,r=("string"==typeof e&&(t="binary"==e?new ba(16):null,e=null),(e=e||{}).random||(e.rng||pa)());if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,t)for(var o=0;o<16;o++)t[a+o]=r[o];return t||wa(r)}(a=Ca).v1=Aa,a.v4=Ca,a.parse=function(e,t,n){var a=t&&n||0,r=0;for(t=t||[],e.toLowerCase().replace(/[0-9a-f]{2}/g,function(e){r<16&&(t[a+r++]=ya[e])});r<16;)t[a+r++]=0;return t},a.unparse=wa,a.BufferClass=ba,axe._uuid=Aa();var ka=Ca,Ta=Object.freeze(["EvalError","RangeError","ReferenceError","SyntaxError","TypeError","URIError"]);function Na(e){var t,n,a,r;try{t=JSON.parse(e)}catch(e){return}if(null!==(e=t)&&"object"===te(e)&&"string"==typeof e.channelId&&e.source===Ra())return n=(e=t).topic,a=e.channelId,r=e.messageId,e=e.keepalive,{topic:n,message:"object"===te(t.error)?function(e){var t=e.message||"Unknown error occurred",n=Ta.includes(e.name)?e.name:"Error",n=window[n]||Error;e.stack&&(t+="\n"+e.stack.replace(e.message,""));return new n(t)}(t.error):t.payload,messageId:r,channelId:a,keepalive:!!e}}function Ra(){var e="axeAPI",t="";return(e=void 0!==axe&&axe._audit&&axe._audit.application?axe._audit.application:e)+"."+(t=void 0!==axe?axe.version:t)}function Oa(e){Sa(e),d(window.parent===e,"Source of the response must be the parent window.")}function _a(e){Sa(e),d(e.parent===window,"Respondable target must be a frame in the current window")}function Sa(e){d(window!==e,"Messages can not be sent to the same window.")}var Ia={},Pa=[];function Ma(){var e="".concat(Ca(),":").concat(Ca());return Pa.includes(e)?Ma():(Pa.push(e),e)}function Ba(n,e,t,a){var r,o;return"function"==typeof a&&function(e,t,n){var a=!(2<arguments.length&&void 0!==n)||n;d(!Ia[e],"A replyHandler already exists for this message channel."),Ia[e]={replyHandler:t,sendToParent:a}}(e.channelId,a,t),(t?Oa:_a)(n),e.message instanceof Error&&!t?(axe.log(e.message),!1):(a=p({messageId:Ma()},e),t=a.topic,e=a.channelId,o=a.message,e={channelId:e,topic:t,messageId:a.messageId,keepalive:!!a.keepalive,source:Ra()},o instanceof Error?e.error={name:o.name,message:o.message,stack:o.stack}:e.payload=o,r=JSON.stringify(e),!(!(t=axe._audit.allowedOrigins)||!t.length||(t.forEach(function(t){try{n.postMessage(r,t)}catch(e){if(e instanceof n.DOMException)throw new Error('allowedOrigins value "'.concat(t,'" is not a valid origin'));throw e}}),0)))}function La(a,r,e){var o=!(2<arguments.length&&void 0!==e)||e;return function(e,t,n){Ba(a,{channelId:r,message:e,keepalive:t},o,n)}}function ja(t,e){var n,a,r,o=t.origin,i=t.data,t=t.source;try{var l=Na(i)||{},s=l.channelId,u=l.message,c=l.messageId;if(a=o,((r=axe._audit.allowedOrigins)&&r.includes("*")||r.includes(a))&&(n=c,!Pa.includes(n))&&(Pa.push(n),!0))if(u instanceof Error&&t.parent!==window)axe.log(u);else try{if(l.topic){var d=La(t,s);Oa(t),e(l,d)}else{var p=t,f=(h=l).channelId,m=h.message,h=h.keepalive,g=(b=function(e){return Ia[e]}(f)||{}).replyHandler,b=b.sendToParent;if(g){(b?Oa:_a)(p);p=La(p,f,b);!h&&f&&!function(e){delete Ia[e]}(f);try{g(m,h,p)}catch(e){axe.log(e),p(e,h)}}}}catch(e){var y=t,v=e,w=s;if(!y.parent!==window)axe.log(v);else try{Ba(y,{topic:null,channelId:w,message:v,messageId:Ma(),keepalive:!0},!0)}catch(e){return void axe.log(e)}}}catch(e){axe.log(e)}}var qa,Va,za={open:function(t){var e;if("function"==typeof window.addEventListener)return window.addEventListener("message",e=function(e){ja(e,t)},!1),function(){window.removeEventListener("message",e,!1)}},post:function(e,t,n){return"function"==typeof window.addEventListener&&Ba(e,t,!1,n)}};function $a(e){e.updateMessenger(za)}var Ua={};function Ha(e,t,n,a,r){t={topic:t,message:n,channelId:"".concat(Ca(),":").concat(Ca()),keepalive:a};return Va(e,t,r)}function Ga(t,n){var e=t.topic,a=t.message,t=t.keepalive,e=Ua[e];if(e)try{e(a,t,n)}catch(e){axe.log(e),n(e,t)}}function Wa(e,t,n,a){var r,o=e.contentWindow,i=null!=(i=null==(i=t.options)?void 0:i.pingWaitTime)?i:500;o?0===i?Ka(e,t,n,a):(r=setTimeout(function(){r=setTimeout(function(){t.debug?a(Ya("No response from frame",e)):n(null)},0)},i),Ha(o,"axe.ping",null,void 0,function(){clearTimeout(r),Ka(e,t,n,a)})):(sn("Frame does not have a content window",e),n(null))}function Ka(e,t,n,a){var r=null!=(r=null==(r=t.options)?void 0:r.frameWaitTime)?r:6e4,o=e.contentWindow,i=setTimeout(function(){a(Ya("Axe in frame timed out",e))},r);Ha(o,"axe.start",t,void 0,function(e){clearTimeout(i),(e instanceof Error==!1?n:a)(e)})}function Ya(e,t){var n;return axe._tree&&(n=Gn(t)),new Error(e+": "+(n||t))}Ha.updateMessenger=function(e){var t=e.open,e=e.post,t=(d("function"==typeof t,"open callback must be a function"),d("function"==typeof e,"post callback must be a function"),qa&&qa(),t(Ga));qa=t?(d("function"==typeof t,"open callback must return a cleanup function"),t):null,Va=e},Ha.subscribe=function(e,t){d("function"==typeof t,"Subscriber callback must be a function"),d(!Ua[e],"Topic ".concat(e," is already registered to.")),Ua[e]=t},Ha.isInFrame=function(){return!!(0<arguments.length&&void 0!==arguments[0]?arguments[0]:window).frameElement},$a(Ha);var Ja=function(e){return[].concat(e.any||[]).concat(e.all||[]).concat(e.none||[])},Xa=function(e,t,n){if(Array.isArray(e))return e.find(function(e){return"object"===te(e)&&e[t]===n})};function Qa(e,t){for(var n=0<arguments.length&&void 0!==e?e:[],a=1<arguments.length&&void 0!==t?t:[],r=Math.max(null==n?void 0:n.length,null==a?void 0:a.length),o=0;o<r;o++){var i=null==n?void 0:n[o],l=null==a?void 0:a[o];if("number"!=typeof i||isNaN(i))return 0===o?1:-1;if("number"!=typeof l||isNaN(l))return 0===o?-1:1;if(i!==l)return i-l}return 0}var Za=function(e,d){var p=[];return e.forEach(function(e){var c,t=(t=e)&&t.results?Array.isArray(t.results)?t.results.length?t.results:null:[t.results]:null;t&&t.length&&(c=function(e,t){{if(e.frameElement)return new Qn(e.frameElement,t);if(e.frameSpec)return e.frameSpec}return null}(e,d),t.forEach(function(e){e.nodes&&c&&(a=e.nodes,t=d,n=c,a.forEach(function(e){e.node=Qn.fromFrame(e.node,t,n),Ja(e).forEach(function(e){e.relatedNodes=e.relatedNodes.map(function(e){return Qn.fromFrame(e,t,n)})})}));var t,n,a=Xa(p,"id",e.id);if(a){if(e.nodes.length){for(var r=a.nodes,o=e.nodes,i=o[0].node,l=0;l<r.length;l++){var s=r[l].node,u=Qa(s.nodeIndexes,i.nodeIndexes);if(0<u||0===u&&i.selector.length<s.selector.length)return void r.splice.apply(r,[l,0].concat(v(o)))}r.push.apply(r,v(o))}}else p.push(e)}))}),p.forEach(function(e){e.nodes&&e.nodes.sort(function(e,t){return Qa(e.node.nodeIndexes,t.node.nodeIndexes)})}),p};function er(e,r,o,i,t,n){var l=ha();e.frames.forEach(function(e){var n=e.node,a=m(e,I);l.defer(function(t,e){Wa(n,{options:r,command:o,parameter:i,context:a},function(e){return t(e?{results:e,frameElement:n}:null)},e)})}),l.then(function(e){t(Za(e,r))}).catch(n)}function tr(e,t){if(!e.shadowId&&!t.shadowId&&e.actualNode&&"function"==typeof e.actualNode.contains)return e.actualNode.contains(t.actualNode);do{if(e===t)return!0;if(t.nodeIndex<e.nodeIndex)return!1}while(t=t.parent);return!1}var nr=function r(){for(var o={},e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.forEach(function(e){if(e&&"object"===te(e)&&!Array.isArray(e))for(var t=0,n=Object.keys(e);t<n.length;t++){var a=n[t];!o.hasOwnProperty(a)||"object"!==te(e[a])||Array.isArray(o[a])?o[a]=e[a]:o[a]=r(o[a],e[a])}}),o},ar=function(t,n){Object.assign(t,n),Object.keys(n).filter(function(e){return"function"==typeof n[e]}).forEach(function(e){t[e]=null;try{t[e]=n[e](t)}catch(e){}})},rr=["article","aside","blockquote","body","div","footer","h1","h2","h3","h4","h5","h6","header","main","nav","p","section","span"],or=function(e){if(e.shadowRoot){e=e.nodeName.toLowerCase();if(rr.includes(e)||/^[a-z][a-z0-9_.-]*-[a-z0-9_.-]*$/.test(e))return!0}return!1},ir={},lr=(fe(ir,{createGrid:function(){return Hr},findElmsInContext:function(){return sr},findNearbyElms:function(){return Qr},findUp:function(){return cr},findUpVirtual:function(){return ur},focusDisabled:function(){return io},getComposedParent:function(){return u},getElementByReference:function(){return co},getElementCoordinates:function(){return Sr},getElementStack:function(){return xo},getModalDialog:function(){return to},getOverflowHiddenAncestors:function(){return mr},getRootNode:function(){return E},getScrollOffset:function(){return _r},getTabbableElements:function(){return Eo},getTextElementStack:function(){return al},getViewportSize:function(){return Ir},getVisibleChildTextRects:function(){return nl},hasContent:function(){return ul},hasContentVirtual:function(){return sl},hasLangText:function(){return cl},idrefs:function(){return Ao},insertedIntoFocusOrder:function(){return dl},isCurrentPageLink:function(){return uo},isFocusable:function(){return k},isHTML5:function(){return hl},isHiddenForEveryone:function(){return Nr},isHiddenWithCSS:function(){return ml},isInTabOrder:function(){return gl},isInTextBlock:function(){return wl},isInert:function(){return no},isModalOpen:function(){return Dl},isMultiline:function(){return xl},isNativelyFocusable:function(){return $o},isNode:function(){return El},isOffscreen:function(){return Pr},isOpaque:function(){return Rl},isSkipLink:function(){return Ol},isVisible:function(){return Ml},isVisibleOnScreen:function(){return F},isVisibleToScreenReaders:function(){return N},isVisualContent:function(){return ol},reduceToElementsBelowFloating:function(){return Bl},shadowElementsFromPoint:function(){return zl},urlPropsFromAttribute:function(){return $l},visuallyContains:function(){return Ll},visuallyOverlaps:function(){return Ul},visuallySort:function(){return po}}),function(e){var t=e.getRootNode&&e.getRootNode()||document;return t=t===e?document:t}),E=lr,sr=function(e){var t=e.context,n=e.value,a=e.attr,e=void 0===(e=e.elm)?"":e,n=y(n),t=9===t.nodeType||11===t.nodeType?t:E(t);return Array.from(t.querySelectorAll(e+"["+a+"="+n+"]"))},ur=function(e,t){var n=e.actualNode;if(!e.shadowId&&"function"==typeof e.actualNode.closest)return e.actualNode.closest(t)||null;for(;(n=(n=n.assignedSlot||n.parentNode)&&11===n.nodeType?n.host:n)&&!On(n,t)&&n!==document.documentElement;);return n&&On(n,t)?n:null},cr=function(e,t){return ur(D(e),t)},dr=he(tn()),t=(axe._memoizedFns=[],function(e){return e=(0,dr.default)(e),axe._memoizedFns.push(e),e});function pr(e,t){return(0|e.left)<(0|t.right)&&(0|e.right)>(0|t.left)&&(0|e.top)<(0|t.bottom)&&(0|e.bottom)>(0|t.top)}var fr=t(function(e){var t=[];return e?("hidden"===e.getComputedStylePropertyValue("overflow")&&t.push(e),t.concat(fr(e.parent))):t}),mr=fr,hr=/rect\s*\(([0-9]+)px,?\s*([0-9]+)px,?\s*([0-9]+)px,?\s*([0-9]+)px\s*\)/,gr=/(\w+)\((\d+)/;function br(e){return["style","script","noscript","template"].includes(e.props.nodeName)}function yr(e){return"area"!==e.props.nodeName&&"none"===e.getComputedStylePropertyValue("display")}function vr(e){return!(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).isAncestor&&["hidden","collapse"].includes(e.getComputedStylePropertyValue("visibility"))}function wr(e){return!!(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).isAncestor&&"hidden"===e.getComputedStylePropertyValue("content-visibility")}function Dr(e){return"true"===e.attr("aria-hidden")}function xr(e){return"0"===e.getComputedStylePropertyValue("opacity")}function Er(e){var t=Ts(e.actualNode),n=parseInt(e.getComputedStylePropertyValue("height")),e=parseInt(e.getComputedStylePropertyValue("width"));return!!t&&(0===n||0===e)}function Fr(e){var t,n;return!(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).isAncestor&&(t=e.boundingClientRect,!!(n=mr(e)).length)&&n.some(function(e){e=e.boundingClientRect;return e.width<2||e.height<2||!pr(t,e)})}function Ar(e){var t=e.getComputedStylePropertyValue("clip").match(hr),n=e.getComputedStylePropertyValue("clip-path").match(gr);if(t&&5===t.length){e=e.getComputedStylePropertyValue("position");if(["fixed","absolute"].includes(e))return t[3]-t[1]<=0&&t[2]-t[4]<=0}if(n){var e=n[1],a=parseInt(n[2],10);switch(e){case"inset":return 50<=a;case"circle":return 0===a}}return!1}function Cr(e,t){var n,a=x(e,"map");return!a||!((a=a.attr("name"))&&(e=lr(e.actualNode))&&9===e.nodeType&&(n=du(axe._tree,'img[usemap="#'.concat(y(a),'"]')))&&n.length)||n.some(function(e){return!t(e)})}function kr(e){var t;if("details"!==(null==(t=e.parent)?void 0:t.props.nodeName))return!1;if("summary"===e.props.nodeName&&e.parent.children.find(function(e){return"summary"===e.props.nodeName})===e)return!1;return!e.parent.hasAttr("open")}var Tr=[yr,vr,wr,kr];function Nr(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},n=t.skipAncestors,t=t.isAncestor,t=void 0!==t&&t;return e=e instanceof b?e:D(e),(n?Rr:Or)(e,t)}var Rr=t(function(t,n){return!!br(t)||!(!t.actualNode||!Tr.some(function(e){return e(t,{isAncestor:n})})&&t.actualNode.isConnected)}),Or=t(function(e,t){return!!Rr(e,t)||!!e.parent&&Or(e.parent,!0)}),u=function e(t){if(t.assignedSlot)return e(t.assignedSlot);if(t.parentNode){if(1===(t=t.parentNode).nodeType)return t;if(t.host)return t.host}return null},_r=function(e){var t,n;return 9===(e=!e.nodeType&&e.document?e.document:e).nodeType?(t=e.documentElement,n=e.body,{left:t&&t.scrollLeft||n&&n.scrollLeft||0,top:t&&t.scrollTop||n&&n.scrollTop||0}):{left:e.scrollLeft,top:e.scrollTop}},Sr=function(e){var t=(n=_r(document)).left,n=n.top;return{top:(e=e.getBoundingClientRect()).top+n,right:e.right+t,bottom:e.bottom+n,left:e.left+t,width:e.right-e.left,height:e.bottom-e.top}},Ir=function(e){var t=e.document,n=t.documentElement;return e.innerWidth?{width:e.innerWidth,height:e.innerHeight}:n?{width:n.clientWidth,height:n.clientHeight}:{width:(e=t.body).clientWidth,height:e.clientHeight}},Pr=function(e){if((1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).isAncestor)return!1;if(e=e instanceof b?e.actualNode:e){var t=document.documentElement,n=window.getComputedStyle(e),a=window.getComputedStyle(document.body||t).getPropertyValue("direction"),r=Sr(e);if(r.bottom<0&&(function(e,t){for(e=u(e);e&&"html"!==e.nodeName.toLowerCase();){if(e.scrollTop&&0<=(t+=e.scrollTop))return;e=u(e)}return 1}(e,r.bottom)||"absolute"===n.position))return!0;if(0!==r.left||0!==r.right)if("ltr"===a){if(r.right<=0)return!0}else if(n=Math.max(t.scrollWidth,Ir(window).width),r.left>=n)return!0;return!1}},Mr=[xr,Er,Fr,Ar,Pr];function F(e){return e=e instanceof b?e:D(e),Br(e)}var Br=t(function(t,n){return t.actualNode&&"area"===t.props.nodeName?!Cr(t,Br):!(Nr(t,{skipAncestors:!0,isAncestor:n})||t.actualNode&&Mr.some(function(e){return e(t,{isAncestor:n})}))&&(!t.parent||Br(t.parent,!0))});function Lr(e,t){var n=Math.min(e.top,t.top),a=Math.max(e.right,t.right),r=Math.max(e.bottom,t.bottom),e=Math.min(e.left,t.left);return new window.DOMRect(e,n,a-e,r-n)}function jr(e,t){var n=e.x,e=e.y,a=t.top,r=t.right,o=t.bottom,t=t.left;return a<=e&&n<=r&&e<=o&&t<=n}var qr=0,Vr=.1,zr=.2,$r=.3,Ur=0;function Hr(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:document.body,t=1<arguments.length?arguments[1]:void 0,n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!w.get("gridCreated")||n){w.set("gridCreated",!0),n||(r=(r=D(document.documentElement))||new Kl(document.documentElement),Ur=0,r._stackingOrder=[Wr(qr,null)],Kr(t=null!=t?t:new Yr,r),Ts(r.actualNode)&&(a=new Yr(r),r._subGrid=a));for(var a,r,o=document.createTreeWalker(e,window.NodeFilter.SHOW_ELEMENT,null,!1),i=n?o.nextNode():o.currentNode;i;){var l=D(i),s=(l&&l.parent?n=l.parent:i.assignedSlot?n=D(i.assignedSlot):i.parentElement?n=D(i.parentElement):i.parentNode&&D(i.parentNode)&&(n=D(i.parentNode)),(l=l||new axe.VirtualNode(i,n))._stackingOrder=function(e,t,n){var a=t._stackingOrder.slice();if(function(e,t){var n=e.getComputedStylePropertyValue("position"),a=e.getComputedStylePropertyValue("z-index");if("fixed"===n||"sticky"===n)return 1;if("auto"!==a&&"static"!==n)return 1;if("1"!==e.getComputedStylePropertyValue("opacity"))return 1;if("none"!==(e.getComputedStylePropertyValue("-webkit-transform")||e.getComputedStylePropertyValue("-ms-transform")||e.getComputedStylePropertyValue("transform")||"none"))return 1;n=e.getComputedStylePropertyValue("mix-blend-mode");if(n&&"normal"!==n)return 1;n=e.getComputedStylePropertyValue("filter");if(n&&"none"!==n)return 1;n=e.getComputedStylePropertyValue("perspective");if(n&&"none"!==n)return 1;n=e.getComputedStylePropertyValue("clip-path");if(n&&"none"!==n)return 1;if("none"!==(e.getComputedStylePropertyValue("-webkit-mask")||e.getComputedStylePropertyValue("mask")||"none"))return 1;if("none"!==(e.getComputedStylePropertyValue("-webkit-mask-image")||e.getComputedStylePropertyValue("mask-image")||"none"))return 1;if("none"!==(e.getComputedStylePropertyValue("-webkit-mask-border")||e.getComputedStylePropertyValue("mask-border")||"none"))return 1;if("isolate"===e.getComputedStylePropertyValue("isolation"))return 1;n=e.getComputedStylePropertyValue("will-change");if("transform"===n||"opacity"===n)return 1;if("touch"===e.getComputedStylePropertyValue("-webkit-overflow-scrolling"))return 1;n=e.getComputedStylePropertyValue("contain");if(["layout","paint","strict","content"].includes(n))return 1;if("auto"!==a&&Gr(t))return 1;return}(e,t)){var r=a.findIndex(function(e){e=e.value;return[qr,zr,$r].includes(e)}),r=(-1!==r&&a.splice(r,a.length-r),function(e,t){return"static"!==e.getComputedStylePropertyValue("position")||Gr(t)?e.getComputedStylePropertyValue("z-index"):"auto"}(e,t));if(["auto","0"].includes(r)){for(var o=n.toString();o.length<10;)o="0"+o;a.push(Wr(parseFloat("".concat(Vr).concat(o)),e))}else a.push(Wr(parseInt(r),e))}else"static"!==e.getComputedStylePropertyValue("position")?a.push(Wr($r,e)):"none"!==e.getComputedStylePropertyValue("float")&&a.push(Wr(zr,e));return a}(l,n,Ur++),function(e,t){var n=null,a=[e];for(;t;){if(Ts(t.actualNode)){n=t;break}if(t._scrollRegionParent){n=t._scrollRegionParent;break}a.push(t),t=D(t.actualNode.parentElement||t.actualNode.parentNode)}return a.forEach(function(e){return e._scrollRegionParent=n}),n}(l,n)),s=s?s._subGrid:t,u=(Ts(l.actualNode)&&(u=new Yr(l),l._subGrid=u),l.boundingClientRect);0!==u.width&&0!==u.height&&F(i)&&Kr(s,l),or(i)&&Hr(i.shadowRoot,s,l),i=o.nextNode()}}return g.gridSize}function Gr(e){if(e)return e=e.getComputedStylePropertyValue("display"),["flex","inline-flex","grid","inline-grid"].includes(e)}function Wr(e,t){return{value:e,vNode:t}}function Kr(t,n){n.clientRects.forEach(function(e){null==n._grid&&(n._grid=t);e=t.getGridPositionOfRect(e);t.loopGridPosition(e,function(e){e.includes(n)||e.push(n)})})}ue(Jr,[{key:"toGridIndex",value:function(e){return Math.floor(e/g.gridSize)}},{key:"getCellFromPoint",value:function(e){var t=e.x,e=e.y,e=(d(this.boundaries,"Grid does not have cells added"),this.toGridIndex(e)),t=this.toGridIndex(t),e=(d(jr({y:e,x:t},this.boundaries),"Element midpoint exceeds the grid bounds"),null!=(e=this.cells[e-this.cells._negativeIndex])?e:[]);return null!=(t=e[t-e._negativeIndex])?t:[]}},{key:"loopGridPosition",value:function(e,a){var t=e,r=t.left,o=t.right,n=t.top,t=t.bottom;this.boundaries&&(e=Lr(this.boundaries,e)),this.boundaries=e,Xr(this.cells,n,t,function(e,n){Xr(e,r,o,function(e,t){a(e,{row:n,col:t})})})}},{key:"getGridPositionOfRect",value:function(e){var t=e.top,n=e.right,a=e.bottom,r=e.left,o=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0,t=this.toGridIndex(t-o),n=this.toGridIndex(n+o-1),a=this.toGridIndex(a+o-1),r=this.toGridIndex(r-o);return new window.DOMRect(r,t,n-r,a-t)}}]);var Yr=Jr;function Jr(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null;le(this,Jr),this.container=e,this.cells=[]}function Xr(e,t,n,a){if(null!=e._negativeIndex||(e._negativeIndex=0),t<e._negativeIndex){for(var r=0;r<e._negativeIndex-t;r++)e.splice(0,0,[]);e._negativeIndex=t}for(var o,i=t-e._negativeIndex,l=n-e._negativeIndex,s=i;s<=l;s++)null==e[o=s]&&(e[o]=[]),a(e[s],s+e._negativeIndex)}function Qr(r){var e,o,t,i,n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0;return Hr(),null!=(t=r._grid)&&null!=(t=t.cells)&&t.length?(t=r.boundingClientRect,e=r._grid,o=Zr(r),t=e.getGridPositionOfRect(t,n),i=[],e.loopGridPosition(t,function(e){var t,n=f(e);try{for(n.s();!(t=n.n()).done;){var a=t.value;a&&a!==r&&!i.includes(a)&&o===Zr(a)&&i.push(a)}}catch(e){n.e(e)}finally{n.f()}}),i):[]}var Zr=t(function(e){return!!e&&("fixed"===e.getComputedStylePropertyValue("position")||Zr(e.parent))});function eo(e,t){var n=Math.max(e.left,t.left),a=Math.min(e.right,t.right),r=Math.max(e.top,t.top),e=Math.min(e.bottom,t.bottom);return a<=n||e<=r?null:new window.DOMRect(n,r,a-n,e-r)}var to=t(function(){var e;return axe._tree&&(e=tu(axe._tree[0],"dialog[open]",function(e){var t=e.boundingClientRect;return document.elementsFromPoint(t.left+1,t.top+1).includes(e.actualNode)&&F(e)})).length?e.find(function(e){var t=e.boundingClientRect;return document.elementsFromPoint(t.left-10,t.top-10).includes(e.actualNode)})||(null!=(e=e.find(function(e){var e=null!=(e=function(e){Hr();var t=axe._tree[0]._grid,n=new window.DOMRect(0,0,window.innerWidth,window.innerHeight);if(t)for(var a=0;a<t.cells.length;a++){var r=t.cells[a];if(r)for(var o=0;o<r.length;o++){var i=r[o];if(i)for(var l=0;l<i.length;l++){var s=i[l],u=eo(s.boundingClientRect,n);if("html"!==s.props.nodeName&&s!==e&&"none"!==s.getComputedStylePropertyValue("pointer-events")&&u)return{vNode:s,rect:u}}}}}(e))?e:{},t=e.vNode,e=e.rect;return!!t&&!document.elementsFromPoint(e.left+1,e.top+1).includes(t.actualNode)}))?e:null):null});function no(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},n=t.skipAncestors,t=t.isAncestor;return(n?ao:ro)(e,t)}var ao=t(function(e,t){if(e.hasAttr("inert"))return!0;if(!t&&e.actualNode){t=to();if(t&&!tr(t,e))return!0}return!1}),ro=t(function(e,t){return!!ao(e,t)||!!e.parent&&ro(e.parent,!0)}),oo=["button","command","fieldset","keygen","optgroup","option","select","textarea","input"],io=function(e){var t=e instanceof b?e:D(e);if(e=t.props.nodeName,oo.includes(e)&&t.hasAttr("disabled")||no(t))return!0;for(var n=t.parent,a=[],r=!1;n&&n.shadowId===t.shadowId&&!r&&(a.push(n),"legend"!==n.props.nodeName);){if(void 0!==n._inDisabledFieldset){r=n._inDisabledFieldset;break}"fieldset"===n.props.nodeName&&n.hasAttr("disabled")&&(r=!0),n=n.parent}return a.forEach(function(e){return e._inDisabledFieldset=r}),!!r||"area"!==t.props.nodeName&&!!t.actualNode&&Nr(t)},lo=/^\/\#/,so=/^#[!/]/;function uo(e){var t,n,a,r,o=e.getAttribute("href");return!(!o||"#"===o)&&(!!lo.test(o)||(r=e.hash,t=e.protocol,n=e.hostname,a=e.port,e=e.pathname,!so.test(r)&&("#"===o.charAt(0)||("string"!=typeof(null==(r=window.location)?void 0:r.origin)||-1===window.location.origin.indexOf("://")?null:(o=window.location.origin+window.location.pathname,r=n?"".concat(t,"//").concat(n).concat(a?":".concat(a):""):window.location.origin,(r+=e?("/"!==e[0]?"/":"")+e:window.location.pathname)===o)))))}var co=function(e,t){var n=e.getAttribute(t);return n&&("href"!==t||uo(e))?(-1!==n.indexOf("#")&&(n=decodeURIComponent(n.substr(n.indexOf("#")+1))),(t=document.getElementById(n))||((t=document.getElementsByName(n)).length?t[0]:null)):null};function po(e,t){Hr();for(var n=Math.max(e._stackingOrder.length,t._stackingOrder.length),a=0;a<n;a++){if(void 0===t._stackingOrder[a])return-1;if(void 0===e._stackingOrder[a])return 1;if(t._stackingOrder[a].value>e._stackingOrder[a].value)return 1;if(t._stackingOrder[a].value<e._stackingOrder[a].value)return-1}var r=e.actualNode,o=t.actualNode;if(r.getRootNode&&r.getRootNode()!==o.getRootNode()){for(var i=[];r;)i.push({root:r.getRootNode(),node:r}),r=r.getRootNode().host;for(;o&&!i.find(function(e){return e.root===o.getRootNode()});)o=o.getRootNode().host;if((r=i.find(function(e){return e.root===o.getRootNode()}).node)===o)return e.actualNode.getRootNode()!==r.getRootNode()?-1:1}var l=window.Node,s=l.DOCUMENT_POSITION_FOLLOWING,u=l.DOCUMENT_POSITION_CONTAINS,l=l.DOCUMENT_POSITION_CONTAINED_BY,c=r.compareDocumentPosition(o),s=c&s?1:-1,u=c&u||c&l,c=fo(e),l=fo(t);return c===l||u?s:l-c}function fo(e){return-1!==e.getComputedStylePropertyValue("display").indexOf("inline")?2:function e(t){if(!t)return!1;if(void 0!==t._isFloated)return t._isFloated;var n=t.getComputedStylePropertyValue("float");if("none"!==n)return t._isFloated=!0;n=e(t.parent);t._isFloated=n;return n}(e)?1:0}var mo={};function ho(e,t){var o,i,l,e=e.boundingClientRect,t=t.boundingClientRect,n=(o=e,i=t,l={},[["x","left","right","width"],["y","top","bottom","height"]].forEach(function(e){var t,e=h(e,4),n=e[0],a=e[1],r=e[2],e=e[3];i[a]<o[a]&&i[r]>o[r]?l[n]=o[a]+o[e]/2:(e=i[a]+i[e]/2,t=Math.abs(e-o[a]),e=Math.abs(e-o[r]),l[n]=e<=t?o[a]:o[r])}),l),e=function(e,t,n){var a=e.x,e=e.y;if(function(e,t){var n=e.x,e=e.y;return e>=t.top&&n<=t.right&&e<=t.bottom&&n>=t.left}({x:a,y:e},n)){var r=function(e,t,n){var a,r,o=e.x,e=e.y;o===t.left&&t.right<n.right?a=t.right:o===t.right&&t.left>n.left&&(a=t.left);e===t.top&&t.bottom<n.bottom?r=t.bottom:e===t.bottom&&t.top>n.top&&(r=t.top);{if(!a&&!r)return null;if(!r)return{x:a,y:e};if(!a)return{x:o,y:r}}return Math.abs(o-a)<Math.abs(e-r)?{x:a,y:e}:{x:o,y:r}}({x:a,y:e},t,n);if(null!==r)return r;n=t}var r=n,t=r.top,n=r.right,o=r.bottom,r=r.left,i=r<=a&&a<=n,l=t<=e&&e<=o,r=Math.abs(r-a)<Math.abs(n-a)?r:n,n=Math.abs(t-e)<Math.abs(o-e)?t:o;{if(!i&&l)return{x:r,y:e};if(i&&!l)return{x:a,y:n};if(!i&&!l)return{x:r,y:n}}return Math.abs(a-r)<Math.abs(e-n)?{x:r,y:e}:{x:a,y:n}}(n,e,t);return t=n,n=e,e=Math.abs(t.x-n.x),t=Math.abs(t.y-n.y),e&&t?Math.sqrt(Math.pow(e,2)+Math.pow(t,2)):e||t}function go(e){var t=e.left,n=e.top,a=e.width,e=e.height;return new window.DOMPoint(t+a/2,n+e/2)}function bo(e,t){var n=e.boundingClientRect,a=t.boundingClientRect;return!(n.left>=a.right||n.right<=a.left||n.top>=a.bottom||n.bottom<=a.top)&&0<po(e,t)}function yo(e,t){var a,r=[e],n=f(t);try{function o(){var n=a.value;r=r.reduce(function(e,t){return e.concat(function(e,t){var n=e.top,a=e.left,r=e.bottom,o=e.right,i=n<t.bottom&&r>t.top,l=a<t.right&&o>t.left,s=[];vo(t.top,n,r)&&l&&s.push({top:n,left:a,bottom:t.top,right:o});vo(t.right,a,o)&&i&&s.push({top:n,left:t.right,bottom:r,right:o});vo(t.bottom,n,r)&&l&&s.push({top:t.bottom,right:o,bottom:r,left:a});vo(t.left,a,o)&&i&&s.push({top:n,left:a,bottom:r,right:t.left});0===s.length&&s.push(e);return s.map(wo)}(t,n))},[])}for(n.s();!(a=n.n()).done;)o()}catch(e){n.e(e)}finally{n.f()}return r}fe(mo,{getBoundingRect:function(){return Lr},getIntersectionRect:function(){return eo},getOffset:function(){return ho},getRectCenter:function(){return go},hasVisualOverlap:function(){return bo},isPointInRect:function(){return jr},rectsOverlap:function(){return pr},splitRects:function(){return yo}});var vo=function(e,t,n){return t<e&&e<n};function wo(e){return p({},e,{x:e.left,y:e.top,height:e.bottom-e.top,width:e.right-e.left})}function Do(e,t,n){var a=2<arguments.length&&void 0!==n&&n,r=go(t),o=e.getCellFromPoint(r)||[],i=Math.floor(r.x),l=Math.floor(r.y),r=o.filter(function(e){return e.clientRects.some(function(e){var t=e.left,n=e.top;return i<Math.floor(t+e.width)&&i>=Math.floor(t)&&l<Math.floor(n+e.height)&&l>=Math.floor(n)})}),o=e.container;return o&&(r=Do(o._grid,o.boundingClientRect,!0).concat(r)),r=a?r:r.sort(po).map(function(e){return e.actualNode}).concat(document.documentElement).filter(function(e,t,n){return n.indexOf(e)===t})}var xo=function(e){Hr();var t=(e=D(e))._grid;return t?Do(t,e.boundingClientRect):[]},Eo=function(e){return du(e,"*").filter(function(e){var t=e.isFocusable,e=e.actualNode.getAttribute("tabindex");return(e=e&&!isNaN(parseInt(e,10))?parseInt(e):null)?t&&0<=e:t})},Fo={},Ao=(fe(Fo,{accessibleText:function(){return Co},accessibleTextVirtual:function(){return l},autocomplete:function(){return Ki},formControlValue:function(){return Bi},formControlValueMethods:function(){return Pi},hasUnicode:function(){return zi},isHumanInterpretable:function(){return Wi},isIconLigature:function(){return $i},isValidAutocomplete:function(){return Yi},label:function(){return Zi},labelText:function(){return bi},labelVirtual:function(){return Qi},nativeElementType:function(){return el},nativeTextAlternative:function(){return xi},nativeTextMethods:function(){return Di},removeUnicode:function(){return Gi},sanitize:function(){return C},subtreeText:function(){return gi},titleText:function(){return pi},unsupported:function(){return Ei},visible:function(){return Xi},visibleTextNodes:function(){return tl},visibleVirtual:function(){return Ai}}),function(e,t){e=e.actualNode||e;try{var n=E(e),a=[];if(r=e.getAttribute(t))for(var r=_(r),o=0;o<r.length;o++)a.push(n.getElementById(r[o]));return a}catch(e){throw new TypeError("Cannot resolve id references for non-DOM nodes")}}),Co=function(e,t){return e=D(e),l(e,t)},ko=function(n){var a=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};if(!(n instanceof b)){if(1!==n.nodeType)return"";n=D(n)}return 1!==n.props.nodeType||a.inLabelledByContext||a.inControlContext||!n.attr("aria-labelledby")?"":Ao(n,"aria-labelledby").filter(function(e){return e}).reduce(function(e,t){t=Co(t,p({inLabelledByContext:!0,startNode:a.startNode||n},a));return e?"".concat(e," ").concat(t):t},"")},To=function(e){if(!(e instanceof b)){if(1!==e.nodeType)return"";e=D(e)}return e.attr("aria-label")||""},No={"aria-activedescendant":{type:"idref",allowEmpty:!0},"aria-atomic":{type:"boolean",global:!0},"aria-autocomplete":{type:"nmtoken",values:["inline","list","both","none"]},"aria-braillelabel":{type:"string",global:!0},"aria-brailleroledescription":{type:"string",global:!0},"aria-busy":{type:"boolean",global:!0},"aria-checked":{type:"nmtoken",values:["false","mixed","true","undefined"]},"aria-colcount":{type:"int",minValue:-1},"aria-colindex":{type:"int",minValue:1},"aria-colspan":{type:"int",minValue:1},"aria-controls":{type:"idrefs",allowEmpty:!0,global:!0},"aria-current":{type:"nmtoken",allowEmpty:!0,values:["page","step","location","date","time","true","false"],global:!0},"aria-describedby":{type:"idrefs",allowEmpty:!0,global:!0},"aria-description":{type:"string",allowEmpty:!0,global:!0},"aria-details":{type:"idref",allowEmpty:!0,global:!0},"aria-disabled":{type:"boolean",global:!0},"aria-dropeffect":{type:"nmtokens",values:["copy","execute","link","move","none","popup"],global:!0},"aria-errormessage":{type:"idref",allowEmpty:!0,global:!0},"aria-expanded":{type:"nmtoken",values:["true","false","undefined"]},"aria-flowto":{type:"idrefs",allowEmpty:!0,global:!0},"aria-grabbed":{type:"nmtoken",values:["true","false","undefined"],global:!0},"aria-haspopup":{type:"nmtoken",allowEmpty:!0,values:["true","false","menu","listbox","tree","grid","dialog"],global:!0},"aria-hidden":{type:"nmtoken",values:["true","false","undefined"],global:!0},"aria-invalid":{type:"nmtoken",values:["grammar","false","spelling","true"],global:!0},"aria-keyshortcuts":{type:"string",allowEmpty:!0,global:!0},"aria-label":{type:"string",allowEmpty:!0,global:!0},"aria-labelledby":{type:"idrefs",allowEmpty:!0,global:!0},"aria-level":{type:"int",minValue:1},"aria-live":{type:"nmtoken",values:["assertive","off","polite"],global:!0},"aria-modal":{type:"boolean"},"aria-multiline":{type:"boolean"},"aria-multiselectable":{type:"boolean"},"aria-orientation":{type:"nmtoken",values:["horizontal","undefined","vertical"]},"aria-owns":{type:"idrefs",allowEmpty:!0,global:!0},"aria-placeholder":{type:"string",allowEmpty:!0},"aria-posinset":{type:"int",minValue:1},"aria-pressed":{type:"nmtoken",values:["false","mixed","true","undefined"]},"aria-readonly":{type:"boolean"},"aria-relevant":{type:"nmtokens",values:["additions","all","removals","text"],global:!0},"aria-required":{type:"boolean"},"aria-roledescription":{type:"string",allowEmpty:!0,global:!0},"aria-rowcount":{type:"int",minValue:-1},"aria-rowindex":{type:"int",minValue:1},"aria-rowspan":{type:"int",minValue:0},"aria-selected":{type:"nmtoken",values:["false","true","undefined"]},"aria-setsize":{type:"int",minValue:-1},"aria-sort":{type:"nmtoken",values:["ascending","descending","none","other"]},"aria-valuemax":{type:"decimal"},"aria-valuemin":{type:"decimal"},"aria-valuenow":{type:"decimal"},"aria-valuetext":{type:"string"}},Ro={alert:{type:"widget",allowedAttrs:["aria-expanded"],superclassRole:["section"]},alertdialog:{type:"widget",allowedAttrs:["aria-expanded","aria-modal"],superclassRole:["alert","dialog"],accessibleNameRequired:!0},application:{type:"landmark",allowedAttrs:["aria-activedescendant","aria-expanded"],superclassRole:["structure"],accessibleNameRequired:!0},article:{type:"structure",allowedAttrs:["aria-posinset","aria-setsize","aria-expanded"],superclassRole:["document"]},banner:{type:"landmark",allowedAttrs:["aria-expanded"],superclassRole:["landmark"]},blockquote:{type:"structure",superclassRole:["section"]},button:{type:"widget",allowedAttrs:["aria-expanded","aria-pressed"],superclassRole:["command"],accessibleNameRequired:!0,nameFromContent:!0,childrenPresentational:!0},caption:{type:"structure",requiredContext:["figure","table","grid","treegrid"],superclassRole:["section"],prohibitedAttrs:["aria-label","aria-labelledby"]},cell:{type:"structure",requiredContext:["row"],allowedAttrs:["aria-colindex","aria-colspan","aria-rowindex","aria-rowspan","aria-expanded"],superclassRole:["section"],nameFromContent:!0},checkbox:{type:"widget",requiredAttrs:["aria-checked"],allowedAttrs:["aria-readonly","aria-required"],superclassRole:["input"],accessibleNameRequired:!0,nameFromContent:!0,childrenPresentational:!0},code:{type:"structure",superclassRole:["section"],prohibitedAttrs:["aria-label","aria-labelledby"]},columnheader:{type:"structure",requiredContext:["row"],allowedAttrs:["aria-sort","aria-colindex","aria-colspan","aria-expanded","aria-readonly","aria-required","aria-rowindex","aria-rowspan","aria-selected"],superclassRole:["cell","gridcell","sectionhead"],accessibleNameRequired:!1,nameFromContent:!0},combobox:{type:"widget",requiredAttrs:["aria-expanded","aria-controls"],allowedAttrs:["aria-owns","aria-autocomplete","aria-readonly","aria-required","aria-activedescendant","aria-orientation"],superclassRole:["select"],accessibleNameRequired:!0},command:{type:"abstract",superclassRole:["widget"]},complementary:{type:"landmark",allowedAttrs:["aria-expanded"],superclassRole:["landmark"]},composite:{type:"abstract",superclassRole:["widget"]},contentinfo:{type:"landmark",allowedAttrs:["aria-expanded"],superclassRole:["landmark"]},comment:{type:"structure",allowedAttrs:["aria-level","aria-posinset","aria-setsize"],superclassRole:["article"]},definition:{type:"structure",allowedAttrs:["aria-expanded"],superclassRole:["section"]},deletion:{type:"structure",superclassRole:["section"],prohibitedAttrs:["aria-label","aria-labelledby"]},dialog:{type:"widget",allowedAttrs:["aria-expanded","aria-modal"],superclassRole:["window"],accessibleNameRequired:!0},directory:{type:"structure",deprecated:!0,allowedAttrs:["aria-expanded"],superclassRole:["list"],nameFromContent:!0},document:{type:"structure",allowedAttrs:["aria-expanded"],superclassRole:["structure"]},emphasis:{type:"structure",superclassRole:["section"],prohibitedAttrs:["aria-label","aria-labelledby"]},feed:{type:"structure",requiredOwned:["article"],allowedAttrs:["aria-expanded"],superclassRole:["list"]},figure:{type:"structure",allowedAttrs:["aria-expanded"],superclassRole:["section"],nameFromContent:!0},form:{type:"landmark",allowedAttrs:["aria-expanded"],superclassRole:["landmark"]},grid:{type:"composite",requiredOwned:["rowgroup","row"],allowedAttrs:["aria-level","aria-multiselectable","aria-readonly","aria-activedescendant","aria-colcount","aria-expanded","aria-rowcount"],superclassRole:["composite","table"],accessibleNameRequired:!1},gridcell:{type:"widget",requiredContext:["row"],allowedAttrs:["aria-readonly","aria-required","aria-selected","aria-colindex","aria-colspan","aria-expanded","aria-rowindex","aria-rowspan"],superclassRole:["cell","widget"],nameFromContent:!0},group:{type:"structure",allowedAttrs:["aria-activedescendant","aria-expanded"],superclassRole:["section"]},heading:{type:"structure",requiredAttrs:["aria-level"],allowedAttrs:["aria-expanded"],superclassRole:["sectionhead"],accessibleNameRequired:!1,nameFromContent:!0},img:{type:"structure",allowedAttrs:["aria-expanded"],superclassRole:["section"],accessibleNameRequired:!0,childrenPresentational:!0},input:{type:"abstract",superclassRole:["widget"]},insertion:{type:"structure",superclassRole:["section"],prohibitedAttrs:["aria-label","aria-labelledby"]},landmark:{type:"abstract",superclassRole:["section"]},link:{type:"widget",allowedAttrs:["aria-expanded"],superclassRole:["command"],accessibleNameRequired:!0,nameFromContent:!0},list:{type:"structure",requiredOwned:["listitem"],allowedAttrs:["aria-expanded"],superclassRole:["section"]},listbox:{type:"widget",requiredOwned:["group","option"],allowedAttrs:["aria-multiselectable","aria-readonly","aria-required","aria-activedescendant","aria-expanded","aria-orientation"],superclassRole:["select"],accessibleNameRequired:!0},listitem:{type:"structure",requiredContext:["list"],allowedAttrs:["aria-level","aria-posinset","aria-setsize","aria-expanded"],superclassRole:["section"],nameFromContent:!0},log:{type:"widget",allowedAttrs:["aria-expanded"],superclassRole:["section"]},main:{type:"landmark",allowedAttrs:["aria-expanded"],superclassRole:["landmark"]},marquee:{type:"widget",allowedAttrs:["aria-expanded"],superclassRole:["section"]},math:{type:"structure",allowedAttrs:["aria-expanded"],superclassRole:["section"],childrenPresentational:!0},menu:{type:"composite",requiredOwned:["group","menuitemradio","menuitem","menuitemcheckbox","menu","separator"],allowedAttrs:["aria-activedescendant","aria-expanded","aria-orientation"],superclassRole:["select"]},menubar:{type:"composite",requiredOwned:["group","menuitemradio","menuitem","menuitemcheckbox","menu","separator"],allowedAttrs:["aria-activedescendant","aria-expanded","aria-orientation"],superclassRole:["menu"]},menuitem:{type:"widget",requiredContext:["menu","menubar","group"],allowedAttrs:["aria-posinset","aria-setsize","aria-expanded"],superclassRole:["command"],accessibleNameRequired:!0,nameFromContent:!0},menuitemcheckbox:{type:"widget",requiredContext:["menu","menubar","group"],requiredAttrs:["aria-checked"],allowedAttrs:["aria-posinset","aria-readonly","aria-setsize"],superclassRole:["checkbox","menuitem"],accessibleNameRequired:!0,nameFromContent:!0,childrenPresentational:!0},menuitemradio:{type:"widget",requiredContext:["menu","menubar","group"],requiredAttrs:["aria-checked"],allowedAttrs:["aria-posinset","aria-readonly","aria-setsize"],superclassRole:["menuitemcheckbox","radio"],accessibleNameRequired:!0,nameFromContent:!0,childrenPresentational:!0},meter:{type:"structure",requiredAttrs:["aria-valuenow"],allowedAttrs:["aria-valuemax","aria-valuemin","aria-valuetext"],superclassRole:["range"],accessibleNameRequired:!0,childrenPresentational:!0},mark:{type:"structure",superclassRole:["section"],prohibitedAttrs:["aria-label","aria-labelledby"]},navigation:{type:"landmark",allowedAttrs:["aria-expanded"],superclassRole:["landmark"]},none:{type:"structure",superclassRole:["structure"],prohibitedAttrs:["aria-label","aria-labelledby"]},note:{type:"structure",allowedAttrs:["aria-expanded"],superclassRole:["section"]},option:{type:"widget",requiredContext:["group","listbox"],allowedAttrs:["aria-selected","aria-checked","aria-posinset","aria-setsize"],superclassRole:["input"],accessibleNameRequired:!0,nameFromContent:!0,childrenPresentational:!0},paragraph:{type:"structure",superclassRole:["section"],prohibitedAttrs:["aria-label","aria-labelledby"]},presentation:{type:"structure",superclassRole:["structure"],prohibitedAttrs:["aria-label","aria-labelledby"]},progressbar:{type:"widget",allowedAttrs:["aria-expanded","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext"],superclassRole:["range"],accessibleNameRequired:!0,childrenPresentational:!0},radio:{type:"widget",requiredAttrs:["aria-checked"],allowedAttrs:["aria-posinset","aria-setsize","aria-required"],superclassRole:["input"],accessibleNameRequired:!0,nameFromContent:!0,childrenPresentational:!0},radiogroup:{type:"composite",allowedAttrs:["aria-readonly","aria-required","aria-activedescendant","aria-expanded","aria-orientation"],superclassRole:["select"],accessibleNameRequired:!1},range:{type:"abstract",superclassRole:["widget"]},region:{type:"landmark",allowedAttrs:["aria-expanded"],superclassRole:["landmark"],accessibleNameRequired:!1},roletype:{type:"abstract",superclassRole:[]},row:{type:"structure",requiredContext:["grid","rowgroup","table","treegrid"],requiredOwned:["cell","columnheader","gridcell","rowheader"],allowedAttrs:["aria-colindex","aria-level","aria-rowindex","aria-selected","aria-activedescendant","aria-expanded","aria-posinset","aria-setsize"],superclassRole:["group","widget"],nameFromContent:!0},rowgroup:{type:"structure",requiredContext:["grid","table","treegrid"],requiredOwned:["row"],superclassRole:["structure"],nameFromContent:!0},rowheader:{type:"structure",requiredContext:["row"],allowedAttrs:["aria-sort","aria-colindex","aria-colspan","aria-expanded","aria-readonly","aria-required","aria-rowindex","aria-rowspan","aria-selected"],superclassRole:["cell","gridcell","sectionhead"],accessibleNameRequired:!1,nameFromContent:!0},scrollbar:{type:"widget",requiredAttrs:["aria-valuenow"],allowedAttrs:["aria-controls","aria-orientation","aria-valuemax","aria-valuemin","aria-valuetext"],superclassRole:["range"],childrenPresentational:!0},search:{type:"landmark",allowedAttrs:["aria-expanded"],superclassRole:["landmark"]},searchbox:{type:"widget",allowedAttrs:["aria-activedescendant","aria-autocomplete","aria-multiline","aria-placeholder","aria-readonly","aria-required"],superclassRole:["textbox"],accessibleNameRequired:!0},section:{type:"abstract",superclassRole:["structure"],nameFromContent:!0},sectionhead:{type:"abstract",superclassRole:["structure"],nameFromContent:!0},select:{type:"abstract",superclassRole:["composite","group"]},separator:{type:"structure",requiredAttrs:["aria-valuenow"],allowedAttrs:["aria-valuemax","aria-valuemin","aria-orientation","aria-valuetext"],superclassRole:["structure","widget"],childrenPresentational:!0},slider:{type:"widget",requiredAttrs:["aria-valuenow"],allowedAttrs:["aria-valuemax","aria-valuemin","aria-orientation","aria-readonly","aria-valuetext"],superclassRole:["input","range"],accessibleNameRequired:!0,childrenPresentational:!0},spinbutton:{type:"widget",allowedAttrs:["aria-valuemax","aria-valuemin","aria-readonly","aria-required","aria-activedescendant","aria-valuetext","aria-valuenow"],superclassRole:["composite","input","range"],accessibleNameRequired:!0},status:{type:"widget",allowedAttrs:["aria-expanded"],superclassRole:["section"]},strong:{type:"structure",superclassRole:["section"],prohibitedAttrs:["aria-label","aria-labelledby"]},structure:{type:"abstract",superclassRole:["roletype"]},subscript:{type:"structure",superclassRole:["section"],prohibitedAttrs:["aria-label","aria-labelledby"]},superscript:{type:"structure",superclassRole:["section"],prohibitedAttrs:["aria-label","aria-labelledby"]},switch:{type:"widget",requiredAttrs:["aria-checked"],allowedAttrs:["aria-readonly"],superclassRole:["checkbox"],accessibleNameRequired:!0,nameFromContent:!0,childrenPresentational:!0},suggestion:{type:"structure",requiredOwned:["insertion","deletion"],superclassRole:["section"],prohibitedAttrs:["aria-label","aria-labelledby"]},tab:{type:"widget",requiredContext:["tablist"],allowedAttrs:["aria-posinset","aria-selected","aria-setsize","aria-expanded"],superclassRole:["sectionhead","widget"],nameFromContent:!0,childrenPresentational:!0},table:{type:"structure",requiredOwned:["rowgroup","row"],allowedAttrs:["aria-colcount","aria-rowcount","aria-expanded"],superclassRole:["section"],accessibleNameRequired:!1,nameFromContent:!0},tablist:{type:"composite",requiredOwned:["tab"],allowedAttrs:["aria-level","aria-multiselectable","aria-orientation","aria-activedescendant","aria-expanded"],superclassRole:["composite"]},tabpanel:{type:"widget",allowedAttrs:["aria-expanded"],superclassRole:["section"],accessibleNameRequired:!1},term:{type:"structure",allowedAttrs:["aria-expanded"],superclassRole:["section"],nameFromContent:!0},text:{type:"structure",superclassRole:["section"],nameFromContent:!0},textbox:{type:"widget",allowedAttrs:["aria-activedescendant","aria-autocomplete","aria-multiline","aria-placeholder","aria-readonly","aria-required"],superclassRole:["input"],accessibleNameRequired:!0},time:{type:"structure",superclassRole:["section"]},timer:{type:"widget",allowedAttrs:["aria-expanded"],superclassRole:["status"]},toolbar:{type:"structure",allowedAttrs:["aria-orientation","aria-activedescendant","aria-expanded"],superclassRole:["group"],accessibleNameRequired:!0},tooltip:{type:"structure",allowedAttrs:["aria-expanded"],superclassRole:["section"],nameFromContent:!0},tree:{type:"composite",requiredOwned:["group","treeitem"],allowedAttrs:["aria-multiselectable","aria-required","aria-activedescendant","aria-expanded","aria-orientation"],superclassRole:["select"],accessibleNameRequired:!1},treegrid:{type:"composite",requiredOwned:["rowgroup","row"],allowedAttrs:["aria-activedescendant","aria-colcount","aria-expanded","aria-level","aria-multiselectable","aria-orientation","aria-readonly","aria-required","aria-rowcount"],superclassRole:["grid","tree"],accessibleNameRequired:!1},treeitem:{type:"widget",requiredContext:["group","tree"],allowedAttrs:["aria-checked","aria-expanded","aria-level","aria-posinset","aria-selected","aria-setsize"],superclassRole:["listitem","option"],accessibleNameRequired:!0,nameFromContent:!0},widget:{type:"abstract",superclassRole:["roletype"]},window:{type:"abstract",superclassRole:["roletype"]}},a={a:{variant:{href:{matches:"[href]",contentTypes:["interactive","phrasing","flow"],allowedRoles:["button","checkbox","menuitem","menuitemcheckbox","menuitemradio","option","radio","switch","tab","treeitem","doc-backlink","doc-biblioref","doc-glossref","doc-noteref"],namingMethods:["subtreeText"]},default:{contentTypes:["phrasing","flow"],allowedRoles:!0}}},abbr:{contentTypes:["phrasing","flow"],allowedRoles:!0},address:{contentTypes:["flow"],allowedRoles:!0},area:{variant:{href:{matches:"[href]",allowedRoles:!1},default:{allowedRoles:["button","link"]}},contentTypes:["phrasing","flow"],namingMethods:["altText"]},article:{contentTypes:["sectioning","flow"],allowedRoles:["feed","presentation","none","document","application","main","region"],shadowRoot:!0},aside:{contentTypes:["sectioning","flow"],allowedRoles:["feed","note","presentation","none","region","search","doc-dedication","doc-example","doc-footnote","doc-pullquote","doc-tip"]},audio:{variant:{controls:{matches:"[controls]",contentTypes:["interactive","embedded","phrasing","flow"]},default:{contentTypes:["embedded","phrasing","flow"]}},allowedRoles:["application"],chromiumRole:"Audio"},b:{contentTypes:["phrasing","flow"],allowedRoles:!0},base:{allowedRoles:!1,noAriaAttrs:!0},bdi:{contentTypes:["phrasing","flow"],allowedRoles:!0},bdo:{contentTypes:["phrasing","flow"],allowedRoles:!0},blockquote:{contentTypes:["flow"],allowedRoles:!0,shadowRoot:!0},body:{allowedRoles:!1,shadowRoot:!0},br:{contentTypes:["phrasing","flow"],allowedRoles:["presentation","none"],namingMethods:["titleText","singleSpace"]},button:{contentTypes:["interactive","phrasing","flow"],allowedRoles:["checkbox","combobox","link","menuitem","menuitemcheckbox","menuitemradio","option","radio","switch","tab"],namingMethods:["subtreeText"]},canvas:{allowedRoles:!0,contentTypes:["embedded","phrasing","flow"],chromiumRole:"Canvas"},caption:{allowedRoles:!1},cite:{contentTypes:["phrasing","flow"],allowedRoles:!0},code:{contentTypes:["phrasing","flow"],allowedRoles:!0},col:{allowedRoles:!1,noAriaAttrs:!0},colgroup:{allowedRoles:!1,noAriaAttrs:!0},data:{contentTypes:["phrasing","flow"],allowedRoles:!0},datalist:{contentTypes:["phrasing","flow"],allowedRoles:!1,noAriaAttrs:!0,implicitAttrs:{"aria-multiselectable":"false"}},dd:{allowedRoles:!1},del:{contentTypes:["phrasing","flow"],allowedRoles:!0},dfn:{contentTypes:["phrasing","flow"],allowedRoles:!0},details:{contentTypes:["interactive","flow"],allowedRoles:!1},dialog:{contentTypes:["flow"],allowedRoles:["alertdialog"]},div:{contentTypes:["flow"],allowedRoles:!0,shadowRoot:!0},dl:{contentTypes:["flow"],allowedRoles:["group","list","presentation","none"],chromiumRole:"DescriptionList"},dt:{allowedRoles:["listitem"]},em:{contentTypes:["phrasing","flow"],allowedRoles:!0},embed:{contentTypes:["interactive","embedded","phrasing","flow"],allowedRoles:["application","document","img","presentation","none"],chromiumRole:"EmbeddedObject"},fieldset:{contentTypes:["flow"],allowedRoles:["none","presentation","radiogroup"],namingMethods:["fieldsetLegendText"]},figcaption:{allowedRoles:["group","none","presentation"]},figure:{contentTypes:["flow"],allowedRoles:!0,namingMethods:["figureText","titleText"]},footer:{contentTypes:["flow"],allowedRoles:["group","none","presentation","doc-footnote"],shadowRoot:!0},form:{contentTypes:["flow"],allowedRoles:["search","none","presentation"]},h1:{contentTypes:["heading","flow"],allowedRoles:["none","presentation","tab","doc-subtitle"],shadowRoot:!0,implicitAttrs:{"aria-level":"1"}},h2:{contentTypes:["heading","flow"],allowedRoles:["none","presentation","tab","doc-subtitle"],shadowRoot:!0,implicitAttrs:{"aria-level":"2"}},h3:{contentTypes:["heading","flow"],allowedRoles:["none","presentation","tab","doc-subtitle"],shadowRoot:!0,implicitAttrs:{"aria-level":"3"}},h4:{contentTypes:["heading","flow"],allowedRoles:["none","presentation","tab","doc-subtitle"],shadowRoot:!0,implicitAttrs:{"aria-level":"4"}},h5:{contentTypes:["heading","flow"],allowedRoles:["none","presentation","tab","doc-subtitle"],shadowRoot:!0,implicitAttrs:{"aria-level":"5"}},h6:{contentTypes:["heading","flow"],allowedRoles:["none","presentation","tab","doc-subtitle"],shadowRoot:!0,implicitAttrs:{"aria-level":"6"}},head:{allowedRoles:!1,noAriaAttrs:!0},header:{contentTypes:["flow"],allowedRoles:["group","none","presentation","doc-footnote"],shadowRoot:!0},hgroup:{contentTypes:["heading","flow"],allowedRoles:!0},hr:{contentTypes:["flow"],allowedRoles:["none","presentation","doc-pagebreak"],namingMethods:["titleText","singleSpace"]},html:{allowedRoles:!1,noAriaAttrs:!0},i:{contentTypes:["phrasing","flow"],allowedRoles:!0},iframe:{contentTypes:["interactive","embedded","phrasing","flow"],allowedRoles:["application","document","img","none","presentation"],chromiumRole:"Iframe"},img:{variant:{nonEmptyAlt:{matches:[{attributes:{alt:"/.+/"}},{hasAccessibleName:!0}],allowedRoles:["button","checkbox","link","menuitem","menuitemcheckbox","menuitemradio","option","progressbar","radio","scrollbar","separator","slider","switch","tab","treeitem","doc-cover"]},usemap:{matches:"[usemap]",contentTypes:["interactive","embedded","flow"]},default:{allowedRoles:["presentation","none"],contentTypes:["embedded","flow"]}},namingMethods:["altText"]},input:{variant:{button:{matches:{properties:{type:"button"}},allowedRoles:["checkbox","combobox","link","menuitem","menuitemcheckbox","menuitemradio","option","radio","switch","tab"]},buttonType:{matches:{properties:{type:["button","submit","reset"]}},namingMethods:["valueText","titleText","buttonDefaultText"]},checkboxPressed:{matches:{properties:{type:"checkbox"},attributes:{"aria-pressed":"/.*/"}},allowedRoles:["button","menuitemcheckbox","option","switch"],implicitAttrs:{"aria-checked":"false"}},checkbox:{matches:{properties:{type:"checkbox"},attributes:{"aria-pressed":null}},allowedRoles:["menuitemcheckbox","option","switch"],implicitAttrs:{"aria-checked":"false"}},noRoles:{matches:{properties:{type:["color","date","datetime-local","file","month","number","password","range","reset","submit","time","week"]}},allowedRoles:!1},hidden:{matches:{properties:{type:"hidden"}},contentTypes:["flow"],allowedRoles:!1,noAriaAttrs:!0},image:{matches:{properties:{type:"image"}},allowedRoles:["link","menuitem","menuitemcheckbox","menuitemradio","radio","switch"],namingMethods:["altText","valueText","labelText","titleText","buttonDefaultText"]},radio:{matches:{properties:{type:"radio"}},allowedRoles:["menuitemradio"],implicitAttrs:{"aria-checked":"false"}},textWithList:{matches:{properties:{type:"text"},attributes:{list:"/.*/"}},allowedRoles:!1},default:{contentTypes:["interactive","flow"],allowedRoles:["combobox","searchbox","spinbutton"],implicitAttrs:{"aria-valuenow":""},namingMethods:["labelText","placeholderText"]}}},ins:{contentTypes:["phrasing","flow"],allowedRoles:!0},kbd:{contentTypes:["phrasing","flow"],allowedRoles:!0},label:{contentTypes:["interactive","phrasing","flow"],allowedRoles:!1,chromiumRole:"Label"},legend:{allowedRoles:!1},li:{allowedRoles:["menuitem","menuitemcheckbox","menuitemradio","option","none","presentation","radio","separator","tab","treeitem","doc-biblioentry","doc-endnote"],implicitAttrs:{"aria-setsize":"1","aria-posinset":"1"}},link:{contentTypes:["phrasing","flow"],allowedRoles:!1,noAriaAttrs:!0},main:{contentTypes:["flow"],allowedRoles:!1,shadowRoot:!0},map:{contentTypes:["phrasing","flow"],allowedRoles:!1,noAriaAttrs:!0},math:{contentTypes:["embedded","phrasing","flow"],allowedRoles:!1},mark:{contentTypes:["phrasing","flow"],allowedRoles:!0},menu:{contentTypes:["flow"],allowedRoles:["directory","group","listbox","menu","menubar","none","presentation","radiogroup","tablist","toolbar","tree"]},meta:{variant:{itemprop:{matches:"[itemprop]",contentTypes:["phrasing","flow"]}},allowedRoles:!1,noAriaAttrs:!0},meter:{contentTypes:["phrasing","flow"],allowedRoles:!1,chromiumRole:"progressbar"},nav:{contentTypes:["sectioning","flow"],allowedRoles:["doc-index","doc-pagelist","doc-toc","menu","menubar","none","presentation","tablist"],shadowRoot:!0},noscript:{contentTypes:["phrasing","flow"],allowedRoles:!1,noAriaAttrs:!0},object:{variant:{usemap:{matches:"[usemap]",contentTypes:["interactive","embedded","phrasing","flow"]},default:{contentTypes:["embedded","phrasing","flow"]}},allowedRoles:["application","document","img"],chromiumRole:"PluginObject"},ol:{contentTypes:["flow"],allowedRoles:["directory","group","listbox","menu","menubar","none","presentation","radiogroup","tablist","toolbar","tree"]},optgroup:{allowedRoles:!1},option:{allowedRoles:!1,implicitAttrs:{"aria-selected":"false"}},output:{contentTypes:["phrasing","flow"],allowedRoles:!0,namingMethods:["subtreeText"]},p:{contentTypes:["flow"],allowedRoles:!0,shadowRoot:!0},param:{allowedRoles:!1,noAriaAttrs:!0},picture:{contentTypes:["phrasing","flow"],allowedRoles:!1,noAriaAttrs:!0},pre:{contentTypes:["flow"],allowedRoles:!0},progress:{contentTypes:["phrasing","flow"],allowedRoles:!1,implicitAttrs:{"aria-valuemax":"100","aria-valuemin":"0","aria-valuenow":"0"}},q:{contentTypes:["phrasing","flow"],allowedRoles:!0},rp:{allowedRoles:!0},rt:{allowedRoles:!0},ruby:{contentTypes:["phrasing","flow"],allowedRoles:!0},s:{contentTypes:["phrasing","flow"],allowedRoles:!0},samp:{contentTypes:["phrasing","flow"],allowedRoles:!0},script:{contentTypes:["phrasing","flow"],allowedRoles:!1,noAriaAttrs:!0},section:{contentTypes:["sectioning","flow"],allowedRoles:["alert","alertdialog","application","banner","complementary","contentinfo","dialog","document","feed","group","log","main","marquee","navigation","none","note","presentation","search","status","tabpanel","doc-abstract","doc-acknowledgments","doc-afterword","doc-appendix","doc-bibliography","doc-chapter","doc-colophon","doc-conclusion","doc-credit","doc-credits","doc-dedication","doc-endnotes","doc-epigraph","doc-epilogue","doc-errata","doc-example","doc-foreword","doc-glossary","doc-index","doc-introduction","doc-notice","doc-pagelist","doc-part","doc-preface","doc-prologue","doc-pullquote","doc-qna","doc-toc"],shadowRoot:!0},select:{variant:{combobox:{matches:{attributes:{multiple:null,size:[null,"1"]}},allowedRoles:["menu"]},default:{allowedRoles:!1}},contentTypes:["interactive","phrasing","flow"],implicitAttrs:{"aria-valuenow":""},namingMethods:["labelText"]},slot:{contentTypes:["phrasing","flow"],allowedRoles:!1,noAriaAttrs:!0},small:{contentTypes:["phrasing","flow"],allowedRoles:!0},source:{allowedRoles:!1,noAriaAttrs:!0},span:{contentTypes:["phrasing","flow"],allowedRoles:!0,shadowRoot:!0},strong:{contentTypes:["phrasing","flow"],allowedRoles:!0},style:{allowedRoles:!1,noAriaAttrs:!0},svg:{contentTypes:["embedded","phrasing","flow"],allowedRoles:!0,chromiumRole:"SVGRoot",namingMethods:["svgTitleText"]},sub:{contentTypes:["phrasing","flow"],allowedRoles:!0},summary:{allowedRoles:!1,namingMethods:["subtreeText"]},sup:{contentTypes:["phrasing","flow"],allowedRoles:!0},table:{contentTypes:["flow"],allowedRoles:!0,namingMethods:["tableCaptionText","tableSummaryText"]},tbody:{allowedRoles:!0},template:{contentTypes:["phrasing","flow"],allowedRoles:!1,noAriaAttrs:!0},textarea:{contentTypes:["interactive","phrasing","flow"],allowedRoles:!1,implicitAttrs:{"aria-valuenow":"","aria-multiline":"true"},namingMethods:["labelText","placeholderText"]},tfoot:{allowedRoles:!0},thead:{allowedRoles:!0},time:{contentTypes:["phrasing","flow"],allowedRoles:!0},title:{allowedRoles:!1,noAriaAttrs:!0},td:{allowedRoles:!0},th:{allowedRoles:!0},tr:{allowedRoles:!0},track:{allowedRoles:!1,noAriaAttrs:!0},u:{contentTypes:["phrasing","flow"],allowedRoles:!0},ul:{contentTypes:["flow"],allowedRoles:["directory","group","listbox","menu","menubar","none","presentation","radiogroup","tablist","toolbar","tree"]},var:{contentTypes:["phrasing","flow"],allowedRoles:!0},video:{variant:{controls:{matches:"[controls]",contentTypes:["interactive","embedded","phrasing","flow"]},default:{contentTypes:["embedded","phrasing","flow"]}},allowedRoles:["application"],chromiumRole:"video"},wbr:{contentTypes:["phrasing","flow"],allowedRoles:["presentation","none"]}},Oo={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},_o={ariaAttrs:No,ariaRoles:p({},Ro,{"doc-abstract":{type:"section",allowedAttrs:["aria-expanded"],superclassRole:["section"]},"doc-acknowledgments":{type:"landmark",allowedAttrs:["aria-expanded"],superclassRole:["landmark"]},"doc-afterword":{type:"landmark",allowedAttrs:["aria-expanded"],superclassRole:["landmark"]},"doc-appendix":{type:"landmark",allowedAttrs:["aria-expanded"],superclassRole:["landmark"]},"doc-backlink":{type:"link",allowedAttrs:["aria-expanded"],nameFromContent:!0,superclassRole:["link"]},"doc-biblioentry":{type:"listitem",allowedAttrs:["aria-expanded","aria-level","aria-posinset","aria-setsize"],superclassRole:["listitem"],deprecated:!0},"doc-bibliography":{type:"landmark",allowedAttrs:["aria-expanded"],superclassRole:["landmark"]},"doc-biblioref":{type:"link",allowedAttrs:["aria-expanded"],nameFromContent:!0,superclassRole:["link"]},"doc-chapter":{type:"landmark",allowedAttrs:["aria-expanded"],superclassRole:["landmark"]},"doc-colophon":{type:"section",allowedAttrs:["aria-expanded"],superclassRole:["section"]},"doc-conclusion":{type:"landmark",allowedAttrs:["aria-expanded"],superclassRole:["landmark"]},"doc-cover":{type:"img",allowedAttrs:["aria-expanded"],superclassRole:["img"]},"doc-credit":{type:"section",allowedAttrs:["aria-expanded"],superclassRole:["section"]},"doc-credits":{type:"landmark",allowedAttrs:["aria-expanded"],superclassRole:["landmark"]},"doc-dedication":{type:"section",allowedAttrs:["aria-expanded"],superclassRole:["section"]},"doc-endnote":{type:"listitem",allowedAttrs:["aria-expanded","aria-level","aria-posinset","aria-setsize"],superclassRole:["listitem"],deprecated:!0},"doc-endnotes":{type:"landmark",allowedAttrs:["aria-expanded"],superclassRole:["landmark"]},"doc-epigraph":{type:"section",allowedAttrs:["aria-expanded"],superclassRole:["section"]},"doc-epilogue":{type:"landmark",allowedAttrs:["aria-expanded"],superclassRole:["landmark"]},"doc-errata":{type:"landmark",allowedAttrs:["aria-expanded"],superclassRole:["landmark"]},"doc-example":{type:"section",allowedAttrs:["aria-expanded"],superclassRole:["section"]},"doc-footnote":{type:"section",allowedAttrs:["aria-expanded"],superclassRole:["section"]},"doc-foreword":{type:"landmark",allowedAttrs:["aria-expanded"],superclassRole:["landmark"]},"doc-glossary":{type:"landmark",allowedAttrs:["aria-expanded"],superclassRole:["landmark"]},"doc-glossref":{type:"link",allowedAttrs:["aria-expanded"],nameFromContent:!0,superclassRole:["link"]},"doc-index":{type:"navigation",allowedAttrs:["aria-expanded"],superclassRole:["navigation"]},"doc-introduction":{type:"landmark",allowedAttrs:["aria-expanded"],superclassRole:["landmark"]},"doc-noteref":{type:"link",allowedAttrs:["aria-expanded"],nameFromContent:!0,superclassRole:["link"]},"doc-notice":{type:"note",allowedAttrs:["aria-expanded"],superclassRole:["note"]},"doc-pagebreak":{type:"separator",allowedAttrs:["aria-expanded","aria-orientation"],superclassRole:["separator"],childrenPresentational:!0},"doc-pagelist":{type:"navigation",allowedAttrs:["aria-expanded"],superclassRole:["navigation"]},"doc-part":{type:"landmark",allowedAttrs:["aria-expanded"],superclassRole:["landmark"]},"doc-preface":{type:"landmark",allowedAttrs:["aria-expanded"],superclassRole:["landmark"]},"doc-prologue":{type:"landmark",allowedAttrs:["aria-expanded"],superclassRole:["landmark"]},"doc-pullquote":{type:"none",superclassRole:["none"]},"doc-qna":{type:"section",allowedAttrs:["aria-expanded"],superclassRole:["section"]},"doc-subtitle":{type:"sectionhead",allowedAttrs:["aria-expanded"],superclassRole:["sectionhead"]},"doc-tip":{type:"note",allowedAttrs:["aria-expanded"],superclassRole:["note"]},"doc-toc":{type:"navigation",allowedAttrs:["aria-expanded"],superclassRole:["navigation"]}},{"graphics-document":{type:"structure",superclassRole:["document"],accessibleNameRequired:!0},"graphics-object":{type:"structure",superclassRole:["group"],nameFromContent:!0},"graphics-symbol":{type:"structure",superclassRole:["img"],accessibleNameRequired:!0,childrenPresentational:!0}}),htmlElms:a,cssColors:Oo},So=p({},_o),A=So,Io=function(e){return!!(e=A.ariaRoles[e])&&!!e.unsupported},Po=function(e){var t=(n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).allowAbstract,n=void 0!==(n=n.flagUnsupported)&&n,a=A.ariaRoles[e],r=Io(e);return!(!a||n&&r||!t&&"abstract"===a.type)},c=function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},n=t.fallback,a=t.abstracts,r=t.dpub;return 1===(e=e instanceof b?e:D(e)).props.nodeType&&(t=(e.attr("role")||"").trim().toLowerCase(),(n?_(t):[t]).find(function(e){return!(!r&&"doc-"===e.substr(0,4))&&Po(e,{allowAbstract:a})}))||null},Mo=function(t){return Object.keys(A.htmlElms).filter(function(e){e=A.htmlElms[e];return e.contentTypes?e.contentTypes.includes(t):!!e.variant&&!(!e.variant.default||!e.variant.default.contentTypes)&&e.variant.default.contentTypes.includes(t)})},Bo=function(){return w.get("globalAriaAttrs",function(){return Object.keys(A.ariaAttrs).filter(function(e){return A.ariaAttrs[e].global})})},Lo=t(function(e){for(var t=[],n=e.rows,a=0,r=n.length;a<r;a++)for(var o=n[a].cells,i=(t[a]=t[a]||[],0),l=0,s=o.length;l<s;l++)for(var u=0;u<o[l].colSpan;u++){for(var c=o[l].getAttribute("rowspan"),d=0===parseInt(c)||0===o[l].rowspan?n.length:o[l].rowSpan,p=0;p<d;p++){for(t[a+p]=t[a+p]||[];t[a+p][i];)i++;t[a+p][i]=o[l]}i++}return t}),jo=t(function(e,t){var n,a;for(t=t||Lo(cr(e,"table")),n=0;n<t.length;n++)if(t[n]&&-1!==(a=t[n].indexOf(e)))return{x:a,y:n}}),qo=function(e){var t,n=e instanceof b?e:D(e),a=(e=n.actualNode,n.attr("scope")),r=n.attr("role");if(["td","th"].includes(n.props.nodeName))return"columnheader"===r?"col":"rowheader"===r?"row":"col"===a||"row"===a?a:"th"===n.props.nodeName&&(n.actualNode?(r=Lo(cr(e,"table")))[(t=jo(e,r)).y].reduce(function(e,t){return e&&"TH"===t.nodeName.toUpperCase()},!0)?"col":r.map(function(e){return e[t.x]}).reduce(function(e,t){return e&&t&&"TH"===t.nodeName.toUpperCase()},!0)?"row":"auto":"auto");throw new TypeError("Expected TD or TH element")},Vo=function(e){return-1!==["col","auto"].indexOf(qo(e))},zo=function(e){return["row","auto"].includes(qo(e))},C=function(e){return e?e.replace(/\r\n/g,"\n").replace(/\u00A0/g," ").replace(/[\s]{2,}/g," ").trim():""},$o=function(e){var t=e instanceof b?e:D(e);if(t&&!io(t))switch(t.props.nodeName){case"a":case"area":if(t.hasAttr("href"))return!0;break;case"input":return"hidden"!==t.props.type;case"textarea":case"select":case"summary":case"button":return!0;case"details":return!du(t,"summary").length}return!1};function k(e){var e=e instanceof b?e:D(e);return 1===e.props.nodeType&&!(io(e)||!$o(e)&&(!(e=e.attr("tabindex"))||isNaN(parseInt(e,10))))}var Uo=Mo("sectioning").map(function(e){return"".concat(e,":not([role])")}).join(", ")+" , main:not([role]), [role=article], [role=complementary], [role=main], [role=navigation], [role=region]";function Ho(e){var t=C(ko(e)),e=C(To(e));return t||e}var Go={a:function(e){return e.hasAttr("href")?"link":null},area:function(e){return e.hasAttr("href")?"link":null},article:"article",aside:"complementary",body:"document",button:"button",datalist:"listbox",dd:"definition",dfn:"term",details:"group",dialog:"dialog",dt:"term",fieldset:"group",figure:"figure",footer:function(e){return x(e,Uo)?null:"contentinfo"},form:function(e){return Ho(e)?"form":null},h1:"heading",h2:"heading",h3:"heading",h4:"heading",h5:"heading",h6:"heading",header:function(e){return x(e,Uo)?null:"banner"},hr:"separator",img:function(t){var e=t.hasAttr("alt")&&!t.attr("alt"),n=Bo().find(function(e){return t.hasAttr(e)});return!e||n||k(t)?"img":"presentation"},input:function(e){var t,n;switch(e.hasAttr("list")&&(n=(t=Ao(e.actualNode,"list").filter(function(e){return!!e})[0])&&"datalist"===t.nodeName.toLowerCase()),e.props.type){case"checkbox":return"checkbox";case"number":return"spinbutton";case"radio":return"radio";case"range":return"slider";case"search":return n?"combobox":"searchbox";case"button":case"image":case"reset":case"submit":return"button";case"text":case"tel":case"url":case"email":case"":return n?"combobox":"textbox";default:return"textbox"}},li:"listitem",main:"main",math:"math",menu:"list",nav:"navigation",ol:"list",optgroup:"group",option:"option",output:"status",progress:"progressbar",section:function(e){return Ho(e)?"region":null},select:function(e){return e.hasAttr("multiple")||1<parseInt(e.attr("size"))?"listbox":"combobox"},summary:"button",table:"table",tbody:"rowgroup",td:function(e){e=x(e,"table"),e=c(e);return["grid","treegrid"].includes(e)?"gridcell":"cell"},textarea:"textbox",tfoot:"rowgroup",th:function(e){return Vo(e)?"columnheader":zo(e)?"rowheader":void 0},thead:"rowgroup",tr:"row",ul:"list"},Wo=function(e,t){var n=te(t);if(Array.isArray(t)&&void 0!==e)return t.includes(e);if("function"===n)return!!t(e);if(null!=e){if(t instanceof RegExp)return t.test(e);if(/^\/.*\/$/.test(t))return n=t.substring(1,t.length-1),new RegExp(n).test(e)}return t===e};function Ko(e,t){return Wo(!!l(e),t)}var Yo=function(t,n){if("object"!==te(n)||Array.isArray(n)||n instanceof RegExp)throw new Error("Expect matcher to be an object");return Object.keys(n).every(function(e){return Wo(t(e),n[e])})};function Jo(t,e){return t instanceof b||(t=D(t)),Yo(function(e){return t.attr(e)},e)}function Xo(e,t){return!!t(e)}function Qo(e,t){return Wo(c(e),t)}function Zo(e,t){return Wo(li(e),t)}function ei(e,t){return e instanceof b||(e=D(e)),Wo(e.props.nodeName,t)}function ti(t,e){return t instanceof b||(t=D(t)),Yo(function(e){return t.props[e]},e)}function ni(e,t){return Wo(T(e),t)}var ai={hasAccessibleName:Ko,attributes:Jo,condition:Xo,explicitRole:Qo,implicitRole:Zo,nodeName:ei,properties:ti,semanticRole:ni},ri=function t(n,a){return n instanceof b||(n=D(n)),Array.isArray(a)?a.some(function(e){return t(n,e)}):"string"==typeof a?ua(n,a):Object.keys(a).every(function(e){var t;if(ai[e])return t=a[e],(0,ai[e])(n,t);throw new Error('Unknown matcher type "'.concat(e,'"'))})},r=function(e,t){return ri(e,t)},oi=(r.hasAccessibleName=Ko,r.attributes=Jo,r.condition=Xo,r.explicitRole=Qo,r.fromDefinition=ri,r.fromFunction=Yo,r.fromPrimative=Wo,r.implicitRole=Zo,r.nodeName=ei,r.properties=ti,r.semanticRole=ni,r),ii=function(e){var t=(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).noMatchAccessibleName,n=void 0!==t&&t,a=A.htmlElms[e.props.nodeName];if(!a)return{};if(!a.variant)return a;var r,o,i=a.variant,l=m(a,P);for(r in i)if(i.hasOwnProperty(r)&&"default"!==r){for(var s=i[r],u=s.matches,c=m(s,M),d=Array.isArray(u)?u:[u],p=0;p<d.length&&n;p++)if(d[p].hasOwnProperty("hasAccessibleName"))return a;if(oi(e,u))for(var f in c)c.hasOwnProperty(f)&&(l[f]=c[f])}for(o in i.default)i.default.hasOwnProperty(o)&&void 0===l[o]&&(l[o]=i.default[o]);return l},li=function(e){var t,n=(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).chromium,a=e instanceof b?e:D(e);if(e=a.actualNode,a)return t=a.props.nodeName,!(t=Go[t])&&n?ii(a).chromiumRole||null:"function"==typeof t?t(a):t||null;throw new ReferenceError("Cannot get implicit role of a node outside the current scope.")},si={td:["tr"],th:["tr"],tr:["thead","tbody","tfoot","table"],thead:["table"],tbody:["table"],tfoot:["table"],li:["ol","ul"],dt:["dl","div"],dd:["dl","div"],div:["dl"]};function ui(e,t){var n=t.chromium,t=m(t,B),n=li(e,{chromium:n});return n?function e(t,n){var a=si[t.props.nodeName];if(a){if(t.parent)return a.includes(t.parent.props.nodeName)?(a=c(t.parent,n),["none","presentation"].includes(a)&&!ci(t.parent)?a:a?null:e(t.parent,n)):null;if(t.actualNode)throw new ReferenceError("Cannot determine role presentational inheritance of a required parent outside the current scope.")}return null}(e,t)||n:null}function ci(t){return Bo().some(function(e){return t.hasAttr(e)})||k(t)}var T=function(e){var t=(n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).noPresentational,n=function(e,t){var n,a=(r=1<arguments.length&&void 0!==t?t:{}).noImplicit,r=m(r,L),o=e instanceof b?e:D(e);return 1!==o.props.nodeType?null:!(n=c(o,r))||["presentation","none"].includes(n)&&ci(o)?a?null:ui(o,r):n}(e,m(n,j));return t&&["presentation","none"].includes(n)?null:n},di=["iframe"],pi=function(e){var t=e instanceof b?e:D(e);return 1!==t.props.nodeType||!e.hasAttr("title")||!r(t,di)&&["none","presentation"].includes(T(t))?"":t.attr("title")},fi=function(e){var t,n,a=(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).strict;return 1===(e=e instanceof b?e:D(e)).props.nodeType&&(t=T(e),!(!(n=A.ariaRoles[t])||!n.nameFromContent)||!a&&(!n||["presentation","none"].includes(t)))},mi=function(e){var t=e.actualNode,n=e.children;if(n)return e.hasAttr("aria-owns")?(e=Ao(t,"aria-owns").filter(function(e){return!!e}).map(function(e){return axe.utils.getNodeFromTree(e)}),[].concat(v(n),v(e))):v(n);throw new Error("getOwnedVirtual requires a virtual node")},hi=Mo("phrasing").concat(["#text"]),gi=function(e){var r=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},t=l.alreadyProcessed;r.startNode=r.startNode||e;var n=(o=r).strict,a=o.inControlContext,o=o.inLabelledByContext,i=ii(e,{noMatchAccessibleName:!0}).contentTypes;return!(t(e,r)||1!==e.props.nodeType||null!=i&&i.includes("embedded"))&&(fi(e,{strict:n})||r.subtreeDescendant)?(n||(r=p({subtreeDescendant:!a&&!o},r)),mi(e).reduce(function(e,t){var n=r,a=t.props.nodeName;return(t=l(t,n))?(hi.includes(a)||(" "!==t[0]&&(t+=" "),e&&" "!==e[e.length-1]&&(t=" "+t)),e+t):e},"")):""},bi=function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},n=l.alreadyProcessed;if(t.inControlContext||t.inLabelledByContext||n(e,t))return"";t.startNode||(t.startNode=e);var a,r=p({inControlContext:!0},t),n=function(e){if(!e.attr("id"))return[];if(e.actualNode)return sr({elm:"label",attr:"for",value:e.attr("id"),context:e.actualNode});throw new TypeError("Cannot resolve explicit label reference for non-DOM nodes")}(e);return(t=x(e,"label"))?(a=[].concat(v(n),[t.actualNode])).sort(zs):a=n,a.map(function(e){return Co(e,r)}).filter(function(e){return""!==e}).join(" ")},yi={submit:"Submit",image:"Submit",reset:"Reset",button:""};function vi(e,t){return t.attr(e)||""}function wi(e,t,n){var t=t.actualNode,a=[e=e.toLowerCase(),t.nodeName.toLowerCase()].join(","),t=t.querySelector(a);return t&&t.nodeName.toLowerCase()===e?Co(t,n):""}var Di={valueText:function(e){return e.actualNode.value||""},buttonDefaultText:function(e){e=e.actualNode;return yi[e.type]||""},tableCaptionText:wi.bind(null,"caption"),figureText:wi.bind(null,"figcaption"),svgTitleText:wi.bind(null,"title"),fieldsetLegendText:wi.bind(null,"legend"),altText:vi.bind(null,"alt"),tableSummaryText:vi.bind(null,"summary"),titleText:pi,subtreeText:gi,labelText:bi,singleSpace:function(){return" "},placeholderText:vi.bind(null,"placeholder")},xi=function(n){var e,a=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},t=n.actualNode;return 1!==n.props.nodeType||["presentation","none"].includes(T(n))?"":(e=(ii(n,{noMatchAccessibleName:!0}).namingMethods||[]).map(function(e){return Di[e]}).reduce(function(e,t){return e||t(n,a)},""),a.debug&&axe.log(e||"{empty-value}",t,a),e)},Ei={accessibleNameFromFieldValue:["combobox","listbox","progressbar"]};function N(e){return e=e instanceof b?e:D(e),Fi(e)}var Fi=t(function(e,t){return!Dr(e)&&!no(e,{skipAncestors:!0,isAncestor:t})&&(e.actualNode&&"area"===e.props.nodeName?!Cr(e,Fi):!Nr(e,{skipAncestors:!0,isAncestor:t})&&(!e.parent||Fi(e.parent,!0)))}),Ai=function a(e,r,o){var t=e instanceof b?e:D(e),n=r?N:F,i=!e.actualNode||e.actualNode&&n(e),n=t.children.map(function(e){var t=(n=e.props).nodeType,n=n.nodeValue;if(3===t){if(n&&i)return n}else if(!o)return a(e,r)}).join("");return C(n)},Ci=["button","checkbox","color","file","hidden","image","password","radio","reset","submit"],ki=function(e){var t=(e=e instanceof b?e:D(e)).props.nodeName;return"textarea"===t||"input"===t&&!Ci.includes((e.attr("type")||"").toLowerCase())},Ti=function(e){return"select"===(e=e instanceof b?e:D(e)).props.nodeName},Ni=function(e){return"textbox"===c(e)},Ri=function(e){return"listbox"===c(e)},Oi=function(e){return"combobox"===c(e)},_i=["progressbar","scrollbar","slider","spinbutton"],Si=function(e){return e=c(e),_i.includes(e)},Ii=["textbox","progressbar","scrollbar","slider","spinbutton","combobox","listbox"],Pi={nativeTextboxValue:function(e){e=e instanceof b?e:D(e);if(ki(e))return e.props.value||"";return""},nativeSelectValue:function(e){e=e instanceof b?e:D(e);if(!Ti(e))return"";var e=du(e,"option"),t=e.filter(function(e){return e.props.selected});t.length||t.push(e[0]);return t.map(function(e){return Ai(e)}).join(" ")||""},ariaTextboxValue:function(e){var e=e instanceof b?e:D(e),t=e.actualNode;if(!Ni(e))return"";return t&&Nr(t)?t.textContent:Ai(e,!0)},ariaListboxValue:Mi,ariaComboboxValue:function(e,t){var e=e instanceof b?e:D(e);return Oi(e)&&(e=mi(e).filter(function(e){return"listbox"===T(e)})[0])?Mi(e,t):""},ariaRangeValue:function(e){var e=e instanceof b?e:D(e);return Si(e)&&e.hasAttr("aria-valuenow")?(e=+e.attr("aria-valuenow"),isNaN(e)?"0":String(e)):""}};function Mi(e,t){var e=e instanceof b?e:D(e);return!Ri(e)||0===(e=mi(e).filter(function(e){return"option"===T(e)&&"true"===e.attr("aria-selected")})).length?"":l(e[0],t)}var Bi=function(n){var a=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},e=n.actualNode,t=Ei.accessibleNameFromFieldValue||[],r=T(n);return a.startNode===n||!Ii.includes(r)||t.includes(r)?"":(t=Object.keys(Pi).map(function(e){return Pi[e]}).reduce(function(e,t){return e||t(n,a)},""),a.debug&&sn(t||"{empty-value}",e,a),t)};function Li(){return/[\u1D00-\u1D7F\u1D80-\u1DBF\u1DC0-\u1DFF\u20A0-\u20CF\u20D0-\u20FF\u2100-\u214F\u2150-\u218F\u2190-\u21FF\u2200-\u22FF\u2300-\u23FF\u2400-\u243F\u2440-\u245F\u2460-\u24FF\u2500-\u257F\u2580-\u259F\u25A0-\u25FF\u2600-\u26FF\u2700-\u27BF\uE000-\uF8FF]/g}function ji(){return/[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&\xa3\xa2\xa5\xa7\u20ac()*+,\-.\/:;<=>?@\[\]^_`{|}~\xb1]/g}function qi(){return/[\uDB80-\uDBBF][\uDC00-\uDFFF]/g}function Vi(){return/[#*0-9]\uFE0F?\u20E3|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26D3\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]\uFE0F?|[\u261D\u270C\u270D](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?|[\u270A\u270B](?:\uD83C[\uDFFB-\uDFFF])?|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\u26F9(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|\u2764\uFE0F?(?:\u200D(?:\uD83D\uDD25|\uD83E\uDE79))?|\uD83C(?:[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]\uFE0F?|[\uDF85\uDFC2\uDFC7](?:\uD83C[\uDFFB-\uDFFF])?|[\uDFC3\uDFC4\uDFCA](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDFCB\uDFCC](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uDDE6\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF]|\uDDE7\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF]|\uDDE8\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF]|\uDDE9\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF]|\uDDEA\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA]|\uDDEB\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7]|\uDDEC\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE]|\uDDED\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA]|\uDDEE\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9]|\uDDEF\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5]|\uDDF0\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF]|\uDDF1\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE]|\uDDF2\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF]|\uDDF3\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF]|\uDDF4\uD83C\uDDF2|\uDDF5\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE]|\uDDF6\uD83C\uDDE6|\uDDF7\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC]|\uDDF8\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF]|\uDDF9\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF]|\uDDFA\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF]|\uDDFB\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA]|\uDDFC\uD83C[\uDDEB\uDDF8]|\uDDFD\uD83C\uDDF0|\uDDFE\uD83C[\uDDEA\uDDF9]|\uDDFF\uD83C[\uDDE6\uDDF2\uDDFC]|\uDFF3\uFE0F?(?:\u200D(?:\u26A7\uFE0F?|\uD83C\uDF08))?|\uDFF4(?:\u200D\u2620\uFE0F?|\uDB40\uDC67\uDB40\uDC62\uDB40(?:\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDC73\uDB40\uDC63\uDB40\uDC74|\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F)?)|\uD83D(?:[\uDC08\uDC26](?:\u200D\u2B1B)?|[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]\uFE0F?|[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC](?:\uD83C[\uDFFB-\uDFFF])?|[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD74\uDD90](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?|[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC25\uDC27-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDC-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uDC15(?:\u200D\uD83E\uDDBA)?|\uDC3B(?:\u200D\u2744\uFE0F?)?|\uDC41\uFE0F?(?:\u200D\uD83D\uDDE8\uFE0F?)?|\uDC68(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDC68\uDC69]\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFE])))?))?|\uDC69(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?[\uDC68\uDC69]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?|\uDC69\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?))|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFE])))?))?|\uDC6F(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDD75(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDE2E(?:\u200D\uD83D\uDCA8)?|\uDE35(?:\u200D\uD83D\uDCAB)?|\uDE36(?:\u200D\uD83C\uDF2B\uFE0F?)?)|\uD83E(?:[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF8](?:\uD83C[\uDFFB-\uDFFF])?|[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDDDE\uDDDF](?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE7C\uDE80-\uDE88\uDE90-\uDEBD\uDEBF-\uDEC2\uDECE-\uDEDB\uDEE0-\uDEE8]|\uDD3C(?:\u200D[\u2640\u2642]\uFE0F?|\uD83C[\uDFFB-\uDFFF])?|\uDDD1(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFC-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFD-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFD\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFE]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?))?|\uDEF1(?:\uD83C(?:\uDFFB(?:\u200D\uD83E\uDEF2\uD83C[\uDFFC-\uDFFF])?|\uDFFC(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFD-\uDFFF])?|\uDFFD(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])?|\uDFFE(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFD\uDFFF])?|\uDFFF(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFE])?))?)/g}var zi=function(e,t){var n=t.emoji,a=t.nonBmp,t=t.punctuations;return n?Vi().test(e):a?Li().test(e)||qi().test(e):!!t&&ji().test(e)},$i=function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:.15,n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:3,a=e.actualNode.nodeValue.trim();if(!C(a)||zi(a,{emoji:!0,nonBmp:!0}))return!1;var r=w.get("canvasContext",function(){return document.createElement("canvas").getContext("2d",{willReadFrequently:!0})}),o=r.canvas,i=(w.get("fonts")||w.set("fonts",{}),w.get("fonts")),l=window.getComputedStyle(e.parent.actualNode).getPropertyValue("font-family");if(i[l]||(i[l]={occurrences:0,numLigatures:0}),(i=i[l]).occurrences>=n){if(i.numLigatures/i.occurrences==1)return!0;if(0===i.numLigatures)return!1}i.occurrences++;var n=30,s="".concat(n,"px ").concat(l),u=(r.font=s,a.charAt(0)),c=r.measureText(u).width,d=(c<30&&(c*=d=30/c,s="".concat(n*=d,"px ").concat(l)),o.width=c,o.height=n,r.font=s,r.textAlign="left",r.textBaseline="top",r.fillText(u,0,0),new Uint32Array(r.getImageData(0,0,c,n).data.buffer));if(!d.some(function(e){return e}))return i.numLigatures++,!0;r.clearRect(0,0,c,n),r.fillText(a,0,0);var p=new Uint32Array(r.getImageData(0,0,c,n).data.buffer),l=d.reduce(function(e,t,n){return 0===t&&0===p[n]||0!==t&&0!==p[n]?e:++e},0),o=a.split("").reduce(function(e,t){return e+r.measureText(t).width},0),s=r.measureText(a).width;return t<=l/d.length&&t<=1-s/o&&(i.numLigatures++,!0)};function Ui(n){var e,t,a,r,o,i=function(e,t){t.startNode||(t=p({startNode:e},t));1===e.props.nodeType&&t.inLabelledByContext&&void 0===t.includeHidden&&(t=p({includeHidden:!N(e)},t));return t}(n,1<arguments.length&&void 0!==arguments[1]?arguments[1]:{});return function(e,t){if(!e)return;if(1!==e.props.nodeType||t.includeHidden)return;return!N(e)}(n,i)||(e=n,t=(o=i).ignoreIconLigature,a=o.pixelThreshold,r=null!=(r=o.occurrenceThreshold)?r:o.occuranceThreshold,3===e.props.nodeType&&t&&$i(e,a,r))?"":(o=[ko,To,xi,Bi,gi,Hi,pi].reduce(function(e,t){return""!==(e=i.startNode===n?C(e):e)?e:t(n,i)},""),i.debug&&axe.log(o||"{empty-value}",n.actualNode,i),o)}function Hi(e){return 3!==e.props.nodeType?"":e.props.nodeValue}Ui.alreadyProcessed=function(e,t){return t.processed=t.processed||[],!!t.processed.includes(e)||(t.processed.push(e),!1)};var l=Ui,Gi=function(e,t){var n=t.emoji,a=t.nonBmp,t=t.punctuations;return n&&(e=e.replace(Vi(),"")),a&&(e=(e=e.replace(Li(),"")).replace(qi(),"")),e=t?e.replace(ji(),""):e},Wi=function(e){return e.length&&!["x","i"].includes(e)&&(e=Gi(e,{emoji:!0,nonBmp:!0,punctuations:!0}),C(e))?1:0},Ki={stateTerms:["on","off"],standaloneTerms:["name","honorific-prefix","given-name","additional-name","family-name","honorific-suffix","nickname","username","new-password","current-password","organization-title","organization","street-address","address-line1","address-line2","address-line3","address-level4","address-level3","address-level2","address-level1","country","country-name","postal-code","cc-name","cc-given-name","cc-additional-name","cc-family-name","cc-number","cc-exp","cc-exp-month","cc-exp-year","cc-csc","cc-type","transaction-currency","transaction-amount","language","bday","bday-day","bday-month","bday-year","sex","url","photo","one-time-code"],qualifiers:["home","work","mobile","fax","pager"],qualifiedTerms:["tel","tel-country-code","tel-national","tel-area-code","tel-local","tel-local-prefix","tel-local-suffix","tel-extension","email","impp"],locations:["billing","shipping"]},Yi=function(e){var t=void 0!==(t=(i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).looseTyped)&&t,n=void 0===(n=i.stateTerms)?[]:n,a=void 0===(a=i.locations)?[]:a,r=void 0===(r=i.qualifiers)?[]:r,o=void 0===(o=i.standaloneTerms)?[]:o,i=void 0===(i=i.qualifiedTerms)?[]:i;return e=e.toLowerCase().trim(),!(!(n=n.concat(Ki.stateTerms)).includes(e)&&""!==e)||(r=r.concat(Ki.qualifiers),a=a.concat(Ki.locations),o=o.concat(Ki.standaloneTerms),i=i.concat(Ki.qualifiedTerms),!("webauthn"===(n=e.split(/\s+/g))[n.length-1]&&(n.pop(),0===n.length)||!t&&(8<n[0].length&&"section-"===n[0].substr(0,8)&&n.shift(),a.includes(n[0])&&n.shift(),r.includes(n[0])&&(n.shift(),o=[]),1!==n.length))&&(t=n[n.length-1],o.includes(t)||i.includes(t)))},Ji=function(e){var t;return e.attr("aria-labelledby")&&(t=Ao(e.actualNode,"aria-labelledby").map(function(e){e=D(e);return e?Ai(e):""}).join(" ").trim())?t:(t=(t=e.attr("aria-label"))&&C(t))||null},Xi=function(e,t,n){return e=D(e),Ai(e,t,n)},Qi=function(e){if(t=Ji(e))return t;if(e.attr("id")){if(!e.actualNode)throw new TypeError("Cannot resolve explicit label reference for non-DOM nodes");var t,n=y(e.attr("id"));if(t=(n=E(e.actualNode).querySelector('label[for="'+n+'"]'))&&Xi(n,!0))return t}return(t=(n=x(e,"label"))&&Ai(n,!0))||null},Zi=function(e){return e=D(e),Qi(e)},el=[{matches:[{nodeName:"textarea"},{nodeName:"input",properties:{type:["text","password","search","tel","email","url"]}}],namingMethods:"labelText"},{matches:{nodeName:"input",properties:{type:["button","submit","reset"]}},namingMethods:["valueText","titleText","buttonDefaultText"]},{matches:{nodeName:"input",properties:{type:"image"}},namingMethods:["altText","valueText","labelText","titleText","buttonDefaultText"]},{matches:"button",namingMethods:"subtreeText"},{matches:"fieldset",namingMethods:"fieldsetLegendText"},{matches:"OUTPUT",namingMethods:"subtreeText"},{matches:[{nodeName:"select"},{nodeName:"input",properties:{type:/^(?!text|password|search|tel|email|url|button|submit|reset)/}}],namingMethods:"labelText"},{matches:"summary",namingMethods:"subtreeText"},{matches:"figure",namingMethods:["figureText","titleText"]},{matches:"img",namingMethods:"altText"},{matches:"table",namingMethods:["tableCaptionText","tableSummaryText"]},{matches:["hr","br"],namingMethods:["titleText","singleSpace"]}],tl=function t(e){var n=F(e),a=[];return e.children.forEach(function(e){3===e.actualNode.nodeType?n&&a.push(e):a=a.concat(t(e))}),a},nl=t(function(e){var t=D(e),o=t.boundingClientRect,i=[],l=mr(t);return e.childNodes.forEach(function(e){var t,n,a,r;3!==e.nodeType||""===C(e.nodeValue)||(e=e,(t=document.createRange()).selectNodeContents(e),e=Array.from(t.getClientRects()),n=o,e.some(function(e){return!jr(go(e),n)}))||i.push.apply(i,v((a=l,r=[],e.forEach(function(e){e.width<1||e.height<1||(e=a.reduce(function(e,t){return e&&eo(e,t.boundingClientRect)},e))&&r.push(e)}),r)))}),i.length?i:[o]}),al=function(e){Hr();var t=D(e)._grid;return t?nl(e).map(function(e){return Do(t,e)}):[]},rl=["checkbox","img","meter","progressbar","scrollbar","radio","slider","spinbutton","textbox"],ol=function(e){var t=e instanceof b?e:D(e);if(e=axe.commons.aria.getExplicitRole(t))return-1!==rl.indexOf(e);switch(t.props.nodeName){case"img":case"iframe":case"object":case"video":case"audio":case"canvas":case"svg":case"math":case"button":case"select":case"textarea":case"keygen":case"progress":case"meter":return!0;case"input":return"hidden"!==t.props.type;default:return!1}},il=["head","title","template","script","style","iframe","object","video","audio","noscript"];function ll(e){return!il.includes(e.props.nodeName)&&e.children.some(function(e){e=e.props;return 3===e.nodeType&&e.nodeValue.trim()})}var sl=function t(e,n,a){return ll(e)||ol(e.actualNode)||!a&&!!Ji(e)||!n&&e.children.some(function(e){return 1===e.actualNode.nodeType&&t(e)})},ul=function(e,t,n){return e=D(e),sl(e,t,n)};function cl(e){return!(void 0!==e.children&&!ll(e))||(1===e.props.nodeType&&ol(e)?!!axe.commons.text.accessibleTextVirtual(e):e.children.some(function(e){return!e.attr("lang")&&cl(e)&&!Nr(e)}))}var dl=function(e){return-1<parseInt(e.getAttribute("tabindex"),10)&&k(e)&&!$o(e)};function pl(e,t){var n=e instanceof b?e:D(e),e=e instanceof window.Node?e:null==n?void 0:n.actualNode;return n?(void 0===n._isHiddenWithCSS&&(n._isHiddenWithCSS=fl(e,t)),n._isHiddenWithCSS):fl(e,t)}function fl(e,t){if(9===e.nodeType)return!1;if(11===e.nodeType&&(e=e.host),["STYLE","SCRIPT"].includes(e.nodeName.toUpperCase()))return!1;var n,a=window.getComputedStyle(e,null);if(a)return"none"===a.getPropertyValue("display")||(n=["hidden","collapse"],a=a.getPropertyValue("visibility"),!(!n.includes(a)||t))||!!(n.includes(a)&&t&&n.includes(t))||!(!(t=u(e))||n.includes(a))&&pl(t,a);throw new Error("Style does not exist for the given element.")}var ml=pl,hl=function(e){return null!==(e=e.doctype)&&"html"===e.name&&!e.publicId&&!e.systemId};function gl(e){e=e instanceof b?e:D(e);return 1===e.props.nodeType&&!(parseInt(e.attr("tabindex",10))<=-1)&&k(e)}var bl=function(e){(e instanceof b||null!=(t=window)&&t.Node&&e instanceof window.Node)&&(e=axe.commons.aria.getRole(e));var t=A.ariaRoles[e];return(null==t?void 0:t.type)||null},yl=["block","list-item","table","flex","grid","inline-block"];function vl(e){e=window.getComputedStyle(e).getPropertyValue("display");return yl.includes(e)||"table-"===e.substr(0,6)}var wl=function(n,e){var t,a,r,o;return!vl(n)&&(t=function(e){for(var t=u(e);t&&!vl(t);)t=u(t);return D(t)}(n),r=a="",o=0,function t(e,n){!1!==n(e.actualNode)&&e.children.forEach(function(e){return t(e,n)})}(t,function(e){if(2===o)return!1;if(3===e.nodeType&&(a+=e.nodeValue),1===e.nodeType){var t=(e.nodeName||"").toUpperCase();if(e===n&&(o=1),!["BR","HR"].includes(t)){if("none"!==e.style.display&&"hidden"!==e.style.overflow&&["",null,"none"].includes(e.style.float)&&["",null,"relative"].includes(e.style.position))return"widget"===bl(e)?(r+=e.textContent,!1):void 0;return!1}0===o?r=a="":o=2}}),a=C(a),null!=e&&e.noLengthCompare?0!==a.length:(r=C(r),a.length>r.length))},Dl=function(e){if(e=(e=e||{}).modalPercent||.75,w.get("isModalOpen"))return w.get("isModalOpen");if(tu(axe._tree[0],"dialog, [role=dialog], [aria-modal=true]",F).length)return w.set("isModalOpen",!0),!0;for(var t=Ir(window),n=t.width*e,a=t.height*e,e=(t.width-n)/2,r=(t.height-a)/2,o=[{x:e,y:r},{x:t.width-e,y:r},{x:t.width/2,y:t.height/2},{x:e,y:t.height-r},{x:t.width-e,y:t.height-r}].map(function(e){return Array.from(document.elementsFromPoint(e.x,e.y))}),i=0;i<o.length;i++){var l=function(e){var t=o[e].find(function(e){e=window.getComputedStyle(e);return parseInt(e.width,10)>=n&&parseInt(e.height,10)>=a&&"none"!==e.getPropertyValue("pointer-events")&&("absolute"===e.position||"fixed"===e.position)});if(t&&o.every(function(e){return e.includes(t)}))return w.set("isModalOpen",!0),{v:!0}}(i);if("object"===te(l))return l.v}w.set("isModalOpen",void 0)};function xl(e){var t,n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:2,a=e.ownerDocument.createRange(),r=(a.setStart(e,0),a.setEnd(e,e.childNodes.length),0),o=0,i=f(a.getClientRects());try{for(i.s();!(t=i.n()).done;){var l=t.value;if(!(l.height<=n))if(r>l.top+n)r=Math.max(r,l.bottom);else{if(0!==o)return!0;r=l.bottom,o++}}}catch(e){i.e(e)}finally{i.f()}return!1}var El=function(e){return e instanceof window.Node},Fl={},R={set:function(e,t){if("string"!=typeof e)throw new Error("Incomplete data: key must be a string");return t&&(Fl[e]=t),Fl[e]},get:function(e){return Fl[e]},clear:function(){Fl={}}},Al=function(e,t){var n=e.nodeName.toUpperCase();return["IMG","CANVAS","OBJECT","IFRAME","VIDEO","SVG"].includes(n)?(R.set("bgColor","imgNode"),!0):((e="none"!==(n=(t=t||window.getComputedStyle(e)).getPropertyValue("background-image")))&&(t=/gradient/.test(n),R.set("bgColor",t?"bgGradient":"bgImage")),e)},Cl=/^#[0-9a-f]{3,8}$/i,kl=/^((?:rgb|hsl)a?)\s*\(([^\)]*)\)/i;function Tl(e,t,n){var a=3<arguments.length&&void 0!==arguments[3]?arguments[3]:1;le(this,Tl),this.red=e,this.green=t,this.blue=n,this.alpha=a}ue(Tl,[{key:"toHexString",value:function(){var e=Math.round(this.red).toString(16),t=Math.round(this.green).toString(16),n=Math.round(this.blue).toString(16);return"#"+(15.5<this.red?e:"0"+e)+(15.5<this.green?t:"0"+t)+(15.5<this.blue?n:"0"+n)}},{key:"toJSON",value:function(){return{red:this.red,green:this.green,blue:this.blue,alpha:this.alpha}}},{key:"parseString",value:function(e){var t,n,a;if(A.cssColors[e]||"transparent"===e)return t=(a=h(A.cssColors[e]||[0,0,0],3))[0],n=a[1],a=a[2],this.red=t,this.green=n,this.blue=a,this.alpha="transparent"===e?0:1,this;if(e.match(kl))return this.parseColorFnString(e),this;if(e.match(Cl))return this.parseHexString(e),this;throw new Error('Unable to parse color "'.concat(e,'"'))}},{key:"parseRgbString",value:function(e){"transparent"===e?(this.red=0,this.green=0,this.blue=0,this.alpha=0):this.parseColorFnString(e)}},{key:"parseHexString",value:function(e){var t,n;e.match(Cl)&&![6,8].includes(e.length)&&((e=e.replace("#","")).length<6&&(e=(t=(n=h(e,4))[0])+t+(t=n[1])+t+(t=n[2])+t,t=n[3])&&(e+=t+t),n=e.match(/.{1,2}/g),this.red=parseInt(n[0],16),this.green=parseInt(n[1],16),this.blue=parseInt(n[2],16),n[3]?this.alpha=parseInt(n[3],16)/255:this.alpha=1)}},{key:"parseColorFnString",value:function(e){var e=h(e.match(kl)||[],3),a=e[1],e=e[2];a&&e&&(e=e.split(/\s*[,\/\s]\s*/).map(function(e){return e.replace(",","").trim()}).filter(function(e){return""!==e}).map(function(e,t){var n=a;if(/%$/.test(e))return 3===t?parseFloat(e)/100:255*parseFloat(e)/100;if("h"===n[t]){if(/turn$/.test(e))return 360*parseFloat(e);if(/rad$/.test(e))return 57.3*parseFloat(e)}return parseFloat(e)}),"hsl"===a.substr(0,3)&&(e=function(e){var e=h(e,4),t=e[0],n=e[1],a=e[2],e=e[3],n=(n/=255,a/=255,(1-Math.abs(2*a-1))*n),r=n*(1-Math.abs(t/60%2-1)),o=a-n/2;a=t<60?[n,r,0]:t<120?[r,n,0]:t<180?[0,n,r]:t<240?[0,r,n]:t<300?[r,0,n]:[n,0,r];return a.map(function(e){return Math.round(255*(e+o))}).concat(e)}(e)),this.red=e[0],this.green=e[1],this.blue=e[2],this.alpha="number"==typeof e[3]?e[3]:1)}},{key:"getRelativeLuminance",value:function(){var e=this.red/255,t=this.green/255,n=this.blue/255;return.2126*(e<=.03928?e/12.92:Math.pow((.055+e)/1.055,2.4))+.7152*(t<=.03928?t/12.92:Math.pow((.055+t)/1.055,2.4))+.0722*(n<=.03928?n/12.92:Math.pow((.055+n)/1.055,2.4))}}]);var O=Tl,Nl=function(e){var t=new O;return t.parseString(e.getPropertyValue("background-color")),0!==t.alpha&&(e=e.getPropertyValue("opacity"),t.alpha=t.alpha*e),t},Rl=function(e){var t=window.getComputedStyle(e);return Al(e,t)||1===Nl(t).alpha};function Ol(e){var t;return!(!e.href||(t=w.get("firstPageLink",_l))&&e.compareDocumentPosition(t.actualNode)!==e.DOCUMENT_POSITION_FOLLOWING)}function _l(){var e=window.location.origin?du(axe._tree,'a[href]:not([href^="javascript:"])').find(function(e){return!uo(e.actualNode)}):du(axe._tree,'a:not([href^="#"]):not([href^="/#"]):not([href^="javascript:"])')[0];return e||null}var Sl=/rect\s*\(([0-9]+)px,?\s*([0-9]+)px,?\s*([0-9]+)px,?\s*([0-9]+)px\s*\)/,Il=/(\w+)\((\d+)/;function Pl(e,t,n){if(!e)throw new TypeError("Cannot determine if element is visible for non-DOM nodes");var a,r,o,i=e instanceof b?e:D(e),l=(e=i?i.actualNode:e,"_isVisible"+(t?"ScreenReader":"")),s=null!=(s=window.Node)?s:{},u=s.DOCUMENT_NODE,s=s.DOCUMENT_FRAGMENT_NODE,c=(i?i.props:e).nodeType,d=i?i.props.nodeName:e.nodeName.toLowerCase();if(i&&void 0!==i[l])return i[l];if(c===u)return!0;if(["style","script","noscript","template"].includes(d))return!1;if((e&&c===s&&(e=e.host),t)&&"true"===(i?i.attr("aria-hidden"):e.getAttribute("aria-hidden")))return!1;return e?null!==(u=window.getComputedStyle(e,null))&&("area"===d?(a=t,r=n,!!(s=cr(c=e,"map"))&&!!(s=s.getAttribute("name"))&&!(!(c=E(c))||9!==c.nodeType||!(o=du(axe._tree,'img[usemap="#'.concat(y(s),'"]')))||!o.length)&&o.some(function(e){return Pl(e.actualNode,a,r)})):"none"!==u.getPropertyValue("display")&&(d=parseInt(u.getPropertyValue("height")),c=parseInt(u.getPropertyValue("width")),o=(s=Ts(e))&&0===d,s=s&&0===c,d="absolute"===u.getPropertyValue("position")&&(d<2||c<2)&&"hidden"===u.getPropertyValue("overflow"),!(!t&&(function(e){var t=e.getPropertyValue("clip").match(Sl),n=e.getPropertyValue("clip-path").match(Il);if(t&&5===t.length){e=e.getPropertyValue("position");if(["fixed","absolute"].includes(e))return t[3]-t[1]<=0&&t[2]-t[4]<=0}if(n){var e=n[1],a=parseInt(n[2],10);switch(e){case"inset":return 50<=a;case"circle":return 0===a}}}(u)||"0"===u.getPropertyValue("opacity")||o||s||d)||!n&&("hidden"===u.getPropertyValue("visibility")||!t&&Pr(e))))&&(c=!1,(s=e.assignedSlot||e.parentNode)&&(c=Pl(s,t,!0)),i&&(i[l]=c),c)):(d=!0,(n=i.parent)&&(d=Pl(n,t,!0)),i&&(i[l]=d),d)}var Ml=Pl,Bl=function(e,t){for(var n=["fixed","sticky"],a=[],r=!1,o=0;o<e.length;++o){var i=e[o],l=(i===t&&(r=!0),window.getComputedStyle(i));r||-1===n.indexOf(l.position)?a.push(i):a=[]}return a};function Ll(e,t){var n=jl(t);do{var a,r,o,i,l,s,u=jl(e);if(u===n||u===t)return i=e,a=t,r=window.getComputedStyle(a),o=r.getPropertyValue("overflow"),"inline"===r.getPropertyValue("display")||(i=Array.from(i.getClientRects()),l=a.getBoundingClientRect(),s={left:l.left,top:l.top,width:l.width,height:l.height},(["scroll","auto"].includes(o)||a instanceof window.HTMLHtmlElement)&&(s.width=a.scrollWidth,s.height=a.scrollHeight),1===i.length&&"hidden"===o&&"nowrap"===r.getPropertyValue("white-space")&&(i[0]=s),i.some(function(e){return!(Math.ceil(e.left)<Math.floor(s.left)||Math.ceil(e.top)<Math.floor(s.top)||Math.floor(e.left+e.width)>Math.ceil(s.left+s.width)||Math.floor(e.top+e.height)>Math.ceil(s.top+s.height))}))}while(e=u);return!1}function jl(e){for(var t=D(e).parent;t;){if(Ts(t.actualNode))return t.actualNode;t=t.parent}}var ql,Vl,zl=function a(r,o){var t=2<arguments.length&&void 0!==arguments[2]?arguments[2]:document,i=3<arguments.length&&void 0!==arguments[3]?arguments[3]:0;if(999<i)throw new Error("Infinite loop detected");return Array.from(t.elementsFromPoint(r,o)||[]).filter(function(e){return E(e)===t}).reduce(function(e,t){var n;return or(t)&&(n=a(r,o,t.shadowRoot,i+1),!(e=e.concat(n)).length||!Ll(e[0],t))||e.push(t),e},[])},$l=function(e,t){var n,a;if(e.hasAttribute(t))return a=e.nodeName.toUpperCase(),n=e,["A","AREA"].includes(a)&&!e.ownerSVGElement||((n=document.createElement("a")).href=e.getAttribute(t)),a=["https:","ftps:"].includes(n.protocol)?n.protocol.replace(/s:$/,":"):n.protocol,e=/^\//.test(n.pathname)?n.pathname:"/".concat(n.pathname),t=(e=(e=(t=e).split("/").pop())&&-1!==e.indexOf(".")?{pathname:t.replace(e,""),filename:/index./.test(e)?"":e}:{pathname:t,filename:""}).pathname,e=e.filename,{protocol:a,hostname:n.hostname,port:(a=n.port,["443","80"].includes(a)?"":a),pathname:/\/$/.test(t)?t:"".concat(t,"/"),search:function(e){var t={};if(e&&e.length){var n=e.substring(1).split("&");if(n&&n.length)for(var a=0;a<n.length;a++){var r=h(n[a].split("="),2),o=r[0],r=r[1],r=void 0===r?"":r;t[decodeURIComponent(o)]=decodeURIComponent(r)}}return t}(n.search),hash:(a=n.hash)&&(t=a.match(/#!?\/?/g))&&"#"!==h(t,1)[0]?a:"",filename:e}},Ul=function(e,t){var n=t.getBoundingClientRect(),a=n.top,r=n.left,o=a-t.scrollTop,a=a-t.scrollTop+t.scrollHeight,i=r-t.scrollLeft,r=r-t.scrollLeft+t.scrollWidth;return!(e.left>r&&e.left>n.right||e.top>a&&e.top>n.bottom||e.right<i&&e.right<n.left||e.bottom<o&&e.bottom<n.top)&&(r=window.getComputedStyle(t),!(e.left>n.right||e.top>n.bottom)||"scroll"===r.overflow||"auto"===r.overflow||t instanceof window.HTMLBodyElement||t instanceof window.HTMLHtmlElement)},Hl=0;function Gl(e,t,n){var a;return le(this,Gl),(a=Vl.call(this)).shadowId=n,a.children=[],a.actualNode=e,(a.parent=t)||(Hl=0),a.nodeIndex=Hl++,a._isHidden=null,a._cache={},void 0===ql&&(ql=_n(e.ownerDocument)),a._isXHTML=ql,"input"===e.nodeName.toLowerCase()&&(n=e.getAttribute("type"),n=a._isXHTML?n:(n||"").toLowerCase(),wu().includes(n)||(n="text"),a._type=n),w.get("nodeMap")&&w.get("nodeMap").set(e,ne(a)),a}Q(Gl,b),Vl=ee(Gl),ue(Gl,[{key:"props",get:function(){var e,t,n,a,r,o,i;return this._cache.hasOwnProperty("props")||(e=(i=this.actualNode).nodeType,t=i.nodeName,n=i.id,a=i.multiple,r=i.nodeValue,o=i.value,i=i.selected,this._cache.props={nodeType:e,nodeName:this._isXHTML?t:t.toLowerCase(),id:n,type:this._type,multiple:a,nodeValue:r,value:o,selected:i}),this._cache.props}},{key:"attr",value:function(e){return"function"!=typeof this.actualNode.getAttribute?null:this.actualNode.getAttribute(e)}},{key:"hasAttr",value:function(e){return"function"==typeof this.actualNode.hasAttribute&&this.actualNode.hasAttribute(e)}},{key:"attrNames",get:function(){var e;return this._cache.hasOwnProperty("attrNames")||(e=(this.actualNode.attributes instanceof window.NamedNodeMap?this.actualNode:this.actualNode.cloneNode(!1)).attributes,this._cache.attrNames=Array.from(e).map(function(e){return e.name})),this._cache.attrNames}},{key:"getComputedStylePropertyValue",value:function(e){var t="computedStyle_"+e;return this._cache.hasOwnProperty(t)||(this._cache.hasOwnProperty("computedStyle")||(this._cache.computedStyle=window.getComputedStyle(this.actualNode)),this._cache[t]=this._cache.computedStyle.getPropertyValue(e)),this._cache[t]}},{key:"isFocusable",get:function(){return this._cache.hasOwnProperty("isFocusable")||(this._cache.isFocusable=k(this.actualNode)),this._cache.isFocusable}},{key:"tabbableElements",get:function(){return this._cache.hasOwnProperty("tabbableElements")||(this._cache.tabbableElements=Eo(this)),this._cache.tabbableElements}},{key:"clientRects",get:function(){return this._cache.hasOwnProperty("clientRects")||(this._cache.clientRects=Array.from(this.actualNode.getClientRects()).filter(function(e){return 0<e.width})),this._cache.clientRects}},{key:"boundingClientRect",get:function(){return this._cache.hasOwnProperty("boundingClientRect")||(this._cache.boundingClientRect=this.actualNode.getBoundingClientRect()),this._cache.boundingClientRect}}]);var Wl,Kl=Gl,_=function(e){return(e||"").trim().replace(/\s{2,}/g," ").split(" ")},Yl=" [idsMap]";function Jl(e,t,n){var a=e[0]._selectorMap;if(a){for(var r=e[0].shadowId,o=0;o<t.length;o++)if(1<t[o].length&&t[o].some(Xl))return;var i=new Set,l=(t.forEach(function(t){var e,n=function(e,t,n){var a=e[e.length-1],r=null,o=1<e.length||!!a.pseudos||!!a.classes;if(Xl(a))r=t["*"];else{if(a.id){if(!t[Yl]||null==(e=t[Yl][a.id])||!e.length)return;r=t[Yl][a.id].filter(function(e){return e.shadowId===n})}if(a.tag&&"*"!==a.tag){if(null==(e=t[a.tag])||!e.length)return;var e=t[a.tag];r=r?Ql(e,r):e}if(a.classes){if(null==(e=t["[class]"])||!e.length)return;e=t["[class]"];r=r?Ql(e,r):e}if(a.attributes)for(var i=0;i<a.attributes.length;i++){var l=a.attributes[i];if("attrValue"===l.type&&(o=!0),null==(s=t["[".concat(l.key,"]")])||!s.length)return;var s=t["[".concat(l.key,"]")];r=r?Ql(s,r):s}}return{nodes:r,isComplexSelector:o}}(t,a,r);null!=n&&null!=(e=n.nodes)&&e.forEach(function(e){n.isComplexSelector&&!sa(e,t)||i.add(e)})}),[]);return i.forEach(function(e){return l.push(e)}),(l=n?l.filter(n):l).sort(function(e,t){return e.nodeIndex-t.nodeIndex})}}function Xl(e){return"*"===e.tag&&!e.attributes&&!e.id&&!e.classes}function Ql(e,t){return e.filter(function(e){return t.includes(e)})}function Zl(e,t,n){n[e]=n[e]||[],n[e].push(t)}function es(t,n){1===t.props.nodeType&&(Zl(t.props.nodeName,t,n),Zl("*",t,n),t.attrNames.forEach(function(e){"id"===e&&(n[Yl]=n[Yl]||{},_(t.attr(e)).forEach(function(e){Zl(e,t,n[Yl])})),Zl("[".concat(e,"]"),t,n)}))}function ts(e,t,n){e=new Kl(e,t,n);return es(e,w.get("selectorMap")),e}function ns(e,a,n){var r,t,o;function i(e,t,n){t=ns(t,a,n);return e=t?e.concat(t):e}return o=(e=e.documentElement?e.documentElement:e).nodeName.toLowerCase(),or(e)?(Wl=!0,r=ts(e,n,a),a="a"+Math.random().toString().substring(2),t=Array.from(e.shadowRoot.childNodes),r.children=t.reduce(function(e,t){return i(e,t,r)},[]),[r]):"content"===o&&"function"==typeof e.getDistributedNodes?(t=Array.from(e.getDistributedNodes())).reduce(function(e,t){return i(e,t,n)},[]):"slot"===o&&"function"==typeof e.assignedNodes?((t=Array.from(e.assignedNodes())).length||(t=function(e){var t=[];for(e=e.firstChild;e;)t.push(e),e=e.nextSibling;return t}(e)),window.getComputedStyle(e),t.reduce(function(e,t){return i(e,t,n)},[])):1===e.nodeType?(r=ts(e,n,a),t=Array.from(e.childNodes),r.children=t.reduce(function(e,t){return i(e,t,r)},[]),[r]):3===e.nodeType?[ts(e,n)]:void 0}var as=function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:document.documentElement,t=1<arguments.length?arguments[1]:void 0,n=(Wl=!1,{});return w.set("nodeMap",new WeakMap),w.set("selectorMap",n),(e=ns(e,t,null))[0]._selectorMap=n,e[0]._hasShadowRoot=Wl,e},rs=function(e){return e?e.trim().split("-")[0].toLowerCase():""},os=function(e){var n={};return n.none=e.none.concat(e.all),n.any=e.any,Object.keys(n).map(function(e){var t;return n[e].length&&(t=axe._audit.data.failureSummaries[e])&&"function"==typeof t.failureMessage?t.failureMessage(n[e].map(function(e){return e.message||""})):void 0}).filter(function(e){return void 0!==e}).join("\n\n")};function is(){var e=axe._audit.data.incompleteFallbackMessage;return"string"!=typeof(e="function"==typeof e?e():e)?"":e}var ls=g.resultGroups,ss=function(e,n){var t=axe.utils.aggregateResult(e);return ls.forEach(function(e){n.resultTypes&&!n.resultTypes.includes(e)&&(t[e]||[]).forEach(function(e){Array.isArray(e.nodes)&&0<e.nodes.length&&(e.nodes=[e.nodes[0]])}),t[e]=(t[e]||[]).map(function(t){return t=Object.assign({},t),Array.isArray(t.nodes)&&0<t.nodes.length&&(t.nodes=t.nodes.map(function(e){var t,a;return"object"===te(e.node)&&(e.html=e.node.source,n.elementRef&&!e.node.fromFrame&&(e.element=e.node.element),!1===n.selectors&&!e.node.fromFrame||(e.target=e.node.selector),n.ancestry&&(e.ancestry=e.node.ancestry),n.xpath)&&(e.xpath=e.node.xpath),delete e.result,delete e.node,t=e,a=n,["any","all","none"].forEach(function(e){Array.isArray(t[e])&&t[e].filter(function(e){return Array.isArray(e.relatedNodes)}).forEach(function(e){e.relatedNodes=e.relatedNodes.map(function(e){var t,n={html:null!=(n=null==e?void 0:e.source)?n:"Undefined"};return!a.elementRef||null!=e&&e.fromFrame||(n.element=null!=(t=null==e?void 0:e.element)?t:null),(!1!==a.selectors||null!=e&&e.fromFrame)&&(n.target=null!=(t=null==e?void 0:e.selector)?t:[":root"]),a.ancestry&&(n.ancestry=null!=(t=null==e?void 0:e.ancestry)?t:[":root"]),a.xpath&&(n.xpath=null!=(t=null==e?void 0:e.xpath)?t:["/"]),n})})}),e})),ls.forEach(function(e){return delete t[e]}),delete t.pageLevel,delete t.result,t})}),t},us=/\$\{\s?data\s?\}/g;function cs(e,t){if("string"==typeof t)return e.replace(us,t);for(var n in t){var a;t.hasOwnProperty(n)&&(a=new RegExp("\\${\\s?data\\."+n+"\\s?}","g"),n=void 0===t[n]?"":String(t[n]),e=e.replace(a,n))}return e}var ds=function e(t,n){var a;if(t)return Array.isArray(n)?(n.values=n.join(", "),"string"==typeof t.singular&&"string"==typeof t.plural?cs(1===n.length?t.singular:t.plural,n):cs(t,n)):"string"==typeof t?cs(t,n):"string"==typeof n?cs(t[n],n):(a=t.default||is(),e(a=n&&n.messageKey&&t[n.messageKey]?t[n.messageKey]:a,n))},ps=function(e,t,n){var a=axe._audit.data.checks[e];if(!a)throw new Error("Cannot get message for unknown check: ".concat(e,"."));if(a.messages[t])return ds(a.messages[t],n);throw new Error('Check "'.concat(e,'"" does not have a "').concat(t,'" message.'))},fs=function(e,t,n){var t=((n.rules&&n.rules[t]||{}).checks||{})[e.id],a=(n.checks||{})[e.id],r=e.enabled,e=e.options;return a&&(a.hasOwnProperty("enabled")&&(r=a.enabled),a.hasOwnProperty("options"))&&(e=a.options),t&&(t.hasOwnProperty("enabled")&&(r=t.enabled),t.hasOwnProperty("options"))&&(e=t.options),{enabled:r,options:e,absolutePaths:n.absolutePaths}};function ms(){var e,t,n,a,r=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null,o=1<arguments.length&&void 0!==arguments[1]?arguments[1]:window;return r&&"object"===te(r)?r:"object"!==te(o)?{}:{testEngine:{name:"axe-core",version:axe.version},testRunner:{name:axe._audit.brand},testEnvironment:(r=o).navigator&&"object"===te(r.navigator)?(e=r.navigator,t=r.innerHeight,n=r.innerWidth,r=function(e){e=e.screen;return e.orientation||e.msOrientation||e.mozOrientation}(r)||{},a=r.angle,r=r.type,{userAgent:e.userAgent,windowWidth:n,windowHeight:t,orientationAngle:a,orientationType:r}):{},timestamp:(new Date).toISOString(),url:null==(e=o.location)?void 0:e.href}}function hs(e,t){var n=t.focusable,t=t.page;return{node:e,include:[],exclude:[],initiator:!1,focusable:n&&(!(n=(n=e).getAttribute("tabindex"))||(n=parseInt(n,10),isNaN(n))||0<=n),size:function(e){var t=parseInt(e.getAttribute("width"),10),n=parseInt(e.getAttribute("height"),10);(isNaN(t)||isNaN(n))&&(e=e.getBoundingClientRect(),t=isNaN(t)?e.width:t,n=isNaN(n)?e.height:n);return{width:t,height:n}}(e),page:t}}function gs(e){var t=0<arguments.length&&void 0!==e?e:[],n=[];Ds(t)||(t=[t]);for(var a=0;a<t.length;a++){var r=function(e){if(e instanceof window.Node)return e;if("string"==typeof e)return[e];vs(e)?(function(e){xs(Array.isArray(e.fromFrames),"fromFrames property must be an array"),xs(e.fromFrames.every(function(e){return!Es(e,"fromFrames")}),"Invalid context; fromFrames selector must be appended, rather than nested"),xs(!Es(e,"fromShadowDom"),"fromFrames and fromShadowDom cannot be used on the same object")}(e),e=e.fromFrames):ws(e)&&(e=[e]);return function(e){if(Array.isArray(e)){var t,n=[],a=f(e);try{for(a.s();!(t=a.n()).done;){var r=t.value;if(ws(r)&&(!function(e){xs(Array.isArray(e.fromShadowDom),"fromShadowDom property must be an array"),xs(e.fromShadowDom.every(function(e){return!Es(e,"fromFrames")}),"shadow selector must be inside fromFrame instead"),xs(e.fromShadowDom.every(function(e){return!Es(e,"fromShadowDom")}),"fromShadowDom selector must be appended, rather than nested")}(r),r=r.fromShadowDom),"string"!=typeof r&&!function(e){return Array.isArray(e)&&e.every(function(e){return"string"==typeof e})}(r))return;n.push(r)}}catch(e){a.e(e)}finally{a.f()}return n}}(e)}(t[a]);r&&n.push(r)}return n}function bs(t){return["include","exclude"].some(function(e){return Es(t,e)&&ys(t[e])})}function ys(e){return"string"==typeof e||e instanceof window.Node||vs(e)||ws(e)||Ds(e)}function vs(e){return Es(e,"fromFrames")}function ws(e){return Es(e,"fromShadowDom")}function Ds(e){return e&&"object"===te(e)&&"number"==typeof e.length&&e instanceof window.Node==!1}function xs(e,t){d(e,"Invalid context; ".concat(t,"\nSee: https://github.com/dequelabs/axe-core/blob/master/doc/context.md"))}function Es(e,t){return!(!e||"object"!==te(e))&&Object.prototype.hasOwnProperty.call(e,t)}function Fs(e,t){for(var n=[],a=0,r=e[t].length;a<r;a++){var o=e[t][a];o instanceof window.Node?o.documentElement instanceof window.Node?n.push(e.flatTree[0]):n.push(D(o)):o&&o.length&&(1<o.length?function(n,a,r){n.frames=n.frames||[],vu(r.shift()).forEach(function(t){var e=n.frames.find(function(e){return e.node===t});e||(e=hs(t,n),n.frames.push(e)),e[a].push(r)})}(e,t,o):(o=vu(o[0]),n.push.apply(n,v(o.map(function(e){return D(e)})))))}return n.filter(function(e){return e})}function As(e,t){var n=this,a=(e=ea(e),this.frames=[],this.page="boolean"==typeof(null==(a=e)?void 0:a.page)?e.page:void 0,this.initiator="boolean"!=typeof(null==(a=e)?void 0:a.initiator)||e.initiator,this.focusable="boolean"!=typeof(null==(a=e)?void 0:a.focusable)||e.focusable,this.size="object"===te(null==(a=e)?void 0:a.size)?e.size:{},e=function(e){if(bs(e)){var t=" must be used inside include or exclude. It should not be on the same object.";xs(!Es(e,"fromFrames"),"fromFrames"+t),xs(!Es(e,"fromShadowDom"),"fromShadowDom"+t)}else{if(!ys(e))return{include:[document],exclude:[]};e={include:e,exclude:[]}}return 0===(t=gs(e.include)).length&&t.push(document),e=gs(e.exclude),{include:t,exclude:e}}(e),this.flatTree=null!=t?t:as(function(e){for(var t=e.include,e=e.exclude,n=Array.from(t).concat(Array.from(e)),a=0;a<n.length;a++){var r=n[a];if(r instanceof window.Element)return r.ownerDocument.documentElement;if(r instanceof window.Document)return r.documentElement}return document.documentElement}(e)),this.exclude=e.exclude,this.include=e.include,this.include=Fs(this,"include"),this.exclude=Fs(this,"exclude"),gu("frame, iframe",this).forEach(function(e){var t;Bs(e,n)&&(t=n,N(e=e.actualNode))&&!Xa(t.frames,"node",e)&&t.frames.push(hs(e,t))}),void 0===this.page&&(this.page=function(e){e=e.include;return 1===e.length&&e[0].actualNode===document.documentElement}(this),this.frames.forEach(function(e){e.page=n.page})),this);if(0===a.include.length&&0===a.frames.length)throw a=Ha.isInFrame()?"frame":"page",new Error("No elements found for include in "+a+" Context");Array.isArray(this.include)||(this.include=Array.from(this.include)),this.include.sort(zs)}function Cs(e){return!1===(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).iframes?[]:new As(e).frames.map(function(e){var t=e.node,e=m(e,V);return e.initiator=!1,{frameSelector:Kn(t),frameContext:e}})}var ks=function(t){var e=axe._audit.rules.find(function(e){return e.id===t});if(e)return e;throw new Error("Cannot find rule by id: ".concat(t))};function Ts(e){var t,n,a=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0,r=e.scrollWidth>e.clientWidth+a,a=e.scrollHeight>e.clientHeight+a;if(r||a)return t=Ns(n=window.getComputedStyle(e),"overflow-x"),n=Ns(n,"overflow-y"),r&&t||a&&n?{elm:e,top:e.scrollTop,left:e.scrollLeft}:void 0}function Ns(e,t){e=e.getPropertyValue(t);return["scroll","auto"].includes(e)}var Rs=function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:window,t=e.document.documentElement;return[void 0!==e.pageXOffset?{elm:e,top:e.pageYOffset,left:e.pageXOffset}:{elm:t,top:t.scrollTop,left:t.scrollLeft}].concat(function a(e){return Array.from(e.children||e.childNodes||[]).reduce(function(e,t){var n=Ts(t);return n&&e.push(n),e.concat(a(t))},[])}(document.body))};function Os(){return ea(A)}var _s,Ss=function(l){if(l)return function(e){var t=e.data,n=e.isCrossOrigin,n=void 0!==n&&n,a=e.shadowId,r=e.root,o=e.priority,e=e.isLink,e=void 0!==e&&e,i=l.createElement("style");return e?(e=l.createTextNode('@import "'.concat(t.href,'"')),i.appendChild(e)):i.appendChild(l.createTextNode(t)),l.head.appendChild(i),{sheet:i.sheet,isCrossOrigin:n,shadowId:a,root:r,priority:o}};throw new Error("axe.utils.getStyleSheetFactory should be invoked with an argument")},Is=function(e){var t;return _s&&_s.parentNode?(void 0===_s.styleSheet?_s.appendChild(document.createTextNode(e)):_s.styleSheet.cssText+=e,_s):e?(t=document.head||document.getElementsByTagName("head")[0],(_s=document.createElement("style")).type="text/css",void 0===_s.styleSheet?_s.appendChild(document.createTextNode(e)):_s.styleSheet.cssText=e,t.appendChild(_s),_s):void 0},Ps=function e(t,n){var a,r=D(t);return 9!==t.nodeType&&(11===t.nodeType&&(t=t.host),r&&null!==r._isHidden?r._isHidden:!(a=window.getComputedStyle(t,null))||!t.parentNode||"none"===a.getPropertyValue("display")||!n&&"hidden"===a.getPropertyValue("visibility")||"true"===t.getAttribute("aria-hidden")||(n=e(t.assignedSlot||t.parentNode,!0),r&&(r._isHidden=n),n))},Ms=function(e){var t=null!=(t=null==(t=e.props)?void 0:t.nodeName)?t:e.nodeName.toLowerCase();return"http://www.w3.org/2000/svg"!==e.namespaceURI&&!!A.htmlElms[t]};function Bs(t,e){var n=e.include,n=void 0===n?[]:n,e=e.exclude,e=void 0===e?[]:e,n=n.filter(function(e){return tr(e,t)});return 0!==n.length&&(0===(e=e.filter(function(e){return tr(e,t)})).length||(n=Ls(n),tr(Ls(e),n)))}function Ls(e){var t,n,a=f(e);try{for(a.s();!(n=a.n()).done;){var r=n.value;t&&tr(r,t)||(t=r)}}catch(e){a.e(e)}finally{a.f()}return t}var js,qs,Vs=function(e,a){return e.length===a.length&&e.every(function(e,t){var n=a[t];return Array.isArray(e)?e.length===n.length&&e.every(function(e,t){return n[t]===e}):e===n})},zs=function(e,t){return(e=e.actualNode||e)===(t=t.actualNode||t)?0:4&e.compareDocumentPosition(t)?-1:1},$s=function(e,a,r,o){var t,n=4<arguments.length&&void 0!==arguments[4]&&arguments[4],i=Array.from(e.cssRules);return i?(t=i.filter(function(e){return 3===e.type})).length?(t=t.filter(function(e){return e.href}).map(function(e){return e.href}).filter(function(e){return!o.includes(e)}).map(function(e,t){var t=[].concat(v(r),[t]),n=/^https?:\/\/|^\/\//i.test(e);return Hs(e,a,t,o,n)}),(i=i.filter(function(e){return 3!==e.type})).length&&t.push(Promise.resolve(a.convertDataToStylesheet({data:i.map(function(e){return e.cssText}).join(),isCrossOrigin:n,priority:r,root:a.rootNode,shadowId:a.shadowId}))),Promise.all(t)):Promise.resolve({isCrossOrigin:n,priority:r,root:a.rootNode,shadowId:a.shadowId,sheet:e}):Promise.resolve()},Us=function(e,t,n,a){var r=4<arguments.length&&void 0!==arguments[4]&&arguments[4];return function(e){try{return!e.cssRules&&e.href?!1:!0}catch(e){return!1}}(e)?$s(e,t,n,a,r):Hs(e.href,t,n,a,!0)},Hs=function(e,t,n,a,r){return a.push(e),new Promise(function(t,n){var a=new window.XMLHttpRequest;a.open("GET",e),a.timeout=g.preload.timeout,a.addEventListener("error",n),a.addEventListener("timeout",n),a.addEventListener("loadend",function(e){if(e.loaded&&a.responseText)return t(a.responseText);n(a.responseText)}),a.send()}).then(function(e){e=t.convertDataToStylesheet({data:e,isCrossOrigin:r,priority:n,root:t.rootNode,shadowId:t.shadowId});return Us(e.sheet,t,n,a,e.isCrossOrigin)})};function Gs(){if(window.performance&&window.performance)return window.performance.now()}js=null,qs=Gs();var Ws,Ks,s={start:function(){this.mark("mark_axe_start")},end:function(){this.mark("mark_axe_end"),this.measure("axe","mark_axe_start","mark_axe_end"),this.logMeasures("axe")},auditStart:function(){this.mark("mark_audit_start")},auditEnd:function(){this.mark("mark_audit_end"),this.measure("audit_start_to_end","mark_audit_start","mark_audit_end"),this.logMeasures()},mark:function(e){window.performance&&void 0!==window.performance.mark&&window.performance.mark(e)},measure:function(e,t,n){window.performance&&void 0!==window.performance.measure&&window.performance.measure(e,t,n)},logMeasures:function(e){function t(e){sn("Measure "+e.name+" took "+e.duration+"ms")}if(window.performance&&void 0!==window.performance.getEntriesByType)for(var n=window.performance.getEntriesByName("mark_axe_start")[0],a=window.performance.getEntriesByType("measure").filter(function(e){return e.startTime>=n.startTime}),r=0;r<a.length;++r){var o=a[r];if(o.name===e)return void t(o);t(o)}},timeElapsed:function(){return Gs()-qs},reset:function(){js=js||Gs(),qs=Gs()}};function Ys(){var e,l,s,u;return document.elementsFromPoint||document.msElementsFromPoint||((e=document.createElement("x")).style.cssText="pointer-events:auto",e="auto"===e.style.pointerEvents,l=e?"pointer-events":"visibility",s=e?"none":"hidden",(u=document.createElement("style")).innerHTML=e?"* { pointer-events: all }":"* { visibility: visible }",function(e,t){var n,a,r,o=[],i=[];for(document.head.appendChild(u);(n=document.elementFromPoint(e,t))&&-1===o.indexOf(n);)o.push(n),i.push({value:n.style.getPropertyValue(l),priority:n.style.getPropertyPriority(l)}),n.style.setProperty(l,s,"important");for(o.indexOf(document.documentElement)<o.length-1&&(o.splice(o.indexOf(document.documentElement),1),o.push(document.documentElement)),a=i.length;r=i[--a];)o[a].style.setProperty(l,r.value||"",r.priority);return document.head.removeChild(u),o})}function Js(e){return"function"==typeof e||"[object Function]"===Ws.call(e)}function Xs(e){return e=function(e){e=Number(e);return isNaN(e)?0:0!==e&&isFinite(e)?(0<e?1:-1)*Math.floor(Math.abs(e)):e}(e),Math.min(Math.max(e,0),Ks)}"function"!=typeof Object.assign&&(Object.assign=function(e){if(null==e)throw new TypeError("Cannot convert undefined or null to object");for(var t=Object(e),n=1;n<arguments.length;n++){var a=arguments[n];if(null!=a)for(var r in a)a.hasOwnProperty(r)&&(t[r]=a[r])}return t}),Array.prototype.find||Object.defineProperty(Array.prototype,"find",{value:function(e){if(null===this)throw new TypeError("Array.prototype.find called on null or undefined");if("function"!=typeof e)throw new TypeError("predicate must be a function");for(var t,n=Object(this),a=n.length>>>0,r=arguments[1],o=0;o<a;o++)if(t=n[o],e.call(r,t,o,n))return t}}),Array.prototype.findIndex||Object.defineProperty(Array.prototype,"findIndex",{value:function(e,t){if(null===this)throw new TypeError("Array.prototype.find called on null or undefined");if("function"!=typeof e)throw new TypeError("predicate must be a function");for(var n,a=Object(this),r=a.length>>>0,o=0;o<r;o++)if(n=a[o],e.call(t,n,o,a))return o;return-1}}),"function"==typeof window.addEventListener&&(document.elementsFromPoint=Ys()),Array.prototype.includes||Object.defineProperty(Array.prototype,"includes",{value:function(e){var t=Object(this),n=parseInt(t.length,10)||0;if(0!==n){var a,r,o=parseInt(arguments[1],10)||0;for(0<=o?a=o:(a=n+o)<0&&(a=0);a<n;){if(e===(r=t[a])||e!=e&&r!=r)return!0;a++}}return!1}}),Array.prototype.some||Object.defineProperty(Array.prototype,"some",{value:function(e){if(null==this)throw new TypeError("Array.prototype.some called on null or undefined");if("function"!=typeof e)throw new TypeError;for(var t=Object(this),n=t.length>>>0,a=2<=arguments.length?arguments[1]:void 0,r=0;r<n;r++)if(r in t&&e.call(a,t[r],r,t))return!0;return!1}}),Array.from||Object.defineProperty(Array,"from",{value:(Ws=Object.prototype.toString,Ks=Math.pow(2,53)-1,function(e){var t=Object(e);if(null==e)throw new TypeError("Array.from requires an array-like object - not null or undefined");var n,a=1<arguments.length?arguments[1]:void 0;if(void 0!==a){if(!Js(a))throw new TypeError("Array.from: when provided, the second argument must be a function");2<arguments.length&&(n=arguments[2])}for(var r,o=Xs(t.length),i=Js(this)?Object(new this(o)):new Array(o),l=0;l<o;)r=t[l],i[l]=a?void 0===n?a(r,l):a.call(n,r,l):r,l+=1;return i.length=o,i})}),String.prototype.includes||(String.prototype.includes=function(e,t){return!((t="number"!=typeof t?0:t)+e.length>this.length)&&-1!==this.indexOf(e,t)}),Array.prototype.flat||Object.defineProperty(Array.prototype,"flat",{configurable:!0,value:function n(){var a=isNaN(arguments[0])?1:Number(arguments[0]);return a?Array.prototype.reduce.call(this,function(e,t){return Array.isArray(t)?e.push.apply(e,n.call(t,a-1)):e.push(t),e},[]):Array.prototype.slice.call(this)},writable:!0}),!window.Node||"isConnected"in window.Node.prototype||Object.defineProperty(window.Node.prototype,"isConnected",{get:function(){return!(this.ownerDocument&&this.ownerDocument.compareDocumentPosition(this)&this.DOCUMENT_POSITION_DISCONNECTED)}});var Qs=function(e,t){return e.concat(t).filter(function(e,t,n){return n.indexOf(e)===t})};function Zs(e,t,n,a,r){r=r||{};return r.vNodes=e,r.vNodesIndex=0,r.anyLevel=t,r.thisLevel=n,r.parentShadowId=a,r}var eu=[],tu=function(e,t,n){e=Array.isArray(e)?e:[e];t=la(t);if(a=Jl(e,t,n))return a;for(var a=e,e=t,r=n,o=[],i=Zs(Array.isArray(a)?a:[a],e,null,a[0].shadowId,eu.pop()),l=[];i.vNodesIndex<i.vNodes.length;){for(var s,u=i.vNodes[i.vNodesIndex++],c=null,d=null,p=((null==(s=i.anyLevel)?void 0:s.length)||0)+((null==(s=i.thisLevel)?void 0:s.length)||0),f=!1,m=0;m<p;m++){var h=m<((null==(h=i.anyLevel)?void 0:h.length)||0)?i.anyLevel[m]:i.thisLevel[m-((null==(h=i.anyLevel)?void 0:h.length)||0)];if((!h[0].id||u.shadowId===i.parentShadowId)&&sa(u,h[0]))if(1===h.length)f||r&&!r(u)||(l.push(u),f=!0);else{var g=h.slice(1);if(!1===[" ",">"].includes(g[0].combinator))throw new Error("axe.utils.querySelectorAll does not support the combinator: "+h[1].combinator);(">"===g[0].combinator?c=c||[]:d=d||[]).push(g)}h[0].id&&u.shadowId!==i.parentShadowId||null==(g=i.anyLevel)||!g.includes(h)||(d=d||[]).push(h)}for(u.children&&u.children.length&&(o.push(i),i=Zs(u.children,d,c,u.shadowId,eu.pop()));i.vNodesIndex===i.vNodes.length&&o.length;)eu.push(i),i=o.pop()}return l},nu=function(e){var t,n,l,s,e=void 0===(e=e.treeRoot)?axe._tree[0]:e;return t=[],e=tu(e=e,"*",function(e){return!t.includes(e.shadowId)&&(t.push(e.shadowId),!0)}).map(function(e){return{shadowId:e.shadowId,rootNode:lr(e.actualNode)}}),(e=Qs(e,[])).length?(n=document.implementation.createHTMLDocument("Dynamic document for loading cssom"),n=Ss(n),l=n,s=[],e.forEach(function(e,t){var n=e.rootNode,e=e.shadowId,a=function(e,t,n){t=11===e.nodeType&&t?function(a,r){return Array.from(a.children).filter(au).reduce(function(e,t){var n=t.nodeName.toUpperCase(),t="STYLE"===n?t.textContent:t,t=r({data:t,isLink:"LINK"===n,root:a});return e.push(t.sheet),e},[])}(e,n):function(e){return Array.from(e.styleSheets).filter(function(e){return!!e.media&&ru(e.media.mediaText)})}(e);return function(e){var t=[];return e.filter(function(e){if(e.href){if(t.includes(e.href))return!1;t.push(e.href)}return!0})}(t)}(n,e,l);if(!a)return Promise.all(s);var r=t+1,o={rootNode:n,shadowId:e,convertDataToStylesheet:l,rootIndex:r},i=[],t=Promise.all(a.map(function(e,t){return Us(e,o,[r,t],i)}));s.push(t)}),Promise.all(s).then(function n(e){return e.reduce(function(e,t){return Array.isArray(t)?e.concat(n(t)):e.concat(t)},[])})):Promise.resolve()};function au(e){var t=e.nodeName.toUpperCase(),n=e.getAttribute("href"),a=e.getAttribute("rel"),n="LINK"===t&&n&&a&&e.rel.toUpperCase().includes("STYLESHEET");return"STYLE"===t||n&&ru(e.media)}function ru(e){return!e||!e.toUpperCase().includes("PRINT")}var ou=function(e){return e=void 0===(e=e.treeRoot)?axe._tree[0]:e,e=tu(e,"video, audio",function(e){e=e.actualNode;return e.hasAttribute("src")?!!e.getAttribute("src"):!(Array.from(e.getElementsByTagName("source")).filter(function(e){return!!e.getAttribute("src")}).length<=0)}),Promise.all(e.map(function(e){var n,e=e.actualNode;return n=e,new Promise(function(t){0<n.readyState&&t(n),n.addEventListener("loadedmetadata",function e(){n.removeEventListener("loadedmetadata",e),t(n)})})}))};function iu(e){return!e||void 0===e.preload||null===e.preload||("boolean"==typeof e.preload?e.preload:(e=e.preload,"object"===te(e)&&Array.isArray(e.assets)))}function lu(e){var t=g.preload,n=t.assets,t=t.timeout,t={assets:n,timeout:t};if(e.preload&&"boolean"!=typeof e.preload){if(!e.preload.assets.every(function(e){return n.includes(e.toLowerCase())}))throw new Error("Requested assets, not supported. Supported assets are: ".concat(n.join(", "),"."));t.assets=Qs(e.preload.assets.map(function(e){return e.toLowerCase()}),[]),e.preload.timeout&&"number"==typeof e.preload.timeout&&!isNaN(e.preload.timeout)&&(t.timeout=e.preload.timeout)}return t}var su=function(o){var i={cssom:nu,media:ou};return iu(o)?new Promise(function(t,n){var e=lu(o),a=e.assets,e=e.timeout,r=setTimeout(function(){return n(new Error("Preload assets timed out."))},e);Promise.all(a.map(function(a){return i[a](o).then(function(e){return t={},e=e,(n=ce(n=a))in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t;var t,n})})).then(function(e){e=e.reduce(function(e,t){return p({},e,t)},{});clearTimeout(r),t(e)}).catch(function(e){clearTimeout(r),n(e)})}):Promise.resolve()};function uu(a,r,o){return function(e){var t=a[e.id]||{},n=t.messages||{},t=Object.assign({},t);delete t.messages,o.reviewOnFail||void 0!==e.result?t.message=e.result===r?n.pass:n.fail:("object"!==te(n.incomplete)||Array.isArray(e.data)||(t.message=function(t,n){function a(e){return e.incomplete&&e.incomplete.default?e.incomplete.default:is()}if(!t||!t.missingData)return t&&t.messageKey?n.incomplete[t.messageKey]:a(n);try{var e=n.incomplete[t.missingData[0].reason];if(e)return e;throw new Error}catch(e){return"string"==typeof t.missingData?n.incomplete[t.missingData]:a(n)}}(e.data,n)),t.message||(t.message=n.incomplete)),"function"!=typeof t.message&&(t.message=ds(t.message,e.data)),ar(e,t)}}var cu=function(e){var t=axe._audit.data.checks||{},n=axe._audit.data.rules||{},a=Xa(axe._audit.rules,"id",e.id)||{},r=(e.tags=ea(a.tags||[]),uu(t,!0,a)),o=uu(t,!1,a);e.nodes.forEach(function(e){e.any.forEach(r),e.all.forEach(r),e.none.forEach(o)}),ar(e,ea(n[e.id]||{}))},du=function(e,t){return tu(e,t)};function pu(t,e){var n,a=axe._audit&&axe._audit.tagExclude?axe._audit.tagExclude:[],r=e.hasOwnProperty("include")||e.hasOwnProperty("exclude")?(n=e.include||[],n=Array.isArray(n)?n:[n],r=e.exclude||[],(r=Array.isArray(r)?r:[r]).concat(a.filter(function(e){return-1===n.indexOf(e)}))):(n=Array.isArray(e)?e:[e],a.filter(function(e){return-1===n.indexOf(e)}));return!!(n.some(function(e){return-1!==t.tags.indexOf(e)})||0===n.length&&!1!==t.enabled)&&r.every(function(e){return-1===t.tags.indexOf(e)})}var fu=function(e,t,n){var a=n.runOnly||{},n=(n.rules||{})[e.id];return!(e.pageLevel&&!t.page)&&("rule"===a.type?-1!==a.values.indexOf(e.id):n&&"boolean"==typeof n.enabled?n.enabled:"tag"===a.type&&a.values?pu(e,a.values):pu(e,[]))};function mu(e,t){var n,a,r;return t?(r=e.cloneNode(!1),n=Rn(r),r=1===r.nodeType?(a=r.outerHTML,w.get(a,function(){return hu(r,n,e,t)})):hu(r,n,e,t),Array.from(e.childNodes).forEach(function(e){r.appendChild(mu(e,t))}),r):e}function hu(r,e,o,i){return e&&(r=document.createElement(r.nodeName),Array.from(e).forEach(function(e){var t,n,a;t=o,n=e.name,void 0!==(a=i)[n]&&(!0===a[n]||On(t,a[n]))||r.setAttribute(e.name,e.value)})),r}function gu(e,t){var n=[];if(axe._selectCache)for(var a=0,r=axe._selectCache.length;a<r;a++){var o=axe._selectCache[a];if(o.selector===e)return o.result}for(var i,l=t.include.reduce(function(e,t){return e.length&&tr(e[e.length-1],t)||e.push(t),e},[]),s=(i=t).exclude&&0!==i.exclude.length?function(e){return Bs(e,i)}:null,u=0;u<l.length;u++)var c=l[u],n=function(e,t){if(0===e.length)return t;{var n;e.length<t.length&&(n=e,e=t,t=n)}for(var a=0,r=t.length;a<r;a++)e.includes(t[a])||e.push(t[a]);return e}(n,tu(c,e,s));return axe._selectCache&&axe._selectCache.push({selector:e,result:n}),n}var bu=function(e){e.forEach(function(e){var t=e.elm,n=e.top,e=e.left;if(t===window)return t.scroll(e,n);t.scrollTop=n,t.scrollLeft=e})};function yu(e){return function e(t,n){var a=t.shift();n=a?n.querySelector(a):null;if(0===t.length)return n;if(null==n||!n.shadowRoot)return null;return e(t,n.shadowRoot)}(Array.isArray(e)?v(e):[e],document)}function vu(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:document,n=Array.isArray(e)?v(e):[e];return 0===e.length?[]:function e(t,n){var t=X(t),a=t[0],r=t.slice(1);t=n.querySelectorAll(a);if(0===r.length)return Array.from(t);var o=[];var i,l=f(t);try{for(l.s();!(i=l.n()).done;){var s=i.value;null!=s&&s.shadowRoot&&o.push.apply(o,v(e(r,s.shadowRoot)))}}catch(e){l.e(e)}finally{l.f()}return o}(n,t)}var wu=function(){return["hidden","text","search","tel","url","email","password","date","month","week","time","datetime-local","number","range","color","checkbox","radio","file","submit","image","reset","button"]},Du=[,[,[1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,,1,1,1,1,1,1,,1],[1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,,1,1,1,,1,1,,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1],[,1,1,,1,1,1,1,1,1,1,,1,,1,1,1,1,1,1,1,1,,1,1,1,1],[1,1,1,1,1,1,,,,,,1,1,1,1,,,1,1,1,,1,,1,,1,1],[1,1,1,,1,1,,1,1,1,,1,,,1,1,1,,,1,1,1,,,,,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,,,,,1,1,1,,1,1,1,1,1,1,,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,,1,1,1],[,1,,,,,,1,,1,,,,,1,,1,,,,1,1,,1,,,1],[1,,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1],[,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,,,1,1,1,1,,,1,,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,,1,1,,,1,,,,,1,1,1,,1,,1,,1,,,,,,1],[1,,1,1,1,1,,,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1],[1,,1,,1,,,,,1,,1,1,1,1,1,,,,1,1,1,1],[,1,1,1,1,1,,1,1,1,,1,,1,1,1,,,1,1,1,1,1,1,1,1],[,,1,,,1,,1,,,,1,1,1,,,,,,,,,,,1],[1,1,1,1,1,1,,1,1,1,,1,1,,1,1,1,1,1,1,1,1,,,1,1,1],[1,1,1,1,1,,,1,,,1,,,1,1,1,,,,,1,,,,,,1]],[,[1,1,1,1,1,1,1,1,1,1,1,,1,,1,1,1,,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,,1,,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1],[1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,,,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1],[,1,1,,1,,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]],[,[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1],[,1,1,1,1,1,,1,1,1,1,1,1,,1,1,,1,1,1,1,1,1,1,,1],[,1,,1,1,1,,1,1,,1,,1,1,1,1,1,1,1,1],[,1,,1,1,1,1,1,1,1,1,,,1,1,1,,,1,1,,,,,,1,1],[1,1,1,,,,,1,,,,1,1,,1,,,,,,1,,,,,1],[,1,,,1,,,1,,,,,,1],[,1,,1,,,,1,,,,1],[1,,1,1,1,,1,1,1,,1,1,1,1,1,1,1,1,1,,1,,,1,1,1,1],[,1,1,1,1,1,,,1,,,1,,1,1,,1,,1,,,,,1,,1],[,1,,,,1,,,1,1,,1,,1,1,1,1,,1,1,,,1,,,1],[,1,1,,,,,,1,,,,1,1,1,1,,1,1,1,1,1,1,,1,1,1],[,1,,1,1,1,,,1,1,1,1,1,1,,1,,,,,1,1,,1,,1],[,1,,1,,1,,1,,1,,1,1,1,1,1,,,1,1,1],[,1,1,1,,,,1,1,1,,1,1,,,1,1,,1,1,1,1,,1,1],[1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,,,1,1,1,1,1,1,1],[,1,1,1,,1,1,1,,1,,,,,1,1,1,,,1,,1,,,1,1],[,,,,1,,,,,,,,,,,,,,,,,1],[1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1],[,1,,1,1,1,,1,1,,,,1,1,1,1,1,,,1,1,1,,,,,1],[1,1,1,1,,,,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1],[1,,,,,,,1,,,,,,,1],[,1,1,,1,1,,1,,,,,,,,,,,,,1],,[1,1,1,,,,,,,,,,,,,1],[,,,,,,,,1,,,1,,,1,1,,,,,1]],[,[1,1,,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,,1,1,1,1,1,1],[,1,1,,1,1,1,1,,1,1,,1,1,1,1,1,1,1,,1,1,1,1,,1],[,,,1,,,,,,,,,,,,,,,1],[,1,,,1,1,,1,,1,1,,,,1,1,,,1,1,,,,1],[1,,,1,1,1,1,1,1,1,,1,1,1,1,,1,1,1,1,,,1,,,,1],,[,1,1,1,1,1,,1,1,1,,1,1,,1,1,,,1,1,1,1,,1,1,,1],[,1,,,1,,,1,,1,,,1,1,1,1,,,1,1,,1,1,1,1],[,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1],[,1,1,1,1,1,1,,,1,1,1,1,1,1,1,,,1,,,1,,1],[,1,,,,,,,,,,1,1,,,,,,1,1,,,,,1],[,,,,,,,1,,,,1,,1,1],[,1,1,1,1,1,1,1,,,,1,1,1,1,1,,,1,1,,1,1,1,1,1],[,1,,,1,1,,1,,1,1,1,,,1,1,,,1,,1,1,1,1,,1],[,1,1,1,,1,1,,1,1,,1,1,,1,1,1,1,1,1,1,,1,1,1,1,1],[,,,,,,,,,,,,,,,,1],,[,1,1,1,1,1,,1,1,1,,,1,,1,1,,1,1,1,1,1,,1,,1],[,,1,,,1,,,1,1,,,1,,1,1,,1],[,1,1,,1,,,,1,1,,1,,1,1,1,1,,1,1,1,1,,,,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1],[1,1],[,1,,,,,,,,,,1,1,,,,,,1,1,,1,,1,,1,1],,[,1,1,,1,,,1,,1,,,,1,1,1,,,,,,1,,,,1],[1,1,,,1,1,,1,,,,,1,,1]],[,[,1],[,,,1,,,,1,,,,1,,,,1,,,1,,,1],[,,,,,,,,,,,,,,,,,,1,1,,,,,,1],,[1,,,,,1],[,1,,,,1,,,,1],[,1,,,,,,,,,,,1,,,1,,,,,,,,,1,1],[,,,,,,,,,,,,,,,,,,,,,1],[,,,,,,,,,,,,,,,,1,,,,1,,1],[,1],[,1,,1,,1,,1,,1,,1,1,1,,1,1,,1,,,,,,,1],[1,,,,,1,,,1,1,,1,,1,,1,1,,,,,1,,,1],[,1,1,,,1,,1,,1,,1,,1,1,1,1,,,1,,1,,1,1,1],[1,1,1,1,1,,1,,1,,,,1,1,1,1,,1,1,,,1,1,1,1],[1,,,,,,,,,,,,,,,,,,,,1],[,,,,,,,,,1],,[,1,,,,,,1,1,1,,1,,,,1,,,1,1,1,,,1],[1,,,,,1,,1,1,1,,1,1,1,1,1,,1,,1,,1,,,1,1],[1,,1,1,,,,,1,,,,,,1,1,,,1,1,1,1,,,1,,1],[1,,,,,,,,,,,,,,,,,1],[,,,,,1,,,1,,,,,,1],[,,,,,,,,,,,,,,,1],[,,,,,,,,,,,,,,,,,,,,1],[,1,,,,,,,,,,,,,,1],[,1,,,,1]],[,[1,1,1,,1,,1,1,1,1,1,1,1,1,1,,1,,1,,1,1,,,1,1,1],[,,,,,,,,,,,,1],[,,,,,,,,,,,,,,,,,,,1],,[,,,,,,,,,,,,,,,,,,1],[1,,,,,,,,,1,,,,1],[,,,,,,,,,,,,,,,,,,1],,[1,1,,,,1,1,,,,,,1,,,,1,,1,,1,1,,1],[1],[,,,,,,,,,,,1,,,,,,,,,,,1],[,1,,,,,,,1,1,,,1,,1,,,,1,,,,,,,1],[,,,,,,,,,,,,,,,,1,,,,,1],[,,1,,,,,1,,1],[1,,,,1,,,,,1,,,,1,1,,,,1,1,,,,,1],[,,,,,1],[,,,,,,,,,,,,,,,,,,,1],[1,,,1,1,,,,,,,1,,1,,1,1,1,1,1,1],[,,,,,1,,,,,,,1,,,,,,,1],,[,,1,1,1,1,1,,1,1,1,,,1,1,,,1,1,,1,1,1,,,1],[,,,,,,,,,,,,,,,,,,1],[,1,,,,1],,[1]],[,[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1],[,,,1,1,1,1,,,,,,1,,1,,,,1,,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,,,1],[,1,1,1,1,,1,1,1,1,1,1,1,1,,,,1,,1,,,1,1,1,1,1],[,,,,,,,,,,,1,,,,,,,,,1,,,,1],[,1,1,,1,1,,1,,,,1,1,,1,1,,,1,,1,1,,1],[,1,,1,,1,,,1,,,1,1,,1,1,,,1,1,1],[,1,1,1,1,1,,1,1,,,,1,1,1,1,1,1,1,1,1,1,,1,1,1,1],[,,,,,,,,,1,,1,,1,1,,,,1,,,1],[,1,,,1,1,,,,,,,,,1,1,1,,,,,1],[1,,,1,1,,,,1,1,1,1,1,,,1,,,1,,,1,,1,,1],[,1,1,,1,1,,1,1,,,,1,1,1,,,1,1,,,1,1,1,1,1,1],[1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,,1,1,,1,1,,1,,,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1],[,1,,,,1,,,,,,,,,1],[,1,,,,,,,,1,,,,,1,,,,1,,,1],[,1,1,1,1,,,1,1,1,1,1,,1,,1,,1,1,1,1,1,1,1,1,1,1],[,,,,,1,,1,,,,,1,1,1,1,1,,,1,,,,1],[,1,,,,,,,,1,,,,,,,,,,,,1],[1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1],[1,1,,1,,1,1,,,,1,,1,1,1,1,1,,1,1,,,,,,1],[,1,1,1,1,1,1,1,,1,1,,,1,1,,,,1,,1,1,,1,1],[,,,,,,,,,,,,,,,,,,,,,,,,1],[,1,1,,1,1,1,1,,1,,,1,1,1,1,,,1,,,,,,,1],[,1,,,,,,,,1,,,,,1]],[,[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,,1,1,1,1,1],[,1,1,,,,,,,,,,,,1,1,,,,,,1],[,1,,,,,,,1],[,,,,,,,,,,,,,,1,,,,,1,,,,,,1],[1,1,,,1,,,1,1,1,,,,1],,[,,,,,,,,,,,,,1,,,,,,,,,,1],[,,,,,,,,,1,,,,,,,,,1,,,,,,,1],[1,1,1,,1,,1,1,1,1,1,1,1,1,,1,,,1,,1,,,1,1],[,,,,,,,,,1],[,1,,,,1,,,,,,1,,,1,,,,,1],[,1,1,,1,1,,,,,,,,,,,,,,,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1],[,1,,,1,1,,1,1,1,1,,,,1,1,,,,1,,1],[1,1,1,1,1,1,,,1,1,1,1,1,1,,1,1,,1,1,1,,1,1,,1,1],[,,,,,,,,,,,,,,,1,,,,1],,[1,1,,1,,1,,,,,,1,,1,,1,1,,1,,1,1,,1,1,,1],[,,1,,,,,,1,,,,1,,1,,,,,1],[1,,,,,,,,,1,,,,,,1,,,,1,,1,,,1],[1,,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1],[,,,1,,1,,,,,,1,,,1,,,,,,,,1],[,1,,1,,,,,,,,,,,,1],,[1,1,,,,,,,,,,,,,,,,,,,,,,1,1],[1]],[,[1,,,,,,,,,1,,,,,1,,1,,1],[,1,1,,1,1,,1,1,1,,,1,1,1,,,,1,,,1,,,,1],[,1,,,,,,,1,,,,1,,,,,,1],[1,1,1,1,1,1,,,,1,,,,,,,,,1,1,1,1],[1],[,1,1,,,1,1,,,,,1,,1,,,,,,,,1,,,,1],[1,,1,,,1,,1,,,,,1,1,1,1,,,,1,,,,1],[,,1,,,,,,,1,,,,,,,1,,,,,,,1],[1,,,,,,,,,,,,,,1,,,,1],[,,,1,,1,,,,,1,,,,1,1,,,,1],[1,,,,,1,,,,1,,1,1,,,1,1,,1,1,1,,1,1,1,,1],[,1,1,,,,,1,,1,,1,1,1,,1,1,,,1,,1,1,1],[,1,,,,1,,,,1,,,1,,1,1,,,1,1,,,,,,1],[1,,1,1,,1,,1,1,,1,,1,1,1,1,1,,,1,1,,,,,,1],[1,,,,,,,,,,,,,,,,,,1,,,1,,1],[,,,,,,,,,1,,,,,,1],[,,,,,,,,,,,,,,,,,,,,,1,,1],[,1,,,,1,,,1,1,,1,,,1,1,,,1,,,1,,,1,1],[1,1,,1,1,1,,1,1,1,,1,,1,1,1,,,1,,1,1],[1,,1,1,1,1,,,,1,,1,1,1,,1,,,1,1,1,,1,1,1,1,1],[1,,,,,,,,,,,,,1],[,,1,,,,,,,,,,,,,,,,,,,,1],[1,,,,,,,,,,,1,,1,,1,,,,1],[,,,1,,,,,,,,,1],[,1,,,,,,,,,,,,,,1,,,,,,,,,1],[,,,,,,,,1,1,,,,,,,,,1,,,,,,,,1]],[,[1,1,1,1,1,1,1,,1,,1,1,1,1,1,1,,1,1,1,1,1,,,1,1,1],[,,,,,1,,,,1,1,1,,,1,1,,,1,,1,1,,1],[,,,,,,,,,,,,,,,,,,,1,1],[,1,,,,,,1,,,,,,,,,,,,,1],[,,1,,,1,,1,1,1,,1,1,,1,,,,1,,1,1],,[,,1,,,1,,,,,,1,,,,1],[,,,,,,,,,1,,,,,,,,,,1],[1,1,1,1,1,1,,1,1,1,,,1,1,,1,,1,,,1,1,1,,,1],[,,,,,1,,,,,,,,,,,,,1],[,1,,,,,,,,,,,,1,,1,1,,1,,,1],[,,,,,1,,,,,,,,,,,,,,1],[,1,1,1,1,,,,,1,,,1,,1,,,,1,1,,,,1,1],[,1,,,1,,,1,,1,1,,1,,,,,,,1],[,,1,,1,,,1,,,,,,,,,,,1,1,,,,1],[,1,,,,,,,,,,,,,,,,,1,,,,,,1],[,,,,,,,,,,,,,,,,,,1],[,1,1,,,,,,,,,,,,,,,,1,,1,1],[,,,,,,,,,,,,1],,[,1,1,1,1,,,,1,1,,1,1,1,1,1,1,,1,1,1,1,,1,,1],[1,,,,1,,,,,,,,,,1],[1,,,,,,,,,1],,[,1,,,,1,,,,,,,,,,,,,,,,,,,,1]],[,[1,1,1,1,1,1,1,1,1,1,1,1,,1,,1,1,1,1,,,,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,,1,1,,1,1,1,,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,,1,1,1,1,1,1,1,1,1,1,,,1,1,1,,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]],[,[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,,1,,1,1,1,1],[1,1,1,1,,1,1,1,,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1],[,,,1,1,1,1,,1,,,,1,1,,,1,1,,1],[,1,1,,1,,,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,,,,,,,,,,,,,1],[1,1,1,,,,,1,1,1,,1,1,1,1,,,1,1,,1,1,,,,,1],[,1,,,,,,,1,1,,,1,1,1,,1,,,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,,,1,1,1,1,1,,1,1,1,1,1,1],[,1,,,,1,,,,1,,,1,,,,1,,,,,,,1,1],[,1,1,1,1,1,,,1,1,1,,1,1,1,1,,,1,1,1,1,,,,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,,1,,,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,,1,1,1,1,1,1],[1,1,1,,1,,,1,1,1,1,,1,1,1,1,,,,1,,1,,1,,,1],[1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,,,,1,,,,,,,,,1,1,,,,,,,,,1],,[,1,,1,,1,,1,,1,,1,1,1,1,1,,,1,,1,,1,,,,1],[,1,,,1,1,,1,1,1,,,1,1,1,1,1,,1,1,1,,1,,,1],[1,,,1,,,,1,1,1,,,,,1,1,,,,1,,1],[1,1,,1,1,1,1,,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1],[1,1,,,,,,,,1,,1,,,,,,,,1,,1],[,1,,,,1,,1,1,,,,1,1,,1,,,,1,1,1,,1],,[,1,,,,,,1,,,,,,,1],[,,,,,,,,1,,,,1,,1,,,,,,,,,,,,1]],[,[,1,1,,1,1,1,1,,1,1,1,,1,1,,1,1,,1,1,1,1,1,1,,1],[,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1],[,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,,1,1,1,1,1,1,1,1,1,,1,,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1],[,1,1,,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]],[,[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,,1,,1],[1,1,1,1,1,,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,,1,1,1,1,1,1,1,1,1,1],[,1,,,1,,,,,,,,1,,,,,,1,,,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,,1,,1,1,1,1,1,1,,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1],[,1,1,,1,,,,1,1,1,,1,1,1,1,,,1,1,1,1,,,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,,1],[1,1,,1,,1,,1,,1,1,1,1,1,1,1,,1,1,,,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1],[1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,,1,1],[,1,1,,,,,1,1,1,,,1,,1,1,,,,1,,1,,,1,1],[,,,,,,,1,,,,1,1,1,1,1,,1,,,,,,,,1],[1,1,1,1,,1,1,1,,1,,1,1,1,1,,1,,1,,1,1,,,1,,1],[,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,,,,1,1,,1,,1,1,1,,1,,1,1,,1,1,,1,,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,,,,,,,,1,,,,,1,,1],[,1,1,1,,1,,1,,1,,,,1,,1,,,1,,,,,,1,1],[,1,,,1,1,,1,,1,,1,1,1,1,1,,1,1,,,1,,,1],[1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,,1,,,,,1,,1,,1,,,,,,1,,1,,,,1,1]],[,[,1,,1,,,,,,,,,,,,,,,1,,,,1],[,,,,,,,,,1,,1,1,1,,1,,,1,,1,1],[1,1,,,,,,,1,,,,,,,1,,,,,,1],[,1,,,,,,,,,,1,,,,,,,,,1,1],,[,,,,,,,,,,,,,,,1,,,,1,,1],[,,1,1,,1,,1,,,,,,,,1,,,,,,1],[,,,,,,,,,,,,,,,,,,,,1,1],[,1,,,,,,,,,,,,,1],[1,,1,1,,,,1,,,,,,,,,1,,,1,,,1,1],[,1,1,,1,1,,1,1,1,1,1,1,1,1,1,,,1,1,,1,1,,1],[,1,,,1,1,,,,,,1,,1,,1,,,1,,1,1],[1,1,1,1,,1,,1,,1,,1,1,,1,1,1,1,1,,1,1,1,1,1],[,1,1,,,1,,1,,1,1,1,,,1,1,1,,1,1,1,1,,1,1],[,,,,1,,,1,,,,,,,1,,,,1,1],[,1,,,,,,,,,,1,,1,,1,,,,,1,,,,,1],,[1,1,,1,,1,,1,1,,,,,,1,1,,,1,1,1,1,1,1,1,1,1],[1,1,,1,,,,,,1,,,,,,1,1,,,,1,1,,,1],[,1,1,,1,1,,,,1,,1,1,1,1,1,,1,1,1,1,1,,1,1,1,1],[,1,1,,,1,,,,1,,,,1,1],[,,,,1],[,,,,,,,,,1,,,1],,[,,1,,1,,,,,,,,,1,,,,,,,,,,,,1],[,,,,,,,,,,,,,1]],[,[1,1,1,1,1,1,1,1,1,1,,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1],[,,1,1,,1,1,1,1,1,,,1,1,1,1,1,,1,1,1,1,1,,,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,,1,,,,,1],[,1,,1,,,,,,1,,,,,1,1,,,,,1,1],[,1,1,,1,1,1,1,1,1,1,1,1,1,,1,1,1,,1,,,1,,1,1,1],[,1,,,,1,,,,,,,1],[,1,,,1,,,1,,1,,1,1,,1,,,,,1,,1,,,,1,1],[,1,,,1,,,1,1,1,,1,1,1,1,1,,1,1,,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,,1,1,1,1,1,1,1,1,1],[,,,,,,,,,,,,,,,,,,,,1],[,1,1,1,,,,1,1,,,,,,1,1,1,,1,1,1,1],[1,1,1,1,1,1,1,1,1,,1,1,1,,1,1,1,1,1,1,1,1,1,1,,1,1],[,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,,1,1,1,1,1,,1,1,1,1],[,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,,,1,1,1,1,1,1,1,,1,,1,1,1,1,1,,1,1,,1,1,1,1,1],[,1,,,,1,,,,1,,1,1,1,1,1,1,1,1,1,1,1],[,1,,,,1,,,,,,,,1,,,,,,,,,,1],[,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1],[1,1,,1,1,1,,1,1,1,,,1,1,1,1,1,1,1,1,1,1,,1,,1],[1,1,,,,,,,1,1,,,,,1,1,1,1,1,,1,1,1,1,,1],[,1,1,1,1,1,1,1,,1,1,1,,1,,1,1,1,1,,1,1,,1,1,1,1],,[,1,1,,,,,1,,1,,,,1,1,1,,,1,,,,,1],[,,,,,,,,,,,,,1],[,,,,,1,,,,,,,,1,1,,,,,1,,1,,,1,1],[,,,,,,,,,,,,,,1]],[,[,1],,,,,,,,,,,,,,,,,,,,[1,1,1,1,1,,1,1,1,1,,1,1,1,1,,1,1,1,1,,,1,1,1,1,1],[,1,,1,,1,,,1,1,1,,1,1,1,1,1,,,1,,,,1,,1,1],[,1,,1,,1,,,1,,,,,1,,,,,,1,1],[,1,,1,,,,,1,,,,1,,1,1,1,1,1,1,1,1,,1],[,1,,,,,,,,,,,,,,,1]],[,[,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,,1,,,,,,,,,1,1,,,,1],[,,,,,,1],[,,1],[,1,1,,,1,,1,,1,1,,1,1,1,,,,1,1,1,,,,,1],,[,1,,,,1,,,,,,1,,,1,,,,1,1,,1],[,,,,,,,1,,,,,,,,,1],[,1,,,,1,1,,,,,,1,1,1,,,,1,,1,1],[,,,,,,,1,,1,,,,,,,,,,1],[,1,1,,,,,,1,1,,,,1,,,,,,,1,,,1],,[1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,,,1,,,1,,,,,1,,1,,1,,1,,,,,1],[1,1,1,1,1,1,1,1,,,,,1,1,,1,1,,1,,,1,,1],[,,,,,,,,,,,,,,1,,,,,,1],,[,,,,,,,,,1,,,,,,1,,,,,1],[,,1,,,,,,,1,,,1,1],[,,,1,,,,,1,,,,,1,,,,,,1,,,,1],[1,,1,1,,1,1,1,1,1,,1,,,,1,1,1,,,1,1,,,,1,1],,[1,1,,,,,,,,,,1,,1,,1,,,1],[,,,,1,,,,,,,,,,,,,,,,,,,1],[,,,,,,,,,,,,,,1,,,,,1,,1],[,,,,,,,,1]],[,[1,1,1,1,1,1,1,,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,,,1,1,1,1,1,,1,1,,1,1,1,1,,1,1,1,1,1,1],[1,1,1,1,,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1],[,,1,,,1,,,,,,,,1,,,,,,1,,,,1],[1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,,1,1,1,1],[1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,,1,1,,1,,,,1,1,1,1,1,1,,1,1,1,1,,1],[1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,,1,1,1,1,1,1,1,1,,1,1,1,,1,1,1,1,1,1,,1,1,1,1],[1,1,1,1,1,,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1],[1,,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1],[1,1,1,1,1,1,,1,1,1,1,1,1,,1,1,1,1,1,1,,1,1,1,1,1,1],[,,1,1,1,1,,1,,1,,1,1,1,1,1,1,1,1,1,1,1,1,,1,1],[1,1,,,,,,,1,,1,1,,1,1,1,,1,1,1,1,1],[1,1,1,1,,1,1,1,1,1,,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1],[1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1],[1,1,1,1,,1,,1,,1,1,1,1,1,,,,1,1,1,1,,1,1,1,1,1],[1,1,1,1,,1,,,,,,1,,1,,,,,1,1,,,,,1],[1,,1,1,,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,,1,1,,1,,1,,,,1,1,1,1,1,,,1,1,,1,,1],[,1,1,1,1,,,,,1,,1,1,1,1,1,,,1,1,,,,1,1,1],[,1,1,1,1,1,,1,,,,,1,,1,,1,,,1,,,1,1,,1]],[,[1,1,1,1,1,1,1,1,,1,1,1,1,,1,1,1,1,1,1,,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,,1,1,1,,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,,1,1],[1,1,1,1,1,1,1,1,1,1,,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,,,,,,,,,1,,,,,1,1,,,1,,1],[1,1,1,1,1,1,1,1,1,1,1,,,,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,,,1,1,1,1,,1,1,,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1],[1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1],[,1,,,,,,1,,1,1,,1,1,1,1,1,,,1,,1,,1],[1,1,1,,1,1,1,1,,,,1,1,1,1,,1,1,1,1,1,1,1,1,1,,1],[1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1],[1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,,1,1,1,1,1,1,1,1,1,,1,1,,1,1,1,1,1,,1,1,1,1,1,1],[,1,,1,,1,1,1,,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1],[,,1,,,,,,,,,,1,1,1,1,1,1,1,,1,1,,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,,,1,1,1,1,1,1,1,1,1,1,1],[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1],[,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,,1,1,1,1,1,1,1,1],[,1,,,1,1,,,,,,1,1,1,1,1,,,,1,1,1,,1,1,1],[1,1,1,1,1,1,1,1,1,,,,1,1,1,1,1,1,1,,1,1,,1,1,1],[,1,1,1,,1,,1,1,1,1,,,1,1,1,,1,1,1,1,1,,,1,1],[1,1,,,,1,,,1,1,1,,1,,1,,1,,1,1,1,1,1,,1,,1],[,1,,,,,,,1,,1,,1,1,1,1,,,,,,,,,1]],[,[,,,,,,,,,,,,,1,1,,,,1],[,1,,,,,,,,1,,,1,,,,,,1,,,1,,,,1],,[,1,,,,1,,1,,1,1,,1,1,,,,,,,,1],[,,,,,,,,,,,,,,,,,,,1],[,,,,,,,,,1],[1,1,1,,,1,,,,,,,,,1,1,,,,,,,,,,1],[,1,,,,,,,,,,,,,1],[,,,,,,,,,,,,,,,,,,,1,,,1],[,,,,,,,,,1],[1,1,,,,,,1,1,1,,1,1,,,,1,1,,1,,1,1,1,,1],[,1,1,1,,1,1,,,1,,1,1,1,1,,,,,,,1,,1],[,1,1,1,1,,,1,,1,,,,1,1,1,1,,1,1,,1],[,1,,,1,1,,1,,,,1,,1,1,,1,,1,,,1,,,1,,1],[,,,,,,,,,,,1],[,,,,,,,,,1,,,,,,,,,,,,,1],,[1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,,1,,1,1,1,1,1,1,1],[,1,,,,,,,1,1,,1,,,,,1,,,1,,1],[,1,,,,1,,,1,,,,,,,,1,,1,,,1],[,,,,,,,,,,,,,1,1,,,,1,,,1],[,,,,,1,,,1,,,,1],[,1],,[,1],[1,,,,,,,,,,,,,,1,,,,,1]],[,[,1,,,,1,1,1,1,1,1,,1,1,1,1,1,,1,1,,1,1,,,1],[,,1,,,,,,,,,1],,,[1,,,1,1,,,,,,,,1,1,,1,1,,1],,[,,,,,,,,,,,,,,,,,,1,,1],,[1,,,1,1,,1,1,,,,,1,,1,,,,,1,1,,1],,[,1,,,,,,,,1,1,1,1,1,,1,1,,,,1,1],[,,,,,,,,,,,,,,,,1,,,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,,,1,1,1,1,,1,1,1,1,1,1],[,,,,,,,,,,,1,,1,,,1],[1,,,,,,,,,,,,,,,,,,1,,1],,,[,1,,,,,,,,,,,,,,1,,,,1,1],[,,,,,,,,,1,,,1,,,,,,,,,,1],[,,,,,,,,,,,,,,,1],[,,,,,,,,,,,,,1,1,,,,,,1],,[,1]],[,[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,,,1,1,,1,1,1,1,1,1,,,1,1,1,1,1,,1,1],[,1,,,,,,,,1],[,,,,1,,,1,,,1,1,,,,,,,,,,1,,,,1],[,1,,1,1,,,1,1,1,,,,1,1,1,1,,1,1,1,1,,1],[,,,,,,,1],[,1,1,,,,,1,,1,,,,,,1,,,,,,1,,1,,1],[,1,,,,,,1,,,,1,,,,,,,,,,1],[,,1,1,,1,1,1,1,1,1,1,1,1,1,,,,1,,1,1,1,1,,1],[,1,,,,,,,,1],[,1,1,,1,,,,,,,,1,,,,,,1,,,1,,1,,1],[,1,,1,,1,,1,1,1,,1,1,1,,1,,,1,1,,1,1,1,1,1],[,1,1,1,1,1,,,1,1,,,,1,1,1,,,,1,1,,,1,1],[,,1,1,1,1,,1,,1,,1,,1,1,1,1,,,,,1,,1,,1],[1,1,1,1,1,1,1,1,,1,,1,,1,1,1,,,1,1,,,,1,,1],[,,,1],,[,1,1,,1,,,1,1,1,,1,1,1,1,1,1,,1,1,,1,1,1,1,1,1],[,1,,,,,,1,,1,,1,,,,,,,1,1,,1,1],[,,,,,,1,,1,1,,1,,1,,,,,,,,,,1],[,1,1,,1,,,,1,,,,1,1,1,,,,1,,1,1,1,,1,1],,[,1,1,,,,,,,,,,,,,1,,,1,,,,,1],[,1,,,,,,,,,,,,,,,,,,,,,,1],[,1,1,,,,,,,1,,,,1,,,,,1,,,,,,,1]],[,[,1,1,1,1,1,,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1],[,1,1,1,1,1,,1,,1,1,,,1,1,1,1,,1,,,,,1,1,1],[,,1,1,,1,,1,1,,,,1,1,1,1,,,1,,1,1,1,1,,1],[,1,,1,,,,,,,,1,,1,,1,,,,,,,,,,1],[,,1,,1,,,1,,,,,1,1,,,1,,1,1,1,1],[,1],[,1,1,,1,,1,1,,1,,,1,1,1,,,,1,,,1,,1],[1,1,,1,1,1,,,,,,,,,,,,,1,,1,1,1],[,1,1,,,,,,,1,,,1,,1,,1,,1,1,,,1,,,1],[,,1,,,,,,,,,,,,,,,,,,1],[,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,1,1,1,,1,,1,,,,,1,1,1,,,1,,1,,,,1],[,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,,1,,,1,1,1,,1,,1,1,1,,,1,1,1,1,,,,1,1],[,,,1,1,,,1,,1,,1,,1,1,1,1,,1,,,,,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,,,,,,,,,,,,,,,,,,,1],[,1,1,,1,1,,1,,1,,,,1,1,,,1,1,,1,1,,1],[,1,1,1,1,1,,,1,1,1,,1,1,1,1,1,1,1,1,,1,1,,,1],[,1,1,1,1,1,,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1,,1,1],[,1,1,,1,,,1,,,1,,1,1,1,1,1,,1,,1,1],[,,,,,1,,,,1,,,,,1,1,,,,1],[,1,,1,1,1,,1,,,1,1,1,,,1,,,1,,1,,,1],[,,1,,,,,,,,,1,,1,,,,,1,,1],[,1,1,,,,,,,,1,1,1,,,,,,,,1,,,,,1],[,,,,,,,,1,,,,,1,,,1]],[,[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,1,,1,1,,,1,1,1,1,1,1,1,1,,,,,,,,,1,1],[,,,,,,,,1,,,,1,,1,,1],[,1,,,1,1,,1,,,,1,,,,,,,,1],[,1,,1,,1,,,,1,1,,1,,1,,,,1,1,1,1,1,,,1],,[,1,,,,,,,,1,,,1,1,,,1,,1,1,,1,,1],[,1,,,1,,,,,,,,1,,,,,,,1],[1,1,,,,,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,,1,1,1],,[,1,,,,,,1,,1,,1,1,1,1,1,,,1,,1,1,,,,1],[,1,1,,,1,,1,,1,,,1,1,1,1,,,1,,,1,,,,1],[,1,1,1,1,1,,1,1,1,,1,1,1,1,1,1,1,1,1,1,,,,1,,1],[,1,,,1,1,,1,1,,,1,1,,1,1,,1,,1,,1],[1,,1,,,,,1,,1,,1,1,1,1,,,,,1,1,,,,1,1],[,1,1,,,,,1,1,,,1,,1,1,1,1,,,,,,,,,,1],,[,1,1,,,1,,,,1,,1,1,1,1,1,,,,1,,,,1,,1],[,,,1,1,,,1,,,,,1,,1,1,1,,1,1,,,,,,1],[,1,,,,,,,,,,,1,,,,1,,,,,,,1,,1],[,1,1,1,1,1,1,1,,1,1,1,1,1,1,,1,1,1,,1,1,,1,1,1,1],[,1,,,,,,,,,,,,,,,,,,,1],[,1,,,,,,1,,,,,1,,1,,,1,1,,1,1,,1],[,1,,,,,,1,,,,,1,1,,,,,,,,1,,,,1],[,,,,,,,,,,,,,,,,,,1,,,1,,,,,1],[,,,,,,,1,,,,1]],[,[1,1,1,1,1,1,1,1,1,1,1,1,1,1,,1,1,1,1,1,1,1,1,1,1,1,1],[,1,,1,,1,,,,,,,1,,,,,,,,1,,,1],[,1,,,,,,,1],[,,,,,,,,,,1],[,1,,,,,,1,1,,,,,,1],,[,1,1,,,,,,1,,,,,1,1,,,,1],[1,,1,,1,,,,,1,,,,,1,,,,,,,,,1,1],[,1,1,,,,,,,,,1,1,1,1,,,,1,,,,,1,,,1],,[,1,1,,1,,,1,1,,,1,,,1,1,1,,1,,1,1,1,,,,1],[,,,,,1,,,,,1,,,1,1,,,1,,1,,,,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,1,,,1,1,,1,,,,1,,,,,,,,1],[,,,1,,,,,1,,,,,1,,1,,1,1,1],[,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],[,,,,,1],[,1,,,,,,1,,,,,,,1,1,1,,,1],[,1,,,,,,,,,,1,1,1,,,,,1,,,1],[,,,,,1,,1,,,,,1,1,1,,1,1,,1,1,1,,,1,1],[1,1,,,,,,,1,,,,,1,1,,,,,,,,,,,1],,[,1],[,,,,,,,,,,,,,,,,,,,,,,,,1],[,,1,,,,,1,,,1,,,,1,,1],[,1,,,,,,,,,1]]];function xu(e){e=Array.isArray(e)?e:Du;var a=[];return e.forEach(function(e,t){var n=String.fromCharCode(t+96).replace("`","");Array.isArray(e)?a=a.concat(xu(e).map(function(e){return n+e})):a.push(n)}),a}var Eu,Fu=function(e){for(var t=Du;e.length<3;)e+="`";for(var n=0;n<=e.length-1;n++)if(!(t=t[e.charCodeAt(n)-96]))return!1;return!0},a=(Q(Au,b),Eu=ee(Au),ue(Au,[{key:"props",get:function(){return this._props}},{key:"attr",value:function(e){return null!=(e=this._attrs[e])?e:null}},{key:"hasAttr",value:function(e){return void 0!==this._attrs[e]}},{key:"attrNames",get:function(){return Object.keys(this._attrs)}}]),Au);function Au(e){var t,a,r;return le(this,Au),(t=Eu.call(this))._props=function(e){var t=null!=(t=e.nodeName)?t:ku[e.nodeType],n=null!=(n=null!=(n=e.nodeType)?n:Cu[e.nodeName])?n:1,a=(d("number"==typeof n,"nodeType has to be a number, got '".concat(n,"'")),d("string"==typeof t,"nodeName has to be a string, got '".concat(t,"'")),t=t.toLowerCase(),null);"input"===t&&(a=(e.type||e.attributes&&e.attributes.type||"").toLowerCase(),wu().includes(a)||(a="text"));e=p({},e,{nodeType:n,nodeName:t});a&&(e.type=a);return delete e.attributes,Object.freeze(e)}(e),t._attrs=(e=(e=e).attributes,a=void 0===e?{}:e,r={htmlFor:"for",className:"class"},Object.keys(a).reduce(function(e,t){var n=a[t];return d("object"!==te(n)||null===n,"expects attributes not to be an object, '".concat(t,"' was")),void 0!==n&&(e[r[t]||t]=null!==n?String(n):null),e},{})),t}var Cu={"#cdata-section":2,"#text":3,"#comment":8,"#document":9,"#document-fragment":11},ku={},Tu=(Object.keys(Cu).forEach(function(e){ku[Cu[e]]=e}),a),Nu=(fe(Oo={},{CssSelectorParser:function(){return Nu.CssSelectorParser},doT:function(){return Ru.default},emojiRegexText:function(){return Vi},memoize:function(){return Ou.default}}),he(Ae())),Ru=he(nn()),Ou=he(tn()),a=he(an()),Ae=he(rn());he(on()),Ru.default.templateSettings.strip=!1,"Promise"in window||a.default.polyfill(),"Uint32Array"in window||(window.Uint32Array=Ae.Uint32Array),window.Uint32Array&&("some"in window.Uint32Array.prototype||Object.defineProperty(window.Uint32Array.prototype,"some",{value:Array.prototype.some}),"reduce"in window.Uint32Array.prototype||Object.defineProperty(window.Uint32Array.prototype,"reduce",{value:Array.prototype.reduce}));var _u,Su=function(t,n){if(t=t||function(){},n=n||axe.log,!axe._audit)throw new Error("No audit configured");var a=axe.utils.queue(),r=[],e=(Object.keys(axe.plugins).forEach(function(e){a.defer(function(t){function n(e){r.push(e),t()}try{axe.plugins[e].cleanup(t,n)}catch(e){n(e)}})}),axe.utils.getFlattenedTree(document.body));axe.utils.querySelectorAll(e,"iframe, frame").forEach(function(n){a.defer(function(e,t){return axe.utils.sendCommandToFrame(n.actualNode,{command:"cleanup-plugin"},e,t)})}),a.then(function(e){0===r.length?t(e):n(r)}).catch(n)},Iu={};function Pu(e){return Iu.hasOwnProperty(e)}function Mu(e){return"string"==typeof e&&Iu[e]?Iu[e]:"function"==typeof e?e:_u}function Bu(e){var t=axe._audit;if(!t)throw new Error("No audit configured");if(e.axeVersion||e.ver){var n=e.axeVersion||e.ver;if(!/^\d+\.\d+\.\d+(-canary)?/.test(n))throw new Error("Invalid configured version ".concat(n));var a=h(n.split("-"),2),r=a[0],a=a[1],r=h(r.split(".").map(Number),3),o=r[0],i=r[1],r=r[2],l=h(axe.version.split("-"),2),s=l[0],l=l[1],s=h(s.split(".").map(Number),3),u=s[0],c=s[1],s=s[2];if(o!==u||c<i||c===i&&s<r||o===u&&i===c&&r===s&&a&&a!==l)throw new Error("Configured version ".concat(n," is not compatible with current axe version ").concat(axe.version))}if(e.reporter&&("function"==typeof e.reporter||Pu(e.reporter))&&(t.reporter=e.reporter),e.checks){if(!Array.isArray(e.checks))throw new TypeError("Checks property must be an array");e.checks.forEach(function(e){if(!e.id)throw new TypeError("Configured check ".concat(JSON.stringify(e)," is invalid. Checks must be an object with at least an id property"));t.addCheck(e)})}var d,p=[];if(e.rules){if(!Array.isArray(e.rules))throw new TypeError("Rules property must be an array");e.rules.forEach(function(e){if(!e.id)throw new TypeError("Configured rule ".concat(JSON.stringify(e)," is invalid. Rules must be an object with at least an id property"));p.push(e.id),t.addRule(e)})}if(e.disableOtherRules&&t.rules.forEach(function(e){!1===p.includes(e.id)&&(e.enabled=!1)}),void 0!==e.branding?t.setBranding(e.branding):t._constructHelpUrls(),e.tagExclude&&(t.tagExclude=e.tagExclude),e.locale&&t.applyLocale(e.locale),e.standards&&(d=e.standards,Object.keys(So).forEach(function(e){d[e]&&(So[e]=nr(So[e],d[e]))})),e.noHtml&&(t.noHtml=!0),e.allowedOrigins){if(!Array.isArray(e.allowedOrigins))throw new TypeError("Allowed origins property must be an array");if(e.allowedOrigins.includes("*"))throw new Error('"*" is not allowed. Use "'.concat(g.allOrigins,'" instead'));t.setAllowedOrigins(e.allowedOrigins)}}function Lu(e){var t=(e=e||[]).length?axe._audit.rules.filter(function(t){return!!e.filter(function(e){return-1!==t.tags.indexOf(e)}).length}):axe._audit.rules,n=axe._audit.data.rules||{};return t.map(function(e){var t=n[e.id]||{};return{ruleId:e.id,description:t.description,help:t.help,helpUrl:t.helpUrl,tags:e.tags,actIds:e.actIds}})}var ju={},qu=(fe(ju,{allowedAttr:function(){return qu},arialabelText:function(){return To},arialabelledbyText:function(){return ko},getAccessibleRefs:function(){return zu},getElementUnallowedRoles:function(){return Gu},getExplicitRole:function(){return c},getImplicitRole:function(){return li},getOwnedVirtual:function(){return mi},getRole:function(){return T},getRoleType:function(){return bl},getRolesByType:function(){return Ku},getRolesWithNameFromContents:function(){return Xu},implicitNodes:function(){return Zu},implicitRole:function(){return li},isAccessibleRef:function(){return ec},isAriaRoleAllowedOnElement:function(){return $u},isComboboxPopup:function(){return tc},isUnsupportedRole:function(){return Io},isValidRole:function(){return Po},label:function(){return ac},labelVirtual:function(){return Ji},lookupTable:function(){return Qu},namedFromContents:function(){return fi},requiredAttr:function(){return rc},requiredContext:function(){return oc},requiredOwned:function(){return ic},validateAttr:function(){return sc},validateAttrValue:function(){return lc}}),function(e){var e=A.ariaRoles[e],t=v(Bo());return e&&(e.allowedAttrs&&t.push.apply(t,v(e.allowedAttrs)),e.requiredAttrs)&&t.push.apply(t,v(e.requiredAttrs)),t}),Vu=/^idrefs?$/,zu=function(e){e=e.actualNode||e;var t=(t=E(e)).documentElement||t,n=w.get("idRefsByRoot",function(){return new WeakMap}),a=n.get(t);return a||(n.set(t,a={}),function e(t,n,a){if(t.hasAttribute){var r;"LABEL"===t.nodeName.toUpperCase()&&t.hasAttribute("for")&&(n[r=t.getAttribute("for")]=n[r]||[],n[r].push(t));for(var o=0;o<a.length;++o){var i=a[o];if(i=C(t.getAttribute(i)||""))for(var l=_(i),s=0;s<l.length;++s)n[l[s]]=n[l[s]]||[],n[l[s]].push(t)}}for(var u=0;u<t.childNodes.length;u++)1===t.childNodes[u].nodeType&&e(t.childNodes[u],n,a)}(t,a,Object.keys(A.ariaAttrs).filter(function(e){e=A.ariaAttrs[e].type;return Vu.test(e)}))),a[e.id]||[]},$u=function(e,t){var e=e instanceof b?e:D(e),n=li(e),e=ii(e);return Array.isArray(e.allowedRoles)?e.allowedRoles.includes(t):t!==n&&!!e.allowedRoles},Uu=["doc-backlink","doc-biblioentry","doc-biblioref","doc-cover","doc-endnote","doc-glossref","doc-noteref"],Hu={header:"banner",footer:"contentinfo"},Gu=function(e){var a,t,n,r=!(1<arguments.length&&void 0!==arguments[1])||arguments[1],o=e instanceof b?e:D(e);return Ms(o)?(n=o.props.nodeName,a=li(o)||Hu[n],n=[],((t=o)?(t.hasAttr("role")&&(t=_(t.attr("role").toLowerCase()),n=n.concat(t)),n.filter(function(e){return Po(e)})):n).filter(function(e){var t=o,n=a;return!(r&&e===n||(!Uu.includes(e)||bl(e)===n)&&$u(t,e))})):[]},Wu=function(t){return Object.keys(A.ariaRoles).filter(function(e){return A.ariaRoles[e].type===t})},Ku=function(e){return Wu(e)},Yu=function(){return w.get("ariaRolesNameFromContent",function(){return Object.keys(A.ariaRoles).filter(function(e){return A.ariaRoles[e].nameFromContent})})};function Ju(e){return null===e}function o(e){return null!==e}var Xu=function(){return Yu()},Qu=((tn={attributes:{"aria-activedescendant":{type:"idref",allowEmpty:!0,unsupported:!1},"aria-atomic":{type:"boolean",values:["true","false"],unsupported:!1},"aria-autocomplete":{type:"nmtoken",values:["inline","list","both","none"],unsupported:!1},"aria-busy":{type:"boolean",values:["true","false"],unsupported:!1},"aria-checked":{type:"nmtoken",values:["true","false","mixed","undefined"],unsupported:!1},"aria-colcount":{type:"int",unsupported:!1},"aria-colindex":{type:"int",unsupported:!1},"aria-colspan":{type:"int",unsupported:!1},"aria-controls":{type:"idrefs",allowEmpty:!0,unsupported:!1},"aria-current":{type:"nmtoken",allowEmpty:!0,values:["page","step","location","date","time","true","false"],unsupported:!1},"aria-describedby":{type:"idrefs",allowEmpty:!0,unsupported:!1},"aria-describedat":{unsupported:!0,unstandardized:!0},"aria-details":{type:"idref",allowEmpty:!0,unsupported:!1},"aria-disabled":{type:"boolean",values:["true","false"],unsupported:!1},"aria-dropeffect":{type:"nmtokens",values:["copy","move","reference","execute","popup","none"],unsupported:!1},"aria-errormessage":{type:"idref",allowEmpty:!0,unsupported:!1},"aria-expanded":{type:"nmtoken",values:["true","false","undefined"],unsupported:!1},"aria-flowto":{type:"idrefs",allowEmpty:!0,unsupported:!1},"aria-grabbed":{type:"nmtoken",values:["true","false","undefined"],unsupported:!1},"aria-haspopup":{type:"nmtoken",allowEmpty:!0,values:["true","false","menu","listbox","tree","grid","dialog"],unsupported:!1},"aria-hidden":{type:"boolean",values:["true","false"],unsupported:!1},"aria-invalid":{type:"nmtoken",allowEmpty:!0,values:["true","false","spelling","grammar"],unsupported:!1},"aria-keyshortcuts":{type:"string",allowEmpty:!0,unsupported:!1},"aria-label":{type:"string",allowEmpty:!0,unsupported:!1},"aria-labelledby":{type:"idrefs",allowEmpty:!0,unsupported:!1},"aria-level":{type:"int",unsupported:!1},"aria-live":{type:"nmtoken",values:["off","polite","assertive"],unsupported:!1},"aria-modal":{type:"boolean",values:["true","false"],unsupported:!1},"aria-multiline":{type:"boolean",values:["true","false"],unsupported:!1},"aria-multiselectable":{type:"boolean",values:["true","false"],unsupported:!1},"aria-orientation":{type:"nmtoken",values:["horizontal","vertical"],unsupported:!1},"aria-owns":{type:"idrefs",allowEmpty:!0,unsupported:!1},"aria-placeholder":{type:"string",allowEmpty:!0,unsupported:!1},"aria-posinset":{type:"int",unsupported:!1},"aria-pressed":{type:"nmtoken",values:["true","false","mixed","undefined"],unsupported:!1},"aria-readonly":{type:"boolean",values:["true","false"],unsupported:!1},"aria-relevant":{type:"nmtokens",values:["additions","removals","text","all"],unsupported:!1},"aria-required":{type:"boolean",values:["true","false"],unsupported:!1},"aria-roledescription":{type:"string",allowEmpty:!0,unsupported:!1},"aria-rowcount":{type:"int",unsupported:!1},"aria-rowindex":{type:"int",unsupported:!1},"aria-rowspan":{type:"int",unsupported:!1},"aria-selected":{type:"nmtoken",values:["true","false","undefined"],unsupported:!1},"aria-setsize":{type:"int",unsupported:!1},"aria-sort":{type:"nmtoken",values:["ascending","descending","other","none"],unsupported:!1},"aria-valuemax":{type:"decimal",unsupported:!1},"aria-valuemin":{type:"decimal",unsupported:!1},"aria-valuenow":{type:"decimal",unsupported:!1},"aria-valuetext":{type:"string",unsupported:!1}},globalAttributes:["aria-atomic","aria-busy","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-dropeffect","aria-flowto","aria-grabbed","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-live","aria-owns","aria-relevant","aria-roledescription"]}).role={alert:{type:"widget",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,unsupported:!1,allowedElements:["section"]},alertdialog:{type:"widget",attributes:{allowed:["aria-expanded","aria-modal","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,unsupported:!1,allowedElements:["dialog","section"]},application:{type:"landmark",attributes:{allowed:["aria-expanded","aria-errormessage","aria-activedescendant"]},owned:null,nameFrom:["author"],context:null,unsupported:!1,allowedElements:["article","audio","embed","iframe","object","section","svg","video"]},article:{type:"structure",attributes:{allowed:["aria-expanded","aria-posinset","aria-setsize","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,implicit:["article"],unsupported:!1},banner:{type:"landmark",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,implicit:["header"],unsupported:!1,allowedElements:["section"]},button:{type:"widget",attributes:{allowed:["aria-expanded","aria-pressed","aria-errormessage"]},owned:null,nameFrom:["author","contents"],context:null,implicit:["button",'input[type="button"]','input[type="image"]','input[type="reset"]','input[type="submit"]',"summary"],unsupported:!1,allowedElements:[{nodeName:"a",attributes:{href:o}}]},cell:{type:"structure",attributes:{allowed:["aria-colindex","aria-colspan","aria-rowindex","aria-rowspan","aria-errormessage"]},owned:null,nameFrom:["author","contents"],context:["row"],implicit:["td","th"],unsupported:!1},checkbox:{type:"widget",attributes:{allowed:["aria-checked","aria-required","aria-readonly","aria-errormessage"]},owned:null,nameFrom:["author","contents"],context:null,implicit:['input[type="checkbox"]'],unsupported:!1,allowedElements:["button"]},columnheader:{type:"structure",attributes:{allowed:["aria-colindex","aria-colspan","aria-expanded","aria-rowindex","aria-rowspan","aria-required","aria-readonly","aria-selected","aria-sort","aria-errormessage"]},owned:null,nameFrom:["author","contents"],context:["row"],implicit:["th"],unsupported:!1},combobox:{type:"composite",attributes:{allowed:["aria-autocomplete","aria-required","aria-activedescendant","aria-orientation","aria-errormessage"],required:["aria-expanded"]},owned:{all:["listbox","tree","grid","dialog","textbox"]},nameFrom:["author"],context:null,unsupported:!1,allowedElements:[{nodeName:"input",properties:{type:["text","search","tel","url","email"]}}]},command:{nameFrom:["author"],type:"abstract",unsupported:!1},complementary:{type:"landmark",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,implicit:["aside"],unsupported:!1,allowedElements:["section"]},composite:{nameFrom:["author"],type:"abstract",unsupported:!1},contentinfo:{type:"landmark",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,implicit:["footer"],unsupported:!1,allowedElements:["section"]},definition:{type:"structure",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,implicit:["dd","dfn"],unsupported:!1},dialog:{type:"widget",attributes:{allowed:["aria-expanded","aria-modal","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,implicit:["dialog"],unsupported:!1,allowedElements:["section"]},directory:{type:"structure",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author","contents"],context:null,unsupported:!1,allowedElements:["ol","ul"]},document:{type:"structure",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,implicit:["body"],unsupported:!1,allowedElements:["article","embed","iframe","object","section","svg"]},"doc-abstract":{type:"section",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,unsupported:!1,allowedElements:["section"]},"doc-acknowledgments":{type:"landmark",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,unsupported:!1,allowedElements:["section"]},"doc-afterword":{type:"landmark",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,unsupported:!1,allowedElements:["section"]},"doc-appendix":{type:"landmark",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,unsupported:!1,allowedElements:["section"]},"doc-backlink":{type:"link",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author","contents"],context:null,unsupported:!1,allowedElements:[{nodeName:"a",attributes:{href:o}}]},"doc-biblioentry":{type:"listitem",attributes:{allowed:["aria-expanded","aria-level","aria-posinset","aria-setsize","aria-errormessage"]},owned:null,nameFrom:["author"],context:["doc-bibliography"],unsupported:!1,allowedElements:["li"]},"doc-bibliography":{type:"landmark",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:{one:["doc-biblioentry"]},nameFrom:["author"],context:null,unsupported:!1,allowedElements:["section"]},"doc-biblioref":{type:"link",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author","contents"],context:null,unsupported:!1,allowedElements:[{nodeName:"a",attributes:{href:o}}]},"doc-chapter":{type:"landmark",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,namefrom:["author"],context:null,unsupported:!1,allowedElements:["section"]},"doc-colophon":{type:"section",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,namefrom:["author"],context:null,unsupported:!1,allowedElements:["section"]},"doc-conclusion":{type:"landmark",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,namefrom:["author"],context:null,unsupported:!1,allowedElements:["section"]},"doc-cover":{type:"img",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,namefrom:["author"],context:null,unsupported:!1},"doc-credit":{type:"section",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,namefrom:["author"],context:null,unsupported:!1,allowedElements:["section"]},"doc-credits":{type:"landmark",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,namefrom:["author"],context:null,unsupported:!1,allowedElements:["section"]},"doc-dedication":{type:"section",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,namefrom:["author"],context:null,unsupported:!1,allowedElements:["section"]},"doc-endnote":{type:"listitem",attributes:{allowed:["aria-expanded","aria-level","aria-posinset","aria-setsize","aria-errormessage"]},owned:null,namefrom:["author"],context:["doc-endnotes"],unsupported:!1,allowedElements:["li"]},"doc-endnotes":{type:"landmark",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:{one:["doc-endnote"]},namefrom:["author"],context:null,unsupported:!1,allowedElements:["section"]},"doc-epigraph":{type:"section",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,namefrom:["author"],context:null,unsupported:!1},"doc-epilogue":{type:"landmark",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,namefrom:["author"],context:null,unsupported:!1,allowedElements:["section"]},"doc-errata":{type:"landmark",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,namefrom:["author"],context:null,unsupported:!1,allowedElements:["section"]},"doc-example":{type:"section",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,namefrom:["author"],context:null,unsupported:!1,allowedElements:["aside","section"]},"doc-footnote":{type:"section",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,namefrom:["author"],context:null,unsupported:!1,allowedElements:["aside","footer","header"]},"doc-foreword":{type:"landmark",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,namefrom:["author"],context:null,unsupported:!1,allowedElements:["section"]},"doc-glossary":{type:"landmark",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:["term","definition"],namefrom:["author"],context:null,unsupported:!1,allowedElements:["dl"]},"doc-glossref":{type:"link",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,namefrom:["author","contents"],context:null,unsupported:!1,allowedElements:[{nodeName:"a",attributes:{href:o}}]},"doc-index":{type:"navigation",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,namefrom:["author"],context:null,unsupported:!1,allowedElements:["nav","section"]},"doc-introduction":{type:"landmark",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,namefrom:["author"],context:null,unsupported:!1,allowedElements:["section"]},"doc-noteref":{type:"link",attributes:{allowed:["aria-expanded"]},owned:null,namefrom:["author","contents"],context:null,unsupported:!1,allowedElements:[{nodeName:"a",attributes:{href:o}}]},"doc-notice":{type:"note",attributes:{allowed:["aria-expanded"]},owned:null,namefrom:["author"],context:null,unsupported:!1,allowedElements:["section"]},"doc-pagebreak":{type:"separator",attributes:{allowed:["aria-expanded"]},owned:null,namefrom:["author"],context:null,unsupported:!1,allowedElements:["hr"]},"doc-pagelist":{type:"navigation",attributes:{allowed:["aria-expanded"]},owned:null,namefrom:["author"],context:null,unsupported:!1,allowedElements:["nav","section"]},"doc-part":{type:"landmark",attributes:{allowed:["aria-expanded"]},owned:null,namefrom:["author"],context:null,unsupported:!1,allowedElements:["section"]},"doc-preface":{type:"landmark",attributes:{allowed:["aria-expanded"]},owned:null,namefrom:["author"],context:null,unsupported:!1,allowedElements:["section"]},"doc-prologue":{type:"landmark",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,namefrom:["author"],context:null,unsupported:!1,allowedElements:["section"]},"doc-pullquote":{type:"none",attributes:{allowed:["aria-expanded"]},owned:null,namefrom:["author"],context:null,unsupported:!1,allowedElements:["aside","section"]},"doc-qna":{type:"section",attributes:{allowed:["aria-expanded"]},owned:null,namefrom:["author"],context:null,unsupported:!1,allowedElements:["section"]},"doc-subtitle":{type:"sectionhead",attributes:{allowed:["aria-expanded"]},owned:null,namefrom:["author"],context:null,unsupported:!1,allowedElements:{nodeName:["h1","h2","h3","h4","h5","h6"]}},"doc-tip":{type:"note",attributes:{allowed:["aria-expanded"]},owned:null,namefrom:["author"],context:null,unsupported:!1,allowedElements:["aside"]},"doc-toc":{type:"navigation",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,namefrom:["author"],context:null,unsupported:!1,allowedElements:["nav","section"]},feed:{type:"structure",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:{one:["article"]},nameFrom:["author"],context:null,unsupported:!1,allowedElements:["article","aside","section"]},figure:{type:"structure",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author","contents"],context:null,implicit:["figure"],unsupported:!1},form:{type:"landmark",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,implicit:["form"],unsupported:!1},grid:{type:"composite",attributes:{allowed:["aria-activedescendant","aria-expanded","aria-colcount","aria-level","aria-multiselectable","aria-readonly","aria-rowcount","aria-errormessage"]},owned:{one:["rowgroup","row"]},nameFrom:["author"],context:null,implicit:["table"],unsupported:!1},gridcell:{type:"widget",attributes:{allowed:["aria-colindex","aria-colspan","aria-expanded","aria-rowindex","aria-rowspan","aria-selected","aria-readonly","aria-required","aria-errormessage"]},owned:null,nameFrom:["author","contents"],context:["row"],implicit:["td","th"],unsupported:!1},group:{type:"structure",attributes:{allowed:["aria-activedescendant","aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,implicit:["details","optgroup"],unsupported:!1,allowedElements:["dl","figcaption","fieldset","figure","footer","header","ol","ul"]},heading:{type:"structure",attributes:{required:["aria-level"],allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author","contents"],context:null,implicit:["h1","h2","h3","h4","h5","h6"],unsupported:!1},img:{type:"structure",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,implicit:["img"],unsupported:!1,allowedElements:["embed","iframe","object","svg"]},input:{nameFrom:["author"],type:"abstract",unsupported:!1},landmark:{nameFrom:["author"],type:"abstract",unsupported:!1},link:{type:"widget",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author","contents"],context:null,implicit:["a[href]","area[href]"],unsupported:!1,allowedElements:["button",{nodeName:"input",properties:{type:["image","button"]}}]},list:{type:"structure",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:{all:["listitem"]},nameFrom:["author"],context:null,implicit:["ol","ul","dl"],unsupported:!1},listbox:{type:"composite",attributes:{allowed:["aria-activedescendant","aria-multiselectable","aria-readonly","aria-required","aria-expanded","aria-orientation","aria-errormessage"]},owned:{all:["option"]},nameFrom:["author"],context:null,implicit:["select"],unsupported:!1,allowedElements:["ol","ul"]},listitem:{type:"structure",attributes:{allowed:["aria-level","aria-posinset","aria-setsize","aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author","contents"],context:["list"],implicit:["li","dt"],unsupported:!1},log:{type:"widget",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,unsupported:!1,allowedElements:["section"]},main:{type:"landmark",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,implicit:["main"],unsupported:!1,allowedElements:["article","section"]},marquee:{type:"widget",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,unsupported:!1,allowedElements:["section"]},math:{type:"structure",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,implicit:["math"],unsupported:!1},menu:{type:"composite",attributes:{allowed:["aria-activedescendant","aria-expanded","aria-orientation","aria-errormessage"]},owned:{one:["menuitem","menuitemradio","menuitemcheckbox"]},nameFrom:["author"],context:null,implicit:['menu[type="context"]'],unsupported:!1,allowedElements:["ol","ul"]},menubar:{type:"composite",attributes:{allowed:["aria-activedescendant","aria-expanded","aria-orientation","aria-errormessage"]},owned:{one:["menuitem","menuitemradio","menuitemcheckbox"]},nameFrom:["author"],context:null,unsupported:!1,allowedElements:["ol","ul"]},menuitem:{type:"widget",attributes:{allowed:["aria-posinset","aria-setsize","aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author","contents"],context:["menu","menubar"],implicit:['menuitem[type="command"]'],unsupported:!1,allowedElements:["button","li",{nodeName:"iput",properties:{type:["image","button"]}},{nodeName:"a",attributes:{href:o}}]},menuitemcheckbox:{type:"widget",attributes:{allowed:["aria-checked","aria-posinset","aria-setsize","aria-errormessage"]},owned:null,nameFrom:["author","contents"],context:["menu","menubar"],implicit:['menuitem[type="checkbox"]'],unsupported:!1,allowedElements:[{nodeName:["button","li"]},{nodeName:"input",properties:{type:["checkbox","image","button"]}},{nodeName:"a",attributes:{href:o}}]},menuitemradio:{type:"widget",attributes:{allowed:["aria-checked","aria-selected","aria-posinset","aria-setsize","aria-errormessage"]},owned:null,nameFrom:["author","contents"],context:["menu","menubar"],implicit:['menuitem[type="radio"]'],unsupported:!1,allowedElements:[{nodeName:["button","li"]},{nodeName:"input",properties:{type:["image","button","radio"]}},{nodeName:"a",attributes:{href:o}}]},navigation:{type:"landmark",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,implicit:["nav"],unsupported:!1,allowedElements:["section"]},none:{type:"structure",attributes:null,owned:null,nameFrom:["author"],context:null,unsupported:!1,allowedElements:[{nodeName:["article","aside","dl","embed","figcaption","fieldset","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hr","iframe","li","ol","section","ul"]},{nodeName:"img",attributes:{alt:o}}]},note:{type:"structure",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,unsupported:!1,allowedElements:["aside"]},option:{type:"widget",attributes:{allowed:["aria-selected","aria-posinset","aria-setsize","aria-checked","aria-errormessage"]},owned:null,nameFrom:["author","contents"],context:["listbox"],implicit:["option"],unsupported:!1,allowedElements:[{nodeName:["button","li"]},{nodeName:"input",properties:{type:["checkbox","button"]}},{nodeName:"a",attributes:{href:o}}]},presentation:{type:"structure",attributes:null,owned:null,nameFrom:["author"],context:null,unsupported:!1,allowedElements:[{nodeName:["article","aside","dl","embed","figcaption","fieldset","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hr","iframe","li","ol","section","ul"]},{nodeName:"img",attributes:{alt:o}}]},progressbar:{type:"widget",attributes:{allowed:["aria-valuetext","aria-valuenow","aria-valuemax","aria-valuemin","aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,implicit:["progress"],unsupported:!1},radio:{type:"widget",attributes:{allowed:["aria-selected","aria-posinset","aria-setsize","aria-required","aria-errormessage","aria-checked"]},owned:null,nameFrom:["author","contents"],context:null,implicit:['input[type="radio"]'],unsupported:!1,allowedElements:[{nodeName:["button","li"]},{nodeName:"input",properties:{type:["image","button"]}}]},radiogroup:{type:"composite",attributes:{allowed:["aria-activedescendant","aria-required","aria-expanded","aria-readonly","aria-errormessage","aria-orientation"]},owned:{all:["radio"]},nameFrom:["author"],context:null,unsupported:!1,allowedElements:{nodeName:["ol","ul","fieldset"]}},range:{nameFrom:["author"],type:"abstract",unsupported:!1},region:{type:"landmark",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,implicit:["section[aria-label]","section[aria-labelledby]","section[title]"],unsupported:!1,allowedElements:{nodeName:["article","aside"]}},roletype:{type:"abstract",unsupported:!1},row:{type:"structure",attributes:{allowed:["aria-activedescendant","aria-colindex","aria-expanded","aria-level","aria-selected","aria-rowindex","aria-errormessage"]},owned:{one:["cell","columnheader","rowheader","gridcell"]},nameFrom:["author","contents"],context:["rowgroup","grid","treegrid","table"],implicit:["tr"],unsupported:!1},rowgroup:{type:"structure",attributes:{allowed:["aria-activedescendant","aria-expanded","aria-errormessage"]},owned:{all:["row"]},nameFrom:["author","contents"],context:["grid","table","treegrid"],implicit:["tbody","thead","tfoot"],unsupported:!1},rowheader:{type:"structure",attributes:{allowed:["aria-colindex","aria-colspan","aria-expanded","aria-rowindex","aria-rowspan","aria-required","aria-readonly","aria-selected","aria-sort","aria-errormessage"]},owned:null,nameFrom:["author","contents"],context:["row"],implicit:["th"],unsupported:!1},scrollbar:{type:"widget",attributes:{required:["aria-controls","aria-valuenow"],allowed:["aria-valuetext","aria-orientation","aria-errormessage","aria-valuemax","aria-valuemin"]},owned:null,nameFrom:["author"],context:null,unsupported:!1},search:{type:"landmark",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,unsupported:!1,allowedElements:{nodeName:["aside","form","section"]}},searchbox:{type:"widget",attributes:{allowed:["aria-activedescendant","aria-autocomplete","aria-multiline","aria-readonly","aria-required","aria-placeholder","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,implicit:['input[type="search"]'],unsupported:!1,allowedElements:{nodeName:"input",properties:{type:"text"}}},section:{nameFrom:["author","contents"],type:"abstract",unsupported:!1},sectionhead:{nameFrom:["author","contents"],type:"abstract",unsupported:!1},select:{nameFrom:["author"],type:"abstract",unsupported:!1},separator:{type:"structure",attributes:{allowed:["aria-expanded","aria-orientation","aria-valuenow","aria-valuemax","aria-valuemin","aria-valuetext","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,implicit:["hr"],unsupported:!1,allowedElements:["li"]},slider:{type:"widget",attributes:{allowed:["aria-valuetext","aria-orientation","aria-readonly","aria-errormessage","aria-valuemax","aria-valuemin"],required:["aria-valuenow"]},owned:null,nameFrom:["author"],context:null,implicit:['input[type="range"]'],unsupported:!1},spinbutton:{type:"widget",attributes:{allowed:["aria-valuetext","aria-required","aria-readonly","aria-errormessage","aria-valuemax","aria-valuemin"],required:["aria-valuenow"]},owned:null,nameFrom:["author"],context:null,implicit:['input[type="number"]'],unsupported:!1,allowedElements:{nodeName:"input",properties:{type:["text","tel"]}}},status:{type:"widget",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,implicit:["output"],unsupported:!1,allowedElements:["section"]},structure:{type:"abstract",unsupported:!1},switch:{type:"widget",attributes:{allowed:["aria-errormessage"],required:["aria-checked"]},owned:null,nameFrom:["author","contents"],context:null,unsupported:!1,allowedElements:["button",{nodeName:"input",properties:{type:["checkbox","image","button"]}},{nodeName:"a",attributes:{href:o}}]},tab:{type:"widget",attributes:{allowed:["aria-selected","aria-expanded","aria-setsize","aria-posinset","aria-errormessage"]},owned:null,nameFrom:["author","contents"],context:["tablist"],unsupported:!1,allowedElements:[{nodeName:["button","h1","h2","h3","h4","h5","h6","li"]},{nodeName:"input",properties:{type:"button"}},{nodeName:"a",attributes:{href:o}}]},table:{type:"structure",attributes:{allowed:["aria-colcount","aria-rowcount","aria-errormessage"]},owned:{one:["rowgroup","row"]},nameFrom:["author","contents"],context:null,implicit:["table"],unsupported:!1},tablist:{type:"composite",attributes:{allowed:["aria-activedescendant","aria-expanded","aria-level","aria-multiselectable","aria-orientation","aria-errormessage"]},owned:{all:["tab"]},nameFrom:["author"],context:null,unsupported:!1,allowedElements:["ol","ul"]},tabpanel:{type:"widget",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,unsupported:!1,allowedElements:["section"]},term:{type:"structure",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author","contents"],context:null,implicit:["dt"],unsupported:!1},textbox:{type:"widget",attributes:{allowed:["aria-activedescendant","aria-autocomplete","aria-multiline","aria-readonly","aria-required","aria-placeholder","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,implicit:['input[type="text"]','input[type="email"]','input[type="password"]','input[type="tel"]','input[type="url"]',"input:not([type])","textarea"],unsupported:!1},timer:{type:"widget",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,unsupported:!1},toolbar:{type:"structure",attributes:{allowed:["aria-activedescendant","aria-expanded","aria-orientation","aria-errormessage"]},owned:null,nameFrom:["author"],context:null,implicit:['menu[type="toolbar"]'],unsupported:!1,allowedElements:["ol","ul"]},tooltip:{type:"structure",attributes:{allowed:["aria-expanded","aria-errormessage"]},owned:null,nameFrom:["author","contents"],context:null,unsupported:!1},tree:{type:"composite",attributes:{allowed:["aria-activedescendant","aria-multiselectable","aria-required","aria-expanded","aria-orientation","aria-errormessage"]},owned:{all:["treeitem"]},nameFrom:["author"],context:null,unsupported:!1,allowedElements:["ol","ul"]},treegrid:{type:"composite",attributes:{allowed:["aria-activedescendant","aria-colcount","aria-expanded","aria-level","aria-multiselectable","aria-readonly","aria-required","aria-rowcount","aria-orientation","aria-errormessage"]},owned:{one:["rowgroup","row"]},nameFrom:["author"],context:null,unsupported:!1},treeitem:{type:"widget",attributes:{allowed:["aria-checked","aria-selected","aria-expanded","aria-level","aria-posinset","aria-setsize","aria-errormessage"]},owned:null,nameFrom:["author","contents"],context:["group","tree"],unsupported:!1,allowedElements:["li",{nodeName:"a",attributes:{href:o}}]},widget:{type:"abstract",unsupported:!1},window:{nameFrom:["author"],type:"abstract",unsupported:!1}},tn.implicitHtmlRole=Go,tn.elementsAllowedNoRole=[{nodeName:["base","body","caption","col","colgroup","datalist","dd","details","dt","head","html","keygen","label","legend","main","map","math","meta","meter","noscript","optgroup","param","picture","progress","script","source","style","template","textarea","title","track"]},{nodeName:"area",attributes:{href:o}},{nodeName:"input",properties:{type:["color","data","datatime","file","hidden","month","number","password","range","reset","submit","time","week"]}},{nodeName:"link",attributes:{href:o}},{nodeName:"menu",attributes:{type:"context"}},{nodeName:"menuitem",attributes:{type:["command","checkbox","radio"]}},{nodeName:"select",condition:function(e){return e instanceof axe.AbstractVirtualNode||(e=axe.utils.getNodeFromTree(e)),1<Number(e.attr("size"))},properties:{multiple:!0}},{nodeName:["clippath","cursor","defs","desc","feblend","fecolormatrix","fecomponenttransfer","fecomposite","feconvolvematrix","fediffuselighting","fedisplacementmap","fedistantlight","fedropshadow","feflood","fefunca","fefuncb","fefuncg","fefuncr","fegaussianblur","feimage","femerge","femergenode","femorphology","feoffset","fepointlight","fespecularlighting","fespotlight","fetile","feturbulence","filter","hatch","hatchpath","lineargradient","marker","mask","meshgradient","meshpatch","meshrow","metadata","mpath","pattern","radialgradient","solidcolor","stop","switch","view"]}],tn.elementsAllowedAnyRole=[{nodeName:"a",attributes:{href:Ju}},{nodeName:"img",attributes:{alt:Ju}},{nodeName:["abbr","address","canvas","div","p","pre","blockquote","ins","del","output","span","table","tbody","thead","tfoot","td","em","strong","small","s","cite","q","dfn","abbr","time","code","var","samp","kbd","sub","sup","i","b","u","mark","ruby","rt","rp","bdi","bdo","br","wbr","th","tr"]}],tn.evaluateRoleForElement={A:function(e){var t=e.node,e=e.out;return"http://www.w3.org/2000/svg"===t.namespaceURI||!t.href.length||e},AREA:function(e){return!e.node.href},BUTTON:function(e){var t=e.node,n=e.role,e=e.out;return"menu"===t.getAttribute("type")?"menuitem"===n:e},IMG:function(e){var t=e.node,n=e.role,a=e.out;switch(t.alt){case null:return a;case"":return"presentation"===n||"none"===n;default:return"presentation"!==n&&"none"!==n}},INPUT:function(e){var t=e.node,n=e.role,a=e.out;switch(t.type){case"button":case"image":return a;case"checkbox":return"button"===n&&t.hasAttribute("aria-pressed")?!0:a;case"radio":return"menuitemradio"===n;case"text":return"combobox"===n||"searchbox"===n||"spinbutton"===n;case"tel":return"combobox"===n||"spinbutton"===n;case"url":case"search":case"email":return"combobox"===n;default:return!1}},LI:function(e){var t=e.node,e=e.out;return!axe.utils.matchesSelector(t,"ol li, ul li")||e},MENU:function(e){return"context"!==e.node.getAttribute("type")},OPTION:function(e){e=e.node;return!axe.utils.matchesSelector(e,"select > option, datalist > option, optgroup > option")},SELECT:function(e){var t=e.node,e=e.role;return!t.multiple&&t.size<=1&&"menu"===e},SVG:function(e){var t=e.node,e=e.out;return!(!t.parentNode||"http://www.w3.org/2000/svg"!==t.parentNode.namespaceURI)||e}},tn.rolesOfType={widget:["button","checkbox","dialog","gridcell","link","log","marquee","menuitem","menuitemcheckbox","menuitemradio","option","progressbar","radio","scrollbar","searchbox","slider","spinbutton","status","switch","tab","tabpanel","textbox","timer","tooltip","tree","treeitem"]},tn),Zu=function(e){var t=null,e=Qu.role[e];return t=e&&e.implicit?ea(e.implicit):t},ec=function(e){return!!zu(e).length};function tc(e){var t=(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).popupRoles,n=T(e);if(!(t=null!=t?t:No["aria-haspopup"].values).includes(n))return!1;t=function(e){for(;e=e.parent;)if(null!==T(e,{noPresentational:!0}))return e;return null}(e);if(nc(t))return!0;n=e.props.id;if(!n)return!1;if(e.actualNode)return t=lr(e.actualNode).querySelectorAll('[aria-owns~="'.concat(n,'"][role~="combobox"]:not(select),\n     [aria-controls~="').concat(n,'"][role~="combobox"]:not(select)')),Array.from(t).some(nc);throw new Error("Unable to determine combobox popup without an actualNode")}var nc=function(e){return e&&"combobox"===T(e)},ac=function(e){return e=D(e),Ji(e)},rc=function(e){return(e=A.ariaRoles[e])&&Array.isArray(e.requiredAttrs)?v(e.requiredAttrs):[]},oc=function(e){return(e=A.ariaRoles[e])&&Array.isArray(e.requiredContext)?v(e.requiredContext):null},ic=function(e){return(e=A.ariaRoles[e])&&Array.isArray(e.requiredOwned)?v(e.requiredOwned):null},lc=function(e,t){var n,a=(e=e instanceof b?e:D(e)).attr(t),r=A.ariaAttrs[t];if(!r)return!0;if(r.allowEmpty&&(!a||""===a.trim()))return!0;switch(r.type){case"boolean":return["true","false"].includes(a.toLowerCase());case"nmtoken":return"string"==typeof a&&r.values.includes(a.toLowerCase());case"nmtokens":return(n=_(a)).reduce(function(e,t){return e&&r.values.includes(t)},0!==n.length);case"idref":try{var o=E(e.actualNode);return!(!a||!o.getElementById(a))}catch(e){throw new TypeError("Cannot resolve id references for partial DOM")}case"idrefs":return Ao(e,t).some(function(e){return!!e});case"string":return""!==a.trim();case"decimal":return!(!(n=a.match(/^[-+]?([0-9]*)\.?([0-9]*)$/))||!n[1]&&!n[2]);case"int":o=void 0!==r.minValue?r.minValue:-1/0;return/^[-+]?[0-9]+$/.test(a)&&parseInt(a)>=o}},sc=function(e){return!!A.ariaAttrs[e]};function uc(e,t,n){return 0<(n=_(n.attr("role")).filter(function(e){return"abstract"===bl(e)})).length&&(this.data(n),!0)}function cc(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},n=2<arguments.length?arguments[2]:void 0,a=void 0===(a=t.allowImplicit)||a,t=void 0===(t=t.ignoredTags)?[]:t,r=n.props.nodeName;return!!t.map(function(e){return e.toLowerCase()}).includes(r)||!(t=Gu(n,a)).length||(this.data(t),!N(n)&&void 0)}function dc(e,t,n){t=Array.isArray(t)?t:[];var a=n.attr("aria-errormessage"),r=n.hasAttr("aria-errormessage"),o=n.attr("aria-invalid");return!n.hasAttr("aria-invalid")||"false"===o||-1!==t.indexOf(a)||!r||(this.data(_(a)),function(t){if(""===t.trim())return A.ariaAttrs["aria-errormessage"].allowEmpty;var e;try{e=t&&Ao(n,"aria-errormessage")[0]}catch(e){return void this.data({messageKey:"idrefs",values:_(t)})}return e?N(e)?"alert"===e.getAttribute("role")||"assertive"===e.getAttribute("aria-live")||"polite"===e.getAttribute("aria-live")||-1<_(n.attr("aria-describedby")).indexOf(t):(this.data({messageKey:"hidden",values:_(t)}),!1):void 0}.call(this,a))}function pc(e,t,n){return"true"!==n.attr("aria-hidden")}function fc(e,t,n){if(n=n.attr("aria-level"),!(6<(n=parseInt(n,10))))return!0}var mc={};function hc(e,t,n,a){var r=c(e);if(!(n=n||oc(r)))return null;for(var o=n.includes("group"),i=a?e:e.parent;i;){var l=T(i,{noPresentational:!0});if(l){if("group"!==l||!o)return n.includes(l)?null:n;t.includes(r)&&n.push(r),n=n.filter(function(e){return"group"!==e})}i=i.parent}return n}function gc(e,t,n){var a=t&&Array.isArray(t.ownGroupRoles)?t.ownGroupRoles:[],r=hc(n,a);if(!r)return!0;var o=function(e){for(var t,n=[];e;)e.getAttribute("id")&&(t=y(e.getAttribute("id")),t=E(e).querySelector("[aria-owns~=".concat(t,"]")))&&n.push(t),e=e.parentElement;return n.length?n:null}(e);if(o)for(var i=0,l=o.length;i<l;i++)if(!(r=hc(D(o[i]),a,r,!0)))return!0;return this.data(r),!1}function bc(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},n=T(2<arguments.length?arguments[2]:void 0);return!!(t.supportedRoles||[]).includes(n)||!(!n||"presentation"===n||"none"===n)&&void 0}function yc(n,e,t){return!!(t=t.attrNames.filter(function(e){var t=A.ariaAttrs[e];return!!sc(e)&&(e=t.unsupported,"object"!==te(e)?!!e:!oi(n,e.exceptions))})).length&&(this.data(t),!0)}function vc(e,t,n){t=Array.isArray(t.value)?t.value:[];var a=[],r=/^aria-/;return n.attrNames.forEach(function(e){-1===t.indexOf(e)&&r.test(e)&&!sc(e)&&a.push(e)}),!a.length||(this.data(a),!1)}function wc(e,t,n){var a=_(n.attr("role"));return!(a.length<=1)&&(a=a,!(!li(n)&&2===a.length&&a.includes("none")&&a.includes("presentation"))||void 0)}function Dc(e,t,n){var a=Bo().filter(function(e){return n.hasAttr(e)});return this.data(a),0<a.length}function xc(e){return null!==(e=e.getAttribute("role"))&&("widget"===(e=bl(e))||"composite"===e)}function Ec(e,t,n){return!!(n=_(n.attr("role"))).every(function(e){return!Po(e,{allowAbstract:!0})})&&(this.data(n),!0)}function Fc(e,t,n){return k(n)}function Ac(e,t,n){var a,r,o=T(n,{noImplicit:!0});this.data(o);try{a=C(bi(n)).toLowerCase(),r=C(l(n)).toLowerCase()}catch(e){return}return!(!r&&!a||(r||!a)&&r.includes(a))&&void 0}function Cc(e,t,n){var n=T(n,{dpub:!0,fallback:!0}),a=Io(n);return a&&this.data(n),a}fe(mc,{getAriaRolesByType:function(){return Wu},getAriaRolesSupportingNameFromContent:function(){return Yu},getElementSpec:function(){return ii},getElementsByContentType:function(){return Mo},getGlobalAriaAttrs:function(){return Bo},implicitHtmlRoles:function(){return Go}});var kc={ARTICLE:!0,ASIDE:!0,NAV:!0,SECTION:!0},Tc={application:!0,article:!0,banner:!1,complementary:!0,contentinfo:!0,form:!0,main:!0,navigation:!0,region:!0,search:!1};function Nc(e,t){return t=t,(n=c(n=e))&&(Tc[n]||t.roles.includes(n))||(t=(t=e).nodeName.toUpperCase(),kc[t])||!1;var n}var Rc={},Oc=(fe(Rc,{Color:function(){return O},centerPointOfRect:function(){return Oc},elementHasImage:function(){return Al},elementIsDistinct:function(){return Sc},filteredRectStack:function(){return Pc},flattenColors:function(){return Lc},flattenShadowColors:function(){return jc},getBackgroundColor:function(){return Wc},getBackgroundStack:function(){return qc},getContrast:function(){return Jc},getForegroundColor:function(){return Xc},getOwnBackgroundColor:function(){return Nl},getRectStack:function(){return Ic},getStackingContext:function(){return zc},getTextShadowColors:function(){return Vc},hasValidContrastRatio:function(){return Qc},incompleteData:function(){return R},stackingContextToColor:function(){return $c}}),function(e){if(!(e.left>window.innerWidth||e.top>window.innerHeight))return{x:Math.min(Math.ceil(e.left+e.width/2),window.innerWidth-1),y:Math.min(Math.ceil(e.top+e.height/2),window.innerHeight-1)}});function _c(e){return e.getPropertyValue("font-family").split(/[,;]/g).map(function(e){return e.trim().toLowerCase()})}var Sc=function(e,t){var n,a=window.getComputedStyle(e);return"none"!==a.getPropertyValue("background-image")||!!["border-bottom","border-top","outline"].reduce(function(e,t){var n=new O;return n.parseString(a.getPropertyValue(t+"-color")),e||"none"!==a.getPropertyValue(t+"-style")&&0<parseFloat(a.getPropertyValue(t+"-width"))&&0!==n.alpha},!1)||(n=window.getComputedStyle(t),_c(a)[0]!==_c(n)[0])||(e=["text-decoration-line","text-decoration-style","font-weight","font-style","font-size"].reduce(function(e,t){return e||a.getPropertyValue(t)!==n.getPropertyValue(t)},!1),(t=a.getPropertyValue("text-decoration")).split(" ").length<3?e||t!==n.getPropertyValue("text-decoration"):e)},Ic=function(e){var t=xo(e);return!(e=al(e))||e.length<=1?[t]:e.some(function(e){return void 0===e})?null:(e.splice(0,0,t),e)},Pc=function(r){var o,i,l=Ic(r);return l&&1===l.length?l[0]:l&&1<l.length?(o=l.shift(),l.forEach(function(e,t){var n,a;0!==t&&(n=l[t-1],a=l[t],i=n.every(function(e,t){return e===a[t]})||o.includes(r))}),i?l[0]:(R.set("bgColor","elmPartiallyObscuring"),null)):(R.set("bgColor","outsideViewport"),null)},Mc={normal:function(e,t){return t},multiply:function(e,t){return t*e},screen:function(e,t){return e+t-e*t},overlay:function(e,t){return this["hard-light"](t,e)},darken:function(e,t){return Math.min(e,t)},lighten:function(e,t){return Math.max(e,t)},"color-dodge":function(e,t){return 0===e?0:1===t?1:Math.min(1,e/(1-t))},"color-burn":function(e,t){return 1===e?1:0===t?0:1-Math.min(1,(1-e)/t)},"hard-light":function(e,t){return t<=.5?this.multiply(e,2*t):this.screen(e,2*t-1)},"soft-light":function(e,t){return t<=.5?e-(1-2*t)*e*(1-e):e+(2*t-1)*((e<=.25?((16*e-12)*e+4)*e:Math.sqrt(e))-e)},difference:function(e,t){return Math.abs(e-t)},exclusion:function(e,t){return e+t-2*e*t}};function Bc(e,t,n,a,r){return t*(1-a)*e+t*a*Mc[r](n/255,e/255)*255+(1-t)*a*n}var Lc=function(e,t){var n,a,r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:"normal",o=Bc(e.red,e.alpha,t.red,t.alpha,r),i=Bc(e.green,e.alpha,t.green,t.alpha,r),r=Bc(e.blue,e.alpha,t.blue,t.alpha,r),l=(n=e.alpha+t.alpha*(1-e.alpha),l=0,a=1,Math.min(Math.max(l,n),a));return 0===l?new O(o,i,r,l):(n=Math.round(o/l),a=Math.round(i/l),o=Math.round(r/l),new O(n,a,o,l))};function jc(e,t){var n=e.alpha,a=(1-n)*t.red+n*e.red,r=(1-n)*t.green+n*e.green,n=(1-n)*t.blue+n*e.blue,t=e.alpha+t.alpha*(1-e.alpha);return new O(a,r,n,t)}function qc(t){for(var e=al(t).map(function(e){return e=function(e){var t=e.indexOf(document.body),n=Nl(window.getComputedStyle(document.documentElement));1<t&&0===n.alpha&&!Al(document.documentElement)&&(1<t&&(e.splice(t,1),e.push(document.body)),0<(n=e.indexOf(document.documentElement)))&&(e.splice(n,1),e.push(document.documentElement));return e}(e=Bl(e,t))}),n=0;n<e.length;n++){var a=e[n];if(a[0]!==t)return R.set("bgColor","bgOverlap"),null;if(0!==n&&!function(e,t){if(e!==t){if(null===e||null===t)return;if(e.length!==t.length)return;for(var n=0;n<e.length;++n)if(e[n]!==t[n])return}return 1}(a,e[0]))return R.set("bgColor","elmPartiallyObscuring"),null}return e[0]||null}var Vc=function(e){var t,r,o,n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},i=n.minRatio,l=n.maxRatio,s=window.getComputedStyle(e);return"none"===(n=s.getPropertyValue("text-shadow"))?[]:(t=s.getPropertyValue("font-size"),r=parseInt(t),d(!1===isNaN(r),"Unable to determine font-size value ".concat(t)),o=[],function(e){var t={pixels:[]},n=e.trim(),a=[t];if(!n)return[];for(;n;){var r=n.match(/^rgba?\([0-9,.\s]+\)/i)||n.match(/^[a-z]+/i)||n.match(/^#[0-9a-f]+/i),o=n.match(/^([0-9.-]+)px/i)||n.match(/^(0)/);if(r)d(!t.colorStr,"Multiple colors identified in text-shadow: ".concat(e)),n=n.replace(r[0],"").trim(),t.colorStr=r[0];else if(o){d(t.pixels.length<3,"Too many pixel units in text-shadow: ".concat(e)),n=n.replace(o[0],"").trim();r=parseFloat(("."===o[1][0]?"0":"")+o[1]);t.pixels.push(r)}else{if(","!==n[0])throw new Error("Unable to process text-shadows: ".concat(e));d(2<=t.pixels.length,"Missing pixel value in text-shadow: ".concat(e)),a.push(t={pixels:[]}),n=n.substr(1).trim()}}return a}(n).forEach(function(e){var t=e.colorStr,e=e.pixels,t=t||s.getPropertyValue("color"),e=h(e,3),n=e[0],a=e[1],e=e[2],e=void 0===e?0:e;(!i||r*i<=e)&&(!l||e<r*l)&&(t=function(e){var t=e.colorStr,n=e.offsetX,a=e.offsetY,r=e.blurRadius,e=e.fontSize;if(r<n||r<a)return new O(0,0,0,0);n=new O;return n.parseString(t),n.alpha*=function(e,t){if(0===e)return 1;return.185/(e/t+.4)}(r,e),n}({colorStr:t,offsetY:n,offsetX:a,blurRadius:e,fontSize:r}),o.push(t))}),o)};function zc(e,t){var n,r,o,a=D(e);return a._stackingContext||(r=[],o=new Map,(t=null!=(n=t)?n:qc(e)).forEach(function(e){var e=D(e),t=(t=e,(n=new O).parseString(t.getComputedStylePropertyValue("background-color")),n),a=e._stackingOrder.filter(function(e){return!!e.vNode}),n=(a.forEach(function(e,t){var e=e.vNode,n=null==(n=a[t-1])?void 0:n.vNode,n=Gc(o,e,n);0!==t||o.get(e)||r.unshift(n),o.set(e,n)}),null==(n=a[a.length-1])?void 0:n.vNode),e=Gc(o,e,n);a.length||r.unshift(e),e.bgColor=t}),a._stackingContext=r)}function $c(e){var t;return null!=(t=e.descendants)&&t.length?(t=e.descendants.reduce(Uc,Hc()),(t=Lc(t,e.bgColor,e.descendants[0].blendMode)).alpha*=e.opacity):(t=e.bgColor).alpha*=e.opacity,{color:t,blendMode:e.blendMode}}function Uc(e,t){var e=e instanceof O?e:$c(e).color,n=$c(t).color;return Lc(n,e,t.blendMode)}function Hc(e,t){return{vNode:e,ancestor:t,opacity:parseFloat(null!=(t=null==e?void 0:e.getComputedStylePropertyValue("opacity"))?t:1),bgColor:new O(0,0,0,0),blendMode:(null==e?void 0:e.getComputedStylePropertyValue("mix-blend-mode"))||void 0,descendants:[]}}function Gc(e,t,n){var a=e.get(n),e=null!=(e=e.get(t))?e:Hc(t,a);return a&&n!==t&&!a.descendants.includes(e)&&a.descendants.unshift(e),e}function Wc(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:[],n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:.1,a=D(e),r=a._cache.getBackgroundColor;return r?(t.push.apply(t,v(r.bgElms)),R.set("bgColor",r.incompleteData),r.bgColor):(r=function(e,t,n){var a=qc(e);if(!a)return null;var r=nl(e),n=Vc(e,{minRatio:n});n.length&&(n=[{color:n.reduce(jc)}]);for(var o=0;o<a.length;o++){var i=a[o],l=window.getComputedStyle(i);if(Al(i,l))return t.push(i),null;var s=Nl(l);if(0!==s.alpha){if("inline"!==l.getPropertyValue("display")&&!Kc(i,r))return t.push(i),R.set("bgColor","elmPartiallyObscured"),null;if(t.push(i),1===s.alpha)break}}var u=zc(e,a),u=(n=u.map($c).concat(n),function(e,t){var n=[];{var a,r,o,i;t||(t=document.documentElement,i=document.body,t=window.getComputedStyle(t),a=window.getComputedStyle(i),r=Nl(t),o=Nl(a),i=0!==o.alpha&&Kc(i,e.getBoundingClientRect()),(0!==o.alpha&&0===r.alpha||i&&1!==o.alpha)&&n.unshift({color:o,blendMode:Yc(a.getPropertyValue("mix-blend-mode"))}),0===r.alpha)||i&&1===o.alpha||n.unshift({color:r,blendMode:Yc(t.getPropertyValue("mix-blend-mode"))})}return n}(e,a.includes(document.body)));if(n.unshift.apply(n,v(u)),0===n.length)return new O(255,255,255,1);e=n.reduce(function(e,t){return Lc(t.color,e.color instanceof O?e.color:e,t.blendMode)});return Lc(e.color instanceof O?e.color:e,new O(255,255,255,1))}(e,t,n),a._cache.getBackgroundColor={bgColor:r,bgElms:t,incompleteData:R.get("bgColor")},r)}function Kc(e,t){t=Array.isArray(t)?t:[t];var n=e.getBoundingClientRect(),a=n.right,r=n.bottom,o=window.getComputedStyle(e).getPropertyValue("overflow");return(["scroll","auto"].includes(o)||e instanceof window.HTMLHtmlElement)&&(a=n.left+e.scrollWidth,r=n.top+e.scrollHeight),t.every(function(e){return e.top>=n.top&&e.bottom<=r&&e.left>=n.left&&e.right<=a})}function Yc(e){return e||void 0}var Jc=function(e,t){return t&&e?(t.alpha<1&&(t=Lc(t,e)),e=e.getRelativeLuminance(),t=t.getRelativeLuminance(),(Math.max(t,e)+.05)/(Math.min(t,e)+.05)):null};function Xc(e,t,n){for(var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:{},o=window.getComputedStyle(e),a=[],i=0,l=[function(){var e,t,n=o,a=void 0===(a=(a=r).textStrokeEmMin)?0:a;return 0===(t=parseFloat(n.getPropertyValue("-webkit-text-stroke-width")))||(e=n.getPropertyValue("font-size"),t/=parseFloat(e),isNaN(t))||t<a?null:(e=n.getPropertyValue("-webkit-text-stroke-color"),(new O).parseString(e))},function(){return e=o,(new O).parseString(e.getPropertyValue("-webkit-text-fill-color")||e.getPropertyValue("color"));var e},function(){return Vc(e,{minRatio:0})}];i<l.length;i++){var s=(0,l[i])();if(s&&(a=a.concat(s),1===s.alpha))break}var u,c,d=a.reduce(function(e,t){return Lc(e,t)});return null===(n=null==n?Wc(e,[]):n)?(u=R.get("bgColor"),R.set("fgColor",u),null):(c=function e(t,n){var a,r=f(t);try{for(r.s();!(a=r.n()).done;){var o,i=a.value;if((null==(o=i.vNode)?void 0:o.actualNode)===n)return i;var l=e(i.descendants,n);if(l)return l}}catch(e){r.e(e)}finally{r.f()}}(u=zc(e),e),Lc(function(e,t,n){for(;t;){var a;1===t.opacity&&t.ancestor?t=t.ancestor:(e.alpha*=t.opacity,a=(null==(a=t.ancestor)?void 0:a.descendants)||n,a=(a=1!==t.opacity?a.slice(0,a.indexOf(t)):a).map($c),t=(a.length&&(a=a.reduce(function(e,t){return Lc(t.color,e.color instanceof O?e.color:e)},{color:new O(0,0,0,0),blendMode:"normal"}),e=Lc(e,a)),t.ancestor))}return e}(d,c,u),new O(255,255,255,1)))}var Qc=function(e,t,n,a){return e=Jc(e,t),{isValid:(t=a&&Math.ceil(72*n)/96<14||!a&&Math.ceil(72*n)/96<18?4.5:3)<e,contrastRatio:e,expectedContrastRatio:t}},Zc=t(function(e,t){function n(e,t){return a.getPropertyValue(e)===t}var a=window.getComputedStyle(e,t);return n("content","none")||n("display","none")||n("visibility","hidden")||!1===n("position","absolute")||0===Nl(a).alpha&&n("background-image","none")?0:(e=e1(a.getPropertyValue("width")),t=e1(a.getPropertyValue("height")),"px"!==e.unit||"px"!==t.unit?0===e.value||0===t.value?0:1/0:e.value*t.value)});function e1(e){var e=h(e.match(/^([0-9.]+)([a-z]+)$/i)||[],3),t=e[1],e=e[2],e=void 0===e?"":e;return{value:parseFloat(void 0===t?"":t),unit:e.toLowerCase()}}function t1(e,t){e=e.getRelativeLuminance(),t=t.getRelativeLuminance();return(Math.max(e,t)+.05)/(Math.min(e,t)+.05)}var n1=["block","list-item","table","flex","grid","inline-block"];function a1(e){e=window.getComputedStyle(e).getPropertyValue("display");return-1!==n1.indexOf(e)||"table-"===e.substr(0,6)}function r1(e,t){var n=t.requiredContrastRatio,t=t.allowSameColor;if(a1(e))return!1;for(var a=u(e);a&&1===a.nodeType&&!a1(a);)a=u(a);if(a){this.relatedNodes([a]);var r=Xc(e),o=Xc(a),e=Wc(e),i=Wc(a),l=r&&o?t1(r,o):void 0;if((l=l&&Math.floor(100*l)/100)&&n<=l)return!0;var s=e&&i?t1(e,i):void 0;if((s=s&&Math.floor(100*s)/100)&&n<=s)return!0;if(s){if(l)return!(!t||1!==l||1!==s)||(1===l&&1<s?this.data({messageKey:"bgContrast",contrastRatio:s,requiredContrastRatio:n,nodeBackgroundColor:e?e.toHexString():void 0,parentBackgroundColor:i?i.toHexString():void 0}):this.data({messageKey:"fgContrast",contrastRatio:l,requiredContrastRatio:n,nodeColor:r?r.toHexString():void 0,parentColor:o?o.toHexString():void 0}),!1)}else s=null!=(t=R.get("bgColor"))?t:"bgContrast",this.data({messageKey:s}),R.clear()}}var o1=["block","list-item","table","flex","grid","inline-block"];function i1(e){e=window.getComputedStyle(e).getPropertyValue("display");return-1!==o1.indexOf(e)||"table-"===e.substr(0,6)}function l1(e){if(i1(e))return!1;for(var t=u(e);t&&1===t.nodeType&&!i1(t);)t=u(t);return t?(this.relatedNodes([t]),Sc(e,t)):void 0}function s1(e,t,n){var a,r,o;return"input"!==n.props.nodeName||(r={bday:["text","search","date"],email:["text","search","email"],username:["text","search","email"],"street-address":["text"],tel:["text","search","tel"],"tel-country-code":["text","search","tel"],"tel-national":["text","search","tel"],"tel-area-code":["text","search","tel"],"tel-local":["text","search","tel"],"tel-local-prefix":["text","search","tel"],"tel-local-suffix":["text","search","tel"],"tel-extension":["text","search","tel"],"cc-number":a=["text","search","number","tel"],"cc-exp":["text","search","month","tel"],"cc-exp-month":a,"cc-exp-year":a,"cc-csc":a,"transaction-amount":a,"bday-day":a,"bday-month":a,"bday-year":a,"new-password":["text","search","password"],"current-password":["text","search","password"],url:o=["text","search","url"],photo:o,impp:o},"object"===te(t)&&Object.keys(t).forEach(function(e){r[e]||(r[e]=[]),r[e]=r[e].concat(t[e])}),o=(a=n.attr("autocomplete").split(/\s+/g).map(function(e){return e.toLowerCase()}))[a.length-1],!!Ki.stateTerms.includes(o))||(a=r[o],o=n.hasAttr("type")?C(n.attr("type")).toLowerCase():"text",o=wu().includes(o)?o:"text",void 0===a?"text"===o:a.includes(o))}function u1(e,t,n){return n=n.attr("autocomplete")||"",Yi(n,t)}function c1(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},n=2<arguments.length?arguments[2]:void 0;if(t.attribute&&"string"==typeof t.attribute)return n.hasAttr(t.attribute)?(n=n.attr(t.attribute),!!C(n)||(this.data({messageKey:"emptyAttr"}),!1)):(this.data({messageKey:"noAttr"}),!1);throw new TypeError("attr-non-space-content requires options.attribute to be a string")}function d1(e){return e.some(function(e){return!0===e.result})&&e.forEach(function(e){e.result=!0}),e}function p1(e,t,n){if(t&&t.selector&&"string"==typeof t.selector)return!(!t.passForModal||!Dl())||(n=tu(n,t.selector,N),this.relatedNodes(n.map(function(e){return e.actualNode})),0<n.length);throw new TypeError("has-descendant requires options.selector to be a string")}function f1(e,t,n){return oi(n,t.matcher)}function m1(e){return e.filter(function(e){return"ignored"!==e.data})}function h1(e,t,n){if(!t||!t.selector||"string"!=typeof t.selector)throw new TypeError("page-no-duplicate requires options.selector to be a string");var a="page-no-duplicate;"+t.selector;if(!w.get(a))return w.set(a,!0),a=tu(axe._tree[0],t.selector,N),"string"==typeof t.nativeScopeFilter&&(a=a.filter(function(e){return e.actualNode.hasAttribute("role")||!ur(e,t.nativeScopeFilter)})),this.relatedNodes(a.filter(function(e){return e!==n}).map(function(e){return e.actualNode})),a.length<=1;this.data("ignored")}function g1(e){var n={};return e.filter(function(e){if(e.data){var t=e.data.toUpperCase();if(!n[t])return(n[t]=e).relatedNodes=[],!0;n[t].relatedNodes.push(e.relatedNodes[0])}return!1}).map(function(e){return e.result=!!e.relatedNodes.length,e})}function b1(e,t,n){return Nr(n)||(this.data(n.attr("accesskey")),this.relatedNodes([e])),!0}function y1(e,t,n){var a=n.tabbableElements;return!!a&&0<a.filter(function(e){return e!==n}).length}function v1(e,t,n){var a=["button","fieldset","input","select","textarea"];return!((n=n.tabbableElements)&&n.length&&(n=n.filter(function(e){return a.includes(e.props.nodeName)}),this.relatedNodes(n.map(function(e){return e.actualNode})),0!==n.length)&&!Dl())||!!n.every(function(e){var t=e.getComputedStylePropertyValue("pointer-events"),n=parseInt(e.getComputedStylePropertyValue("width")),a=parseInt(e.getComputedStylePropertyValue("height"));return e.actualNode.onfocus||(0===n||0===a)&&"none"===t})&&void 0}function w1(e,t,n){return!(!n.hasAttr("contenteditable")||!function e(t){t=t.attr("contenteditable");if("true"===t||""===t)return!0;if("false"===t)return!1;t=x(n.parent,"[contenteditable]");if(!t)return!1;return e(t)}(n))||gl(n)}function D1(e,t,n){return!(n=n.tabbableElements.map(function(e){return e.actualNode}))||!n.length||!Dl()||void this.relatedNodes(n)}function x1(e,t,n){var a=n.attr("tabindex");if(!(k(n)&&-1<a))return!1;try{return!l(n)}catch(e){}}function E1(e,t,n){var a=["button","fieldset","input","select","textarea"];return!((n=n.tabbableElements)&&n.length&&(n=n.filter(function(e){return!a.includes(e.props.nodeName)}),this.relatedNodes(n.map(function(e){return e.actualNode})),0!==n.length)&&!Dl())||!!n.every(function(e){var t=e.getComputedStylePropertyValue("pointer-events"),n=parseInt(e.getComputedStylePropertyValue("width")),a=parseInt(e.getComputedStylePropertyValue("height"));return e.actualNode.onfocus||(0===n||0===a)&&"none"===t})&&void 0}function F1(e){var t=Wu("landmark"),n=u(e),a=T(e);for(this.data({role:a});n;){var r=n.getAttribute("role");if((r=r||"FORM"===n.nodeName.toUpperCase()?r:li(n))&&t.includes(r)&&("main"!==r||"complementary"!==a))return!1;n=u(n)}return!0}function A1(e){e=parseInt(e.attr("tabindex"),10);return!isNaN(e)&&e<0}function C1(e,t,n){return n=parseInt(n.attr("tabindex"),10),!!isNaN(n)||n<=0}function k1(e,t,n){return"string"==typeof(n=n.attr("alt"))&&/^\s+$/.test(n)}function T1(e,t,n){return!["none","presentation"].includes(T(n))&&!!(t=x(n,t.parentSelector))&&""!==(t=Ai(t,!0).toLowerCase())&&t===l(n).toLowerCase()}function N1(e,t,n){var a=this;if(!n.attr("id"))return!1;if(n.actualNode){var r=E(n.actualNode),o=y(n.attr("id")),r=Array.from(r.querySelectorAll('label[for="'.concat(o,'"]')));if(this.relatedNodes(r),!r.length)return!1;try{return r.some(function(e){return!F(e)||(e=C(Co(e,{inControlContext:!0,startNode:n})),a.data({explicitLabel:e}),!!e)})}catch(e){}}}function R1(e,t,n){var n=Qi(n),a=e.getAttribute("title");return!!n&&(a||(a="",e.getAttribute("aria-describedby")&&(a=Ao(e,"aria-describedby").map(function(e){return e?Co(e):""}).join(""))),C(a)===C(n))}function O1(e,t,n){if(n.hasAttr("id")){if(!n.actualNode)return;var a,r=E(e),e=y(e.getAttribute("id")),r=r.querySelector('label[for="'.concat(e,'"]'));if(r&&!N(r)){try{a=l(n).trim()}catch(e){return}return""===a}}return!1}function _1(e,t,n){try{var a,r=x(n,"label");return r?(a=C(l(r,{inControlContext:!0,startNode:n})),r.actualNode&&this.relatedNodes([r.actualNode]),this.data({implicitLabel:a}),!!a):!1}catch(e){}}function S1(e,t){t=I1(t),e=I1(e);return!(!t||!e)&&t.includes(e)}function I1(e){e=Gi(e,{emoji:!0,nonBmp:!0,punctuations:!0});return C(e)}function P1(e,t,n){var a=null==t?void 0:t.pixelThreshold,r=null!=(r=null==t?void 0:t.occurrenceThreshold)?r:null==t?void 0:t.occuranceThreshold,t=Co(e).toLowerCase();if(!(Wi(t)<1))return!(e=C(gi(n,{subtreeDescendant:!0,ignoreIconLigature:!0,pixelThreshold:a,occurrenceThreshold:r})).toLowerCase())||(Wi(e)<1?!!S1(e,t)||void 0:S1(e,t))}function M1(e){var t=y(e.getAttribute("id")),n=e.parentNode,a=(a=E(e)).documentElement||a,r=Array.from(a.querySelectorAll('label[for="'.concat(t,'"]')));for(r.length&&(r=r.filter(function(e){return!Nr(e)}));n;)"LABEL"===n.nodeName.toUpperCase()&&-1===r.indexOf(n)&&r.push(n),n=n.parentNode;return this.relatedNodes(r),1<r.length&&(1<(a=r.filter(N)).length||!Ao(e,"aria-labelledby").includes(a[0]))&&void 0}function B1(e,t,n){var a=Qi(n),r=pi(n),n=n.attr("aria-describedby");return!(a||!r&&!n)}function L1(e){var n=[];return e.filter(function(t){var e=n.find(function(e){return t.data.role===e.data.role&&t.data.accessibleText===e.data.accessibleText});return e?(e.result=!1,e.relatedNodes.push(t.relatedNodes[0]),!1):(n.push(t),t.relatedNodes=[],!0)})}function j1(e,t,n){var a=T(e),n=(n=l(n))?n.toLowerCase():null;return this.data({role:a,accessibleText:n}),this.relatedNodes([e]),!0}function q1(e){return""!==(e||"").trim()}function V1(e,t,n){var a=void 0!==document&&_n(document);return t.attributes.includes("xml:lang")&&t.attributes.includes("lang")&&q1(n.attr("xml:lang"))&&!q1(n.attr("lang"))&&!a?(this.data({messageKey:"noXHTML"}),!1):!!t.attributes.some(function(e){return q1(n.attr(e))})||(this.data({messageKey:"noLang"}),!1)}function z1(e,r,o){var i=[];return r.attributes.forEach(function(e){var t,n,a=o.attr(e);"string"==typeof a&&(t=rs(a),n=r.value?!r.value.map(rs).includes(t):!Fu(t),""!==t&&n||""!==a&&!C(a))&&i.push(e+'="'+o.attr(e)+'"')}),!!i.length&&!("html"!==o.props.nodeName&&!cl(o)||(this.data(i),0))}function $1(e,t,n){return rs(n.attr("lang"))===rs(n.attr("xml:lang"))}function U1(e){var t=(e=u(e)).nodeName.toUpperCase(),n=c(e);return"DIV"===t&&["presentation","none",null].includes(n)&&(t=(e=u(e)).nodeName.toUpperCase(),n=c(e)),"DL"===t&&!(n&&!["presentation","none","list"].includes(n))}function H1(e,t){var n=1<arguments.length&&void 0!==t&&t;return e.map(function(e){return{vChild:e,nested:n}})}function G1(e,t,n){var r=["definition","term","list"];return(n=n.children.reduce(function(e,t){var n=t.actualNode;return"DIV"===n.nodeName.toUpperCase()&&null===T(n)?e.concat(t.children):e.concat(t)},[]).reduce(function(e,t){var n,t=t.actualNode,a=t.nodeName.toUpperCase();return 1===t.nodeType&&N(t)?(n=c(t),("DT"!==a&&"DD"!==a||n)&&!r.includes(n)&&e.badNodes.push(t)):3===t.nodeType&&""!==t.nodeValue.trim()&&(e.hasNonEmptyTextNode=!0),e},{badNodes:[],hasNonEmptyTextNode:!1})).badNodes.length&&this.relatedNodes(n.badNodes),!!n.badNodes.length||n.hasNonEmptyTextNode}function W1(e,t,n){var r=!1,o=!1,i=!0,l=[],s=[],u=[];if(n.children.forEach(function(e){var t,n,a=e.actualNode;3===a.nodeType&&""!==a.nodeValue.trim()?r=!0:1===a.nodeType&&N(a)&&(i=!1,t="LI"===a.nodeName.toUpperCase(),n="listitem"===(e=T(e)),t||n||l.push(a),t&&!n&&(s.push(a),u.includes(e)||u.push(e)),n)&&(o=!0)}),r||l.length)this.relatedNodes(l);else{if(i||o)return!1;this.relatedNodes(s),this.data({messageKey:"roleNotValid",roles:u.join(", ")})}return!0}function K1(e,t,n){var a=n.children;if(!a||!a.length)return!1;for(var r,o=!1,i=!1,l=0;l<a.length;l++){if((o="DT"===(r=a[l].props.nodeName.toUpperCase())?!0:o)&&"DD"===r)return!1;"DD"===r&&(i=!0)}return o||i}function Y1(e,t,n){return!du(n,"track").some(function(e){return"captions"===(e.attr("kind")||"").toLowerCase()})&&void 0}function J1(e){var n={};return e.filter(function(e){var t;return"html"!==e.node.ancestry[e.node.ancestry.length-1]?(t=e.node.ancestry.flat(1/0).join(" > "),n[t]=e,!0):(t=e.node.ancestry.slice(0,e.node.ancestry.length-1).flat(1/0).join(" > "),n[t]&&(n[t].result=!0),!1)})}function X1(e,t){return!t.isViolation&&void 0}function Q1(e,t){var n,a;if(e.duration)return t=void 0===(t=t.allowedDuration)?3:t,((n=e).currentSrc?(a=function(e){e=e.match(/#t=(.*)/);if(e)return h(e,2)[1].split(",").map(function(e){if(/:/.test(e)){for(var t=e.split(":"),n=0,a=1;0<t.length;)n+=a*parseInt(t.pop(),10),a*=60;return parseFloat(n)}return parseFloat(e)})}(n.currentSrc))?1!==a.length?Math.abs(a[1]-a[0]):Math.abs(n.duration-a[0]):Math.abs(n.duration-(n.currentTime||0)):0)<=t&&!e.hasAttribute("loop")||!!e.hasAttribute("controls");console.warn("axe.utils.preloadMedia did not load metadata")}function Z1(e,t,n,a){var a=void 0===(a=(a||{}).cssom)?void 0:a,r=void 0===(t=(t||{}).degreeThreshold)?0:t;if(a&&a.length){function o(){var e=c[u],e=s[e],n=e.root,e=e.rules.filter(d);if(!e.length)return"continue";e.forEach(function(e){e=e.cssRules;Array.from(e).forEach(function(e){var t=function(e){var t=e.selectorText,e=e.style;if(!t||e.length<=0)return!1;t=e.transform||e.webkitTransform||e.msTransform||!1;if(!t&&!e.rotate)return!1;t=function(e){var t;return e&&(e=e.match(/(rotate|rotateZ|rotate3d|matrix|matrix3d)\(([^)]+)\)(?!.*(rotate|rotateZ|rotate3d|matrix|matrix3d))/))?(e=h(e,3),t=e[1],e=e[2],p(t,e)):0}(t),e=p("rotate",e.rotate),t+=e;if(!t)return!1;if(t=Math.abs(t),Math.abs(t-180)%180<=r)return!1;return Math.abs(t-90)%90<=r}(e);t&&"HTML"!==e.selectorText.toUpperCase()&&(e=Array.from(n.querySelectorAll(e.selectorText))||[],l=l.concat(e)),i=i||t})})}for(var i=!1,l=[],s=a.reduce(function(e,t){var n=t.sheet,a=t.root,t=t.shadowId,t=t||"topDocument";return e[t]||(e[t]={root:a,rules:[]}),n&&n.cssRules&&(a=Array.from(n.cssRules),e[t].rules=e[t].rules.concat(a)),e},{}),u=0,c=Object.keys(s);u<c.length;u++)o();return i?(l.length&&this.relatedNodes(l),!1):!0}function d(e){var t=e.type,e=e.cssText;return 4===t&&(/orientation:\s*landscape/i.test(e)||/orientation:\s*portrait/i.test(e))}function p(e,t){switch(e){case"rotate":case"rotateZ":return f(t);case"rotate3d":var n=h(t.split(",").map(function(e){return e.trim()}),4),a=n[2],n=n[3];return 0===parseInt(a)?void 0:f(n);case"matrix":case"matrix3d":var r,o,a=t;return(a=a.split(",")).length<=6?(o=h(a,2),r=o[0],o=o[1],m(Math.atan2(parseFloat(o),parseFloat(r)))):(o=parseFloat(a[8]),r=Math.asin(o),o=Math.cos(r),m(Math.acos(parseFloat(a[0])/o)));default:return 0}}function f(e){var t=h(e.match(/(deg|grad|rad|turn)/)||[],1)[0];if(!t)return 0;var n=parseFloat(e.replace(t,""));switch(t){case"rad":return m(n);case"grad":var a=n;return(a%=400)<0&&(a+=400),Math.round(a/400*360);case"turn":return Math.round(360/(1/n));default:return parseInt(n)}}function m(e){return Math.round(e*(180/Math.PI))}}function ed(e,t,n){var a,r=void 0===(r=(t=t||{}).scaleMinimum)?2:r,t=void 0!==(t=t.lowerBound)&&t;return!((n=n.attr("content")||"")&&(n=n.split(/[;,]/).reduce(function(e,t){var n,t=t.trim();return t&&(n=(t=h(t.split("="),2))[0],t=t[1],n)&&t&&(n=n.toLowerCase().trim(),t=t.toLowerCase().trim(),"maximum-scale"===n&&"yes"===t&&(t=1),"maximum-scale"===n&&parseFloat(t)<0||(e[n]=t)),e},{}),!(t&&n["maximum-scale"]&&parseFloat(n["maximum-scale"])<t))&&(t||"no"!==n["user-scalable"]?(a=parseFloat(n["user-scalable"]),!t&&n["user-scalable"]&&(a||0===a)&&-1<a&&a<1?(this.data("user-scalable"),1):n["maximum-scale"]&&parseFloat(n["maximum-scale"])<r&&(this.data("maximum-scale"),1)):(this.data("user-scalable=no"),1)))}var td=.05;function nd(e,t){e=e.boundingClientRect,t=t.boundingClientRect;return e.top>=t.top&&e.left>=t.left&&e.bottom<=t.bottom&&e.right<=t.right}function ad(e){return{width:Math.round(10*e.width)/10,height:Math.round(10*e.height)/10}}function rd(e,t){return e.actualNode.contains(t.actualNode)&&!gl(t)}function od(e,t){var n=t.width,t=t.height;return e<=n+td&&e<=t+td}function id(e){return e.map(function(e){return e.actualNode})}function ld(e,t){var n=null==(n=t.data)?void 0:n.headingOrder,a=ud(t.node.ancestry,1);return n&&(t=n.map(function(e){var t=a;return t=a.concat(e.ancestry),p({},e,{ancestry:t})}),-1===(n=function(e,t){for(;t.length;){var n=sd(e,t);if(-1!==n)return n;t=ud(t,1)}return-1}(e,a))?e.push.apply(e,v(t)):e.splice.apply(e,[n,0].concat(v(t)))),e}function sd(e,t){return e.findIndex(function(e){return Vs(e.ancestry,t)})}function ud(e,t){return e.slice(0,e.length-t)}function cd(){var e,t=w.get("headingOrder");return t||(t=(e=tu(axe._tree[0],"h1, h2, h3, h4, h5, h6, [role=heading], iframe, frame",N)).map(function(e){return{ancestry:[Kn(e.actualNode)],level:(t=(t=T(e=e))&&t.includes("heading"),n=e.attr("aria-level"),a=parseInt(n,10),e=h(e.props.nodeName.match(/h(\d)/)||[],2)[1],t?e&&!n?parseInt(e,10):isNaN(a)||a<1?e?parseInt(e,10):2:a||-1:-1)};var t,n,a}),this.data({headingOrder:t}),w.set("headingOrder",e)),!0}function dd(e){if(e.length<2)return e;function t(n){var e=i[n],t=e.data,a=t.name,r=t.urlProps;if(s[a])return"continue";var o=(t=i.filter(function(e,t){return e.data.name===a&&t!==n})).every(function(e){return function n(a,r){var e,t;return!(!a||!r)&&(e=Object.getOwnPropertyNames(a),t=Object.getOwnPropertyNames(r),e.length===t.length)&&e.every(function(e){var t=a[e],e=r[e];return te(t)===te(e)&&("object"===te(t)||"object"===te(e)?n(t,e):t===e)})}(e.data.urlProps,r)});t.length&&!o&&(e.result=void 0),e.relatedNodes=[],(o=e.relatedNodes).push.apply(o,v(t.map(function(e){return e.relatedNodes[0]}))),s[a]=t,l.push(e)}for(var i=e.filter(function(e){return void 0!==e.result}),l=[],s={},n=0;n<i.length;n++)t(n);return l}fe(an={},{aria:function(){return ju},color:function(){return Rc},dom:function(){return ir},forms:function(){return pd},matches:function(){return oi},math:function(){return mo},standards:function(){return mc},table:function(){return hd},text:function(){return Fo},utils:function(){return dn}});var pd={},fd=(fe(pd,{isAriaCombobox:function(){return Oi},isAriaListbox:function(){return Ri},isAriaRange:function(){return Si},isAriaTextbox:function(){return Ni},isDisabled:function(){return md},isNativeSelect:function(){return Ti},isNativeTextbox:function(){return ki}}),["fieldset","button","select","input","textarea"]),md=function e(t){var n,a,r=t._isDisabled;return"boolean"!=typeof r&&(n=t.props.nodeName,a=t.attr("aria-disabled"),r=!(!fd.includes(n)||!t.hasAttr("disabled"))||(a?"true"===a.toLowerCase():!!t.parent&&e(t.parent)),t._isDisabled=r),r},hd={},gd=(fe(hd,{getAllCells:function(){return gd},getCellPosition:function(){return jo},getHeaders:function(){return yd},getScope:function(){return qo},isColumnHeader:function(){return Vo},isDataCell:function(){return vd},isDataTable:function(){return wd},isHeader:function(){return Dd},isRowHeader:function(){return zo},toArray:function(){return Lo},toGrid:function(){return Lo},traverse:function(){return xd}}),function(e){for(var t,n,a=[],r=0,o=e.rows.length;r<o;r++)for(t=0,n=e.rows[r].cells.length;t<n;t++)a.push(e.rows[r].cells[t]);return a});function bd(e,t,n){for(var a,r="row"===e?"_rowHeaders":"_colHeaders",o="row"===e?zo:Vo,i=n[t.y][t.x],l=i.colSpan-1,s=i.getAttribute("rowspan"),s=0===parseInt(s)||0===i.rowspan?n.length:i.rowSpan,i=t.y+(s-1),u=t.x+l,c="row"===e?t.y:0,d="row"===e?0:t.x,p=[],f=i;c<=f&&!a;f--)for(var m=u;d<=m;m--){var h=n[f]?n[f][m]:void 0;if(h){var g=axe.utils.getNodeFromTree(h);if(g[r]){a=g[r];break}p.push(h)}}return a=(a||[]).concat(p.filter(o)),p.forEach(function(e){axe.utils.getNodeFromTree(e)[r]=a}),a}var yd=function(e,t){if(e.getAttribute("headers")){var n=Ao(e,"headers");if(n.filter(function(e){return e}).length)return n}return t=t||Lo(cr(e,"table")),n=jo(e,t),e=bd("row",n,t),n=bd("col",n,t),[].concat(e,n).reverse()},vd=function(e){var t;return!(!e.children.length&&!e.textContent.trim())&&(t=e.getAttribute("role"),Po(t)?["cell","gridcell"].includes(t):"TD"===e.nodeName.toUpperCase())},wd=function(e){var t=(e.getAttribute("role")||"").toLowerCase();if(("presentation"===t||"none"===t)&&!k(e))return!1;if("true"===e.getAttribute("contenteditable")||cr(e,'[contenteditable="true"]'))return!0;if("grid"===t||"treegrid"===t||"table"===t)return!0;if("landmark"===bl(t))return!0;if("0"===e.getAttribute("datatable"))return!1;if(e.getAttribute("summary"))return!0;if(e.tHead||e.tFoot||e.caption)return!0;for(var n=0,a=e.children.length;n<a;n++)if("COLGROUP"===e.children[n].nodeName.toUpperCase())return!0;for(var r,o,i,l=0,s=e.rows.length,u=!1,c=0;c<s;c++)for(var d,p=0,f=(d=e.rows[c]).cells.length;p<f;p++){if("TH"===(r=d.cells[p]).nodeName.toUpperCase())return!0;if(u||r.offsetWidth===r.clientWidth&&r.offsetHeight===r.clientHeight||(u=!0),r.getAttribute("scope")||r.getAttribute("headers")||r.getAttribute("abbr"))return!0;if(["columnheader","rowheader"].includes((r.getAttribute("role")||"").toLowerCase()))return!0;if(1===r.children.length&&"ABBR"===r.children[0].nodeName.toUpperCase())return!0;l++}if(e.getElementsByTagName("table").length)return!1;if(s<2)return!1;if(1===(t=e.rows[Math.ceil(s/2)]).cells.length&&1===t.cells[0].colSpan)return!1;if(5<=t.cells.length)return!0;if(u)return!0;for(c=0;c<s;c++){if(d=e.rows[c],o&&o!==window.getComputedStyle(d).getPropertyValue("background-color"))return!0;if(o=window.getComputedStyle(d).getPropertyValue("background-color"),i&&i!==window.getComputedStyle(d).getPropertyValue("background-image"))return!0;i=window.getComputedStyle(d).getPropertyValue("background-image")}return 20<=s||!(Sr(e).width>.95*Ir(window).width||l<10||e.querySelector("object, embed, iframe, applet"))},Dd=function(e){return!(!Vo(e)&&!zo(e))||!!e.getAttribute("id")&&(e=y(e.getAttribute("id")),!!document.querySelector('[headers~="'.concat(e,'"]')))},xd=function(e,t,n,a){if(Array.isArray(t)&&(a=n,n=t,t={x:0,y:0}),"string"==typeof e)switch(e){case"left":e={x:-1,y:0};break;case"up":e={x:0,y:-1};break;case"right":e={x:1,y:0};break;case"down":e={x:0,y:1}}return function e(t,n,a,r){var o,i=a[n.y]?a[n.y][n.x]:void 0;return i?"function"==typeof r&&!0===(o=r(i,n,a))?[i]:((o=e(t,{x:n.x+t.x,y:n.y+t.y},a,r)).unshift(i),o):[]}(e,{x:t.x+e.x,y:t.y+e.y},n,a)};function Ed(e,t,n){var n=Fo.accessibleTextVirtual(n);if(n=Fo.sanitize(Fo.removeUnicode(n,{emoji:!0,nonBmp:!0,punctuations:!0})).toLowerCase())return n={name:n,urlProps:ir.urlPropsFromAttribute(e,"href")},this.data(n),this.relatedNodes([e]),!0}function Fd(e,t,n){return du(n,"a[href]").some(function(e){return/^#[^/!]/.test(e.attr("href"))})}var Ad=/[;,\s]/,Cd=/^[0-9.]+$/;function kd(e){e=window.getComputedStyle(function(e){for(var t=e,n=e.textContent.trim(),a=n;a===n&&void 0!==t;){var r=-1;if(0===(e=t).children.length)return e;for(;r++,""===(a=e.children[r].textContent.trim())&&r+1<e.children.length;);t=e.children[r]}return e}(e));return{fontWeight:function(e){switch(e){case"lighter":return 100;case"normal":return 400;case"bold":return 700;case"bolder":return 900}return e=parseInt(e),isNaN(e)?400:e}(e.getPropertyValue("font-weight")),fontSize:parseInt(e.getPropertyValue("font-size")),isItalic:"italic"===e.getPropertyValue("font-style")}}function Td(n,a,e){return e.reduce(function(e,t){return e||(!t.size||n.fontSize/t.size>a.fontSize)&&(!t.weight||n.fontWeight-t.weight>a.fontWeight)&&(!t.italic||n.isItalic&&!a.isItalic)},!1)}function Nd(e,t,n){var a=(i=Array.from(e.parentNode.children)).indexOf(e),r=(t=t||{}).margins||[],o=i.slice(a+1).find(function(e){return"P"===e.nodeName.toUpperCase()}),i=i.slice(0,a).reverse().find(function(e){return"P"===e.nodeName.toUpperCase()}),a=kd(e),l=o?kd(o):null,i=i?kd(i):null,s=t.passLength,t=t.failLength,e=e.textContent.trim().length;return(o=null==o?void 0:o.textContent.trim().length)*s<e||!l||!Td(a,l,r)||!!((s=ur(n,"blockquote"))&&"BLOCKQUOTE"===s.nodeName.toUpperCase()||i&&!Td(a,i,r)||o*t<e)&&void 0}function Rd(e){var o=e.filter(function(e){return e.data.isIframe});return e.forEach(function(e){if(!e.result&&1!==e.node.ancestry.length){var t,n=e.node.ancestry.slice(0,-1),a=f(o);try{for(a.s();!(t=a.n()).done;){var r=t.value;if(Vs(n,r.node.ancestry)){e.result=r.result;break}}}catch(e){a.e(e)}finally{a.f()}}}),o.forEach(function(e){e.result||(e.result=!0)}),e}var Od=Wu("landmark"),_d=["alert","log","status"];function Sd(e,t){var n=e.actualNode,a=T(e),n=(n.getAttribute("aria-live")||"").toLowerCase().trim();return!!(["assertive","polite"].includes(n)||_d.includes(a)||Od.includes(a)||t.regionMatcher&&oi(e,t.regionMatcher))}function Id(e){return!!(e=co(e,"href"))&&(N(e)||void 0)}function Pd(e){var t={};return e.forEach(function(e){t[e.data]=void 0!==t[e.data]?++t[e.data]:0}),e.forEach(function(e){e.result=!!t[e.data]}),e}function Md(e,t,n){return n=C(n.attr("title")).toLowerCase(),this.data(n),!0}function Bd(e){var t=[];return e.filter(function(e){return-1===t.indexOf(e.data)&&(t.push(e.data),!0)})}function Ld(t){var e,n=t.getAttribute("id").trim();return!n||(e=E(t),(e=Array.from(e.querySelectorAll('[id="'.concat(y(n),'"]'))).filter(function(e){return e!==t})).length&&this.relatedNodes(e),this.data(n),0===e.length)}function jd(e,t,n){return!!C(To(n))}function qd(e,t,n){try{return!!C(ko(n))}catch(e){}}function Vd(t,e){return!(0<(e=e.cssProperties.filter(function(e){if("important"===t.style.getPropertyPriority(e))return e})).length&&(this.data(e),1))}function zd(){var e=document.title;return!!C(e)}function $d(){}function Ud(e,t,n){var a=n.props.nodeName;return!!["img","input","area"].includes(a)&&n.hasAttr("alt")}function Hd(e,t,n){var a=n.props.nodeName,r=(n.attr("type")||"").toLowerCase();return(n=n.attr("value"))&&this.data({messageKey:"has-label"}),!("input"!==a||!["submit","reset"].includes(r))&&null===n}function Gd(e,t,n){if(n.children){n=n.children.find(function(e){return"title"===e.props.nodeName});if(!n)return this.data({messageKey:"noTitle"}),!1;try{if(""===gi(n,{includeHidden:!0}).trim())return this.data({messageKey:"emptyTitle"}),!1}catch(e){return}return!0}}function Wd(e){var t=Lo(e),a=t[0];return t.length<=1||a.length<=1||e.rows.length<=1||a.reduce(function(e,t,n){return e||t!==a[n+1]&&void 0!==a[n+1]},!1)}function Kd(e){return!hl(document)||"TH"===e.nodeName.toUpperCase()}function Yd(e,t,n){var a;if(void 0!==n.children)return a=n.attr("summary"),!(!(n=!!(n=n.children.find(Jd))&&C(gi(n)))||!a)&&C(a).toLowerCase()===C(n).toLowerCase()}function Jd(e){return"caption"===e.props.nodeName}function Xd(e,t){return e=e.getAttribute("scope").toLowerCase(),-1!==t.values.indexOf(e)}function Qd(e){var t=[],n=gd(e),a=Lo(e);return n.forEach(function(e){ul(e)&&vd(e)&&!ac(e)&&!yd(e,a).some(function(e){return null!==e&&!!ul(e)})&&t.push(e)}),!t.length||(this.relatedNodes(t),!1)}function Zd(e){for(var t=[],a=[],r=[],n=0;n<e.rows.length;n++)for(var o=e.rows[n],i=0;i<o.cells.length;i++)t.push(o.cells[i]);var l=t.reduce(function(e,t){return t.getAttribute("id")&&e.push(t.getAttribute("id")),e},[]);return t.forEach(function(e){var t,n=!1;if(e.hasAttribute("headers")&&N(e))return(t=e.getAttribute("headers").trim())?void(0!==(t=_(t)).length&&(e.getAttribute("id")&&(n=-1!==t.indexOf(e.getAttribute("id").trim())),t=t.some(function(e){return!l.includes(e)}),n||t)&&r.push(e)):a.push(e)}),0<r.length?(this.relatedNodes(r),!1):!a.length||void this.relatedNodes(a)}function ep(e){var t=gd(e),a=this,r=[],t=(t.forEach(function(e){var t=e.getAttribute("headers"),t=(t&&(r=r.concat(t.split(/\s+/))),e.getAttribute("aria-labelledby"));t&&(r=r.concat(t.split(/\s+/)))}),t.filter(function(e){return""!==C(e.textContent)&&("TH"===e.nodeName.toUpperCase()||-1!==["rowheader","columnheader"].indexOf(e.getAttribute("role")))})),o=Lo(e),i=!0;return t.forEach(function(t){var e,n;t.getAttribute("id")&&r.includes(t.getAttribute("id"))||(e=jo(t,o),n=!1,(n=!(n=Vo(t)?xd("down",e,o).find(function(e){return!Vo(e)&&yd(e,o).includes(t)}):n)&&zo(t)?xd("right",e,o).find(function(e){return!zo(e)&&yd(e,o).includes(t)}):n)||a.relatedNodes(t),i=i&&n)}),!!i||void 0}function tp(e,t,n){if(!["SCRIPT","HEAD","TITLE","NOSCRIPT","STYLE","TEMPLATE"].includes(e.nodeName.toUpperCase())&&sl(n)){n=window.getComputedStyle(e);if("none"===n.getPropertyValue("display"))return;if("hidden"===n.getPropertyValue("visibility")){n=u(e),e=n&&window.getComputedStyle(n);if(!e||"hidden"!==e.getPropertyValue("visibility"))return}}return!0}function np(e,t){var n=/^aria-/,a=t.attrNames;if(a.length)for(var r=0,o=a.length;r<o;r++)if(n.test(a[r]))return!0;return!1}function ap(e,t){return null!==c(t,{dpub:!0,fallback:!0})}function rp(e,t){var n=/^aria-/;return t.attrNames.some(function(e){return n.test(e)})}function op(e){return function e(t){return!t||"true"!==t.getAttribute("aria-hidden")&&e(u(t))}(u(e))}function ip(e,t){return t=c(t,{dpub:!0}),!!ic(t)}function lp(e,t){return t=c(t),!!oc(t)}function sp(e,t){if(!(n=t.attr("autocomplete"))||""===C(n))return!1;if(n=t.props.nodeName,!1===["textarea","input","select"].includes(n))return!1;if("input"===n&&["submit","reset","button","hidden"].includes(t.props.type))return!1;if(n=t.attr("aria-disabled")||"false",t.hasAttr("disabled")||"true"===n.toLowerCase())return!1;var n=t.attr("role"),a=t.attr("tabindex");if("-1"===a&&n){n=A.ariaRoles[n];if(void 0===n||"widget"!==n.type)return!1}return!("-1"===a&&t.actualNode&&!F(t)&&!N(t))}rn=F;var up=function(e,t,n){return n.initiator};function cp(e,t,n){return!up(e,t,n)||!!e.querySelector("a[href]")}function dp(e,t){var n=(a=t.props).nodeName,a=a.type;if("option"!==n&&("select"!==n||e.options.length))if(!("input"===n&&["hidden","range","color","checkbox","radio","image"].includes(a)||md(t)||no(t))){if(["input","select","textarea"].includes(n)){a=window.getComputedStyle(e),a=parseInt(a.getPropertyValue("text-indent"),10);if(a){var r={top:(r=e.getBoundingClientRect()).top,bottom:r.bottom,left:r.left+a,right:r.right+a};if(!Ul(r,e))return!1}return!0}a=ur(t,"label");if("label"===n||a){r=a||e,n=a?D(a):t;if(r.htmlFor){a=E(r).getElementById(r.htmlFor),r=a&&D(a);if(r&&md(r))return!1}a=du(n,'input:not([type="hidden"],[type="image"],[type="button"],[type="submit"],[type="reset"]), select, textarea')[0];if(a&&md(a))return!1}for(var o,i=[],l=t;l;)l.props.id&&(o=zu(l).filter(function(e){return _(e.getAttribute("aria-labelledby")||"").includes(l.props.id)}).map(function(e){return D(e)}),i.push.apply(i,v(o))),l=l.parent;if(!(0<i.length&&i.every(md))&&""!==(n=Ai(r=t,!1,!0))&&""!==Gi(n,pp)&&r.children.some(function(e){return"#text"===e.props.nodeName&&!$i(e)})){for(var s=document.createRange(),u=t.children,c=0;c<u.length;c++){var d=u[c];3===d.actualNode.nodeType&&""!==C(d.actualNode.nodeValue)&&s.selectNodeContents(d.actualNode)}for(var p=s.getClientRects(),f=0;f<p.length;f++)if(Ul(p[f],e))return!0}}return!1}var pp={emoji:!0,nonBmp:!1,punctuations:!0};function fp(e){return!!wd(e)&&3<=(e=Lo(e)).length&&3<=e[0].length&&3<=e[1].length&&3<=e[2].length}function mp(e){return wd(e)}function hp(e){var t=e.getAttribute("id").trim(),t='*[id="'.concat(y(t),'"]'),t=Array.from(E(e).querySelectorAll(t));return!ec(e)&&t.some(k)}function gp(e){return ec(e)}function bp(e){var t=e.getAttribute("id").trim(),t='*[id="'.concat(y(t),'"]'),t=Array.from(E(e).querySelectorAll(t));return!ec(e)&&t.every(function(e){return!k(e)})}function yp(e,t,n){var a;return!n.initiator&&!n.focusable&&1<(null==(a=n.size)?void 0:a.width)*(null==(a=n.size)?void 0:a.height)}function vp(e){return e=e.getAttribute("title"),!!C(e)}function wp(e,t){return null!==li(t,{chromium:!0})}var Dp=function(e,t){try{return"svg"===t.props.nodeName?!0:!!x(t,"svg")}catch(e){return!1}};function xp(e,t){return!Dp(e,t)}function Ep(e,t){return!(!l(t)||(t=T(e))&&"link"!==t)}function Fp(e){return dl(e)}function Ap(e,t){var n=T(e);return!!(n&&Wu("widget").includes(n)&&Yu().includes(n)&&(C(To(t))||C(ko(e)))&&C(Ai(t)))}function Cp(e,t){return"input"!==t.props.nodeName||!1===t.hasAttr("type")||(t=t.attr("type").toLowerCase(),!1===["hidden","image","button","submit","reset"].includes(t))}function kp(e,t){return e.hasAttribute("role")||!ur(t,"article, aside, main, nav, section")}function Tp(e,t){var n,a,r,o=["article","aside","main","nav","section"].join(",");return n=(t=t).actualNode,a=Wu("landmark"),!!(r=T(n))&&("HEADER"===(n=n.nodeName.toUpperCase())||"FOOTER"===n?!x(t,o):"SECTION"===n||"FORM"===n?!!l(t):0<=a.indexOf(r)||"region"===r)&&N(e)}function Np(e){return!wd(e)&&!k(e)}function Rp(e){var t=C(e.innerText),n=e.getAttribute("role");return!(n&&"link"!==n||!t||!F(e))&&wl(e)}function Op(e,t){return!!(t=T(t))&&!!A.ariaRoles[t].childrenPresentational}function _p(e){return!!e.currentSrc&&!e.hasAttribute("paused")&&!e.hasAttribute("muted")}function Sp(e,t){return!!t.hasAttr("role")&&!!t.attr("role").trim()}var Ip=function(e,t){var n=c(t);return!(n&&!["none","presentation"].includes(n)&&!(Ro[n]||{}).accessibleNameRequired&&!k(t))};function Pp(e,t){var n=ii(t).namingMethods;return!(n&&0!==n.length||"combobox"===c(t)&&du(t,'input:not([type="hidden"])').length||tc(t,{popupRoles:["listbox"]}))}function Mp(e,t){return t=parseInt(t.attr("tabindex"),10),isNaN(t)||0<=t}function Bp(e,t){return!t.attr("role")}function Lp(e,t){return"html"!==t.props.nodeName}function jp(e){var t=Array.from(e.parentNode.childNodes),n=e.textContent.trim();return!(0===n.length||2<=(n.match(/[.!?:;](?![.!?:;])/g)||[]).length)&&0!==t.slice(t.indexOf(e)+1).filter(function(e){return"P"===e.nodeName.toUpperCase()&&""!==e.textContent.trim()}).length}function qp(e,t){return null!==li(t,{chromiumRoles:!0})}function Vp(e){return Ol(e)&&Pr(e)}var zp=[function(e,t){return $p(t)},function(e,t){return"area"!==t.props.nodeName},function(e,t){return!Dp(e,t)},function(e,t){return k(t)},function(e,t){return gl(t)||!Up(t)},function(e){return!wl(e,{noLengthCompare:!0})}];function $p(e){return"widget"===bl(e)}var Up=t(function e(t){return!(null==t||!t.parent)&&(!(!$p(t.parent)||!gl(t.parent))||e(t.parent))}),Hp={"abstractrole-evaluate":uc,"accesskeys-after":g1,"accesskeys-evaluate":b1,"alt-space-value-evaluate":k1,"aria-allowed-attr-evaluate":function(e,t,n){var a=[],r=T(n),o=n.attrNames,i=qu(r),l=(Array.isArray(t[r])&&(i=Qs(t[r].concat(i))),w.get("aria-allowed-attr-table",function(){return new WeakMap}));function s(){if(n.parent&&"row"===r){var e=x(n,'table, [role="treegrid"], [role="table"], [role="grid"]'),t=l.get(e);if(e&&!t&&(t=T(e),l.set(e,t)),["table","grid"].includes(t)&&"row"===r)return!0}}var t=Array.isArray(t.validTreeRowAttrs)?t.validTreeRowAttrs:[],u={};if(t.forEach(function(e){u[e]=s}),i)for(var c=0;c<o.length;c++){var d,p=o[c];(sc(p)&&null!=(d=u[p])&&d.call(u)||sc(p)&&!i.includes(p))&&a.push(p+'="'+n.attr(p)+'"')}return!a.length||(this.data(a),!(Ms(n)||r||k(n))&&void 0)},"aria-allowed-attr-matches":np,"aria-allowed-role-evaluate":cc,"aria-allowed-role-matches":ap,"aria-busy-evaluate":function(e,t,n){return"true"===n.attr("aria-busy")},"aria-errormessage-evaluate":dc,"aria-has-attr-matches":rp,"aria-hidden-body-evaluate":pc,"aria-hidden-focus-matches":op,"aria-label-evaluate":jd,"aria-labelledby-evaluate":qd,"aria-level-evaluate":fc,"aria-prohibited-attr-evaluate":function(e){var t,n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},a=2<arguments.length?arguments[2]:void 0,n=(null==n?void 0:n.elementsAllowedAriaLabel)||[],r=a.props.nodeName,o=T(a,{chromium:!0});return 0!==(n=function(e,t,n){var a=A.ariaRoles[e];if(a)return a.prohibitedAttrs||[];return e||n.includes(t)?[]:["aria-label","aria-labelledby"]}(o,r,n).filter(function(e){return!!a.attrNames.includes(e)&&""!==C(a.attr(e))})).length&&(t=a.hasAttr("role")?"hasRole":"noRole",t+=1<n.length?"Plural":"Singular",this.data({role:o,nodeName:r,messageKey:t,prohibited:n}),o=gi(a,{subtreeDescendant:!0}),""===C(o)||void 0)},"aria-required-attr-evaluate":function(e){var n,t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},a=2<arguments.length?arguments[2]:void 0,r=c(a),o=a.attrNames,i=rc(r);return Array.isArray(t[r])&&(i=Qs(t[r],i)),!(r&&o.length&&i.length&&(t=a,"separator"!==r||k(t))&&(o=a,"combobox"!==r||"false"!==o.attr("aria-expanded"))&&(n=ii(a),(t=i.filter(function(e){return!(a.attr(e)||(e=e,void 0!==(null==(t=(t=n).implicitAttrs)?void 0:t[e])));var t})).length&&(this.data(t),1)))},"aria-required-children-evaluate":function(e,t,n){var a,r,t=t&&Array.isArray(t.reviewEmpty)?t.reviewEmpty:[],o=c(n,{dpub:!0}),i=ic(o);return null===i||((r=(a=function(e,o){function t(e){if(1!==(e=l[e]).props.nodeType)return"continue";var t,n=T(e,{noPresentational:!0}),a=(t=e,Bo().find(function(e){return t.hasAttr(e)})),r=!!a||k(e);!N(e)||!n&&!r||["group","rowgroup"].includes(n)&&o.some(function(e){return e===n})?l.push.apply(l,v(e.children)):(n||r)&&i.push({role:n,attr:a||"tabindex",ownedElement:e})}for(var i=[],l=mi(e),n=0;n<l.length;n++)t(n);return i}(n,i)).filter(function(e){e=e.role;return!i.includes(e)})).length?(this.relatedNodes(r.map(function(e){return e.ownedElement})),this.data({messageKey:"unallowed",values:r.map(function(e){var t=e.ownedElement,e=e.attr,n=t.props,a=n.nodeName;return 3===n.nodeType?"#text":(n=c(t,{dpub:!0}))?"[role=".concat(n,"]"):e?a+"[".concat(e,"]"):a}).filter(function(e,t,n){return n.indexOf(e)===t}).join(", ")}),!1):!(r=function(n,a){for(var e=0;e<a.length;e++){var t=function(e){var t=a[e].role;if(n.includes(t))return n=n.filter(function(e){return e!==t}),{v:null}}(e);if("object"===te(t))return t.v}if(n.length)return n;return null}(i,a))||(this.data(r),!(!t.includes(o)||sl(n,!1,!0)||a.length||n.hasAttr("aria-owns")&&Ao(e,"aria-owns").length)&&void 0))},"aria-required-children-matches":ip,"aria-required-parent-evaluate":gc,"aria-required-parent-matches":lp,"aria-roledescription-evaluate":bc,"aria-unsupported-attr-evaluate":yc,"aria-valid-attr-evaluate":vc,"aria-valid-attr-value-evaluate":function(e,a,r){a=Array.isArray(a.value)?a.value:[];var o="",i="",l=[],s=/^aria-/,u=["aria-errormessage"],c={"aria-controls":function(){return"false"!==r.attr("aria-expanded")&&"false"!==r.attr("aria-selected")},"aria-current":function(e){e||(o='aria-current="'.concat(r.attr("aria-current"),'"'),i="ariaCurrent")},"aria-owns":function(){return"false"!==r.attr("aria-expanded")},"aria-describedby":function(e){e||(o='aria-describedby="'.concat(r.attr("aria-describedby"),'"'),i=axe._tree&&axe._tree[0]._hasShadowRoot?"noIdShadow":"noId")},"aria-labelledby":function(e){e||(o='aria-labelledby="'.concat(r.attr("aria-labelledby"),'"'),i=axe._tree&&axe._tree[0]._hasShadowRoot?"noIdShadow":"noId")}};return r.attrNames.forEach(function(t){if(!u.includes(t)&&!a.includes(t)&&s.test(t)){var e,n=r.attr(t);try{e=lc(r,t)}catch(e){return o="".concat(t,'="').concat(n,'"'),void(i="idrefs")}c[t]&&!c[t](e)||e||(""===n&&(e=t,"string"!==(null==(e=A.ariaAttrs[t])?void 0:e.type))?(o=t,i="empty"):l.push("".concat(t,'="').concat(n,'"')))}}),l.length?(this.data(l),!1):!o||void this.data({messageKey:i,needsReview:o})},"attr-non-space-content-evaluate":c1,"autocomplete-appropriate-evaluate":s1,"autocomplete-matches":sp,"autocomplete-valid-evaluate":u1,"avoid-inline-spacing-evaluate":Vd,"bypass-matches":cp,"caption-evaluate":Y1,"caption-faked-evaluate":Wd,"color-contrast-evaluate":function(e,t,n){var a=t.ignoreUnicode,r=t.ignoreLength,o=t.ignorePseudo,i=t.boldValue,l=t.boldTextPt,s=t.largeTextPt,u=t.contrastRatio,c=t.shadowOutlineEmMax,d=t.pseudoSizeThreshold;if(!F(e))return this.data({messageKey:"hidden"}),!0;var p=Ai(n,!1,!0);if(a&&(y=zi(a=p,b={nonBmp:!0}),a=""===C(Gi(a,b)),y)&&a)this.data({messageKey:"nonBmp"});else{var f,m,h,g,b=window.getComputedStyle(e),y=parseFloat(b.getPropertyValue("font-size")),a=b.getPropertyValue("font-weight"),i=parseFloat(a)>=i||"bold"===a,a=Math.ceil(72*y)/96,l=i&&a<l||!i&&a<s?u.normal:u.large,a=l.expected,s=l.minThreshold,u=l.maxThreshold,l=function(e,t){var n=t.pseudoSizeThreshold,n=void 0===n?.25:n,t=t.ignorePseudo,t=void 0!==t&&t;if(!t){var t=e.boundingClientRect,a=t.width*t.height*n;do{var r=Zc(e.actualNode,":before"),o=Zc(e.actualNode,":after");if(a<r+o)return e}while(e=e.parent)}}(n,{ignorePseudo:o,pseudoSizeThreshold:d});if(!l)return d=Xc(e,!(n=[]),o=Wc(e,n,c),t),m=f=t=null,0===(e=Vc(e,{minRatio:.001,maxRatio:c})).length?t=Jc(o,d):d&&o&&(m=[].concat(v(e),[o]).reduce(jc),c=Jc(o,d),e=Jc(o,m),h=Jc(m,d),(t=Math.max(c,e,h))!==c)&&(f=h<e?"shadowOnBgColor":"fgOnShadowColor"),c=a<t,"number"==typeof s&&("number"!=typeof t||t<s)||"number"==typeof u&&("number"!=typeof t||u<t)?(this.data({contrastRatio:t}),!0):(h=Math.floor(100*t)/100,null===o?g=R.get("bgColor"):c||(g=f),e=1===p.length,(s=1==h)?g=R.set("bgColor","equalRatio"):c||!e||r||(g="shortTextContent"),this.data({fgColor:d?d.toHexString():void 0,bgColor:o?o.toHexString():void 0,contrastRatio:h,fontSize:"".concat((72*y/96).toFixed(1),"pt (").concat(y,"px)"),fontWeight:i?"bold":"normal",messageKey:g,expectedContrastRatio:a+":1",shadowColor:m?m.toHexString():void 0}),null===d||null===o||s||e&&!r&&!c?(g=null,R.clear(),void this.relatedNodes(n)):(c||this.relatedNodes(n),c));this.data({fontSize:"".concat((72*y/96).toFixed(1),"pt (").concat(y,"px)"),fontWeight:i?"bold":"normal",messageKey:"pseudoContent",expectedContrastRatio:a+":1"}),this.relatedNodes(l.actualNode)}},"color-contrast-matches":dp,"css-orientation-lock-evaluate":Z1,"data-table-large-matches":fp,"data-table-matches":mp,"deprecatedrole-evaluate":function(e,t,n){var n=T(n,{dpub:!0,fallback:!0}),a=A.ariaRoles[n];return!(null==a||!a.deprecated||(this.data(n),0))},"dlitem-evaluate":U1,"doc-has-title-evaluate":zd,"duplicate-id-active-matches":hp,"duplicate-id-after":Bd,"duplicate-id-aria-matches":gp,"duplicate-id-evaluate":Ld,"duplicate-id-misc-matches":bp,"duplicate-img-label-evaluate":T1,"exists-evaluate":$d,"explicit-evaluate":N1,"fallbackrole-evaluate":wc,"focusable-content-evaluate":y1,"focusable-disabled-evaluate":v1,"focusable-element-evaluate":w1,"focusable-modal-open-evaluate":D1,"focusable-no-name-evaluate":x1,"focusable-not-tabbable-evaluate":E1,"frame-focusable-content-evaluate":function(e,t,n){if(n.children)try{return!n.children.some(function t(e){if(gl(e))return!0;if(!e.children){if(1===e.props.nodeType)throw new Error("Cannot determine children");return!1}return e.children.some(function(e){return t(e)})})}catch(e){}},"frame-focusable-content-matches":yp,"frame-tested-after":J1,"frame-tested-evaluate":X1,"frame-title-has-text-matches":vp,"has-alt-evaluate":Ud,"has-descendant-after":d1,"has-descendant-evaluate":p1,"has-global-aria-attribute-evaluate":Dc,"has-implicit-chromium-role-matches":wp,"has-lang-evaluate":V1,"has-text-content-evaluate":function(e,t,n){try{return""!==C(gi(n))}catch(e){}},"has-widget-role-evaluate":xc,"heading-matches":function(e,t){return"heading"===T(t)},"heading-order-after":function(e){(t=v(t=e)).sort(function(e,t){e=e.node,t=t.node;return e.ancestry.length-t.ancestry.length});var t,n=t.reduce(ld,[]).filter(function(e){return-1!==e.level});return e.forEach(function(e){e.result=function(e,t){var e=sd(t,e.node.ancestry),n=null!=(n=null==(n=t[e])?void 0:n.level)?n:-1,t=null!=(t=null==(t=t[e-1])?void 0:t.level)?t:-1;if(0===e)return!0;if(-1!==n)return n-t<=1}(e,n)}),e},"heading-order-evaluate":cd,"help-same-as-label-evaluate":R1,"hidden-content-evaluate":tp,"hidden-explicit-label-evaluate":O1,"html-namespace-matches":xp,"html5-scope-evaluate":Kd,"identical-links-same-purpose-after":dd,"identical-links-same-purpose-evaluate":Ed,"identical-links-same-purpose-matches":Ep,"implicit-evaluate":_1,"inline-style-property-evaluate":function(e,t){var n=t.cssProperty,a=t.absoluteValues,r=t.minValue,o=t.maxValue,i=void 0===(i=t.normalValue)?0:i,l=t.noImportant,t=t.multiLineOnly;return!!(!l&&"important"!==e.style.getPropertyPriority(n)||t&&!xl(e))||(l={},"number"==typeof r&&(l.minValue=r),"number"==typeof o&&(l.maxValue=o),t=e.style.getPropertyValue(n),["inherit","unset","revert","revert-layer"].includes(t)?(this.data(p({value:t},l)),!0):(t=function(e,t){var n=t.cssProperty,a=t.absoluteValues,t=t.normalValue,e=window.getComputedStyle(e),n=e.getPropertyValue(n);if("normal"===n)return t;t=parseFloat(n);if(a)return t;a=parseFloat(e.getPropertyValue("font-size")),e=Math.round(t/a*100)/100;if(isNaN(e))return n;return e}(e,{absoluteValues:a,cssProperty:n,normalValue:i}),this.data(p({value:t},l)),"number"==typeof t?("number"!=typeof r||r<=t)&&("number"!=typeof o||t<=o):void 0))},"inserted-into-focus-order-matches":Fp,"internal-link-present-evaluate":Fd,"invalid-children-evaluate":function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},n=2<arguments.length?arguments[2]:void 0,a=[],r=[];if(n.children){for(var o=H1(n.children);o.length;){var i=o.shift(),l=i.vChild,i=i.nested;if(t.divGroups&&!i&&"div"===(s=l).props.nodeName&&null===c(s)){if(!l.children)return;var s=H1(l.children,!0);o.push.apply(o,v(s))}else{s=function(e,t,n){var a=n.validRoles,a=void 0===a?[]:a,n=n.validNodeNames,n=void 0===n?[]:n,r=e.props,o=r.nodeName,i=r.nodeType,r=r.nodeValue,t=t?"div > ":"";if(3===i&&""!==r.trim())return t+"#text";if(1!==i||!N(e))return!1;r=c(e);return r?!a.includes(r)&&t+"[role=".concat(r,"]"):!n.includes(o)&&t+o}(l,i,t);s&&(r.includes(s)||r.push(s),1===(null==l||null==(i=l.actualNode)?void 0:i.nodeType))&&a.push(l.actualNode)}}return 0===r.length?!1:(this.data({values:r.join(", ")}),this.relatedNodes(a),!0)}},"invalidrole-evaluate":Ec,"is-element-focusable-evaluate":Fc,"is-initiator-matches":up,"is-on-screen-evaluate":rn,"is-visible-matches":F,"is-visible-on-screen-matches":function(e,t){return F(t)},"label-content-name-mismatch-evaluate":P1,"label-content-name-mismatch-matches":Ap,"label-matches":Cp,"landmark-has-body-context-matches":kp,"landmark-is-top-level-evaluate":F1,"landmark-is-unique-after":L1,"landmark-is-unique-evaluate":j1,"landmark-unique-matches":Tp,"layout-table-matches":Np,"link-in-text-block-evaluate":r1,"link-in-text-block-matches":Rp,"link-in-text-block-style-evaluate":l1,"listitem-evaluate":function(e,t,n){var a;if(n=n.parent)return a=n.props.nodeName,n=c(n),!!["presentation","none","list"].includes(n)||(n&&Po(n)?(this.data({messageKey:"roleNotValid"}),!1):["ul","ol","menu"].includes(a))},"matches-definition-evaluate":f1,"meta-refresh-evaluate":function(e,t,n){var a=(r=t||{}).minDelay,r=r.maxDelay;return!(n=h((n.attr("content")||"").trim().split(Ad),1)[0]).match(Cd)||(n=parseFloat(n),this.data({redirectDelay:n}),"number"==typeof a&&n<=t.minDelay)||"number"==typeof r&&n>t.maxDelay},"meta-viewport-scale-evaluate":ed,"multiple-label-evaluate":M1,"nested-interactive-matches":Op,"no-autoplay-audio-evaluate":Q1,"no-autoplay-audio-matches":_p,"no-empty-role-matches":Sp,"no-explicit-name-required-matches":Ip,"no-focusable-content-evaluate":function(e,t,n){if(n.children)try{var a,r=function t(e){if(!e.children){if(1===e.props.nodeType)throw new Error("Cannot determine children");return[]}var n=[];e.children.forEach(function(e){"widget"===bl(e)&&k(e)?n.push(e):n.push.apply(n,v(t(e)))});return n}(n);return r.length?(0<(a=r.filter(A1)).length?(this.data({messageKey:"notHidden"}),this.relatedNodes(a)):this.relatedNodes(r),!1):!0}catch(e){}},"no-implicit-explicit-label-evaluate":Ac,"no-naming-method-matches":Pp,"no-negative-tabindex-matches":Mp,"no-role-matches":Bp,"non-empty-if-present-evaluate":Hd,"not-html-matches":Lp,"object-is-loaded-matches":function(t,n){return[Ip,function(e){var t;return null==e||null==(t=e.ownerDocument)||!t.createRange||((t=e.ownerDocument.createRange()).setStart(e,0),t.setEnd(e,e.childNodes.length),0===t.getClientRects().length)}].every(function(e){return e(t,n)})},"only-dlitems-evaluate":G1,"only-listitems-evaluate":W1,"p-as-heading-evaluate":Nd,"p-as-heading-matches":jp,"page-no-duplicate-after":m1,"page-no-duplicate-evaluate":h1,"presentation-role-conflict-matches":qp,"presentational-role-evaluate":function(e,t,n){var a=c(n);if(["presentation","none"].includes(a)&&["iframe","frame"].includes(n.props.nodeName)&&n.hasAttr("title"))this.data({messageKey:"iframe",nodeName:n.props.nodeName});else{var r,o=T(n);if(["presentation","none"].includes(o))return this.data({role:o}),!0;["presentation","none"].includes(a)&&(a=Bo().some(function(e){return n.hasAttr(e)}),r=k(n),this.data({messageKey:a&&!r?"globalAria":!a&&r?"focusable":"both",role:o}))}return!1},"region-after":Rd,"region-evaluate":function(e,t,n){return this.data({isIframe:["iframe","frame"].includes(n.props.nodeName)}),!w.get("regionlessNodes",function(){return function t(e,n){var a=e.actualNode;{if("button"===T(e)||Sd(e,n)||["iframe","frame"].includes(e.props.nodeName)||Ol(e.actualNode)&&co(e.actualNode,"href")||!N(a)){for(var r=e;r;)r._hasRegionDescendant=!0,r=r.parent;return["iframe","frame"].includes(e.props.nodeName)?[e]:[]}return a!==document.body&&ul(a,!0)?[e]:e.children.filter(function(e){e=e.actualNode;return 1===e.nodeType}).map(function(e){return t(e,n)}).reduce(function(e,t){return e.concat(t)},[])}}(axe._tree[0],t).map(function(e){for(;e.parent&&!e.parent._hasRegionDescendant&&e.parent.actualNode!==document.body;)e=e.parent;return e}).filter(function(e,t,n){return n.indexOf(e)===t})}).includes(n)},"same-caption-summary-evaluate":Yd,"scope-value-evaluate":Xd,"scrollable-region-focusable-matches":function(e,t){return void 0!==Ts(e,13)&&!1===tc(t)&&du(t,"*").some(function(e){return sl(e,!0,!0)})},"skip-link-evaluate":Id,"skip-link-matches":Vp,"structured-dlitems-evaluate":K1,"svg-namespace-matches":Dp,"svg-non-empty-title-evaluate":Gd,"tabindex-evaluate":C1,"table-or-grid-role-matches":function(e,t){return t=T(t),["treegrid","grid","table"].includes(t)},"target-offset-evaluate":function(e,t,n){var a,r,o=(null==t?void 0:t.minOffset)||24,i=[],l=o,s=f(Qr(n,o));try{for(s.s();!(a=s.n()).done;){var u,c=a.value;"widget"===bl(c)&&k(c)&&(r=ho(n,c),o<=.05+(u=Math.round(10*r)/10)||(l=Math.min(l,u),i.push(c)))}}catch(e){s.e(e)}finally{s.f()}return 0===i.length?(this.data({closestOffset:l,minOffset:o}),!0):(this.relatedNodes(i.map(function(e){return e.actualNode})),i.some(gl)?(this.data({closestOffset:l,minOffset:o}),!gl(n)&&void 0):void this.data({messageKey:"nonTabbableNeighbor",closestOffset:l,minOffset:o}))},"target-size-evaluate":function(e,t,n){var a,r,t=(null==t?void 0:t.minSize)||24,o=n.boundingClientRect,i=od.bind(null,t),l=Qr(n),s=(a=n,l.filter(function(e){return!nd(e,a)&&rd(a,e)})),u=(l=function(e,t){var n,a=[],r=[],o=f(t);try{for(o.s();!(n=o.n()).done;){var i=n.value;!rd(e,i)&&bo(e,i)&&"none"!==i.getComputedStylePropertyValue("pointer-events")&&(nd(e,i)?a:r).push(i)}}catch(e){o.e(e)}finally{o.f()}return{fullyObscuringElms:a,partialObscuringElms:r}}(n,l)).fullyObscuringElms,l=l.partialObscuringElms;return u.length&&!s.length?(this.relatedNodes(id(u)),this.data({messageKey:"obscured"}),!0):(r=!gl(n)&&void 0,i(o)||s.length?(l=function(e,t){e=e.boundingClientRect;if(0===t.length)return null;t=t.map(function(e){return e.boundingClientRect});return function(e,a){return e.reduce(function(e,t){var n=od(a,e);return n!==od(a,t)?n?e:t:(n=e.width*e.height,t.width*t.height<n?e:t)})}(yo(e,t))}(n,n=l.filter(function(e){return"widget"===bl(e)&&k(e)})),!s.length||!u.length&&i(l||o)?0===n.length||i(l)?(this.data(p({minSize:t},ad(l||o))),this.relatedNodes(id(n)),!0):(u=n.every(gl),i="partiallyObscured".concat(u?"":"NonTabbable"),this.data(p({messageKey:i,minSize:t},ad(l))),this.relatedNodes(id(n)),u?r:void 0):(this.data({minSize:t,messageKey:"contentOverflow"}),void this.relatedNodes(id(s)))):(this.data(p({minSize:t},ad(o))),r))},"td-has-header-evaluate":Qd,"td-headers-attr-evaluate":Zd,"th-has-data-cells-evaluate":ep,"title-only-evaluate":B1,"unique-frame-title-after":Pd,"unique-frame-title-evaluate":Md,"unsupportedrole-evaluate":Cc,"valid-lang-evaluate":z1,"valid-scrollable-semantics-evaluate":Nc,"widget-not-inline-matches":function(t,n){return zp.every(function(e){return e(t,n)})},"window-is-top-matches":function(e){return e.ownerDocument.defaultView.self===e.ownerDocument.defaultView.top},"xml-lang-mismatch-evaluate":$1,"xml-lang-mismatch-matches":function(e){var t=rs(e.getAttribute("lang")),e=rs(e.getAttribute("xml:lang"));return Fu(t)&&Fu(e)}},Gp=function(e){this.id=e.id,this.data=null,this.relatedNodes=[],this.result=null};function Wp(e){if("string"!=typeof e)return e;if(Hp[e])return Hp[e];if(/^\s*function[\s\w]*\(/.test(e))return new Function("return "+e+";")();throw new ReferenceError("Function ID does not exist in the metadata-function-map: ".concat(e))}function Kp(e){var t=0<arguments.length&&void 0!==e?e:{};return t=!Array.isArray(t)&&"object"===te(t)?t:{value:t}}function Yp(e){e&&(this.id=e.id,this.configure(e))}Yp.prototype.enabled=!0,Yp.prototype.run=function(t,e,n,a,r){var o=((e=e||{}).hasOwnProperty("enabled")?e:this).enabled,i=this.getOptions(e.options);if(o){var l,o=new Gp(this),e=Zn(o,e,a,r);try{l=this.evaluate.call(e,t.actualNode,i,t,n)}catch(e){return t&&t.actualNode&&(e.errorNode=new Qn(t).toJSON()),void r(e)}e.isAsync||(o.result=l,a(o))}else a(null)},Yp.prototype.runSync=function(t,e,n){var a=(e=e||{}).enabled;if(!(void 0===a?this.enabled:a))return null;var r,a=this.getOptions(e.options),o=new Gp(this),e=Zn(o,e);e.async=function(){throw new Error("Cannot run async check while in a synchronous run")};try{r=this.evaluate.call(e,t.actualNode,a,t,n)}catch(e){throw t&&t.actualNode&&(e.errorNode=new Qn(t).toJSON()),e}return o.result=r,o},Yp.prototype.configure=function(t){var n=this;t.evaluate&&!Hp[t.evaluate]||(this._internalCheck=!0),t.hasOwnProperty("enabled")&&(this.enabled=t.enabled),t.hasOwnProperty("options")&&(this._internalCheck?this.options=Kp(t.options):this.options=t.options),["evaluate","after"].filter(function(e){return t.hasOwnProperty(e)}).forEach(function(e){return n[e]=Wp(t[e])})},Yp.prototype.getOptions=function(e){return this._internalCheck?nr(this.options,Kp(e||{})):e||this.options};var Jp=Yp,Xp=function(e){this.id=e.id,this.result=g.NA,this.pageLevel=e.pageLevel,this.impact=null,this.nodes=[]};function Qp(e,t){this._audit=t,this.id=e.id,this.selector=e.selector||"*",e.impact&&(d(g.impact.includes(e.impact),"Impact ".concat(e.impact," is not a valid impact")),this.impact=e.impact),this.excludeHidden="boolean"!=typeof e.excludeHidden||e.excludeHidden,this.enabled="boolean"!=typeof e.enabled||e.enabled,this.pageLevel="boolean"==typeof e.pageLevel&&e.pageLevel,this.reviewOnFail="boolean"==typeof e.reviewOnFail&&e.reviewOnFail,this.any=e.any||[],this.all=e.all||[],this.none=e.none||[],this.tags=e.tags||[],this.preload=!!e.preload,this.actIds=e.actIds,e.matches&&(this.matches=Wp(e.matches))}function Zp(e){var n,a;if(e.length)return n=!1,a={},e.forEach(function(e){var t=e.results.filter(function(e){return e});(a[e.type]=t).length&&(n=!0)}),n?a:null}Qp.prototype.matches=function(){return!0},Qp.prototype.gather=function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},n="mark_gather_start_"+this.id,a="mark_gather_end_"+this.id,r="mark_isVisibleToScreenReaders_start_"+this.id,o="mark_isVisibleToScreenReaders_end_"+this.id,i=(t.performanceTimer&&s.mark(n),gu(this.selector,e));return this.excludeHidden&&(t.performanceTimer&&s.mark(r),i=i.filter(N),t.performanceTimer)&&(s.mark(o),s.measure("rule_"+this.id+"#gather_axe.utils.isVisibleToScreenReaders",r,o)),t.performanceTimer&&(s.mark(a),s.measure("rule_"+this.id+"#gather",n,a)),i},Qp.prototype.runChecks=function(t,r,o,i,n,e){var l=this,s=ha();this[t].forEach(function(e){var n=l._audit.checks[e.id||e],a=fs(n,l.id,o);s.defer(function(e,t){n.run(r,a,i,e,t)})}),s.then(function(e){e=e.filter(function(e){return e}),n({type:t,results:e})}).catch(e)},Qp.prototype.runChecksSync=function(e,n,a,r){var o=this,i=[];return this[e].forEach(function(e){var e=o._audit.checks[e.id||e],t=fs(e,o.id,a);i.push(e.runSync(n,t,r))}),{type:e,results:i=i.filter(function(e){return e})}},Qp.prototype.run=function(r){var e,o=this,i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},t=2<arguments.length?arguments[2]:void 0,n=3<arguments.length?arguments[3]:void 0,l=(i.performanceTimer&&this._trackPerformance(),ha()),s=new Xp(this);try{e=this.gatherAndMatchNodes(r,i)}catch(e){return void n(new S({cause:e,ruleId:this.id}))}i.performanceTimer&&this._logGatherPerformance(e),e.forEach(function(a){l.defer(function(n,t){var e=ha();["any","all","none"].forEach(function(n){e.defer(function(e,t){o.runChecks(n,a,i,r,e,t)})}),e.then(function(e){var t=Zp(e);t&&(t.node=new Qn(a,i),s.nodes.push(t),o.reviewOnFail)&&(["any","all"].forEach(function(e){t[e].forEach(function(e){!1===e.result&&(e.result=void 0)})}),t.none.forEach(function(e){!0===e.result&&(e.result=void 0)})),n()}).catch(function(e){return t(e)})})}),l.defer(function(e){return setTimeout(e,0)}),i.performanceTimer&&this._logRulePerformance(),l.then(function(){return t(s)}).catch(function(e){return n(e)})},Qp.prototype.runSync=function(r){var e,o=this,i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},l=(i.performanceTimer&&this._trackPerformance(),new Xp(this));try{e=this.gatherAndMatchNodes(r,i)}catch(e){throw new S({cause:e,ruleId:this.id})}return i.performanceTimer&&this._logGatherPerformance(e),e.forEach(function(t){var n=[],a=(["any","all","none"].forEach(function(e){n.push(o.runChecksSync(e,t,i,r))}),Zp(n));a&&(a.node=t.actualNode?new Qn(t,i):null,l.nodes.push(a),o.reviewOnFail)&&(["any","all"].forEach(function(e){a[e].forEach(function(e){!1===e.result&&(e.result=void 0)})}),a.none.forEach(function(e){!0===e.result&&(e.result=void 0)}))}),i.performanceTimer&&this._logRulePerformance(),l},Qp.prototype._trackPerformance=function(){this._markStart="mark_rule_start_"+this.id,this._markEnd="mark_rule_end_"+this.id,this._markChecksStart="mark_runchecks_start_"+this.id,this._markChecksEnd="mark_runchecks_end_"+this.id},Qp.prototype._logGatherPerformance=function(e){sn("gather (",e.length,"):",s.timeElapsed()+"ms"),s.mark(this._markChecksStart)},Qp.prototype._logRulePerformance=function(){s.mark(this._markChecksEnd),s.mark(this._markEnd),s.measure("runchecks_"+this.id,this._markChecksStart,this._markChecksEnd),s.measure("rule_"+this.id,this._markStart,this._markEnd)},Qp.prototype.gatherAndMatchNodes=function(t,e){var n=this,a="mark_matches_start_"+this.id,r="mark_matches_end_"+this.id,o=this.gather(t,e);return e.performanceTimer&&s.mark(a),o=o.filter(function(e){return n.matches(e.actualNode,e,t)}),e.performanceTimer&&(s.mark(r),s.measure("rule_"+this.id+"#matches",a,r)),o},Qp.prototype.after=function(i,l){var t,e,n,s=this,a=Ja(t=this).map(function(e){e=t._audit.checks[e.id||e];return e&&"function"==typeof e.after?e:null}).filter(Boolean),u=this.id;return a.forEach(function(e){t=i.nodes,n=e.id,a=[],t.forEach(function(t){Ja(t).forEach(function(e){e.id===n&&(e.node=t.node,a.push(e))})});var n,a,t=a,r=fs(e,u,l),o=e.after(t,r);s.reviewOnFail&&o.forEach(function(e){var t=(s.any.includes(e.id)||s.all.includes(e.id))&&!1===e.result,n=s.none.includes(e.id)&&!0===e.result;(t||n)&&(e.result=void 0)}),t.forEach(function(e){delete e.node,-1===o.indexOf(e)&&(e.filtered=!0)})}),i.nodes=(e=["any","all","none"],n=(a=i).nodes.filter(function(t){var n=0;return e.forEach(function(e){t[e]=t[e].filter(function(e){return!0!==e.filtered}),n+=t[e].length}),0<n}),n=a.pageLevel&&n.length?[n.reduce(function(t,n){if(t)return e.forEach(function(e){t[e].push.apply(t[e],n[e])}),t})]:n),i},Qp.prototype.configure=function(e){e.hasOwnProperty("selector")&&(this.selector=e.selector),e.hasOwnProperty("excludeHidden")&&(this.excludeHidden="boolean"!=typeof e.excludeHidden||e.excludeHidden),e.hasOwnProperty("enabled")&&(this.enabled="boolean"!=typeof e.enabled||e.enabled),e.hasOwnProperty("pageLevel")&&(this.pageLevel="boolean"==typeof e.pageLevel&&e.pageLevel),e.hasOwnProperty("reviewOnFail")&&(this.reviewOnFail="boolean"==typeof e.reviewOnFail&&e.reviewOnFail),e.hasOwnProperty("any")&&(this.any=e.any),e.hasOwnProperty("all")&&(this.all=e.all),e.hasOwnProperty("none")&&(this.none=e.none),e.hasOwnProperty("tags")&&(this.tags=e.tags),e.hasOwnProperty("actIds")&&(this.actIds=e.actIds),e.hasOwnProperty("matches")&&(this.matches=Wp(e.matches)),e.impact&&(d(g.impact.includes(e.impact),"Impact ".concat(e.impact," is not a valid impact")),this.impact=e.impact)};var ef=Qp,tf=he(nn()),nf=/\{\{.+?\}\}/g;function af(){return window.origin&&"null"!==window.origin?window.origin:window.location&&window.location.origin&&"null"!==window.location.origin?window.location.origin:void 0}function rf(e,t,n){for(var a=0,r=e.length;a<r;a++)t[n](e[a])}function of(e){le(this,of),this.lang="en",this.defaultConfig=e,this.standards=A,this._init(),this._defaultLocale=null}function lf(a,e,r){return r.performanceTimer&&s.mark("mark_rule_start_"+a.id),function(t,n){a.run(e,r,function(e){t(e)},function(e){r.debug?n(e):(e=Object.assign(new Xp(a),{result:g.CANTTELL,description:"An error occured while running this rule",message:e.message,stack:e.stack,error:e,errorNode:e.errorNode}),t(e))})}}function sf(e,t,n){var a=e.brand,r=e.application,e=e.lang;return g.helpUrlBase+a+"/"+(n||axe.version.substring(0,axe.version.lastIndexOf(".")))+"/"+t+"?application="+encodeURIComponent(r)+(e&&"en"!==e?"&lang="+encodeURIComponent(e):"")}ue(of,[{key:"_setDefaultLocale",value:function(){if(!this._defaultLocale){for(var e={checks:{},rules:{},failureSummaries:{},incompleteFallbackMessage:"",lang:this.lang},t=Object.keys(this.data.checks),n=0;n<t.length;n++){var a=t[n],r=this.data.checks[a].messages,o=r.pass,i=r.fail,r=r.incomplete;e.checks[a]={pass:o,fail:i,incomplete:r}}for(var l=Object.keys(this.data.rules),s=0;s<l.length;s++){var u=l[s],c=this.data.rules[u],d=c.description,c=c.help;e.rules[u]={description:d,help:c}}for(var p=Object.keys(this.data.failureSummaries),f=0;f<p.length;f++){var m=p[f],h=this.data.failureSummaries[m].failureMessage;e.failureSummaries[m]={failureMessage:h}}e.incompleteFallbackMessage=this.data.incompleteFallbackMessage,this._defaultLocale=e}}},{key:"_resetLocale",value:function(){var e=this._defaultLocale;e&&this.applyLocale(e)}},{key:"_applyCheckLocale",value:function(e){for(var t,n,a,r=Object.keys(e),o=0;o<r.length;o++){var i=r[o];if(!this.data.checks[i])throw new Error('Locale provided for unknown check: "'.concat(i,'"'));this.data.checks[i]=(t=this.data.checks[i],i=e[i],a=n=void 0,n=i.pass,a=i.fail,"string"==typeof n&&nf.test(n)&&(n=tf.default.compile(n)),"string"==typeof a&&nf.test(a)&&(a=tf.default.compile(a)),p({},t,{messages:{pass:n||t.messages.pass,fail:a||t.messages.fail,incomplete:"object"===te(t.messages.incomplete)?p({},t.messages.incomplete,i.incomplete):i.incomplete}}))}}},{key:"_applyRuleLocale",value:function(e){for(var t,n,a=Object.keys(e),r=0;r<a.length;r++){var o=a[r];if(!this.data.rules[o])throw new Error('Locale provided for unknown rule: "'.concat(o,'"'));this.data.rules[o]=(t=this.data.rules[o],o=e[o],n=void 0,n=o.help,o=o.description,"string"==typeof n&&nf.test(n)&&(n=tf.default.compile(n)),"string"==typeof o&&nf.test(o)&&(o=tf.default.compile(o)),p({},t,{help:n||t.help,description:o||t.description}))}}},{key:"_applyFailureSummaries",value:function(e){for(var t=Object.keys(e),n=0;n<t.length;n++){var a=t[n];if(!this.data.failureSummaries[a])throw new Error('Locale provided for unknown failureMessage: "'.concat(a,'"'));this.data.failureSummaries[a]=function(e,t){t=t.failureMessage;return p({},e,{failureMessage:(t="string"==typeof t&&nf.test(t)?tf.default.compile(t):t)||e.failureMessage})}(this.data.failureSummaries[a],e[a])}}},{key:"applyLocale",value:function(e){var t,n;this._setDefaultLocale(),e.checks&&this._applyCheckLocale(e.checks),e.rules&&this._applyRuleLocale(e.rules),e.failureSummaries&&this._applyFailureSummaries(e.failureSummaries,"failureSummaries"),e.incompleteFallbackMessage&&(this.data.incompleteFallbackMessage=(t=this.data.incompleteFallbackMessage,(n="string"==typeof(n=e.incompleteFallbackMessage)&&nf.test(n)?tf.default.compile(n):n)||t)),e.lang&&(this.lang=e.lang)}},{key:"setAllowedOrigins",value:function(e){var t,n=af(),a=(this.allowedOrigins=[],f(e));try{for(a.s();!(t=a.n()).done;){var r=t.value;if(r===g.allOrigins)return void(this.allowedOrigins=["*"]);r!==g.sameOrigin?this.allowedOrigins.push(r):n&&this.allowedOrigins.push(n)}}catch(e){a.e(e)}finally{a.f()}}},{key:"_init",value:function(){(t=this.defaultConfig)?(e=ea(t)).commons=t.commons:e={},e.reporter=e.reporter||null,e.noHtml=e.noHtml||!1,e.allowedOrigins||(t=af(),e.allowedOrigins=t?[t]:[]),e.rules=e.rules||[],e.checks=e.checks||[],e.data=p({checks:{},rules:{}},e.data);var e,t=e;this.lang=t.lang||"en",this.reporter=t.reporter,this.commands={},this.rules=[],this.checks={},this.brand="axe",this.application="axeAPI",this.tagExclude=["experimental"],this.noHtml=t.noHtml,this.allowedOrigins=t.allowedOrigins,rf(t.rules,this,"addRule"),rf(t.checks,this,"addCheck"),this.data={},this.data.checks=t.data&&t.data.checks||{},this.data.rules=t.data&&t.data.rules||{},this.data.failureSummaries=t.data&&t.data.failureSummaries||{},this.data.incompleteFallbackMessage=t.data&&t.data.incompleteFallbackMessage||"",this._constructHelpUrls()}},{key:"registerCommand",value:function(e){this.commands[e.id]=e.callback}},{key:"addRule",value:function(e){e.metadata&&(this.data.rules[e.id]=e.metadata);var t=this.getRule(e.id);t?t.configure(e):this.rules.push(new ef(e,this))}},{key:"addCheck",value:function(e){var t=e.metadata;"object"===te(t)&&(this.data.checks[e.id]=t,"object"===te(t.messages))&&Object.keys(t.messages).filter(function(e){return t.messages.hasOwnProperty(e)&&"string"==typeof t.messages[e]}).forEach(function(e){0===t.messages[e].indexOf("function")&&(t.messages[e]=new Function("return "+t.messages[e]+";")())}),this.checks[e.id]?this.checks[e.id].configure(e):this.checks[e.id]=new Jp(e)}},{key:"run",value:function(r,o,i,l){this.normalizeOptions(o),axe._selectCache=[];e=this.rules,n=r,a=o;var n,a,e=e.reduce(function(e,t){return fu(t,n,a)&&(t.preload?e.later:e.now).push(t),e},{now:[],later:[]}),t=e.now,s=e.later,u=ha(),e=(t.forEach(function(e){u.defer(lf(e,r,o))}),ha()),t=(s.length&&e.defer(function(t){su(o).then(function(e){return t(e)}).catch(function(e){console.warn("Couldn't load preload assets: ",e),t(void 0)})}),ha());t.defer(u),t.defer(e),t.then(function(e){var t,n=e.pop(),a=(n&&n.length&&(n=n[0])&&(r=p({},r,n)),e[0]);s.length?(t=ha(),s.forEach(function(e){e=lf(e,r,o);t.defer(e)}),t.then(function(e){axe._selectCache=void 0,i(a.concat(e).filter(function(e){return!!e}))}).catch(l)):(axe._selectCache=void 0,i(a.filter(function(e){return!!e})))}).catch(l)}},{key:"after",value:function(e,n){var a=this.rules;return e.map(function(e){var t=Xa(a,"id",e.id);if(t)return t.after(e,n);throw new Error("Result for unknown rule. You may be running mismatch axe-core versions")})}},{key:"getRule",value:function(t){return this.rules.find(function(e){return e.id===t})}},{key:"normalizeOptions",value:function(e){var t=[],n=[];if(this.rules.forEach(function(e){n.push(e.id),e.tags.forEach(function(e){t.includes(e)||t.push(e)})}),["object","string"].includes(te(e.runOnly))){if("string"==typeof e.runOnly&&(e.runOnly=[e.runOnly]),Array.isArray(e.runOnly)){var a=e.runOnly.find(function(e){return t.includes(e)}),r=e.runOnly.find(function(e){return n.includes(e)});if(a&&r)throw new Error("runOnly cannot be both rules and tags");e.runOnly=r?{type:"rule",values:e.runOnly}:{type:"tag",values:e.runOnly}}a=e.runOnly;if(a.value&&!a.values&&(a.values=a.value,delete a.value),!Array.isArray(a.values)||0===a.values.length)throw new Error("runOnly.values must be a non-empty array");if(["rule","rules"].includes(a.type))a.type="rule",a.values.forEach(function(e){if(!n.includes(e))throw new Error("unknown rule `"+e+"` in options.runOnly")});else{if(!["tag","tags",void 0].includes(a.type))throw new Error("Unknown runOnly type '".concat(a.type,"'"));a.type="tag";r=a.values.filter(function(e){return!t.includes(e)&&!/wcag2[1-3]a{1,3}/.test(e)});0!==r.length&&axe.log("Could not find tags `"+r.join("`, `")+"`")}}return"object"===te(e.rules)&&Object.keys(e.rules).forEach(function(e){if(!n.includes(e))throw new Error("unknown rule `"+e+"` in options.rules")}),e}},{key:"setBranding",value:function(e){var t={brand:this.brand,application:this.application};"string"==typeof e&&(this.application=e),e&&e.hasOwnProperty("brand")&&e.brand&&"string"==typeof e.brand&&(this.brand=e.brand),e&&e.hasOwnProperty("application")&&e.application&&"string"==typeof e.application&&(this.application=e.application),this._constructHelpUrls(t)}},{key:"_constructHelpUrls",value:function(){var n=this,a=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null,r=(axe.version.match(/^[1-9][0-9]*\.[0-9]+/)||["x.y"])[0];this.rules.forEach(function(e){n.data.rules[e.id]||(n.data.rules[e.id]={});var t=n.data.rules[e.id];("string"!=typeof t.helpUrl||a&&t.helpUrl===sf(a,e.id,r))&&(t.helpUrl=sf(n,e.id,r))})}},{key:"resetRulesAndChecks",value:function(){this._init(),this._resetLocale()}}]);var uf=of;function cf(){w.get("globalDocumentSet")&&(w.set("globalDocumentSet",!1),document=null),w.get("globalWindowSet")&&(w.set("globalWindowSet",!1),window=null)}var df=function(){cf(),axe._memoizedFns.forEach(function(e){return e.clear()}),w.clear(),axe._tree=void 0,axe._selectorData=void 0,axe._selectCache=void 0},pf=function(n,a,r,o){try{n=new As(n),axe._tree=n.flatTree,axe._selectorData=zn(n.flatTree)}catch(e){return df(),o(e)}var e=ha(),i=axe._audit;a.performanceTimer&&s.auditStart(),n.frames.length&&!1!==a.iframes&&e.defer(function(e,t){er(n,a,"rules",null,e,t)}),e.defer(function(e,t){i.run(n,a,e,t)}),e.then(function(e){try{a.performanceTimer&&s.auditEnd();var t=Za(e.map(function(e){return{results:e}}));n.initiator&&((t=i.after(t,a)).forEach(cu),t=t.map(vn));try{r(t,df)}catch(e){df(),sn(e)}}catch(e){df(),o(e)}}).catch(function(e){df(),o(e)})};function ff(e,t,n){function a(e){e instanceof Error==!1&&(e=new Error(e)),n(e)}var r=n,o=e&&e.context||{},i=(o.hasOwnProperty("include")&&!o.include.length&&(o.include=[document]),e&&e.options||{});switch(e.command){case"rules":return pf(o,i,function(e,t){r(e),t()},a);case"cleanup-plugin":return Su(r,a);default:if(axe._audit&&axe._audit.commands&&axe._audit.commands[e.command])return axe._audit.commands[e.command](e,n)}}function mf(e){axe._audit=new uf(e)}function hf(e){this._run=e.run,this._collect=e.collect,this._registry={},e.commands.forEach(function(e){axe._audit.registerCommand(e)})}function gf(e){axe.plugins[e.id]=new hf(e)}function bf(){var e=axe._audit;if(!e)throw new Error("No audit configured");e.resetRulesAndChecks(),Object.keys(So).forEach(function(e){So[e]=_o[e]})}function yf(e){var t,e=h(e,3),n=e[0],a=e[1],e=e[2],r=new TypeError("axe.run arguments are invalid");if(!bs(t=n)&&!ys(t)){if(void 0!==e)throw r;e=a,a=n,n=document}if("object"!==te(a)){if(void 0!==e)throw r;e=a,a={}}if("function"!=typeof e&&void 0!==e)throw r;return(a=ea(a)).reporter=null!=(t=null!=(t=a.reporter)?t:null==(r=axe._audit)?void 0:r.reporter)?t:"v1",{context:n,options:a,callback:e}}window.top!==window&&(Ha.subscribe("axe.start",ff),Ha.subscribe("axe.ping",function(e,t,n){n({axe:!0})})),hf.prototype.run=function(){return this._run.apply(this,arguments)},hf.prototype.collect=function(){return this._collect.apply(this,arguments)},hf.prototype.cleanup=function(e){var n=axe.utils.queue(),a=this;Object.keys(this._registry).forEach(function(t){n.defer(function(e){a._registry[t].cleanup(e)})}),n.then(e)},hf.prototype.add=function(e){this._registry[e.id]=e};var vf=function(){};function wf(e){var t=e.node,n=m(e,$);n.node=t.toJSON();for(var a=0,r=["any","all","none"];a<r.length;a++){var o=r[a];n[o]=n[o].map(function(e){var t=e.relatedNodes;return p({},m(e,U),{relatedNodes:t.map(function(e){return e.toJSON()})})})}return n}function Df(e){if(axe._tree)throw new Error("Axe is already setup. Call `axe.teardown()` before calling `axe.setup` again.");return axe._tree=as(e),axe._selectorData=zn(axe._tree),axe._tree[0]}function xf(e,t,n){console.warn('"na" reporter will be deprecated in axe v4.0. Use the "v2" reporter instead.'),"function"==typeof t&&(n=t,t={});var a=t.environmentData,r=m(r=t,H);n(p({},ms(a),{toolOptions:r},ss(e,t)))}function Ef(e,t,n){"function"==typeof t&&(n=t,t={});var a=t.environmentData,r=m(r=t,G);t.resultTypes=["violations"],e=ss(e,t).violations,n(p({},ms(a),{toolOptions:r,violations:e}))}function Ff(e,t,n){"function"==typeof t&&(n=t,t={});var a=t.environmentData,t=m(t,K);kf(e,t,function(e){var t=ms(a);n({raw:e,env:t})})}function Af(e,t,n){function a(e){e.nodes.forEach(function(e){e.failureSummary=os(e)})}"function"==typeof t&&(n=t,t={});var r=t.environmentData,o=m(o=t,Y);(e=ss(e,t)).incomplete.forEach(a),e.violations.forEach(a),n(p({},ms(r),{toolOptions:o},e))}function Cf(e,t,n){"function"==typeof t&&(n=t,t={});var a=t.environmentData,r=m(r=t,J),e=ss(e,t);n(p({},ms(a),{toolOptions:r},e))}var kf=function(e,t,n){if("function"==typeof t&&(n=t,t={}),!e||!Array.isArray(e))return n(e);n(e.map(function(e){for(var t=p({},e),n=0,a=["passes","violations","incomplete","inapplicable"];n<a.length;n++){var r=a[n];t[r]&&Array.isArray(t[r])&&(t[r]=t[r].map(function(e){var t,n=e.node,e=m(e,W);return p({node:n="function"==typeof(null==(t=n)?void 0:t.toJSON)?n.toJSON():n},e)}))}return t}))},on={base:{Audit:uf,CheckResult:Gp,Check:Jp,Context:As,RuleResult:Xp,Rule:ef,metadataFunctionMap:Hp},public:{reporters:Iu},helpers:{failureSummary:os,incompleteFallbackMessage:is,processAggregate:ss},utils:{setDefaultFrameMessenger:$a,cacheNodeSelectors:es,getNodesMatchingExpression:Jl,convertSelector:la},commons:{dom:{nativelyHidden:br,displayHidden:yr,visibilityHidden:vr,contentVisibiltyHidden:wr,ariaHidden:Dr,opacityHidden:xr,scrollHidden:Er,overflowHidden:Fr,clipHidden:Ar,areaHidden:Cr,detailsHidden:kr}}};axe._thisWillBeDeletedDoNotUse=on,axe.constants=g,axe.log=sn,axe.AbstractVirtualNode=b,axe.SerialVirtualNode=Tu,axe.VirtualNode=Kl,axe._cache=w,axe.imports=Oo,axe.cleanup=Su,axe.configure=Bu,axe.frameMessenger=function(e){Ha.updateMessenger(e)},axe.getRules=Lu,axe._load=mf,axe.plugins={},axe.registerPlugin=gf,axe.hasReporter=Pu,axe.getReporter=Mu,axe.addReporter=function(e,t,n){Iu[e]=t,n&&(_u=t)},axe.reset=bf,axe._runRules=pf,axe.runVirtualRule=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{},a=(n.reporter=n.reporter||axe._audit.reporter||"v1",axe._selectorData={},t instanceof b||(t=new Tu(t)),ks(e));if(a)return a=(a=Object.create(a,{excludeHidden:{value:!1}})).runSync({initiator:!0,include:[t],exclude:[],frames:[],page:!1,focusable:!0,size:{},flatTree:[]},n),cu(a),vn(a),(a=xn([a])).violations.forEach(function(e){return e.nodes.forEach(function(e){e.failureSummary=os(e)})}),p({},ms(),a,{toolOptions:n});throw new Error("unknown rule `"+e+"`")},axe.run=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var a=t[0],r=window&&"Node"in window&&"NodeList"in window,o=!!document;if(!r||!o){if(!a||!a.ownerDocument)throw new Error('Required "window" or "document" globals not defined and cannot be deduced from the context. Either set the globals before running or pass in a valid Element.');o||(w.set("globalDocumentSet",!0),document=a.ownerDocument),r||(w.set("globalWindowSet",!0),window=document.defaultView)}var a=(o=yf(t)).context,i=o.options,l=void 0===(r=o.callback)?vf:r,r=(o=function(e){var t,n,a;"function"==typeof Promise&&e===vf?t=new Promise(function(e,t){n=t,a=e}):a=n=vf;return{thenable:t,reject:n,resolve:a}}(l)).thenable,s=o.resolve,u=o.reject;try{d(axe._audit,"No audit configured"),d(!axe._running,"Axe is already running. Use `await axe.run()` to wait for the previous run to finish before starting a new run.")}catch(e){var o=e,c=l;if(cf(),"function"!=typeof c||c===vf)throw o;return void c(o.message)}return axe._running=!0,i.performanceTimer&&axe.utils.performanceTimer.start(),axe._runRules(a,i,function(e,t){i.performanceTimer&&axe.utils.performanceTimer.end();try{var n=e,a=i,r=function(e){axe._running=!1,t();try{l(null,e)}catch(e){axe.log(e)}s(e)};void 0!==(n=Mu(a.reporter)(n,a,r))&&r(n)}catch(e){axe._running=!1,t(),l(e),u(e)}},function(e){i.performanceTimer&&axe.utils.performanceTimer.end(),axe._running=!1,cf(),l(e),u(e)}),r},axe.setup=Df,axe.teardown=df,axe.runPartial=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var a=(r=yf(t)).options,r=r.context,o=(d(axe._audit,"Axe is not configured. Audit is missing."),d(!axe._running,"Axe is already running. Use `await axe.run()` to wait for the previous run to finish before starting a new run."),new As(r,axe._tree));return axe._tree=o.flatTree,axe._selectorData=zn(o.flatTree),axe._running=!0,new Promise(function(e,t){axe._audit.run(o,a,e,t)}).then(function(e){e=e.map(function(e){var t=e.nodes,e=m(e,z);return p({nodes:t.map(wf)},e)});var t,n=o.frames.map(function(e){e=e.node;return new Qn(e,a).toJSON()});return o.initiator&&(t=ms()),axe._running=!1,df(),{results:e,frames:n,environmentData:t}}).catch(function(e){return axe._running=!1,df(),Promise.reject(e)})},axe.finishRun=function(e){var t,n=ea(n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}),a=(e.find(function(e){return e.environmentData})||{}).environmentData;axe._audit.normalizeOptions(n),n.reporter=null!=(d=null!=(d=n.reporter)?d:null==(d=axe._audit)?void 0:d.reporter)?d:"v1";var r=[],o=f(d=e);try{for(o.s();!(t=o.n()).done;){var i,l=t.value,s=r.shift();l&&(l.frameSpec=null!=s?s:null,i=function(e){var t=e.frames,n=e.frameSpec;return n?t.map(function(e){return Qn.mergeSpecs(e,n)}):t}(l),r.unshift.apply(r,v(i)))}}catch(e){o.e(e)}finally{o.f()}var u,c,d=Za(e);return(d=axe._audit.after(d,n)).forEach(cu),d=d.map(vn),u=d,c=p({environmentData:a},n),new Promise(function(e){Mu(c.reporter)(u,c,e)})},axe.commons=an,axe.utils=dn,axe.addReporter("na",xf),axe.addReporter("no-passes",Ef),axe.addReporter("rawEnv",Ff),axe.addReporter("raw",kf),axe.addReporter("v1",Af),axe.addReporter("v2",Cf,!0),axe._load({lang:"en",data:{rules:{accesskeys:{description:"Ensures every accesskey attribute value is unique",help:"accesskey attribute value should be unique"},"area-alt":{description:"Ensures <area> elements of image maps have alternate text",help:"Active <area> elements must have alternate text"},"aria-allowed-attr":{description:"Ensures ARIA attributes are allowed for an element's role",help:"Elements must only use allowed ARIA attributes"},"aria-allowed-role":{description:"Ensures role attribute has an appropriate value for the element",help:"ARIA role should be appropriate for the element"},"aria-command-name":{description:"Ensures every ARIA button, link and menuitem has an accessible name",help:"ARIA commands must have an accessible name"},"aria-dialog-name":{description:"Ensures every ARIA dialog and alertdialog node has an accessible name",help:"ARIA dialog and alertdialog nodes should have an accessible name"},"aria-hidden-body":{description:"Ensures aria-hidden='true' is not present on the document body.",help:"aria-hidden='true' must not be present on the document body"},"aria-hidden-focus":{description:"Ensures aria-hidden elements are not focusable nor contain focusable elements",help:"ARIA hidden element must not be focusable or contain focusable elements"},"aria-input-field-name":{description:"Ensures every ARIA input field has an accessible name",help:"ARIA input fields must have an accessible name"},"aria-meter-name":{description:"Ensures every ARIA meter node has an accessible name",help:"ARIA meter nodes must have an accessible name"},"aria-progressbar-name":{description:"Ensures every ARIA progressbar node has an accessible name",help:"ARIA progressbar nodes must have an accessible name"},"aria-required-attr":{description:"Ensures elements with ARIA roles have all required ARIA attributes",help:"Required ARIA attributes must be provided"},"aria-required-children":{description:"Ensures elements with an ARIA role that require child roles contain them",help:"Certain ARIA roles must contain particular children"},"aria-required-parent":{description:"Ensures elements with an ARIA role that require parent roles are contained by them",help:"Certain ARIA roles must be contained by particular parents"},"aria-roledescription":{description:"Ensure aria-roledescription is only used on elements with an implicit or explicit role",help:"aria-roledescription must be on elements with a semantic role"},"aria-roles":{description:"Ensures all elements with a role attribute use a valid value",help:"ARIA roles used must conform to valid values"},"aria-text":{description:'Ensures "role=text" is used on elements with no focusable descendants',help:'"role=text" should have no focusable descendants'},"aria-toggle-field-name":{description:"Ensures every ARIA toggle field has an accessible name",help:"ARIA toggle fields must have an accessible name"},"aria-tooltip-name":{description:"Ensures every ARIA tooltip node has an accessible name",help:"ARIA tooltip nodes must have an accessible name"},"aria-treeitem-name":{description:"Ensures every ARIA treeitem node has an accessible name",help:"ARIA treeitem nodes should have an accessible name"},"aria-valid-attr-value":{description:"Ensures all ARIA attributes have valid values",help:"ARIA attributes must conform to valid values"},"aria-valid-attr":{description:"Ensures attributes that begin with aria- are valid ARIA attributes",help:"ARIA attributes must conform to valid names"},"audio-caption":{description:"Ensures <audio> elements have captions",help:"<audio> elements must have a captions track"},"autocomplete-valid":{description:"Ensure the autocomplete attribute is correct and suitable for the form field",help:"autocomplete attribute must be used correctly"},"avoid-inline-spacing":{description:"Ensure that text spacing set through style attributes can be adjusted with custom stylesheets",help:"Inline text spacing must be adjustable with custom stylesheets"},blink:{description:"Ensures <blink> elements are not used",help:"<blink> elements are deprecated and must not be used"},"button-name":{description:"Ensures buttons have discernible text",help:"Buttons must have discernible text"},bypass:{description:"Ensures each page has at least one mechanism for a user to bypass navigation and jump straight to the content",help:"Page must have means to bypass repeated blocks"},"color-contrast-enhanced":{description:"Ensures the contrast between foreground and background colors meets WCAG 2 AAA enhanced contrast ratio thresholds",help:"Elements must meet enhanced color contrast ratio thresholds"},"color-contrast":{description:"Ensures the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds",help:"Elements must meet minimum color contrast ratio thresholds"},"css-orientation-lock":{description:"Ensures content is not locked to any specific display orientation, and the content is operable in all display orientations",help:"CSS Media queries must not lock display orientation"},"definition-list":{description:"Ensures <dl> elements are structured correctly",help:"<dl> elements must only directly contain properly-ordered <dt> and <dd> groups, <script>, <template> or <div> elements"},dlitem:{description:"Ensures <dt> and <dd> elements are contained by a <dl>",help:"<dt> and <dd> elements must be contained by a <dl>"},"document-title":{description:"Ensures each HTML document contains a non-empty <title> element",help:"Documents must have <title> element to aid in navigation"},"duplicate-id-active":{description:"Ensures every id attribute value of active elements is unique",help:"IDs of active elements must be unique"},"duplicate-id-aria":{description:"Ensures every id attribute value used in ARIA and in labels is unique",help:"IDs used in ARIA and labels must be unique"},"duplicate-id":{description:"Ensures every id attribute value is unique",help:"id attribute value must be unique"},"empty-heading":{description:"Ensures headings have discernible text",help:"Headings should not be empty"},"empty-table-header":{description:"Ensures table headers have discernible text",help:"Table header text should not be empty"},"focus-order-semantics":{description:"Ensures elements in the focus order have a role appropriate for interactive content",help:"Elements in the focus order should have an appropriate role"},"form-field-multiple-labels":{description:"Ensures form field does not have multiple label elements",help:"Form field must not have multiple label elements"},"frame-focusable-content":{description:"Ensures <frame> and <iframe> elements with focusable content do not have tabindex=-1",help:"Frames with focusable content must not have tabindex=-1"},"frame-tested":{description:"Ensures <iframe> and <frame> elements contain the axe-core script",help:"Frames should be tested with axe-core"},"frame-title-unique":{description:"Ensures <iframe> and <frame> elements contain a unique title attribute",help:"Frames must have a unique title attribute"},"frame-title":{description:"Ensures <iframe> and <frame> elements have an accessible name",help:"Frames must have an accessible name"},"heading-order":{description:"Ensures the order of headings is semantically correct",help:"Heading levels should only increase by one"},"hidden-content":{description:"Informs users about hidden content.",help:"Hidden content on the page should be analyzed"},"html-has-lang":{description:"Ensures every HTML document has a lang attribute",help:"<html> element must have a lang attribute"},"html-lang-valid":{description:"Ensures the lang attribute of the <html> element has a valid value",help:"<html> element must have a valid value for the lang attribute"},"html-xml-lang-mismatch":{description:"Ensure that HTML elements with both valid lang and xml:lang attributes agree on the base language of the page",help:"HTML elements with lang and xml:lang must have the same base language"},"identical-links-same-purpose":{description:"Ensure that links with the same accessible name serve a similar purpose",help:"Links with the same name must have a similar purpose"},"image-alt":{description:"Ensures <img> elements have alternate text or a role of none or presentation",help:"Images must have alternate text"},"image-redundant-alt":{description:"Ensure image alternative is not repeated as text",help:"Alternative text of images should not be repeated as text"},"input-button-name":{description:"Ensures input buttons have discernible text",help:"Input buttons must have discernible text"},"input-image-alt":{description:'Ensures <input type="image"> elements have alternate text',help:"Image buttons must have alternate text"},"label-content-name-mismatch":{description:"Ensures that elements labelled through their content must have their visible text as part of their accessible name",help:"Elements must have their visible text as part of their accessible name"},"label-title-only":{description:"Ensures that every form element has a visible label and is not solely labeled using hidden labels, or the title or aria-describedby attributes",help:"Form elements should have a visible label"},label:{description:"Ensures every form element has a label",help:"Form elements must have labels"},"landmark-banner-is-top-level":{description:"Ensures the banner landmark is at top level",help:"Banner landmark should not be contained in another landmark"},"landmark-complementary-is-top-level":{description:"Ensures the complementary landmark or aside is at top level",help:"Aside should not be contained in another landmark"},"landmark-contentinfo-is-top-level":{description:"Ensures the contentinfo landmark is at top level",help:"Contentinfo landmark should not be contained in another landmark"},"landmark-main-is-top-level":{description:"Ensures the main landmark is at top level",help:"Main landmark should not be contained in another landmark"},"landmark-no-duplicate-banner":{description:"Ensures the document has at most one banner landmark",help:"Document should not have more than one banner landmark"},"landmark-no-duplicate-contentinfo":{description:"Ensures the document has at most one contentinfo landmark",help:"Document should not have more than one contentinfo landmark"},"landmark-no-duplicate-main":{description:"Ensures the document has at most one main landmark",help:"Document should not have more than one main landmark"},"landmark-one-main":{description:"Ensures the document has a main landmark",help:"Document should have one main landmark"},"landmark-unique":{help:"Ensures landmarks are unique",description:"Landmarks should have a unique role or role/label/title (i.e. accessible name) combination"},"link-in-text-block":{description:"Ensure links are distinguished from surrounding text in a way that does not rely on color",help:"Links must be distinguishable without relying on color"},"link-name":{description:"Ensures links have discernible text",help:"Links must have discernible text"},list:{description:"Ensures that lists are structured correctly",help:"<ul> and <ol> must only directly contain <li>, <script> or <template> elements"},listitem:{description:"Ensures <li> elements are used semantically",help:"<li> elements must be contained in a <ul> or <ol>"},marquee:{description:"Ensures <marquee> elements are not used",help:"<marquee> elements are deprecated and must not be used"},"meta-refresh-no-exceptions":{description:'Ensures <meta http-equiv="refresh"> is not used for delayed refresh',help:"Delayed refresh must not be used"},"meta-refresh":{description:'Ensures <meta http-equiv="refresh"> is not used for delayed refresh',help:"Delayed refresh under 20 hours must not be used"},"meta-viewport-large":{description:'Ensures <meta name="viewport"> can scale a significant amount',help:"Users should be able to zoom and scale the text up to 500%"},"meta-viewport":{description:'Ensures <meta name="viewport"> does not disable text scaling and zooming',help:"Zooming and scaling must not be disabled"},"nested-interactive":{description:"Ensures interactive controls are not nested as they are not always announced by screen readers or can cause focus problems for assistive technologies",help:"Interactive controls must not be nested"},"no-autoplay-audio":{description:"Ensures <video> or <audio> elements do not autoplay audio for more than 3 seconds without a control mechanism to stop or mute the audio",help:"<video> or <audio> elements must not play automatically"},"object-alt":{description:"Ensures <object> elements have alternate text",help:"<object> elements must have alternate text"},"p-as-heading":{description:"Ensure bold, italic text and font-size is not used to style <p> elements as a heading",help:"Styled <p> elements must not be used as headings"},"page-has-heading-one":{description:"Ensure that the page, or at least one of its frames contains a level-one heading",help:"Page should contain a level-one heading"},"presentation-role-conflict":{description:"Elements marked as presentational should not have global ARIA or tabindex to ensure all screen readers ignore them",help:"Ensure elements marked as presentational are consistently ignored"},region:{description:"Ensures all page content is contained by landmarks",help:"All page content should be contained by landmarks"},"role-img-alt":{description:"Ensures [role='img'] elements have alternate text",help:"[role='img'] elements must have an alternative text"},"scope-attr-valid":{description:"Ensures the scope attribute is used correctly on tables",help:"scope attribute should be used correctly"},"scrollable-region-focusable":{description:"Ensure elements that have scrollable content are accessible by keyboard",help:"Scrollable region must have keyboard access"},"select-name":{description:"Ensures select element has an accessible name",help:"Select element must have an accessible name"},"server-side-image-map":{description:"Ensures that server-side image maps are not used",help:"Server-side image maps must not be used"},"skip-link":{description:"Ensure all skip links have a focusable target",help:"The skip-link target should exist and be focusable"},"svg-img-alt":{description:"Ensures <svg> elements with an img, graphics-document or graphics-symbol role have an accessible text",help:"<svg> elements with an img role must have an alternative text"},tabindex:{description:"Ensures tabindex attribute values are not greater than 0",help:"Elements should not have tabindex greater than zero"},"table-duplicate-name":{description:"Ensure the <caption> element does not contain the same text as the summary attribute",help:"tables should not have the same summary and caption"},"table-fake-caption":{description:"Ensure that tables with a caption use the <caption> element.",help:"Data or header cells must not be used to give caption to a data table."},"target-size":{description:"Ensure touch target have sufficient size and space",help:"All touch targets must be 24px large, or leave sufficient space"},"td-has-header":{description:"Ensure that each non-empty data cell in a <table> larger than 3 by 3  has one or more table headers",help:"Non-empty <td> elements in larger <table> must have an associated table header"},"td-headers-attr":{description:"Ensure that each cell in a table that uses the headers attribute refers only to other cells in that table",help:"Table cells that use the headers attribute must only refer to cells in the same table"},"th-has-data-cells":{description:"Ensure that <th> elements and elements with role=columnheader/rowheader have data cells they describe",help:"Table headers in a data table must refer to data cells"},"valid-lang":{description:"Ensures lang attributes have valid values",help:"lang attribute must have a valid value"},"video-caption":{description:"Ensures <video> elements have captions",help:"<video> elements must have captions"}},checks:{abstractrole:{impact:"serious",messages:{pass:"Abstract roles are not used",fail:{singular:"Abstract role cannot be directly used: ${data.values}",plural:"Abstract roles cannot be directly used: ${data.values}"}}},"aria-allowed-attr":{impact:"critical",messages:{pass:"ARIA attributes are used correctly for the defined role",fail:{singular:"ARIA attribute is not allowed: ${data.values}",plural:"ARIA attributes are not allowed: ${data.values}"},incomplete:"Check that there is no problem if the ARIA attribute is ignored on this element: ${data.values}"}},"aria-allowed-role":{impact:"minor",messages:{pass:"ARIA role is allowed for given element",fail:{singular:"ARIA role ${data.values} is not allowed for given element",plural:"ARIA roles ${data.values} are not allowed for given element"},incomplete:{singular:"ARIA role ${data.values} must be removed when the element is made visible, as it is not allowed for the element",plural:"ARIA roles ${data.values} must be removed when the element is made visible, as they are not allowed for the element"}}},"aria-busy":{impact:"serious",messages:{pass:"Element has an aria-busy attribute",fail:'Element uses aria-busy="true" while showing a loader'}},"aria-errormessage":{impact:"critical",messages:{pass:"aria-errormessage exists and references elements visible to screen readers that use a supported aria-errormessage technique",fail:{singular:"aria-errormessage value `${data.values}` must use a technique to announce the message (e.g., aria-live, aria-describedby, role=alert, etc.)",plural:"aria-errormessage values `${data.values}` must use a technique to announce the message (e.g., aria-live, aria-describedby, role=alert, etc.)",hidden:"aria-errormessage value `${data.values}` cannot reference a hidden element"},incomplete:{singular:"ensure aria-errormessage value `${data.values}` references an existing element",plural:"ensure aria-errormessage values `${data.values}` reference existing elements",idrefs:"unable to determine if aria-errormessage element exists on the page: ${data.values}"}}},"aria-hidden-body":{impact:"critical",messages:{pass:"No aria-hidden attribute is present on document body",fail:"aria-hidden=true should not be present on the document body"}},"aria-level":{impact:"serious",messages:{pass:"aria-level values are valid",incomplete:"aria-level values greater than 6 are not supported in all screenreader and browser combinations"}},"aria-prohibited-attr":{impact:"serious",messages:{pass:"ARIA attribute is allowed",fail:{hasRolePlural:'${data.prohibited} attributes cannot be used with role "${data.role}".',hasRoleSingular:'${data.prohibited} attribute cannot be used with role "${data.role}".',noRolePlural:"${data.prohibited} attributes cannot be used on a ${data.nodeName} with no valid role attribute.",noRoleSingular:"${data.prohibited} attribute cannot be used on a ${data.nodeName} with no valid role attribute."},incomplete:{hasRoleSingular:'${data.prohibited} attribute is not well supported with role "${data.role}".',hasRolePlural:'${data.prohibited} attributes are not well supported with role "${data.role}".',noRoleSingular:"${data.prohibited} attribute is not well supported on a ${data.nodeName} with no valid role attribute.",noRolePlural:"${data.prohibited} attributes are not well supported on a ${data.nodeName} with no valid role attribute."}}},"aria-required-attr":{impact:"critical",messages:{pass:"All required ARIA attributes are present",fail:{singular:"Required ARIA attribute not present: ${data.values}",plural:"Required ARIA attributes not present: ${data.values}"}}},"aria-required-children":{impact:"critical",messages:{pass:"Required ARIA children are present",fail:{singular:"Required ARIA child role not present: ${data.values}",plural:"Required ARIA children role not present: ${data.values}",unallowed:"Element has children which are not allowed: ${data.values}"},incomplete:{singular:"Expecting ARIA child role to be added: ${data.values}",plural:"Expecting ARIA children role to be added: ${data.values}"}}},"aria-required-parent":{impact:"critical",messages:{pass:"Required ARIA parent role present",fail:{singular:"Required ARIA parent role not present: ${data.values}",plural:"Required ARIA parents role not present: ${data.values}"}}},"aria-roledescription":{impact:"serious",messages:{pass:"aria-roledescription used on a supported semantic role",incomplete:"Check that the aria-roledescription is announced by supported screen readers",fail:"Give the element a role that supports aria-roledescription"}},"aria-unsupported-attr":{impact:"critical",messages:{pass:"ARIA attribute is supported",fail:"ARIA attribute is not widely supported in screen readers and assistive technologies: ${data.values}"}},"aria-valid-attr-value":{impact:"critical",messages:{pass:"ARIA attribute values are valid",fail:{singular:"Invalid ARIA attribute value: ${data.values}",plural:"Invalid ARIA attribute values: ${data.values}"},incomplete:{noId:"ARIA attribute element ID does not exist on the page: ${data.needsReview}",noIdShadow:"ARIA attribute element ID does not exist on the page or is a descendant of a different shadow DOM tree: ${data.needsReview}",ariaCurrent:'ARIA attribute value is invalid and will be treated as "aria-current=true": ${data.needsReview}',idrefs:"Unable to determine if ARIA attribute element ID exists on the page: ${data.needsReview}",empty:"ARIA attribute value is ignored while empty: ${data.needsReview}"}}},"aria-valid-attr":{impact:"critical",messages:{pass:"ARIA attribute name is valid",fail:{singular:"Invalid ARIA attribute name: ${data.values}",plural:"Invalid ARIA attribute names: ${data.values}"}}},deprecatedrole:{impact:"minor",messages:{pass:"ARIA role is not deprecated",fail:"The role used is deprecated: ${data}"}},fallbackrole:{impact:"serious",messages:{pass:"Only one role value used",fail:"Use only one role value, since fallback roles are not supported in older browsers",incomplete:"Use only role 'presentation' or 'none' since they are synonymous."}},"has-global-aria-attribute":{impact:"minor",messages:{pass:{singular:"Element has global ARIA attribute: ${data.values}",plural:"Element has global ARIA attributes: ${data.values}"},fail:"Element does not have global ARIA attribute"}},"has-widget-role":{impact:"minor",messages:{pass:"Element has a widget role.",fail:"Element does not have a widget role."}},invalidrole:{impact:"critical",messages:{pass:"ARIA role is valid",fail:{singular:"Role must be one of the valid ARIA roles: ${data.values}",plural:"Roles must be one of the valid ARIA roles: ${data.values}"}}},"is-element-focusable":{impact:"minor",messages:{pass:"Element is focusable.",fail:"Element is not focusable."}},"no-implicit-explicit-label":{impact:"moderate",messages:{pass:"There is no mismatch between a <label> and accessible name",incomplete:"Check that the <label> does not need be part of the ARIA ${data} field's name"}},unsupportedrole:{impact:"critical",messages:{pass:"ARIA role is supported",fail:"The role used is not widely supported in screen readers and assistive technologies: ${data}"}},"valid-scrollable-semantics":{impact:"minor",messages:{pass:"Element has valid semantics for an element in the focus order.",fail:"Element has invalid semantics for an element in the focus order."}},"color-contrast-enhanced":{impact:"serious",messages:{pass:"Element has sufficient color contrast of ${data.contrastRatio}",fail:{default:"Element has insufficient color contrast of ${data.contrastRatio} (foreground color: ${data.fgColor}, background color: ${data.bgColor}, font size: ${data.fontSize}, font weight: ${data.fontWeight}). Expected contrast ratio of ${data.expectedContrastRatio}",fgOnShadowColor:"Element has insufficient color contrast of ${data.contrastRatio} between the foreground and shadow color (foreground color: ${data.fgColor}, text-shadow color: ${data.shadowColor}, font size: ${data.fontSize}, font weight: ${data.fontWeight}). Expected contrast ratio of ${data.expectedContrastRatio}",shadowOnBgColor:"Element has insufficient color contrast of ${data.contrastRatio} between the shadow color and background color (text-shadow color: ${data.shadowColor}, background color: ${data.bgColor}, font size: ${data.fontSize}, font weight: ${data.fontWeight}). Expected contrast ratio of ${data.expectedContrastRatio}"},incomplete:{default:"Unable to determine contrast ratio",bgImage:"Element's background color could not be determined due to a background image",bgGradient:"Element's background color could not be determined due to a background gradient",imgNode:"Element's background color could not be determined because element contains an image node",bgOverlap:"Element's background color could not be determined because it is overlapped by another element",fgAlpha:"Element's foreground color could not be determined because of alpha transparency",elmPartiallyObscured:"Element's background color could not be determined because it's partially obscured by another element",elmPartiallyObscuring:"Element's background color could not be determined because it partially overlaps other elements",outsideViewport:"Element's background color could not be determined because it's outside the viewport",equalRatio:"Element has a 1:1 contrast ratio with the background",shortTextContent:"Element content is too short to determine if it is actual text content",nonBmp:"Element content contains only non-text characters",pseudoContent:"Element's background color could not be determined due to a pseudo element"}}},"color-contrast":{impact:"serious",messages:{pass:{default:"Element has sufficient color contrast of ${data.contrastRatio}",hidden:"Element is hidden"},fail:{default:"Element has insufficient color contrast of ${data.contrastRatio} (foreground color: ${data.fgColor}, background color: ${data.bgColor}, font size: ${data.fontSize}, font weight: ${data.fontWeight}). Expected contrast ratio of ${data.expectedContrastRatio}",fgOnShadowColor:"Element has insufficient color contrast of ${data.contrastRatio} between the foreground and shadow color (foreground color: ${data.fgColor}, text-shadow color: ${data.shadowColor}, font size: ${data.fontSize}, font weight: ${data.fontWeight}). Expected contrast ratio of ${data.expectedContrastRatio}",shadowOnBgColor:"Element has insufficient color contrast of ${data.contrastRatio} between the shadow color and background color (text-shadow color: ${data.shadowColor}, background color: ${data.bgColor}, font size: ${data.fontSize}, font weight: ${data.fontWeight}). Expected contrast ratio of ${data.expectedContrastRatio}"},incomplete:{default:"Unable to determine contrast ratio",bgImage:"Element's background color could not be determined due to a background image",bgGradient:"Element's background color could not be determined due to a background gradient",imgNode:"Element's background color could not be determined because element contains an image node",bgOverlap:"Element's background color could not be determined because it is overlapped by another element",fgAlpha:"Element's foreground color could not be determined because of alpha transparency",elmPartiallyObscured:"Element's background color could not be determined because it's partially obscured by another element",elmPartiallyObscuring:"Element's background color could not be determined because it partially overlaps other elements",outsideViewport:"Element's background color could not be determined because it's outside the viewport",equalRatio:"Element has a 1:1 contrast ratio with the background",shortTextContent:"Element content is too short to determine if it is actual text content",nonBmp:"Element content contains only non-text characters",pseudoContent:"Element's background color could not be determined due to a pseudo element"}}},"link-in-text-block-style":{impact:"serious",messages:{pass:"Links can be distinguished from surrounding text by visual styling",fail:"The link has no styling (such as underline) to distinguish it from the surrounding text"}},"link-in-text-block":{impact:"serious",messages:{pass:"Links can be distinguished from surrounding text in some way other than by color",fail:{fgContrast:"The link has insufficient color contrast of ${data.contrastRatio}:1 with the surrounding text. (Minimum contrast is ${data.requiredContrastRatio}:1, link text: ${data.nodeColor}, surrounding text: ${data.parentColor})",bgContrast:"The link background has insufficient color contrast of ${data.contrastRatio} (Minimum contrast is ${data.requiredContrastRatio}:1, link background color: ${data.nodeBackgroundColor}, surrounding background color: ${data.parentBackgroundColor})"},incomplete:{default:"Element's foreground contrast ratio could not be determined",bgContrast:"Element's background contrast ratio could not be determined",bgImage:"Element's contrast ratio could not be determined due to a background image",bgGradient:"Element's contrast ratio could not be determined due to a background gradient",imgNode:"Element's contrast ratio could not be determined because element contains an image node",bgOverlap:"Element's contrast ratio could not be determined because of element overlap"}}},"autocomplete-appropriate":{impact:"serious",messages:{pass:"the autocomplete value is on an appropriate element",fail:"the autocomplete value is inappropriate for this type of input"}},"autocomplete-valid":{impact:"serious",messages:{pass:"the autocomplete attribute is correctly formatted",fail:"the autocomplete attribute is incorrectly formatted"}},accesskeys:{impact:"serious",messages:{pass:"Accesskey attribute value is unique",fail:"Document has multiple elements with the same accesskey"}},"focusable-content":{impact:"serious",messages:{pass:"Element contains focusable elements",fail:"Element should have focusable content"}},"focusable-disabled":{impact:"serious",messages:{pass:"No focusable elements contained within element",incomplete:"Check if the focusable elements immediately move the focus indicator",fail:"Focusable content should be disabled or be removed from the DOM"}},"focusable-element":{impact:"serious",messages:{pass:"Element is focusable",fail:"Element should be focusable"}},"focusable-modal-open":{impact:"serious",messages:{pass:"No focusable elements while a modal is open",incomplete:"Check that focusable elements are not tabbable in the current state"}},"focusable-no-name":{impact:"serious",messages:{pass:"Element is not in tab order or has accessible text",fail:"Element is in tab order and does not have accessible text",incomplete:"Unable to determine if element has an accessible name"}},"focusable-not-tabbable":{impact:"serious",messages:{pass:"No focusable elements contained within element",incomplete:"Check if the focusable elements immediately move the focus indicator",fail:"Focusable content should have tabindex='-1' or be removed from the DOM"}},"frame-focusable-content":{impact:"serious",messages:{pass:"Element does not have focusable descendants",fail:"Element has focusable descendants",incomplete:"Could not determine if element has descendants"}},"landmark-is-top-level":{impact:"moderate",messages:{pass:"The ${data.role} landmark is at the top level.",fail:"The ${data.role} landmark is contained in another landmark."}},"no-focusable-content":{impact:"serious",messages:{pass:"Element does not have focusable descendants",fail:{default:"Element has focusable descendants",notHidden:"Using a negative tabindex on an element inside an interactive control does not prevent assistive technologies from focusing the element (even with 'aria-hidden=true')"},incomplete:"Could not determine if element has descendants"}},"page-has-heading-one":{impact:"moderate",messages:{pass:"Page has at least one level-one heading",fail:"Page must have a level-one heading"}},"page-has-main":{impact:"moderate",messages:{pass:"Document has at least one main landmark",fail:"Document does not have a main landmark"}},"page-no-duplicate-banner":{impact:"moderate",messages:{pass:"Document does not have more than one banner landmark",fail:"Document has more than one banner landmark"}},"page-no-duplicate-contentinfo":{impact:"moderate",messages:{pass:"Document does not have more than one contentinfo landmark",fail:"Document has more than one contentinfo landmark"}},"page-no-duplicate-main":{impact:"moderate",messages:{pass:"Document does not have more than one main landmark",fail:"Document has more than one main landmark"}},tabindex:{impact:"serious",messages:{pass:"Element does not have a tabindex greater than 0",fail:"Element has a tabindex greater than 0"}},"alt-space-value":{impact:"critical",messages:{pass:"Element has a valid alt attribute value",fail:"Element has an alt attribute containing only a space character, which is not ignored by all screen readers"}},"duplicate-img-label":{impact:"minor",messages:{pass:"Element does not duplicate existing text in <img> alt text",fail:"Element contains <img> element with alt text that duplicates existing text"}},"explicit-label":{impact:"critical",messages:{pass:"Form element has an explicit <label>",fail:"Form element does not have an explicit <label>",incomplete:"Unable to determine if form element has an explicit <label>"}},"help-same-as-label":{impact:"minor",messages:{pass:"Help text (title or aria-describedby) does not duplicate label text",fail:"Help text (title or aria-describedby) text is the same as the label text"}},"hidden-explicit-label":{impact:"critical",messages:{pass:"Form element has a visible explicit <label>",fail:"Form element has explicit <label> that is hidden",incomplete:"Unable to determine if form element has explicit <label> that is hidden"}},"implicit-label":{impact:"critical",messages:{pass:"Form element has an implicit (wrapped) <label>",fail:"Form element does not have an implicit (wrapped) <label>",incomplete:"Unable to determine if form element has an implicit (wrapped} <label>"}},"label-content-name-mismatch":{impact:"serious",messages:{pass:"Element contains visible text as part of it's accessible name",fail:"Text inside the element is not included in the accessible name"}},"multiple-label":{impact:"moderate",messages:{pass:"Form field does not have multiple label elements",incomplete:"Multiple label elements is not widely supported in assistive technologies. Ensure the first label contains all necessary information."}},"title-only":{impact:"serious",messages:{pass:"Form element does not solely use title attribute for its label",fail:"Only title used to generate label for form element"}},"landmark-is-unique":{impact:"moderate",messages:{pass:"Landmarks must have a unique role or role/label/title (i.e. accessible name) combination",fail:"The landmark must have a unique aria-label, aria-labelledby, or title to make landmarks distinguishable"}},"has-lang":{impact:"serious",messages:{pass:"The <html> element has a lang attribute",fail:{noXHTML:"The xml:lang attribute is not valid on HTML pages, use the lang attribute.",noLang:"The <html> element does not have a lang attribute"}}},"valid-lang":{impact:"serious",messages:{pass:"Value of lang attribute is included in the list of valid languages",fail:"Value of lang attribute not included in the list of valid languages"}},"xml-lang-mismatch":{impact:"moderate",messages:{pass:"Lang and xml:lang attributes have the same base language",fail:"Lang and xml:lang attributes do not have the same base language"}},dlitem:{impact:"serious",messages:{pass:"Description list item has a <dl> parent element",fail:"Description list item does not have a <dl> parent element"}},listitem:{impact:"serious",messages:{pass:'List item has a <ul>, <ol> or role="list" parent element',fail:{default:"List item does not have a <ul>, <ol> parent element",roleNotValid:'List item does not have a <ul>, <ol> parent element without a role, or a role="list"'}}},"only-dlitems":{impact:"serious",messages:{pass:"dl element only has direct children that are allowed inside; <dt>, <dd>, or <div> elements",fail:"dl element has direct children that are not allowed: ${data.values}"}},"only-listitems":{impact:"serious",messages:{pass:"List element only has direct children that are allowed inside <li> elements",fail:"List element has direct children that are not allowed: ${data.values}"}},"structured-dlitems":{impact:"serious",messages:{pass:"When not empty, element has both <dt> and <dd> elements",fail:"When not empty, element does not have at least one <dt> element followed by at least one <dd> element"}},caption:{impact:"critical",messages:{pass:"The multimedia element has a captions track",incomplete:"Check that captions is available for the element"}},"frame-tested":{impact:"critical",messages:{pass:"The iframe was tested with axe-core",fail:"The iframe could not be tested with axe-core",incomplete:"The iframe still has to be tested with axe-core"}},"no-autoplay-audio":{impact:"moderate",messages:{pass:"<video> or <audio> does not output audio for more than allowed duration or has controls mechanism",fail:"<video> or <audio> outputs audio for more than allowed duration and does not have a controls mechanism",incomplete:"Check that the <video> or <audio> does not output audio for more than allowed duration or provides a controls mechanism"}},"css-orientation-lock":{impact:"serious",messages:{pass:"Display is operable, and orientation lock does not exist",fail:"CSS Orientation lock is applied, and makes display inoperable",incomplete:"CSS Orientation lock cannot be determined"}},"meta-viewport-large":{impact:"minor",messages:{pass:"<meta> tag does not prevent significant zooming on mobile devices",fail:"<meta> tag limits zooming on mobile devices"}},"meta-viewport":{impact:"critical",messages:{pass:"<meta> tag does not disable zooming on mobile devices",fail:"${data} on <meta> tag disables zooming on mobile devices"}},"target-offset":{impact:"serious",messages:{pass:"Target has sufficient offset from its closest neighbor (${data.closestOffset}px should be at least ${data.minOffset}px)",fail:"Target has insufficient offset from its closest neighbor (${data.closestOffset}px should be at least ${data.minOffset}px)",incomplete:{default:"Element with negative tabindex has insufficient offset from its closest neighbor (${data.closestOffset}px should be at least ${data.minOffset}px). Is this a target?",nonTabbableNeighbor:"Target has insufficient offset from a neighbor with negative tabindex (${data.closestOffset}px should be at least ${data.minOffset}px). Is the neighbor a target?"}}},"target-size":{impact:"serious",messages:{pass:{default:"Control has sufficient size (${data.width}px by ${data.height}px, should be at least ${data.minSize}px by ${data.minSize}px)",obscured:"Control is ignored because it is fully obscured and thus not clickable"},fail:{default:"Target has insufficient size (${data.width}px by ${data.height}px, should be at least ${data.minSize}px by ${data.minSize}px)",partiallyObscured:"Target has insufficient size because it is partially obscured (smallest space is ${data.width}px by ${data.height}px, should be at least ${data.minSize}px by ${data.minSize}px)"},incomplete:{default:"Element with negative tabindex has insufficient size (${data.width}px by ${data.height}px, should be at least ${data.minSize}px by ${data.minSize}px). Is this a target?",contentOverflow:"Element size could not be accurately determined due to overflow content",partiallyObscured:"Element with negative tabindex has insufficient size because it is partially obscured (smallest space is ${data.width}px by ${data.height}px, should be at least ${data.minSize}px by ${data.minSize}px). Is this a target?",partiallyObscuredNonTabbable:"Target has insufficient size because it is partially obscured by a neighbor with negative tabindex (smallest space is ${data.width}px by ${data.height}px, should be at least ${data.minSize}px by ${data.minSize}px). Is the neighbor a target?"}}},"header-present":{impact:"serious",messages:{pass:"Page has a heading",fail:"Page does not have a heading"}},"heading-order":{impact:"moderate",messages:{pass:"Heading order valid",fail:"Heading order invalid",incomplete:"Unable to determine previous heading"}},"identical-links-same-purpose":{impact:"minor",messages:{pass:"There are no other links with the same name, that go to a different URL",incomplete:"Check that links have the same purpose, or are intentionally ambiguous."}},"internal-link-present":{impact:"serious",messages:{pass:"Valid skip link found",fail:"No valid skip link found"}},landmark:{impact:"serious",messages:{pass:"Page has a landmark region",fail:"Page does not have a landmark region"}},"meta-refresh-no-exceptions":{impact:"minor",messages:{pass:"<meta> tag does not immediately refresh the page",fail:"<meta> tag forces timed refresh of page"}},"meta-refresh":{impact:"critical",messages:{pass:"<meta> tag does not immediately refresh the page",fail:"<meta> tag forces timed refresh of page (less than 20 hours)"}},"p-as-heading":{impact:"serious",messages:{pass:"<p> elements are not styled as headings",fail:"Heading elements should be used instead of styled <p> elements",incomplete:"Unable to determine if <p> elements are styled as headings"}},region:{impact:"moderate",messages:{pass:"All page content is contained by landmarks",fail:"Some page content is not contained by landmarks"}},"skip-link":{impact:"moderate",messages:{pass:"Skip link target exists",incomplete:"Skip link target should become visible on activation",fail:"No skip link target"}},"unique-frame-title":{impact:"serious",messages:{pass:"Element's title attribute is unique",fail:"Element's title attribute is not unique"}},"duplicate-id-active":{impact:"serious",messages:{pass:"Document has no active elements that share the same id attribute",fail:"Document has active elements with the same id attribute: ${data}"}},"duplicate-id-aria":{impact:"critical",messages:{pass:"Document has no elements referenced with ARIA or labels that share the same id attribute",fail:"Document has multiple elements referenced with ARIA with the same id attribute: ${data}"}},"duplicate-id":{impact:"minor",messages:{pass:"Document has no static elements that share the same id attribute",fail:"Document has multiple static elements with the same id attribute: ${data}"}},"aria-label":{impact:"serious",messages:{pass:"aria-label attribute exists and is not empty",fail:"aria-label attribute does not exist or is empty"}},"aria-labelledby":{impact:"serious",messages:{pass:"aria-labelledby attribute exists and references elements that are visible to screen readers",fail:"aria-labelledby attribute does not exist, references elements that do not exist or references elements that are empty",incomplete:"ensure aria-labelledby references an existing element"}},"avoid-inline-spacing":{impact:"serious",messages:{pass:"No inline styles with '!important' that affect text spacing has been specified",fail:{singular:"Remove '!important' from inline style ${data.values}, as overriding this is not supported by most browsers",plural:"Remove '!important' from inline styles ${data.values}, as overriding this is not supported by most browsers"}}},"button-has-visible-text":{impact:"critical",messages:{pass:"Element has inner text that is visible to screen readers",fail:"Element does not have inner text that is visible to screen readers",incomplete:"Unable to determine if element has children"}},"doc-has-title":{impact:"serious",messages:{pass:"Document has a non-empty <title> element",fail:"Document does not have a non-empty <title> element"}},exists:{impact:"minor",messages:{pass:"Element does not exist",incomplete:"Element exists"}},"has-alt":{impact:"critical",messages:{pass:"Element has an alt attribute",fail:"Element does not have an alt attribute"}},"has-visible-text":{impact:"minor",messages:{pass:"Element has text that is visible to screen readers",fail:"Element does not have text that is visible to screen readers",incomplete:"Unable to determine if element has children"}},"important-letter-spacing":{impact:"serious",messages:{pass:"Letter-spacing in the style attribute is not set to !important, or meets the minimum",fail:"letter-spacing in the style attribute must not use !important, or be at ${data.minValue}em (current ${data.value}em)"}},"important-line-height":{impact:"serious",messages:{pass:"line-height in the style attribute is not set to !important, or meets the minimum",fail:"line-height in the style attribute must not use !important, or be at ${data.minValue}em (current ${data.value}em)"}},"important-word-spacing":{impact:"serious",messages:{pass:"word-spacing in the style attribute is not set to !important, or meets the minimum",fail:"word-spacing in the style attribute must not use !important, or be at ${data.minValue}em (current ${data.value}em)"}},"is-on-screen":{impact:"serious",messages:{pass:"Element is not visible",fail:"Element is visible"}},"non-empty-alt":{impact:"critical",messages:{pass:"Element has a non-empty alt attribute",fail:{noAttr:"Element has no alt attribute",emptyAttr:"Element has an empty alt attribute"}}},"non-empty-if-present":{impact:"critical",messages:{pass:{default:"Element does not have a value attribute","has-label":"Element has a non-empty value attribute"},fail:"Element has a value attribute and the value attribute is empty"}},"non-empty-placeholder":{impact:"serious",messages:{pass:"Element has a placeholder attribute",fail:{noAttr:"Element has no placeholder attribute",emptyAttr:"Element has an empty placeholder attribute"}}},"non-empty-title":{impact:"serious",messages:{pass:"Element has a title attribute",fail:{noAttr:"Element has no title attribute",emptyAttr:"Element has an empty title attribute"}}},"non-empty-value":{impact:"critical",messages:{pass:"Element has a non-empty value attribute",fail:{noAttr:"Element has no value attribute",emptyAttr:"Element has an empty value attribute"}}},"presentational-role":{impact:"minor",messages:{pass:'Element\'s default semantics were overriden with role="${data.role}"',fail:{default:'Element\'s default semantics were not overridden with role="none" or role="presentation"',globalAria:"Element's role is not presentational because it has a global ARIA attribute",focusable:"Element's role is not presentational because it is focusable",both:"Element's role is not presentational because it has a global ARIA attribute and is focusable",iframe:'Using the "title" attribute on an ${data.nodeName} element with a presentational role behaves inconsistently between screen readers'}}},"role-none":{impact:"minor",messages:{pass:'Element\'s default semantics were overriden with role="none"',fail:'Element\'s default semantics were not overridden with role="none"'}},"role-presentation":{impact:"minor",messages:{pass:'Element\'s default semantics were overriden with role="presentation"',fail:'Element\'s default semantics were not overridden with role="presentation"'}},"svg-non-empty-title":{impact:"serious",messages:{pass:"Element has a child that is a title",fail:{noTitle:"Element has no child that is a title",emptyTitle:"Element child title is empty"},incomplete:"Unable to determine element has a child that is a title"}},"caption-faked":{impact:"serious",messages:{pass:"The first row of a table is not used as a caption",fail:"The first child of the table should be a caption instead of a table cell"}},"html5-scope":{impact:"moderate",messages:{pass:"Scope attribute is only used on table header elements (<th>)",fail:"In HTML 5, scope attributes may only be used on table header elements (<th>)"}},"same-caption-summary":{impact:"minor",messages:{pass:"Content of summary attribute and <caption> are not duplicated",fail:"Content of summary attribute and <caption> element are identical",incomplete:"Unable to determine if <table> element has a caption"}},"scope-value":{impact:"critical",messages:{pass:"Scope attribute is used correctly",fail:"The value of the scope attribute may only be 'row' or 'col'"}},"td-has-header":{impact:"critical",messages:{pass:"All non-empty data cells have table headers",fail:"Some non-empty data cells do not have table headers"}},"td-headers-attr":{impact:"serious",messages:{pass:"The headers attribute is exclusively used to refer to other cells in the table",incomplete:"The headers attribute is empty",fail:"The headers attribute is not exclusively used to refer to other cells in the table"}},"th-has-data-cells":{impact:"serious",messages:{pass:"All table header cells refer to data cells",fail:"Not all table header cells refer to data cells",incomplete:"Table data cells are missing or empty"}},"hidden-content":{impact:"minor",messages:{pass:"All content on the page has been analyzed.",fail:"There were problems analyzing the content on this page.",incomplete:"There is hidden content on the page that was not analyzed. You will need to trigger the display of this content in order to analyze it."}}},failureSummaries:{any:{failureMessage:function(e){var t="Fix any of the following:",n=e;if(n)for(var a=-1,r=n.length-1;a<r;)t+="\n  "+n[a+=1].split("\n").join("\n  ");return t}},none:{failureMessage:function(e){var t="Fix all of the following:",n=e;if(n)for(var a=-1,r=n.length-1;a<r;)t+="\n  "+n[a+=1].split("\n").join("\n  ");return t}}},incompleteFallbackMessage:"axe couldn't tell the reason. Time to break out the element inspector!"},rules:[{id:"accesskeys",selector:"[accesskey]",excludeHidden:!1,tags:["cat.keyboard","best-practice"],all:[],any:[],none:["accesskeys"]},{id:"area-alt",selector:"map area[href]",excludeHidden:!1,tags:["cat.text-alternatives","wcag2a","wcag244","wcag412","section508","section508.22.a","ACT","TTv5","TT6.a"],actIds:["c487ae"],all:[],any:[{options:{attribute:"alt"},id:"non-empty-alt"},"aria-label","aria-labelledby",{options:{attribute:"title"},id:"non-empty-title"}],none:[]},{id:"aria-allowed-attr",matches:"aria-allowed-attr-matches",tags:["cat.aria","wcag2a","wcag412"],actIds:["5c01ea"],all:[],any:[{options:{validTreeRowAttrs:["aria-posinset","aria-setsize","aria-expanded","aria-level"]},id:"aria-allowed-attr"}],none:["aria-unsupported-attr",{options:{elementsAllowedAriaLabel:["applet","input"]},id:"aria-prohibited-attr"}]},{id:"aria-allowed-role",excludeHidden:!1,selector:"[role]",matches:"aria-allowed-role-matches",tags:["cat.aria","best-practice"],all:[],any:[{options:{allowImplicit:!0,ignoredTags:[]},id:"aria-allowed-role"}],none:[]},{id:"aria-command-name",selector:'[role="link"], [role="button"], [role="menuitem"]',matches:"no-naming-method-matches",tags:["cat.aria","wcag2a","wcag412","ACT","TTv5","TT6.a"],actIds:["97a4e1"],all:[],any:["has-visible-text","aria-label","aria-labelledby",{options:{attribute:"title"},id:"non-empty-title"}],none:[]},{id:"aria-dialog-name",selector:'[role="dialog"], [role="alertdialog"]',matches:"no-naming-method-matches",tags:["cat.aria","best-practice"],all:[],any:["aria-label","aria-labelledby",{options:{attribute:"title"},id:"non-empty-title"}],none:[]},{id:"aria-hidden-body",selector:"body",excludeHidden:!1,matches:"is-initiator-matches",tags:["cat.aria","wcag2a","wcag412"],all:[],any:["aria-hidden-body"],none:[]},{id:"aria-hidden-focus",selector:'[aria-hidden="true"]',matches:"aria-hidden-focus-matches",excludeHidden:!1,tags:["cat.name-role-value","wcag2a","wcag412"],actIds:["6cfa84"],all:["focusable-modal-open","focusable-disabled","focusable-not-tabbable"],any:[],none:[]},{id:"aria-input-field-name",selector:'[role="combobox"], [role="listbox"], [role="searchbox"], [role="slider"], [role="spinbutton"], [role="textbox"]',matches:"no-naming-method-matches",tags:["cat.aria","wcag2a","wcag412","ACT","TTv5","TT5.c"],actIds:["e086e5"],all:[],any:["aria-label","aria-labelledby",{options:{attribute:"title"},id:"non-empty-title"}],none:["no-implicit-explicit-label"]},{id:"aria-meter-name",selector:'[role="meter"]',matches:"no-naming-method-matches",tags:["cat.aria","wcag2a","wcag111"],all:[],any:["aria-label","aria-labelledby",{options:{attribute:"title"},id:"non-empty-title"}],none:[]},{id:"aria-progressbar-name",selector:'[role="progressbar"]',matches:"no-naming-method-matches",tags:["cat.aria","wcag2a","wcag111"],all:[],any:["aria-label","aria-labelledby",{options:{attribute:"title"},id:"non-empty-title"}],none:[]},{id:"aria-required-attr",selector:"[role]",tags:["cat.aria","wcag2a","wcag412"],actIds:["4e8ab6"],all:[],any:["aria-required-attr"],none:[]},{id:"aria-required-children",selector:"[role]",matches:"aria-required-children-matches",tags:["cat.aria","wcag2a","wcag131"],actIds:["bc4a75","ff89c9"],all:[],any:[{options:{reviewEmpty:["doc-bibliography","doc-endnotes","grid","list","listbox","menu","menubar","table","tablist","tree","treegrid","rowgroup"]},id:"aria-required-children"},"aria-busy"],none:[]},{id:"aria-required-parent",selector:"[role]",matches:"aria-required-parent-matches",tags:["cat.aria","wcag2a","wcag131"],actIds:["ff89c9"],all:[],any:[{options:{ownGroupRoles:["listitem","treeitem"]},id:"aria-required-parent"}],none:[]},{id:"aria-roledescription",selector:"[aria-roledescription]",tags:["cat.aria","wcag2a","wcag412","deprecated"],enabled:!1,all:[],any:[{options:{supportedRoles:["button","img","checkbox","radio","combobox","menuitemcheckbox","menuitemradio"]},id:"aria-roledescription"}],none:[]},{id:"aria-roles",selector:"[role]",matches:"no-empty-role-matches",tags:["cat.aria","wcag2a","wcag412"],actIds:["674b10"],all:[],any:[],none:["invalidrole","abstractrole","unsupportedrole","deprecatedrole"]},{id:"aria-text",selector:"[role=text]",tags:["cat.aria","best-practice"],all:[],any:["no-focusable-content"],none:[]},{id:"aria-toggle-field-name",selector:'[role="checkbox"], [role="menuitemcheckbox"], [role="menuitemradio"], [role="radio"], [role="switch"], [role="option"]',matches:"no-naming-method-matches",tags:["cat.aria","wcag2a","wcag412","ACT","TTv5","TT5.c"],actIds:["e086e5"],all:[],any:["has-visible-text","aria-label","aria-labelledby",{options:{attribute:"title"},id:"non-empty-title"}],none:["no-implicit-explicit-label"]},{id:"aria-tooltip-name",selector:'[role="tooltip"]',matches:"no-naming-method-matches",tags:["cat.aria","wcag2a","wcag412"],all:[],any:["has-visible-text","aria-label","aria-labelledby",{options:{attribute:"title"},id:"non-empty-title"}],none:[]},{id:"aria-treeitem-name",selector:'[role="treeitem"]',matches:"no-naming-method-matches",tags:["cat.aria","best-practice"],all:[],any:["has-visible-text","aria-label","aria-labelledby",{options:{attribute:"title"},id:"non-empty-title"}],none:[]},{id:"aria-valid-attr-value",matches:"aria-has-attr-matches",tags:["cat.aria","wcag2a","wcag412"],actIds:["6a7281"],all:[{options:[],id:"aria-valid-attr-value"},"aria-errormessage","aria-level"],any:[],none:[]},{id:"aria-valid-attr",matches:"aria-has-attr-matches",tags:["cat.aria","wcag2a","wcag412"],actIds:["5f99a7"],all:[],any:[{options:[],id:"aria-valid-attr"}],none:[]},{id:"audio-caption",selector:"audio",enabled:!1,excludeHidden:!1,tags:["cat.time-and-media","wcag2a","wcag121","section508","section508.22.a","deprecated"],actIds:["2eb176","afb423"],all:[],any:[],none:["caption"]},{id:"autocomplete-valid",matches:"autocomplete-matches",tags:["cat.forms","wcag21aa","wcag135","ACT"],actIds:["73f2c2"],all:[{options:{stateTerms:["none","false","true","disabled","enabled","undefined","null"]},id:"autocomplete-valid"}],any:[],none:[]},{id:"avoid-inline-spacing",selector:"[style]",matches:"is-visible-on-screen-matches",tags:["cat.structure","wcag21aa","wcag1412","ACT"],actIds:["24afc2","9e45ec","78fd32"],all:[{options:{cssProperty:"letter-spacing",minValue:.12},id:"important-letter-spacing"},{options:{cssProperty:"word-spacing",minValue:.16},id:"important-word-spacing"},{options:{multiLineOnly:!0,cssProperty:"line-height",minValue:1.5,normalValue:1},id:"important-line-height"}],any:[],none:[]},{id:"blink",selector:"blink",excludeHidden:!1,tags:["cat.time-and-media","wcag2a","wcag222","section508","section508.22.j","TTv5","TT2.b"],all:[],any:[],none:["is-on-screen"]},{id:"button-name",selector:"button",matches:"no-explicit-name-required-matches",tags:["cat.name-role-value","wcag2a","wcag412","section508","section508.22.a","ACT","TTv5","TT6.a"],actIds:["97a4e1","m6b1q3"],all:[],any:["button-has-visible-text","aria-label","aria-labelledby",{options:{attribute:"title"},id:"non-empty-title"},"presentational-role"],none:[]},{id:"bypass",selector:"html",pageLevel:!0,matches:"bypass-matches",reviewOnFail:!0,tags:["cat.keyboard","wcag2a","wcag241","section508","section508.22.o","TTv5","TT9.a"],actIds:["cf77f2","047fe0","b40fd1","3e12e1","ye5d6e"],all:[],any:["internal-link-present",{options:{selector:":is(h1, h2, h3, h4, h5, h6):not([role]), [role=heading]"},id:"header-present"},{options:{selector:"main, [role=main]"},id:"landmark"}],none:[]},{id:"color-contrast-enhanced",matches:"color-contrast-matches",excludeHidden:!1,enabled:!1,tags:["cat.color","wcag2aaa","wcag146","ACT"],actIds:["09o5cg"],all:[],any:[{options:{ignoreUnicode:!0,ignoreLength:!1,ignorePseudo:!1,boldValue:700,boldTextPt:14,largeTextPt:18,contrastRatio:{normal:{expected:7,minThreshold:4.5},large:{expected:4.5,minThreshold:3}},pseudoSizeThreshold:.25,shadowOutlineEmMax:.1,textStrokeEmMin:.03},id:"color-contrast-enhanced"}],none:[]},{id:"color-contrast",matches:"color-contrast-matches",excludeHidden:!1,tags:["cat.color","wcag2aa","wcag143","ACT","TTv5","TT13.c"],actIds:["afw4f7","09o5cg"],all:[],any:[{options:{ignoreUnicode:!0,ignoreLength:!1,ignorePseudo:!1,boldValue:700,boldTextPt:14,largeTextPt:18,contrastRatio:{normal:{expected:4.5},large:{expected:3}},pseudoSizeThreshold:.25,shadowOutlineEmMax:.2,textStrokeEmMin:.03},id:"color-contrast"}],none:[]},{id:"css-orientation-lock",selector:"html",tags:["cat.structure","wcag134","wcag21aa","experimental"],actIds:["b33eff"],all:[{options:{degreeThreshold:2},id:"css-orientation-lock"}],any:[],none:[],preload:!0},{id:"definition-list",selector:"dl",matches:"no-role-matches",tags:["cat.structure","wcag2a","wcag131"],all:[],any:[],none:["structured-dlitems",{options:{validRoles:["definition","term","listitem"],validNodeNames:["dt","dd"],divGroups:!0},id:"only-dlitems"}]},{id:"dlitem",selector:"dd, dt",matches:"no-role-matches",tags:["cat.structure","wcag2a","wcag131"],all:[],any:["dlitem"],none:[]},{id:"document-title",selector:"html",matches:"is-initiator-matches",tags:["cat.text-alternatives","wcag2a","wcag242","ACT","TTv5","TT12.a"],actIds:["2779a5"],all:[],any:["doc-has-title"],none:[]},{id:"duplicate-id-active",selector:"[id]",matches:"duplicate-id-active-matches",excludeHidden:!1,tags:["cat.parsing","wcag2a","wcag411"],actIds:["3ea0c8"],all:[],any:["duplicate-id-active"],none:[]},{id:"duplicate-id-aria",selector:"[id]",matches:"duplicate-id-aria-matches",excludeHidden:!1,tags:["cat.parsing","wcag2a","wcag411"],actIds:["3ea0c8"],all:[],any:["duplicate-id-aria"],none:[]},{id:"duplicate-id",selector:"[id]",matches:"duplicate-id-misc-matches",excludeHidden:!1,tags:["cat.parsing","wcag2a","wcag411"],actIds:["3ea0c8"],all:[],any:["duplicate-id"],none:[]},{id:"empty-heading",selector:'h1, h2, h3, h4, h5, h6, [role="heading"]',matches:"heading-matches",tags:["cat.name-role-value","best-practice"],actIds:["ffd0e9"],impact:"minor",all:[],any:["has-visible-text","aria-label","aria-labelledby",{options:{attribute:"title"},id:"non-empty-title"}],none:[]},{id:"empty-table-header",selector:'th:not([role]), [role="rowheader"], [role="columnheader"]',tags:["cat.name-role-value","best-practice"],all:[],any:["has-visible-text"],none:[]},{id:"focus-order-semantics",selector:"div, h1, h2, h3, h4, h5, h6, [role=heading], p, span",matches:"inserted-into-focus-order-matches",tags:["cat.keyboard","best-practice","experimental"],all:[],any:[{options:[],id:"has-widget-role"},{options:{roles:["tooltip"]},id:"valid-scrollable-semantics"}],none:[]},{id:"form-field-multiple-labels",selector:"input, select, textarea",matches:"label-matches",tags:["cat.forms","wcag2a","wcag332","TTv5","TT5.c"],all:[],any:[],none:["multiple-label"]},{id:"frame-focusable-content",selector:"html",matches:"frame-focusable-content-matches",tags:["cat.keyboard","wcag2a","wcag211","TTv5","TT4.a"],actIds:["akn7bn"],all:[],any:["frame-focusable-content"],none:[]},{id:"frame-tested",selector:"html, frame, iframe",tags:["cat.structure","review-item","best-practice"],all:[{options:{isViolation:!1},id:"frame-tested"}],any:[],none:[]},{id:"frame-title-unique",selector:"frame[title], iframe[title]",matches:"frame-title-has-text-matches",tags:["cat.text-alternatives","wcag412","wcag2a","TTv5","TT12.c"],actIds:["4b1c6c"],all:[],any:[],none:["unique-frame-title"],reviewOnFail:!0},{id:"frame-title",selector:"frame, iframe",matches:"no-negative-tabindex-matches",tags:["cat.text-alternatives","wcag2a","wcag412","section508","section508.22.i","TTv5","TT12.c"],actIds:["cae760"],all:[],any:[{options:{attribute:"title"},id:"non-empty-title"},"aria-label","aria-labelledby","presentational-role"],none:[]},{id:"heading-order",selector:"h1, h2, h3, h4, h5, h6, [role=heading]",matches:"heading-matches",tags:["cat.semantics","best-practice"],all:[],any:["heading-order"],none:[]},{id:"hidden-content",selector:"*",excludeHidden:!1,tags:["cat.structure","experimental","review-item","best-practice"],all:[],any:["hidden-content"],none:[]},{id:"html-has-lang",selector:"html",matches:"is-initiator-matches",tags:["cat.language","wcag2a","wcag311","ACT","TTv5","TT11.a"],actIds:["b5c3f8"],all:[],any:[{options:{attributes:["lang","xml:lang"]},id:"has-lang"}],none:[]},{id:"html-lang-valid",selector:'html[lang]:not([lang=""]), html[xml\\:lang]:not([xml\\:lang=""])',tags:["cat.language","wcag2a","wcag311","ACT","TTv5","TT11.a"],actIds:["bf051a"],all:[],any:[],none:[{options:{attributes:["lang","xml:lang"]},id:"valid-lang"}]},{id:"html-xml-lang-mismatch",selector:"html[lang][xml\\:lang]",matches:"xml-lang-mismatch-matches",tags:["cat.language","wcag2a","wcag311","ACT"],actIds:["5b7ae0"],all:["xml-lang-mismatch"],any:[],none:[]},{id:"identical-links-same-purpose",selector:'a[href], area[href], [role="link"]',excludeHidden:!1,enabled:!1,matches:"identical-links-same-purpose-matches",tags:["cat.semantics","wcag2aaa","wcag249"],actIds:["b20e66"],all:["identical-links-same-purpose"],any:[],none:[]},{id:"image-alt",selector:"img",matches:"no-explicit-name-required-matches",tags:["cat.text-alternatives","wcag2a","wcag111","section508","section508.22.a","ACT","TTv5","TT7.a","TT7.b"],actIds:["23a2a8"],all:[],any:["has-alt","aria-label","aria-labelledby",{options:{attribute:"title"},id:"non-empty-title"},"presentational-role"],none:["alt-space-value"]},{id:"image-redundant-alt",selector:"img",tags:["cat.text-alternatives","best-practice"],all:[],any:[],none:[{options:{parentSelector:"button, [role=button], a[href], p, li, td, th"},id:"duplicate-img-label"}]},{id:"input-button-name",selector:'input[type="button"], input[type="submit"], input[type="reset"]',matches:"no-explicit-name-required-matches",tags:["cat.name-role-value","wcag2a","wcag412","section508","section508.22.a","ACT","TTv5","TT5.c"],actIds:["97a4e1"],all:[],any:["non-empty-if-present",{options:{attribute:"value"},id:"non-empty-value"},"aria-label","aria-labelledby",{options:{attribute:"title"},id:"non-empty-title"},"presentational-role"],none:[]},{id:"input-image-alt",selector:'input[type="image"]',matches:"no-explicit-name-required-matches",tags:["cat.text-alternatives","wcag2a","wcag111","wcag412","section508","section508.22.a","ACT","TTv5","TT7.a"],actIds:["59796f"],all:[],any:[{options:{attribute:"alt"},id:"non-empty-alt"},"aria-label","aria-labelledby",{options:{attribute:"title"},id:"non-empty-title"}],none:[]},{id:"label-content-name-mismatch",matches:"label-content-name-mismatch-matches",tags:["cat.semantics","wcag21a","wcag253","experimental"],actIds:["2ee8b8"],all:[],any:[{options:{pixelThreshold:.1,occurrenceThreshold:3},id:"label-content-name-mismatch"}],none:[]},{id:"label-title-only",selector:"input, select, textarea",matches:"label-matches",tags:["cat.forms","best-practice"],all:[],any:[],none:["title-only"]},{id:"label",selector:"input, textarea",matches:"label-matches",tags:["cat.forms","wcag2a","wcag412","section508","section508.22.n","ACT","TTv5","TT5.c"],actIds:["e086e5"],all:[],any:["implicit-label","explicit-label","aria-label","aria-labelledby",{options:{attribute:"title"},id:"non-empty-title"},{options:{attribute:"placeholder"},id:"non-empty-placeholder"},"presentational-role"],none:["help-same-as-label","hidden-explicit-label"]},{id:"landmark-banner-is-top-level",selector:"header:not([role]), [role=banner]",matches:"landmark-has-body-context-matches",tags:["cat.semantics","best-practice"],all:[],any:["landmark-is-top-level"],none:[]},{id:"landmark-complementary-is-top-level",selector:"aside:not([role]), [role=complementary]",tags:["cat.semantics","best-practice"],all:[],any:["landmark-is-top-level"],none:[]},{id:"landmark-contentinfo-is-top-level",selector:"footer:not([role]), [role=contentinfo]",matches:"landmark-has-body-context-matches",tags:["cat.semantics","best-practice"],all:[],any:["landmark-is-top-level"],none:[]},{id:"landmark-main-is-top-level",selector:"main:not([role]), [role=main]",tags:["cat.semantics","best-practice"],all:[],any:["landmark-is-top-level"],none:[]},{id:"landmark-no-duplicate-banner",selector:"header:not([role]), [role=banner]",tags:["cat.semantics","best-practice"],all:[],any:[{options:{selector:"header:not([role]), [role=banner]",nativeScopeFilter:"article, aside, main, nav, section"},id:"page-no-duplicate-banner"}],none:[]},{id:"landmark-no-duplicate-contentinfo",selector:"footer:not([role]), [role=contentinfo]",tags:["cat.semantics","best-practice"],all:[],any:[{options:{selector:"footer:not([role]), [role=contentinfo]",nativeScopeFilter:"article, aside, main, nav, section"},id:"page-no-duplicate-contentinfo"}],none:[]},{id:"landmark-no-duplicate-main",selector:"main:not([role]), [role=main]",tags:["cat.semantics","best-practice"],all:[],any:[{options:{selector:"main:not([role]), [role='main']"},id:"page-no-duplicate-main"}],none:[]},{id:"landmark-one-main",selector:"html",tags:["cat.semantics","best-practice"],all:[{options:{selector:"main:not([role]), [role='main']",passForModal:!0},id:"page-has-main"}],any:[],none:[]},{id:"landmark-unique",selector:"[role=banner], [role=complementary], [role=contentinfo], [role=main], [role=navigation], [role=region], [role=search], [role=form], form, footer, header, aside, main, nav, section",tags:["cat.semantics","best-practice"],matches:"landmark-unique-matches",all:[],any:["landmark-is-unique"],none:[]},{id:"link-in-text-block",selector:"a[href], [role=link]",matches:"link-in-text-block-matches",excludeHidden:!1,tags:["cat.color","wcag2a","wcag141","TTv5","TT13.a"],all:[],any:[{options:{requiredContrastRatio:3,allowSameColor:!0},id:"link-in-text-block"},"link-in-text-block-style"],none:[]},{id:"link-name",selector:"a[href]",tags:["cat.name-role-value","wcag2a","wcag412","wcag244","section508","section508.22.a","ACT","TTv5","TT6.a"],actIds:["c487ae"],all:[],any:["has-visible-text","aria-label","aria-labelledby",{options:{attribute:"title"},id:"non-empty-title"}],none:["focusable-no-name"]},{id:"list",selector:"ul, ol",matches:"no-role-matches",tags:["cat.structure","wcag2a","wcag131"],all:[],any:[],none:[{options:{validRoles:["listitem"],validNodeNames:["li"]},id:"only-listitems"}]},{id:"listitem",selector:"li",matches:"no-role-matches",tags:["cat.structure","wcag2a","wcag131"],all:[],any:["listitem"],none:[]},{id:"marquee",selector:"marquee",excludeHidden:!1,tags:["cat.parsing","wcag2a","wcag222","TTv5","TT2.b"],all:[],any:[],none:["is-on-screen"]},{id:"meta-refresh-no-exceptions",selector:'meta[http-equiv="refresh"][content]',excludeHidden:!1,enabled:!1,tags:["cat.time-and-media","wcag2aaa","wcag224","wcag325"],actIds:["bisz58"],all:[],any:[{options:{minDelay:72e3,maxDelay:!1},id:"meta-refresh-no-exceptions"}],none:[]},{id:"meta-refresh",selector:'meta[http-equiv="refresh"][content]',excludeHidden:!1,tags:["cat.time-and-media","wcag2a","wcag221","TTv5","TT2.c"],actIds:["bc659a","bisz58"],all:[],any:[{options:{minDelay:0,maxDelay:72e3},id:"meta-refresh"}],none:[]},{id:"meta-viewport-large",selector:'meta[name="viewport"]',matches:"is-initiator-matches",excludeHidden:!1,tags:["cat.sensory-and-visual-cues","best-practice"],all:[],any:[{options:{scaleMinimum:5,lowerBound:2},id:"meta-viewport-large"}],none:[]},{id:"meta-viewport",selector:'meta[name="viewport"]',matches:"is-initiator-matches",excludeHidden:!1,tags:["cat.sensory-and-visual-cues","wcag2aa","wcag144","ACT"],actIds:["b4f0c3"],all:[],any:[{options:{scaleMinimum:2},id:"meta-viewport"}],none:[]},{id:"nested-interactive",matches:"nested-interactive-matches",tags:["cat.keyboard","wcag2a","wcag412","TTv5","TT4.a"],actIds:["307n5z"],all:[],any:["no-focusable-content"],none:[]},{id:"no-autoplay-audio",excludeHidden:!1,selector:"audio[autoplay], video[autoplay]",matches:"no-autoplay-audio-matches",reviewOnFail:!0,tags:["cat.time-and-media","wcag2a","wcag142","ACT","TTv5","TT2.a"],actIds:["80f0bf"],preload:!0,all:[{options:{allowedDuration:3},id:"no-autoplay-audio"}],any:[],none:[]},{id:"object-alt",selector:"object[data]",matches:"object-is-loaded-matches",tags:["cat.text-alternatives","wcag2a","wcag111","section508","section508.22.a"],actIds:["8fc3b6"],all:[],any:["aria-label","aria-labelledby",{options:{attribute:"title"},id:"non-empty-title"},"presentational-role"],none:[]},{id:"p-as-heading",selector:"p",matches:"p-as-heading-matches",tags:["cat.semantics","wcag2a","wcag131","experimental"],all:[{options:{margins:[{weight:150,italic:!0},{weight:150,size:1.15},{italic:!0,size:1.15},{size:1.4}],passLength:1,failLength:.5},id:"p-as-heading"}],any:[],none:[]},{id:"page-has-heading-one",selector:"html",tags:["cat.semantics","best-practice"],all:[{options:{selector:"h1:not([role], [aria-level]), :is(h1, h2, h3, h4, h5, h6):not([role])[aria-level=1], [role=heading][aria-level=1]",passForModal:!0},id:"page-has-heading-one"}],any:[],none:[]},{id:"presentation-role-conflict",selector:'img[alt=\'\'], [role="none"], [role="presentation"]',matches:"has-implicit-chromium-role-matches",tags:["cat.aria","best-practice","ACT"],actIds:["46ca7f"],all:[],any:[],none:["is-element-focusable","has-global-aria-attribute"]},{id:"region",selector:"body *",tags:["cat.keyboard","best-practice"],all:[],any:[{options:{regionMatcher:"dialog, [role=dialog], [role=alertdialog], svg"},id:"region"}],none:[]},{id:"role-img-alt",selector:"[role='img']:not(img, area, input, object)",matches:"html-namespace-matches",tags:["cat.text-alternatives","wcag2a","wcag111","section508","section508.22.a","ACT","TTv5","TT7.a"],actIds:["23a2a8"],all:[],any:["aria-label","aria-labelledby",{options:{attribute:"title"},id:"non-empty-title"}],none:[]},{id:"scope-attr-valid",selector:"td[scope], th[scope]",tags:["cat.tables","best-practice"],all:["html5-scope",{options:{values:["row","col","rowgroup","colgroup"]},id:"scope-value"}],any:[],none:[]},{id:"scrollable-region-focusable",selector:"*:not(select,textarea)",matches:"scrollable-region-focusable-matches",tags:["cat.keyboard","wcag2a","wcag211"],actIds:["0ssw9k"],all:[],any:["focusable-content","focusable-element"],none:[]},{id:"select-name",selector:"select",tags:["cat.forms","wcag2a","wcag412","section508","section508.22.n","ACT","TTv5","TT5.c"],actIds:["e086e5"],all:[],any:["implicit-label","explicit-label","aria-label","aria-labelledby",{options:{attribute:"title"},id:"non-empty-title"},"presentational-role"],none:["help-same-as-label","hidden-explicit-label"]},{id:"server-side-image-map",selector:"img[ismap]",tags:["cat.text-alternatives","wcag2a","wcag211","section508","section508.22.f"],all:[],any:[],none:["exists"]},{id:"skip-link",selector:'a[href^="#"], a[href^="/#"]',matches:"skip-link-matches",tags:["cat.keyboard","best-practice"],all:[],any:["skip-link"],none:[]},{id:"svg-img-alt",selector:'[role="img"], [role="graphics-symbol"], svg[role="graphics-document"]',matches:"svg-namespace-matches",tags:["cat.text-alternatives","wcag2a","wcag111","section508","section508.22.a","ACT","TTv5","TT7.a"],actIds:["7d6734"],all:[],any:["svg-non-empty-title","aria-label","aria-labelledby",{options:{attribute:"title"},id:"non-empty-title"}],none:[]},{id:"tabindex",selector:"[tabindex]",tags:["cat.keyboard","best-practice"],all:[],any:["tabindex"],none:[]},{id:"table-duplicate-name",selector:"table",tags:["cat.tables","best-practice"],all:[],any:[],none:["same-caption-summary"]},{id:"table-fake-caption",selector:"table",matches:"data-table-matches",tags:["cat.tables","experimental","wcag2a","wcag131","section508","section508.22.g"],all:["caption-faked"],any:[],none:[]},{id:"target-size",selector:"*",enabled:!1,matches:"widget-not-inline-matches",tags:["wcag22aa","wcag258","cat.sensory-and-visual-cues"],all:[],any:[{options:{minSize:24},id:"target-size"},{options:{minOffset:24},id:"target-offset"}],none:[]},{id:"td-has-header",selector:"table",matches:"data-table-large-matches",tags:["cat.tables","experimental","wcag2a","wcag131","section508","section508.22.g","TTv5","TT14.b"],all:["td-has-header"],any:[],none:[]},{id:"td-headers-attr",selector:"table",matches:"table-or-grid-role-matches",tags:["cat.tables","wcag2a","wcag131","section508","section508.22.g"],actIds:["a25f45"],all:["td-headers-attr"],any:[],none:[]},{id:"th-has-data-cells",selector:"table",matches:"data-table-matches",tags:["cat.tables","wcag2a","wcag131","section508","section508.22.g","TTv5","14.b"],actIds:["d0f69e"],all:["th-has-data-cells"],any:[],none:[]},{id:"valid-lang",selector:"[lang]:not(html), [xml\\:lang]:not(html)",tags:["cat.language","wcag2aa","wcag312","ACT","TTv5","TT11.b"],actIds:["de46e4"],all:[],any:[],none:[{options:{attributes:["lang","xml:lang"]},id:"valid-lang"}]},{id:"video-caption",selector:"video",tags:["cat.text-alternatives","wcag2a","wcag122","section508","section508.22.a","TTv5","TT17.a"],actIds:["eac66b"],all:[],any:[],none:["caption"]}],checks:[{id:"abstractrole",evaluate:"abstractrole-evaluate"},{id:"aria-allowed-attr",evaluate:"aria-allowed-attr-evaluate",options:{validTreeRowAttrs:["aria-posinset","aria-setsize","aria-expanded","aria-level"]}},{id:"aria-allowed-role",evaluate:"aria-allowed-role-evaluate",options:{allowImplicit:!0,ignoredTags:[]}},{id:"aria-busy",evaluate:"aria-busy-evaluate"},{id:"aria-errormessage",evaluate:"aria-errormessage-evaluate"},{id:"aria-hidden-body",evaluate:"aria-hidden-body-evaluate"},{id:"aria-level",evaluate:"aria-level-evaluate"},{id:"aria-prohibited-attr",evaluate:"aria-prohibited-attr-evaluate",options:{elementsAllowedAriaLabel:["applet","input"]}},{id:"aria-required-attr",evaluate:"aria-required-attr-evaluate"},{id:"aria-required-children",evaluate:"aria-required-children-evaluate",options:{reviewEmpty:["doc-bibliography","doc-endnotes","grid","list","listbox","menu","menubar","table","tablist","tree","treegrid","rowgroup"]}},{id:"aria-required-parent",evaluate:"aria-required-parent-evaluate",options:{ownGroupRoles:["listitem","treeitem"]}},{id:"aria-roledescription",evaluate:"aria-roledescription-evaluate",options:{supportedRoles:["button","img","checkbox","radio","combobox","menuitemcheckbox","menuitemradio"]}},{id:"aria-unsupported-attr",evaluate:"aria-unsupported-attr-evaluate"},{id:"aria-valid-attr-value",evaluate:"aria-valid-attr-value-evaluate",options:[]},{id:"aria-valid-attr",evaluate:"aria-valid-attr-evaluate",options:[]},{id:"deprecatedrole",evaluate:"deprecatedrole-evaluate"},{id:"fallbackrole",evaluate:"fallbackrole-evaluate"},{id:"has-global-aria-attribute",evaluate:"has-global-aria-attribute-evaluate"},{id:"has-widget-role",evaluate:"has-widget-role-evaluate",options:[]},{id:"invalidrole",evaluate:"invalidrole-evaluate"},{id:"is-element-focusable",evaluate:"is-element-focusable-evaluate"},{id:"no-implicit-explicit-label",evaluate:"no-implicit-explicit-label-evaluate"},{id:"unsupportedrole",evaluate:"unsupportedrole-evaluate"},{id:"valid-scrollable-semantics",evaluate:"valid-scrollable-semantics-evaluate",options:{roles:["tooltip"]}},{id:"color-contrast-enhanced",evaluate:"color-contrast-evaluate",options:{ignoreUnicode:!0,ignoreLength:!1,ignorePseudo:!1,boldValue:700,boldTextPt:14,largeTextPt:18,contrastRatio:{normal:{expected:7,minThreshold:4.5},large:{expected:4.5,minThreshold:3}},pseudoSizeThreshold:.25,shadowOutlineEmMax:.1,textStrokeEmMin:.03}},{id:"color-contrast",evaluate:"color-contrast-evaluate",options:{ignoreUnicode:!0,ignoreLength:!1,ignorePseudo:!1,boldValue:700,boldTextPt:14,largeTextPt:18,contrastRatio:{normal:{expected:4.5},large:{expected:3}},pseudoSizeThreshold:.25,shadowOutlineEmMax:.2,textStrokeEmMin:.03}},{id:"link-in-text-block-style",evaluate:"link-in-text-block-style-evaluate"},{id:"link-in-text-block",evaluate:"link-in-text-block-evaluate",options:{requiredContrastRatio:3,allowSameColor:!0}},{id:"autocomplete-appropriate",evaluate:"autocomplete-appropriate-evaluate",deprecated:!0},{id:"autocomplete-valid",evaluate:"autocomplete-valid-evaluate",options:{stateTerms:["none","false","true","disabled","enabled","undefined","null"]}},{id:"accesskeys",evaluate:"accesskeys-evaluate",after:"accesskeys-after"},{id:"focusable-content",evaluate:"focusable-content-evaluate"},{id:"focusable-disabled",evaluate:"focusable-disabled-evaluate"},{id:"focusable-element",evaluate:"focusable-element-evaluate"},{id:"focusable-modal-open",evaluate:"focusable-modal-open-evaluate"},{id:"focusable-no-name",evaluate:"focusable-no-name-evaluate"},{id:"focusable-not-tabbable",evaluate:"focusable-not-tabbable-evaluate"},{id:"frame-focusable-content",evaluate:"frame-focusable-content-evaluate"},{id:"landmark-is-top-level",evaluate:"landmark-is-top-level-evaluate"},{id:"no-focusable-content",evaluate:"no-focusable-content-evaluate"},{id:"page-has-heading-one",evaluate:"has-descendant-evaluate",after:"has-descendant-after",options:{selector:"h1:not([role], [aria-level]), :is(h1, h2, h3, h4, h5, h6):not([role])[aria-level=1], [role=heading][aria-level=1]",passForModal:!0}},{id:"page-has-main",evaluate:"has-descendant-evaluate",after:"has-descendant-after",options:{selector:"main:not([role]), [role='main']",passForModal:!0}},{id:"page-no-duplicate-banner",evaluate:"page-no-duplicate-evaluate",after:"page-no-duplicate-after",options:{selector:"header:not([role]), [role=banner]",nativeScopeFilter:"article, aside, main, nav, section"}},{id:"page-no-duplicate-contentinfo",evaluate:"page-no-duplicate-evaluate",after:"page-no-duplicate-after",options:{selector:"footer:not([role]), [role=contentinfo]",nativeScopeFilter:"article, aside, main, nav, section"}},{id:"page-no-duplicate-main",evaluate:"page-no-duplicate-evaluate",after:"page-no-duplicate-after",options:{selector:"main:not([role]), [role='main']"}},{id:"tabindex",evaluate:"tabindex-evaluate"},{id:"alt-space-value",evaluate:"alt-space-value-evaluate"},{id:"duplicate-img-label",evaluate:"duplicate-img-label-evaluate",options:{parentSelector:"button, [role=button], a[href], p, li, td, th"}},{id:"explicit-label",evaluate:"explicit-evaluate"},{id:"help-same-as-label",evaluate:"help-same-as-label-evaluate",enabled:!1},{id:"hidden-explicit-label",evaluate:"hidden-explicit-label-evaluate"},{id:"implicit-label",evaluate:"implicit-evaluate"},{id:"label-content-name-mismatch",evaluate:"label-content-name-mismatch-evaluate",options:{pixelThreshold:.1,occurrenceThreshold:3}},{id:"multiple-label",evaluate:"multiple-label-evaluate"},{id:"title-only",evaluate:"title-only-evaluate"},{id:"landmark-is-unique",evaluate:"landmark-is-unique-evaluate",after:"landmark-is-unique-after"},{id:"has-lang",evaluate:"has-lang-evaluate",options:{attributes:["lang","xml:lang"]}},{id:"valid-lang",evaluate:"valid-lang-evaluate",options:{attributes:["lang","xml:lang"]}},{id:"xml-lang-mismatch",evaluate:"xml-lang-mismatch-evaluate"},{id:"dlitem",evaluate:"dlitem-evaluate"},{id:"listitem",evaluate:"listitem-evaluate"},{id:"only-dlitems",evaluate:"invalid-children-evaluate",options:{validRoles:["definition","term","listitem"],validNodeNames:["dt","dd"],divGroups:!0}},{id:"only-listitems",evaluate:"invalid-children-evaluate",options:{validRoles:["listitem"],validNodeNames:["li"]}},{id:"structured-dlitems",evaluate:"structured-dlitems-evaluate"},{id:"caption",evaluate:"caption-evaluate"},{id:"frame-tested",evaluate:"frame-tested-evaluate",after:"frame-tested-after",options:{isViolation:!1}},{id:"no-autoplay-audio",evaluate:"no-autoplay-audio-evaluate",options:{allowedDuration:3}},{id:"css-orientation-lock",evaluate:"css-orientation-lock-evaluate",options:{degreeThreshold:2}},{id:"meta-viewport-large",evaluate:"meta-viewport-scale-evaluate",options:{scaleMinimum:5,lowerBound:2}},{id:"meta-viewport",evaluate:"meta-viewport-scale-evaluate",options:{scaleMinimum:2}},{id:"target-offset",evaluate:"target-offset-evaluate",options:{minOffset:24}},{id:"target-size",evaluate:"target-size-evaluate",options:{minSize:24}},{id:"header-present",evaluate:"has-descendant-evaluate",after:"has-descendant-after",options:{selector:":is(h1, h2, h3, h4, h5, h6):not([role]), [role=heading]"}},{id:"heading-order",evaluate:"heading-order-evaluate",after:"heading-order-after"},{id:"identical-links-same-purpose",evaluate:"identical-links-same-purpose-evaluate",after:"identical-links-same-purpose-after"},{id:"internal-link-present",evaluate:"internal-link-present-evaluate"},{id:"landmark",evaluate:"has-descendant-evaluate",options:{selector:"main, [role=main]"}},{id:"meta-refresh-no-exceptions",evaluate:"meta-refresh-evaluate",options:{minDelay:72e3,maxDelay:!1}},{id:"meta-refresh",evaluate:"meta-refresh-evaluate",options:{minDelay:0,maxDelay:72e3}},{id:"p-as-heading",evaluate:"p-as-heading-evaluate",options:{margins:[{weight:150,italic:!0},{weight:150,size:1.15},{italic:!0,size:1.15},{size:1.4}],passLength:1,failLength:.5}},{id:"region",evaluate:"region-evaluate",after:"region-after",options:{regionMatcher:"dialog, [role=dialog], [role=alertdialog], svg"}},{id:"skip-link",evaluate:"skip-link-evaluate"},{id:"unique-frame-title",evaluate:"unique-frame-title-evaluate",after:"unique-frame-title-after"},{id:"duplicate-id-active",evaluate:"duplicate-id-evaluate",after:"duplicate-id-after"},{id:"duplicate-id-aria",evaluate:"duplicate-id-evaluate",after:"duplicate-id-after"},{id:"duplicate-id",evaluate:"duplicate-id-evaluate",after:"duplicate-id-after"},{id:"aria-label",evaluate:"aria-label-evaluate"},{id:"aria-labelledby",evaluate:"aria-labelledby-evaluate"},{id:"avoid-inline-spacing",evaluate:"avoid-inline-spacing-evaluate",options:{cssProperties:["line-height","letter-spacing","word-spacing"]}},{id:"button-has-visible-text",evaluate:"has-text-content-evaluate"},{id:"doc-has-title",evaluate:"doc-has-title-evaluate"},{id:"exists",evaluate:"exists-evaluate"},{id:"has-alt",evaluate:"has-alt-evaluate"},{id:"has-visible-text",evaluate:"has-text-content-evaluate"},{id:"important-letter-spacing",evaluate:"inline-style-property-evaluate",options:{cssProperty:"letter-spacing",minValue:.12}},{id:"important-line-height",evaluate:"inline-style-property-evaluate",options:{multiLineOnly:!0,cssProperty:"line-height",minValue:1.5,normalValue:1}},{id:"important-word-spacing",evaluate:"inline-style-property-evaluate",options:{cssProperty:"word-spacing",minValue:.16}},{id:"is-on-screen",evaluate:"is-on-screen-evaluate"},{id:"non-empty-alt",evaluate:"attr-non-space-content-evaluate",options:{attribute:"alt"}},{id:"non-empty-if-present",evaluate:"non-empty-if-present-evaluate"},{id:"non-empty-placeholder",evaluate:"attr-non-space-content-evaluate",options:{attribute:"placeholder"}},{id:"non-empty-title",evaluate:"attr-non-space-content-evaluate",options:{attribute:"title"}},{id:"non-empty-value",evaluate:"attr-non-space-content-evaluate",options:{attribute:"value"}},{id:"presentational-role",evaluate:"presentational-role-evaluate"},{id:"role-none",evaluate:"matches-definition-evaluate",deprecated:!0,options:{matcher:{attributes:{role:"none"}}}},{id:"role-presentation",evaluate:"matches-definition-evaluate",deprecated:!0,options:{matcher:{attributes:{role:"presentation"}}}},{id:"svg-non-empty-title",evaluate:"svg-non-empty-title-evaluate"},{id:"caption-faked",evaluate:"caption-faked-evaluate"},{id:"html5-scope",evaluate:"html5-scope-evaluate"},{id:"same-caption-summary",evaluate:"same-caption-summary-evaluate"},{id:"scope-value",evaluate:"scope-value-evaluate",options:{values:["row","col","rowgroup","colgroup"]}},{id:"td-has-header",evaluate:"td-has-header-evaluate"},{id:"td-headers-attr",evaluate:"td-headers-attr-evaluate"},{id:"th-has-data-cells",evaluate:"th-has-data-cells-evaluate"},{id:"hidden-content",evaluate:"hidden-content-evaluate"}]})}("object"==typeof window?window:this);
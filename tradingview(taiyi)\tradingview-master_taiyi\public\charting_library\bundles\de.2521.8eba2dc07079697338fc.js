(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[2521],{12978:e=>{e.exports=["Die Echtzeitdaten für {symbolName}"]},64565:e=>{e.exports=["werden von der {exchange} Börse bereitgestellt."]},19801:e=>{e.exports="Fr"},11268:e=>{e.exports="Mo"},63331:e=>{e.exports="Sa"},85954:e=>{e.exports="Su"},26230:e=>{e.exports="We"},24793:e=>{e.exports="Th"},31533:e=>{e.exports="Tu"},89790:e=>{e.exports=["Pine Quellcode konnte nicht geladen werden"]},39589:e=>{e.exports=["Fenster zuklappen"]},38154:e=>{e.exports=["Bestätigen Sie das Löschen des gesamten Studienbaums"]},53205:e=>{e.exports=["Fortwährende Terminkontrakte"]},15993:e=>{e.exports=["Die fortwährenden Terminkontrakte sind synthetische Instrumente, die aus einer Kombination von individuellen Kontrakten bestehen. Der 1! Kontrakt stellt hierbei den Frontmonat dar (kürzeste Laufzeit), während 2! für den Kontrakt mit der zweitkürzesten Laufzeit steht."]},45e3:e=>{e.exports=["Cboe BZX"]},36004:e=>{e.exports=["Erstellen Sie einen kostenlosen Account"]},69419:e=>{e.exports=["Alles ist gut — Markt ist geöffnet."]},97637:e=>{e.exports="April"},86797:e=>{e.exports="August"},22519:e=>{e.exports=["Balken Änderungswerte"]},52003:e=>{e.exports=["Möchten Sie wirklich diese Studie und ihre Ableger löschen?"]},68854:e=>{e.exports=["Doppelklick"]},97325:e=>{e.exports=["Daten Problem"]},52916:e=>{e.exports=["Daten werden einmal täglich aktualisiert."]},25978:e=>{e.exports=["Daten werden nur einmal pro Sekunde aktualisiert, auch wenn es mehr Änderungen am Markt gibt."]},57310:e=>{e.exports=["Daten sind verzögert"]},49321:e=>{e.exports=["Daten für Free-User werden nur ein mal pro Sekunde aktualisiert, auch wenn mehr Änderungen im Markt geschehen."]},55669:e=>{e.exports=["Dezember"]},83498:e=>{e.exports=["Bereich Löschen"]},6044:e=>{e.exports=["Abgeleitete Daten"]},31461:e=>{e.exports=["Abgeleitete Daten bezieht sich auf finanzielle Indikatoren, die mit einer Kombination von und/oder der Verarbeitung von Rohdaten erstellt wurden, die aus unterschiedlichen Quellen stammen."]},59315:e=>{e.exports=["End of Day Daten"]},82751:e=>{e.exports=["Fehler"]},40519:e=>{e.exports=["Guten Abend. Der Markt befindet sich im nachbörslichen Handel."]},80227:e=>{e.exports=["Zeitzone wechseln"]},16467:e=>{e.exports=["Februar"]},25046:e=>{e.exports=["Börsen-Vereinbarungen ausfüllen"]},93666:e=>{e.exports=["Symbol markieren"]},564:e=>{e.exports=["Fr"]},72970:e=>{e.exports=["Freitag"]},88958:e=>{e.exports=["Urlaub / Feiertag"]},32960:e=>{e.exports=["Halal-Symbol"]},21686:e=>{e.exports=["Indikator-Legende verbergen"]},26935:e=>{e.exports=["Funktionsargument des Indikators"]},26315:e=>{e.exports=["Titel des Indikators"]},84098:e=>{e.exports=["Werte des Indikators"]},91459:e=>{e.exports=["Wenn Sie Echtzeitdaten von {listedExchange} wünschen, müssen Sie eine Börsenvereinbarung ausfüllen. Keine Sorge, es dauert nur ein paar Klicks"]},50634:e=>{e.exports=["Der nachbörsliche Handel beginnt in {remainingTime}."]},74537:e=>{e.exports=["Der vorbörsliche Handel beginnt in {remainingTime}."]},26910:e=>{
e.exports=["Januar"]},23230:e=>{e.exports=["Juli"]},49385:e=>{e.exports=["Juni"]},99487:e=>{e.exports=["OHLC Werte"]},15815:e=>{e.exports=["Ein Update pro Sekunde"]},90784:e=>{e.exports="October"},75991:e=>{e.exports=["Markt-Öffnungs-Status anzeigen"]},36051:e=>{e.exports=["Mehr erfahren"]},39899:e=>{e.exports=["Bereich nach unten bewegen"]},70343:e=>{e.exports=["Bereich nach oben bewegen"]},83085:e=>{e.exports="Mon"},61199:e=>{e.exports=["Montag"]},41610:e=>{e.exports=["Mehr"]},1653:e=>{e.exports=["Guten Morgen. Markt befindet sich im vorbörslichen Handel."]},56470:e=>{e.exports=["Chart maximieren"]},19603:e=>{e.exports=["Bereich Maximieren"]},68327:e=>{e.exports=["Mai"]},35732:e=>{e.exports=["Bereiche Verwalten"]},84675:e=>{e.exports=["März"]},83949:e=>{e.exports=["Markt ist geöffnet"]},35701:e=>{e.exports=["Markt öffnet in {remainingTime}."]},95814:e=>{e.exports=["Markt ist geschlossen"]},98105:e=>{e.exports=["Markt schließt in {remainingTime}."]},56086:e=>{e.exports=["Der Markt ist derzeit im Urlaub. Was für ein Leben."]},71194:e=>{e.exports="November"},66324:e=>{e.exports="Source code"},36835:e=>{e.exports=["Sa"]},1144:e=>{e.exports=["Samstag"]},40653:e=>{e.exports=["Nach links scrollen"]},26721:e=>{e.exports=["Zum neuesten Balken scrollen"]},35809:e=>{e.exports=["Nach rechts scrollen"]},61132:e=>{e.exports="September"},28705:e=>{e.exports=["Indikator-Legende anzeigen"]},51072:e=>{e.exports=["Objektbaum anzeigen"]},37809:e=>{e.exports=["Intervalleinstellungen anzeigen"]},39045:e=>{e.exports="Study Error"},86577:e=>{e.exports=["Son"]},72149:e=>{e.exports=["Sonntag"]},46041:e=>{e.exports=["Quelle für Symbolpreis"]},63143:e=>{e.exports=["Titel des Symbols"]},29985:e=>{e.exports=["Nachbörslich"]},28412:e=>{e.exports=["Kostenpflichtige Abos enthalten schnellere Daten-Updates"]},56042:e=>{e.exports=["Vorbörslich"]},24680:e=>{e.exports=["Primäre Auflistung"]},89022:e=>{e.exports=["Zurzeit werden keine Echtzeitdaten für dieses Symbol unterstützt. Dies kann sich jedoch in Zukunft ändern."]},6667:e=>{e.exports=["Echtzeit Daten für {symbolName} werden von der {exchange} Börse bereitgestellt."]},48293:e=>{e.exports=["Chart wiederherstellen"]},91029:e=>{e.exports=["Bereich wiederherstellen"]},75094:e=>{e.exports=["Mi"]},7147:e=>{e.exports=["Mittwoch"]},52984:e=>{e.exports=["Um Echtzeit Daten für {description} zu erhalten, kaufen Sie bitte das Echtzeit Datenpaket."]},9787:e=>{e.exports=["Do"]},7951:e=>{e.exports=["Donnerstag"]},99214:e=>{e.exports=["Die Haupt- oder erste Börse, auf der die Aktie eines Unternehmens notiert ist und gehandelt wird."]},2310:e=>{e.exports=["Hierbei handelt es sich um Echtzeitdaten. Sie können sich jedoch leicht von den offiziellen Gegenstücken unterscheiden, die von den Primärbörsen ausgesendet werden."]},29512:e=>{e.exports=["Hierbei handelt es sich um Echtzeitdaten. Sie können sich jedoch leicht von dem offiziellen Gegenstück unterscheiden, das von der {exchange} ausgesendet wird."]},52449:e=>{
e.exports=["Dies ist eine scharia-konforme Aktie, welche das islamische Gesetz befolgt. Dieses Unternehmen erhebt und erhält keine Zinsen, und ist nicht in bestimmten Sektoren aktiv (Glücksspiel, Alkohol, Tabak, Schweinefleischerzeugnisse)."]},73717:e=>{e.exports=["Dieses Symbol existiert nicht. Bitte wählen Sie ein anderes Symbol."]},57048:e=>{e.exports=["Zeit für einen Spaziergang — Dieser Markt ist geschlossen."]},94316:e=>{e.exports=["Die"]},44979:e=>{e.exports=["Dienstag"]},8209:e=>{e.exports=["Markierung aufheben"]},1111:e=>{e.exports=["Volumen"]},61311:e=>{e.exports=["Vergrößern"]},47602:e=>{e.exports=["Verkleinern"]},57889:e=>{e.exports=["Sichtbarkeit der OHLC-Werte ändern"]},18644:e=>{e.exports=["Sichtbarkeit des Marktstatus ändern"]},45110:e=>{e.exports=["Sichtbarkeit des Countdowns bei Balkenschluss ändern"]},31325:e=>{e.exports=["Sichtbarkeit der Indikatortitel ändern"]},99774:e=>{e.exports=["Sichtbarkeit der Indikatorwerte ändern"]},96162:e=>{e.exports=["Sichtbarkeit der Indikatorargumente ändern"]},26717:e=>{e.exports=["Sichtbarkeit der Symbolbeschreibung ändern"]},6091:e=>{e.exports=["Die Sichtbarkeit des Symbolfelds verändern"]},9455:e=>{e.exports=["Sichtbarkeit der Volumenwerte ändern"]},39348:e=>{e.exports=["weniger als 1 Minute"]},87358:e=>{e.exports=["{title} anzeigen"]},7827:e=>{e.exports=["{days} und {hours}"]},7435:e=>{e.exports="{exchange} by {originalExchange}"},19830:e=>{e.exports=["{hours} und {minutes}"]},1084:e=>{e.exports=["{listedExchange} Echtzeit Daten sind für registrierte User kostenlos verfügbar."]},11155:e=>{e.exports=["{symbolName} Daten sind um {time} Minuten verzögert."]},77033:e=>{e.exports=["Die Daten werden einmal pro {amount} Sekunde aktualisiert, selbst wenn es mehr Updates auf dem Markt gibt.","Die Daten werden einmal alle {amount} Sekunden aktualisiert, selbst wenn es mehr Updates auf dem Markt gibt."]},2121:e=>{e.exports=["Die Daten bei unseren Basis-Abonnements werden einmal pro {amount} Sekunde aktualisiert, selbst wenn es mehr Updates auf dem Markt gibt.","Die Daten bei unseren Basis-Abonnements werden einmal alle {amount} Sekunden aktualisiert, selbst wenn es mehr Updates auf dem Markt gibt."]},5223:e=>{e.exports=["Ein Update jede {amount} Sekunde","Ein Update alle {amount} Sekunden"]},58609:e=>{e.exports=["{number} Tag","{number} Tagen"]},24430:e=>{e.exports=["{number} Stunde","{number} Stunden"]},67151:e=>{e.exports=["{number} Minute","{number} Minuten"]}}]);
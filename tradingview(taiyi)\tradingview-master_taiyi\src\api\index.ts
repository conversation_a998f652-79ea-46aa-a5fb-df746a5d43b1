import { ResolutionString } from "@/public/charting_library";

type HistroyParams = {
  resolution?: number | string;
  symbol: string;
  from?: number;
  to?: number;
  quote?: number;
  compress?: number;
};

// EGX API functions (placeholder - replace with actual EGX data source)
export const getEGXQuote = (symbol: string, params?: { column?: string }) => {
  // TODO: Replace with actual EGX API endpoint
  // For now, return mock data
  console.log(`[EGX API] Getting quote for ${symbol}`);

  return Promise.resolve({
    statusCode: 200,
    data: [{
      "200009": symbol.split(":")[1], // Stock code
      "200026": "25.50", // Mock price
    }]
  });
};

// Legacy function for backward compatibility
export const getQuoteBySymbol = getEGXQuote;

// 將tradingview的resolution轉成api的resolution, 1c是跨日
const formatResolution = (resolution: ResolutionString) => {
  switch (resolution) {
    case "1":
    case "5":
    case "10":
    case "15":
    case "30":
    case "60":
      return "1c";
    default:
      return /[0-9]+[DWM]/.test(resolution) ? String(resolution).replace(/[0-9]/g, "") : resolution;
  }
};

type HistoryProps = {
  from: number;
  to: number;
  resolution: ResolutionString;
  symbol: string;
};

// EGX Historical data function
export const getEGXHistories = async ({ from, to, resolution, symbol }: HistoryProps) => {
  console.log(`[EGX API] Getting history for ${symbol} from ${from} to ${to}`);

  // TODO: Replace with actual EGX historical data API
  // For now, generate mock OHLC data
  const mockData = generateMockOHLCData(from, to, resolution);

  return {
    statusCode: 200,
    data: mockData
  };
};

// Generate mock OHLC data for demonstration
const generateMockOHLCData = (from: number, to: number, resolution: ResolutionString) => {
  const bars = [];
  const timeStep = getTimeStep(resolution);
  let currentTime = from;
  let price = 25.0; // Starting price

  while (currentTime <= to) {
    const open = price;
    const change = (Math.random() - 0.5) * 2; // Random change between -1 and +1
    const close = Math.max(0.1, open + change);
    const high = Math.max(open, close) + Math.random() * 0.5;
    const low = Math.min(open, close) - Math.random() * 0.5;

    bars.push({
      t: currentTime,
      o: parseFloat(open.toFixed(2)),
      h: parseFloat(high.toFixed(2)),
      l: parseFloat(Math.max(0.1, low).toFixed(2)),
      c: parseFloat(close.toFixed(2)),
    });

    price = close;
    currentTime += timeStep;
  }

  return {
    t: bars.map(bar => bar.t),
    o: bars.map(bar => bar.o),
    h: bars.map(bar => bar.h),
    l: bars.map(bar => bar.l),
    c: bars.map(bar => bar.c),
  };
};

const getTimeStep = (resolution: ResolutionString): number => {
  switch (resolution) {
    case "1": return 60; // 1 minute
    case "5": return 300; // 5 minutes
    case "15": return 900; // 15 minutes
    case "30": return 1800; // 30 minutes
    case "60": return 3600; // 1 hour
    case "D": return 86400; // 1 day
    case "W": return 604800; // 1 week
    case "M": return 2592000; // 1 month (30 days)
    default: return 86400; // Default to 1 day
  }
};

// Legacy function for backward compatibility
export const getSymbolHistories = getEGXHistories;

export const GETv1HistoryBySymbol = ({ resolution = 5, symbol, from, to, quote = 0, compress }: HistroyParams) => {
  // Redirect to EGX function
  return getEGXHistories({
    from: from || 0,
    to: to || Math.floor(Date.now() / 1000),
    resolution: resolution.toString() as ResolutionString,
    symbol
  });
};

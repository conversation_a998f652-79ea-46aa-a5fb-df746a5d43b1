import { ResolutionString } from "@/public/charting_library";

// EODHD API Configuration
const EODHD_API_KEY = process.env.REACT_APP_EODHD_API_KEY || 'demo'; // Replace with your API key
const EODHD_BASE_URL = 'https://eodhd.com/api';

type HistroyParams = {
  resolution?: number | string;
  symbol: string;
  from?: number;
  to?: number;
  quote?: number;
  compress?: number;
};

// EGX Stock symbols mapping
export const EGX_STOCKS = {
  "COMI.EGX": { name: "Commercial International Bank", sector: "Banking" },
  "ETEL.EGX": { name: "Egyptian Company for Mobile Services", sector: "Telecom" },
  "SWDY.EGX": { name: "El Sewedy Electric Company", sector: "Industrial" },
  "HRHO.EGX": { name: "Hassan Allam Holding", sector: "Construction" },
  "EAST.EGX": { name: "Eastern Company", sector: "Tobacco" },
  "EGCH.EGX": { name: "Egyptian Chemical Industries (Kima)", sector: "Chemicals" },
  "EGAL.EGX": { name: "Egypt Aluminum", sector: "Industrial" },
  "ALEX.EGX": { name: "Alexandria Cement", sector: "Construction Materials" },
  "DOMT.EGX": { name: "Arabian Food Industries DOMTY", sector: "Food" },
  "EFID.EGX": { name: "Edita Food Industries", sector: "Food" },
};

// EODHD API functions for EGX data
export const getEODHDQuote = async (symbol: string) => {
  try {
    const response = await fetch(
      `${EODHD_BASE_URL}/real-time/${symbol}?api_token=${EODHD_API_KEY}&fmt=json`
    );

    if (!response.ok) {
      throw new Error(`EODHD API error: ${response.statusText}`);
    }

    const data = await response.json();

    // Transform EODHD response to match our expected format
    return {
      statusCode: 200,
      data: [{
        "200009": symbol.split(".")[0], // Stock code (COMI from COMI.EGX)
        "200026": data.close?.toString() || "0", // Current price
        timestamp: data.timestamp,
        change: data.change,
        change_p: data.change_p,
      }]
    };
  } catch (error) {
    console.error(`[EODHD API] Error fetching quote for ${symbol}:`, error);
    return {
      statusCode: 500,
      data: []
    };
  }
};

// Legacy function for backward compatibility
export const getQuoteBySymbol = getEODHDQuote;

// 將tradingview的resolution轉成api的resolution, 1c是跨日
const formatResolution = (resolution: ResolutionString) => {
  switch (resolution) {
    case "1":
    case "5":
    case "10":
    case "15":
    case "30":
    case "60":
      return "1c";
    default:
      return /[0-9]+[DWM]/.test(resolution) ? String(resolution).replace(/[0-9]/g, "") : resolution;
  }
};

type HistoryProps = {
  from: number;
  to: number;
  resolution: ResolutionString;
  symbol: string;
};

// EGX Historical data function
export const getEGXHistories = async ({ from, to, resolution, symbol }: HistoryProps) => {
  console.log(`[EGX API] Getting history for ${symbol} from ${from} to ${to}`);

  // TODO: Replace with actual EGX historical data API
  // For now, generate mock OHLC data
  const mockData = generateMockOHLCData(from, to, resolution);

  return {
    statusCode: 200,
    data: mockData
  };
};

// EODHD Historical data function
export const getEODHDHistoricalData = async ({ from, to, resolution, symbol }: HistoryProps) => {
  try {
    console.log(`[EODHD API] Getting historical data for ${symbol} from ${from} to ${to}`);

    // Convert timestamps to EODHD format (YYYY-MM-DD)
    const fromDate = new Date(from * 1000).toISOString().split('T')[0];
    const toDate = new Date(to * 1000).toISOString().split('T')[0];

    // Determine API endpoint based on resolution
    let endpoint = '';
    let period = '';

    if (resolution === 'D' || resolution === 'W' || resolution === 'M') {
      // End of day data
      endpoint = `${EODHD_BASE_URL}/eod/${symbol}`;
      period = resolution === 'W' ? 'w' : resolution === 'M' ? 'm' : 'd';
    } else {
      // Intraday data
      endpoint = `${EODHD_BASE_URL}/intraday/${symbol}`;
      period = `${resolution}m`; // Convert to minutes format
    }

    const url = `${endpoint}?api_token=${EODHD_API_KEY}&from=${fromDate}&to=${toDate}&period=${period}&fmt=json`;

    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`EODHD API error: ${response.statusText}`);
    }

    const data = await response.json();

    if (!Array.isArray(data) || data.length === 0) {
      return { statusCode: 200, data: { t: [], o: [], h: [], l: [], c: [] } };
    }

    // Transform EODHD response to TradingView format
    const transformedData = {
      t: data.map(item => {
        // Convert date/datetime to timestamp
        const date = item.datetime || item.date;
        return Math.floor(new Date(date).getTime() / 1000);
      }),
      o: data.map(item => parseFloat(item.open)),
      h: data.map(item => parseFloat(item.high)),
      l: data.map(item => parseFloat(item.low)),
      c: data.map(item => parseFloat(item.close)),
    };

    console.log(`[EODHD API] Successfully fetched ${data.length} data points for ${symbol}`);
    return { statusCode: 200, data: transformedData };

  } catch (error) {
    console.error(`[EODHD API] Error fetching historical data for ${symbol}:`, error);

    // Fallback to mock data for development
    console.log(`[EODHD API] Falling back to mock data for ${symbol}`);
    const mockData = generateMockOHLCData(from, to, resolution);
    return { statusCode: 200, data: mockData };
  }
};

// Generate mock OHLC data for demonstration/fallback
const generateMockOHLCData = (from: number, to: number, resolution: ResolutionString) => {
  const bars = [];
  const timeStep = getTimeStep(resolution);
  let currentTime = from;
  let price = 25.0; // Starting price

  while (currentTime <= to && bars.length < 1000) { // Limit to prevent infinite loops
    const open = price;
    const change = (Math.random() - 0.5) * 2; // Random change between -1 and +1
    const close = Math.max(0.1, open + change);
    const high = Math.max(open, close) + Math.random() * 0.5;
    const low = Math.min(open, close) - Math.random() * 0.5;

    bars.push({
      t: currentTime,
      o: parseFloat(open.toFixed(2)),
      h: parseFloat(high.toFixed(2)),
      l: parseFloat(Math.max(0.1, low).toFixed(2)),
      c: parseFloat(close.toFixed(2)),
    });

    price = close;
    currentTime += timeStep;
  }

  return {
    t: bars.map(bar => bar.t),
    o: bars.map(bar => bar.o),
    h: bars.map(bar => bar.h),
    l: bars.map(bar => bar.l),
    c: bars.map(bar => bar.c),
  };
};

const getTimeStep = (resolution: ResolutionString): number => {
  switch (resolution) {
    case "1": return 60; // 1 minute
    case "5": return 300; // 5 minutes
    case "15": return 900; // 15 minutes
    case "30": return 1800; // 30 minutes
    case "60": return 3600; // 1 hour
    case "D": return 86400; // 1 day
    case "W": return 604800; // 1 week
    case "M": return 2592000; // 1 month (30 days)
    default: return 86400; // Default to 1 day
  }
};

// Legacy functions for backward compatibility
export const getSymbolHistories = getEODHDHistoricalData;

export const GETv1HistoryBySymbol = ({ resolution = 5, symbol, from, to, quote = 0, compress }: HistroyParams) => {
  // Redirect to EODHD function
  return getEODHDHistoricalData({
    from: from || 0,
    to: to || Math.floor(Date.now() / 1000),
    resolution: resolution.toString() as ResolutionString,
    symbol
  });
};

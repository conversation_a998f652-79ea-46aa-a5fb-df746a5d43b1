import { OnReadyCallback, LibrarySymbolInfo, ResolutionString, PeriodParams, HistoryCallback, Bar, ErrorCallback } from "@/public/charting_library";
import { getSymbolHistories, getQuoteBySymbol, EGX_STOCKS } from "../api";
import { formatTime } from "../utils";

// EGX-specific datafeed for TradingView
const EGXDataFeed = {
  onReady: (callback: OnReadyCallback) => {
    console.log("[EGX DataFeed]: Initializing EGX datafeed");
    const config = {
      supported_resolutions: ["1", "5", "15", "30", "60", "D", "W", "M"] as ResolutionString[],
      supports_group_request: false,
      supports_marks: false,
      supports_search: true,
      supports_timescale_marks: false,
      currency_codes: ["EGP"],
      exchanges: [
        {
          value: "EGX",
          name: "Egyptian Exchange",
          desc: "Egyptian Exchange (EGX)"
        }
      ]
    };
    setTimeout(() => callback(config));
  },
  
  searchSymbols: (userInput: string, exchange: string, symbolType: string, onResultReadyCallback: any) => {
    console.log("[EGX DataFeed]: Searching for symbols:", userInput);
    
    const results = Object.keys(EGX_STOCKS)
      .filter(symbol => 
        symbol.toLowerCase().includes(userInput.toLowerCase()) ||
        EGX_STOCKS[symbol as keyof typeof EGX_STOCKS].name.toLowerCase().includes(userInput.toLowerCase())
      )
      .map(symbol => ({
        symbol: symbol,
        full_name: `EGX:${symbol.split('.')[0]}`,
        description: EGX_STOCKS[symbol as keyof typeof EGX_STOCKS].name,
        exchange: "EGX",
        ticker: symbol,
        type: "stock",
      }));
    
    console.log(`[EGX DataFeed]: Found ${results.length} matching symbols`);
    onResultReadyCallback(results);
  },

  resolveSymbol: async (symbolName: string, onSymbolResolvedCallback: (info: LibrarySymbolInfo) => void) => {
    console.log("[EGX DataFeed]: Resolving symbol", symbolName);
    
    // Convert symbol format (handle both EGX:COMI and COMI.EGX formats)
    let eodhSymbol = symbolName;
    if (symbolName.startsWith("EGX:")) {
      eodhSymbol = symbolName.replace("EGX:", "") + ".EGX";
    }
    
    const stockInfo = EGX_STOCKS[eodhSymbol as keyof typeof EGX_STOCKS];
    const description = stockInfo ? stockInfo.name : symbolName;

    try {
      // Try to get current quote to validate symbol and get price scale
      const quoteResponse = await getQuoteBySymbol(eodhSymbol);
      const price = quoteResponse?.data?.[0]?.["200026"] || "25.00";
      const countDecimal = price.toString().split(".")[1]?.length || 2;
      const pricescale = Math.pow(10, countDecimal);

      const symbolInfo: LibrarySymbolInfo = {
        description: description,
        name: symbolName,
        full_name: `EGX:${symbolName}`,
        ticker: eodhSymbol,
        session: "0930-1530", // EGX trading hours (9:30 AM - 3:30 PM Cairo time)
        timezone: "Africa/Cairo",
        type: "stock",
        exchange: "EGX",
        listed_exchange: "EGX",
        format: "price",
        has_intraday: true,
        has_daily: true,
        has_weekly_and_monthly: true,
        minmov: 1,
        minmove2: 0,
        fractional: false,
        currency_code: "EGP",
        pricescale: pricescale,
        supported_resolutions: ["1", "5", "15", "30", "60", "D", "W", "M"] as ResolutionString[],
        volume_precision: 0,
        data_status: "streaming",
      };
      
      console.log(`[EGX DataFeed]: Successfully resolved ${symbolName}`);
      onSymbolResolvedCallback(symbolInfo);
    } catch (error) {
      console.error(`[EGX DataFeed]: Error resolving symbol ${symbolName}:`, error);
      
      // Fallback symbol info
      const fallbackSymbolInfo: LibrarySymbolInfo = {
        description: description,
        name: symbolName,
        full_name: `EGX:${symbolName}`,
        ticker: eodhSymbol,
        session: "0930-1530",
        timezone: "Africa/Cairo",
        type: "stock",
        exchange: "EGX",
        listed_exchange: "EGX",
        format: "price",
        has_intraday: true,
        has_daily: true,
        has_weekly_and_monthly: true,
        minmov: 1,
        minmove2: 0,
        fractional: false,
        currency_code: "EGP",
        pricescale: 100, // Default 2 decimal places
        supported_resolutions: ["1", "5", "15", "30", "60", "D", "W", "M"] as ResolutionString[],
        volume_precision: 0,
        data_status: "streaming",
      };
      
      onSymbolResolvedCallback(fallbackSymbolInfo);
    }
  },

  getBars: async (
    symbolInfo: LibrarySymbolInfo,
    resolution: ResolutionString,
    periodParams: PeriodParams,
    onHistoryCallback: HistoryCallback,
    onErrorCallback: ErrorCallback
  ) => {
    console.log("[EGX DataFeed]: Getting bars for", symbolInfo.name);
    const symbol = symbolInfo.ticker || symbolInfo.name;
    const { from, to } = periodParams;

    if (symbol) {
      try {
        const { data, statusCode } = await getSymbolHistories({
          resolution,
          symbol,
          to,
          from,
        });

        console.log("[EGX DataFeed]: EODHD API Response");
        console.table({
          symbol: symbol,
          from: formatTime(from, "YYYYMMDD"),
          to: formatTime(to, "YYYYMMDD"),
          resolution,
          "data points": data?.t?.length,
        });

        if (statusCode !== 200 || !data?.t || data.t.length === 0) {
          console.log("[EGX DataFeed]: No data available");
          onHistoryCallback([], { noData: true });
          return;
        }

        const { l, h, o, c, t } = data;

        const bars = t?.map((timestamp: number, index: number) => ({
          time: timestamp * 1000, // Convert to milliseconds
          low: l[index],
          high: h[index],
          open: o[index],
          close: c[index],
        })) as Bar[];

        // Sort bars by time
        bars.sort((a: Bar, b: Bar) => a.time - b.time);

        console.log(`[EGX DataFeed]: Returning ${bars.length} bars for ${symbol}`);
        onHistoryCallback(bars, { noData: false });
      } catch (error) {
        console.error("[EGX DataFeed]: Error fetching data", error);
        const errorMessage = error instanceof Error ? error.message : String(error);
        onErrorCallback(errorMessage);
      }
    }
  },
  
  subscribeBars: (symbolInfo: LibrarySymbolInfo, resolution: ResolutionString, onRealtimeCallback: any, subscriberUID: string) => {
    console.log(`[EGX DataFeed]: Subscribing to real-time data for ${symbolInfo.name}`);
    // TODO: Implement real-time EGX data subscription when needed
  },
  
  unsubscribeBars: (subscriberUID: string) => {
    console.log(`[EGX DataFeed]: Unsubscribing ${subscriberUID}`);
    // TODO: Implement real-time data unsubscription when needed
  },
};

export default EGXDataFeed;

import { useEffect } from "react";

declare global {
  interface Window {
    TradingView: any;
    Datafeeds: any;
  }
}

const index = () => {
  useEffect(() => {
    const tvWidget = new window.TradingView.widget({
      container: "chartContainer",
      locale: "en", // Could be "ar" for Arabic
      library_path: "charting_library/",
      datafeed: new window.Datafeeds.UDFCompatibleDatafeed("https://demo_feed.tradingview.com"),
      symbol: "AAPL", // Back to demo symbol that works
      interval: "1D",
      fullscreen: true,
      disabled_features: ["use_localstorage_for_settings"],
      enabled_features: ["study_templates"],
      // EGX-ready configuration
      timezone: "Africa/Cairo",
      theme: "light",
      toolbar_bg: "#f1f3f6",
      studies_overrides: {},
    });

    return () => {
      tvWidget.remove();
    };
  }, []);

  return <div id="chartContainer"></div>;
};

export default index;

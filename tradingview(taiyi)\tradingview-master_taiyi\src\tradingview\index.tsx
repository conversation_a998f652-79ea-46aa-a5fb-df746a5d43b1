import { useEffect } from "react";
import EGXDataFeed from "./egx-datafeed";

declare global {
  interface Window {
    TradingView: any;
    Datafeeds: any;
  }
}

const index = () => {
  useEffect(() => {
    const tvWidget = new window.TradingView.widget({
      container: "chartContainer",
      locale: "en", // Could be "ar" for Arabic later
      library_path: "charting_library/",
      datafeed: EGXDataFeed, // Use our custom EGX datafeed
      symbol: "COMI.EGX", // Commercial International Bank - EGX stock!
      interval: "1D",
      fullscreen: true,
      disabled_features: ["use_localstorage_for_settings"],
      enabled_features: ["study_templates"],
      // EGX-specific configuration
      timezone: "Africa/Cairo",
      theme: "light",
      toolbar_bg: "#f1f3f6",
      studies_overrides: {},
      // EGX branding
      custom_css_url: "/egx-theme.css", // We'll create this later
      loading_screen: { backgroundColor: "#ffffff" },
    });

    return () => {
      tvWidget.remove();
    };
  }, []);

  return (
    <div>
      <div style={{
        padding: "10px",
        backgroundColor: "#1e3a8a",
        color: "white",
        textAlign: "center",
        fontSize: "18px",
        fontWeight: "bold"
      }}>
        🇪🇬 EGX Trading Platform - Egyptian Exchange
      </div>
      <div id="chartContainer"></div>
    </div>
  );
};

export default index;

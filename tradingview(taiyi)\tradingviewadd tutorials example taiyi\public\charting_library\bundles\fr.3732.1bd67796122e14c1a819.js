(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[3732],{20747:e=>{e.exports="Re"},9846:e=>{e.exports="A"},55765:e=>{e.exports="L"},14642:e=>{e.exports=["Foncé"]},69841:e=>{e.exports=["Clair"]},673:e=>{e.exports=Object.create(null),e.exports.d_dates=["j"],e.exports.h_dates="h",e.exports.m_dates="m",e.exports.s_dates="s",e.exports.in_dates=["en"]},97840:e=>{e.exports=["j"]},64302:e=>{e.exports="h"},79442:e=>{e.exports="m"},22448:e=>{e.exports="s"},16493:e=>{e.exports=["copie {title}"]},13395:e=>{e.exports="D"},37720:e=>{e.exports="M"},69838:e=>{e.exports="R"},59231:e=>{e.exports="T"},85521:e=>{e.exports="W"},13994:e=>{e.exports="h"},6791:e=>{e.exports="m"},2949:e=>{e.exports="s"},77297:e=>{e.exports="C"},56723:e=>{e.exports="H"},5801:e=>{e.exports="HL2"},98865:e=>{e.exports="HLC3"},42659:e=>{e.exports="OHLC4"},4292:e=>{e.exports=["B"]},78155:e=>{e.exports="O"},88601:e=>{e.exports=Object.create(null),e.exports.Close_input=["Fermeture"],e.exports.Back_input=["Retour"],e.exports.Minimize_input=["Minimiser"],e.exports["Hull MA_input"]=["Hul MA"],e.exports["{number} item_combobox_input"]=["{number} objet","{number} objets"],e.exports.Length_input=["Longueur"],e.exports.Plot_input=["Tracé"],e.exports.Zero_input=["Zéro"],e.exports.Signal_input="Signal",e.exports.Long_input="Long",e.exports.Short_input="Short",e.exports.UpperLimit_input=["Limite supérieure"],e.exports.LowerLimit_input=["Limite inférieure"],e.exports.Offset_input=["Décalage"],e.exports.length_input=["longueur"],e.exports.mult_input="mult",e.exports.short_input="short",e.exports.long_input="long",e.exports.Limit_input=["Limite"],e.exports.Move_input=["Mouvement"],e.exports.Value_input=["Valeur"],e.exports.Method_input=["Méthode"],e.exports["Values in status line_input"]=["Valeurs dans la ligne d'état"],e.exports["Labels on price scale_input"]=["Étiquettes sur l'échelle de prix"],e.exports["Accumulation/Distribution_input"]="Accumulation/Distribution",e.exports.ADR_B_input="ADR_B",e.exports["Equality Line_input"]=["Ligne d'égalité"],e.exports["Window Size_input"]=["Taille de la fenêtre"],e.exports.Sigma_input="Sigma",e.exports["Aroon Up_input"]="Aroon Up",e.exports["Aroon Down_input"]=["AroonDown"],e.exports.Upper_input=["Supérieur"],e.exports.Lower_input=["Inférieur"],e.exports.Deviation_input=["Déviation"],e.exports["Levels Format_input"]=["Format des Niveaux"],e.exports["Labels Position_input"]=["Position des Etiquettes"],e.exports["0 Level Color_input"]=["Couleur de niveau 0"],e.exports["0.236 Level Color_input"]=["Couleur de niveau 0.236"],e.exports["0.382 Level Color_input"]=["Couleur de niveau 0.382"],e.exports["0.5 Level Color_input"]=["Couleur de niveau 0.5"],e.exports["0.618 Level Color_input"]=["Couleur de niveau 0.618"],e.exports["0.65 Level Color_input"]=["Couleur de niveau 0.65"],e.exports["0.786 Level Color_input"]=["Couleur de niveau 0.786"],e.exports["1 Level Color_input"]=["Couleur de niveau 1"],e.exports["1.272 Level Color_input"]=["Couleur de niveau 1.272"],e.exports["1.414 Level Color_input"]=["Couleur de niveau 1.414"],
e.exports["1.618 Level Color_input"]=["Couleur de niveau 1.618"],e.exports["1.65 Level Color_input"]=["Couleur de niveau 1.65"],e.exports["2.618 Level Color_input"]=["Couleur de niveau 2.618"],e.exports["2.65 Level Color_input"]=["Couleur de niveau 2.65"],e.exports["3.618 Level Color_input"]=["Couleur de niveau 3.618"],e.exports["3.65 Level Color_input"]=["Couleur de niveau 3.65"],e.exports["4.236 Level Color_input"]=["Couleur de niveau 4.236"],e.exports["-0.236 Level Color_input"]=["Couleur de niveau -0.236"],e.exports["-0.382 Level Color_input"]=["Couleur de niveau -0.382"],e.exports["-0.618 Level Color_input"]=["Couleur de niveau -0.618"],e.exports["-0.65 Level Color_input"]=["Couleur de niveau -0.65"],e.exports.ADX_input="ADX",e.exports["ADX Smoothing_input"]=["ADXSmoothing"],e.exports["DI Length_input"]=["Longueur de DI"],e.exports.Smoothing_input=["Adoucissement"],e.exports.ATR_input="ATR",e.exports.Growing_input=["En croissance"],e.exports.Falling_input=["En chute"],e.exports["Color 0_input"]=["Couleur 0"],e.exports["Color 1_input"]=["Couleur 1"],e.exports.Source_input="Source",e.exports.StdDev_input="StdDev",e.exports.Basis_input=["Base"],e.exports.Median_input=["Médiane"],e.exports["Bollinger Bands %B_input"]=["Bandes de Bollinger %B"],e.exports.Overbought_input=["Suracheté"],e.exports.Oversold_input=["Survendu"],e.exports["Bollinger Bands Width_input"]=["Largeur des Bandes de Bollinger"],e.exports["RSI Length_input"]=["Longueur RSI"],e.exports["UpDown Length_input"]=["Longueur Haut-Bas"],e.exports["ROC Length_input"]=["Longueur ROC"],e.exports.MF_input="MF",e.exports.resolution_input=["résolution"],e.exports["Fast Length_input"]=["Longueur rapide"],e.exports["Slow Length_input"]=["Longueur lente"],e.exports["Chaikin Oscillator_input"]=["Oscillateur de Chaikin"],e.exports.P_input="P",e.exports.X_input="X",e.exports.Q_input="Q",e.exports.p_input="p",e.exports.x_input="x",e.exports.q_input="q",e.exports.Price_input=["Prix"],e.exports["Chande MO_input"]="Chande MO",e.exports["Zero Line_input"]=["Ligne de zéro"],e.exports["Color 2_input"]=["Couleur 2"],e.exports["Color 3_input"]=["Couleur 3"],e.exports["Color 4_input"]=["Couleur 4"],e.exports["Color 5_input"]=["Couleur 5"],e.exports["Color 6_input"]=["Couleur 6"],e.exports["Color 7_input"]=["Couleur 7"],e.exports["Color 8_input"]=["Couleur 8"],e.exports.CHOP_input="CHOP",e.exports["Upper Band_input"]=["Bande supérieure"],e.exports["Lower Band_input"]=["Bande inférieure"],e.exports.CCI_input="CCI",e.exports["Smoothing Line_input"]=["Ligne de lissage"],e.exports["Smoothing Length_input"]=["Longueur de lissage"],e.exports["WMA Length_input"]=["Longueur du WMA"],e.exports["Long RoC Length_input"]=["Grande longueur RoC"],e.exports["Short RoC Length_input"]=["Longueur du Short RoC"],e.exports.sym_input="sym",e.exports.Symbol_input=["Symbole"],e.exports.Correlation_input=["Corrélation"],e.exports.Period_input=["Période"],e.exports.Centered_input=["Centré"],e.exports["Detrended Price Oscillator_input"]=["Oscillateur de prix hors tendance"],
e.exports.isCentered_input=["estCentré"],e.exports.DPO_input="DPO",e.exports["ADX smoothing_input"]=["ADXsmoothing"],e.exports["+DI_input"]="+DI",e.exports["-DI_input"]="-DI",e.exports.DEMA_input="DEMA",e.exports["Multi timeframe_input"]=["Multi-plages temporelles"],e.exports.Timeframe_input=["Plage temporelle"],e.exports["Wait for timeframe closes_input"]=["Attendre la fermeture de la plage temporelle"],e.exports.Divisor_input=["Diviseur"],e.exports.EOM_input="EOM",e.exports["Elder's Force Index_input"]=["Indice Elder's Force"],e.exports.Percent_input=["Pourcent"],e.exports.Exponential_input=["Exponentiel"],e.exports.Average_input=["Moyenne"],e.exports["Upper Percentage_input"]=["Pourcentage supérieur"],e.exports["Lower Percentage_input"]=["Pourcentage inférieur"],e.exports.Fisher_input="Fisher",e.exports.Trigger_input="Trigger",e.exports.Level_input=["Niveau"],e.exports["Trader EMA 1 length_input"]=["Longueur Trader EMA 1"],e.exports["Trader EMA 2 length_input"]=["Longueur Trader EMA 2"],e.exports["Trader EMA 3 length_input"]=["Longueur Trader EMA 3"],e.exports["Trader EMA 4 length_input"]=["Longueur Trader EMA 4"],e.exports["Trader EMA 5 length_input"]=["Longueur Trader EMA 5"],e.exports["Trader EMA 6 length_input"]=["Longueur Trader EMA 6"],e.exports["Investor EMA 1 length_input"]=["Longueur Investisseur EMA 1"],e.exports["Investor EMA 2 length_input"]=["Longueur Investisseur EMA 2"],e.exports["Investor EMA 3 length_input"]=["Longueur Investisseur EMA 3"],e.exports["Investor EMA 4 length_input"]=["Longueur Investisseur EMA 4"],e.exports["Investor EMA 5 length_input"]=["Longueur Investisseur EMA 5"],e.exports["Investor EMA 6 length_input"]=["Longueur Investisseur EMA 6"],e.exports.HV_input="HV",e.exports["Conversion Line Periods_input"]=["Pérodes de lignes de conversion"],e.exports["Base Line Periods_input"]=["Périodes de ligne de base"],e.exports["Lagging Span_input"]=["Délai de retournement"],e.exports["Conversion Line_input"]=["Ligne de conversion"],e.exports["Base Line_input"]=["Ligne de base"],e.exports["Leading Span A_input"]="Leading Span A",e.exports["Leading Span B_input"]="Leading Span B",e.exports["Plots Background_input"]=["Arrière-plan des tracés"],e.exports["yay Color 0_input"]=["yay Couleur 0"],e.exports["yay Color 1_input"]=["yay Couleur 1"],e.exports.Multiplier_input=["Multiplicateur"],e.exports["Bands style_input"]=["Style de bandes"],e.exports.Middle_input=["Milieu"],e.exports.useTrueRange_input=["UtiliserVraieGamme"],e.exports.ROCLen1_input="ROCLen1",e.exports.ROCLen2_input="ROCLen2",e.exports.ROCLen3_input="ROCLen3",e.exports.ROCLen4_input="ROCLen4",e.exports.SMALen1_input="SMALen1",e.exports.SMALen2_input="SMALen2",e.exports.SMALen3_input="SMALen3",e.exports.SMALen4_input="SMALen4",e.exports.SigLen_input="SigLen",e.exports.KST_input="KST",e.exports.Sig_input="Sig",e.exports.roclen1_input="roclen1",e.exports.roclen2_input="roclen2",e.exports.roclen3_input="roclen3",e.exports.roclen4_input="roclen4",e.exports.smalen1_input="smalen1",e.exports.smalen2_input="smalen2",
e.exports.smalen3_input="smalen3",e.exports.smalen4_input="smalen4",e.exports.siglen_input="siglen",e.exports["Upper Deviation_input"]=["Ecart supérieur"],e.exports["Lower Deviation_input"]=["Écart inférieur"],e.exports["Use Upper Deviation_input"]=["Utiliser l'écart supérieur"],e.exports["Use Lower Deviation_input"]=["Utiliser l'écart inférieur"],e.exports.Count_input=["Compter"],e.exports.Crosses_input="Crosses",e.exports.MOM_input="MOM",e.exports.MA_input="MA",e.exports["Length EMA_input"]=["Longueur EMA"],e.exports["Length MA_input"]=["Longueur MA"],e.exports["Fast length_input"]=["Longueur rapide"],e.exports["Slow length_input"]=["Longueur lente"],e.exports["Signal smoothing_input"]=["Adoucissement du signal"],e.exports["Simple ma(oscillator)_input"]=["Simple ma(oscillateur)"],e.exports["Simple ma(signal line)_input"]=["Simple ma(ligne de signal)"],e.exports.Histogram_input=["Histogramme"],e.exports.MACD_input="MACD",e.exports.fastLength_input=["LongueurRapide"],e.exports.slowLength_input=["Longueurlente"],e.exports.signalLength_input=["Longueur de signal"],e.exports.NV_input="NV",e.exports.OnBalanceVolume_input=["Volume OnBalance"],e.exports.Start_input=["Début"],e.exports.Increment_input=["Incrément"],e.exports["Max value_input"]=["Valeur max"],e.exports.ParabolicSAR_input=["SAR Parabolique"],e.exports.start_input="start",e.exports.increment_input=["incrément"],e.exports.maximum_input="maximum",e.exports["Short length_input"]=["Longueur du Short"],e.exports["Long length_input"]=["Grande longueur"],e.exports.OSC_input="OSC",e.exports.shortlen_input="shortlen",e.exports.longlen_input="longlen",e.exports.PVT_input="PVT",e.exports.ROC_input="ROC",e.exports.RSI_input="RSI",e.exports.RVGI_input="RVGI",e.exports.RVI_input="RVI",e.exports["Long period_input"]=["Longue période"],e.exports["Short period_input"]=["Période du Short"],e.exports["Signal line period_input"]=["Période de la ligne de signal"],e.exports.SMI_input="SMI",e.exports["SMI Ergodic Oscillator_input"]=["Oscillateur SMI Ergodic"],e.exports.Indicator_input=["Indicateur"],e.exports.Oscillator_input=["Oscillateur"],e.exports.K_input="K",e.exports.D_input="D",e.exports.smoothK_input="smoothK",e.exports.smoothD_input="smoothD",e.exports["%K_input"]="%K",e.exports["%D_input"]="%D",e.exports["Stochastic Length_input"]=["Longueur stochastique"],e.exports["RSI Source_input"]=["Source RSI"],e.exports.lengthRSI_input=["longueurRSI"],e.exports.lengthStoch_input=["longueurStoch"],e.exports.TRIX_input="TRIX",e.exports.TEMA_input="TEMA",e.exports["Long Length_input"]=["Grande longueur"],e.exports["Short Length_input"]=["Longueur du Short"],e.exports["Signal Length_input"]=["Longueur du signal"],e.exports.Length1_input=["Longueur 1"],e.exports.Length2_input=["Longueur 2"],e.exports.Length3_input=["Longueur 3"],e.exports.length7_input=["longueur7"],e.exports.length14_input=["longueur14"],e.exports.length28_input=["longueur28"],e.exports.UO_input="UO",e.exports.VWMA_input="VWMA",e.exports.len_input="len",e.exports["VI +_input"]="VI +",e.exports["VI -_input"]="VI -",
e.exports["%R_input"]="%R",e.exports["Jaw Length_input"]=["Longueur de Jaw"],e.exports["Teeth Length_input"]=["Longueur des Teeth"],e.exports["Lips Length_input"]=["Longueur des lips"],e.exports.Jaw_input="Jaw",e.exports.Teeth_input="Teeth",e.exports.Lips_input="Lips",e.exports["Jaw Offset_input"]="Jaw Offset",e.exports["Teeth Offset_input"]="Teeth Offset",e.exports["Lips Offset_input"]="Lips Offset",e.exports["Down fractals_input"]=["Fractales inférieures"],e.exports["Up fractals_input"]=["Fractales supérieures"],e.exports.Periods_input=["Périodes"],e.exports.Shapes_input=["Formes"],e.exports["show MA_input"]=["Montrer MA"],e.exports["MA Length_input"]=["Longueur MA"],e.exports["Color based on previous close_input"]=["Couleur basée sur la clôture précédente"],e.exports["Rows Layout_input"]=["Disposition des rangées"],e.exports["Row Size_input"]=["Taille de la rangée"],e.exports.Volume_input="Volume",e.exports["Value Area volume_input"]=["Volume de la zone de valeur"],e.exports["Extend Right_input"]=["Étendre à droite"],e.exports["Extend POC Right_input"]=["Étendre POC à droite"],e.exports["Extend VAH Right_input"]=["Étendre VAH à droite"],e.exports["Extend VAL Right_input"]=["Étendre VAL à droite"],e.exports["Value Area Volume_input"]=["Volume de la zone de valeur"],e.exports.Placement_input=["Localisation"],e.exports.POC_input="POC",e.exports["Developing Poc_input"]=["Développer POC"],e.exports["Up Volume_input"]=["Volume d'achat"],e.exports["Down Volume_input"]=["Volume des transactions de vente"],e.exports["Value Area_input"]=["Zone de valeur"],e.exports["Histogram Box_input"]=["Boîte d'histogramme"],e.exports["Value Area Up_input"]=["Zone de valeur vers le haut"],e.exports["Value Area Down_input"]=["Zone de valeur vers le bas"],e.exports["Number Of Rows_input"]=["Nombre de rangées"],e.exports["Ticks Per Row_input"]=["Ticks par rangée"],e.exports["Up/Down_input"]=["Haut/Bas"],e.exports.Total_input="Total",e.exports.Delta_input="Delta",e.exports.Bar_input=["Barre"],e.exports.Day_input=["Jours"],e.exports["Deviation (%)_input"]="Deviation (%)",e.exports.Depth_input=["Profondeur"],e.exports["Extend to last bar_input"]=["Etendre jusqu'à la dernière barre"],e.exports.Simple_input="Simple",e.exports.Weighted_input=["Pondérée"],e.exports["Wilder's Smoothing_input"]=["Lissage de Wilder"],e.exports["1st Period_input"]=["1ère période"],e.exports["2nd Period_input"]=["2ème période"],e.exports["3rd Period_input"]=["3ème période"],e.exports["4th Period_input"]=["4ème période"],e.exports["5th Period_input"]=["5ème période"],e.exports["6th Period_input"]=["6ème période"],e.exports["Rate of Change Lookback_input"]=["Rétrospective des taux de change"],e.exports["Instrument 1_input"]="Instrument 1",e.exports["Instrument 2_input"]="Instrument 2",e.exports["Rolling Period_input"]=["Période de roulement"],e.exports["Standard Errors_input"]=["Erreurs Standard"],e.exports["Averaging Periods_input"]=["Périodes de moyennes"],e.exports["Days Per Year_input"]=["Jours par an"],
e.exports["Market Closed Percentage_input"]=["Pourcentage de marché fermé"],e.exports["ATR Mult_input"]="ATR Mult",e.exports.VWAP_input="VWAP",e.exports["Anchor Period_input"]=["Période de référence"],e.exports.Session_input="Session",e.exports.Week_input=["Semaine"],e.exports.Month_input=["Mois"],e.exports.Year_input=["Année"],e.exports.Decade_input=["Décennie"],e.exports.Century_input=["Siècle"],e.exports.Sessions_input="Sessions",e.exports["Each (pre-market, market, post-market)_input"]=["Chacune (pré-marché, marché, post-marché)"],e.exports["Pre-market only_input"]=["Pré-marché uniquement"],e.exports["Market only_input"]=["Marché uniquement"],e.exports["Post-market only_input"]=["Post-marché uniquement"],e.exports["Main chart symbol_input"]=["Symbole principal du graphique"],e.exports["Another symbol_input"]=["Un autre symbole"],e.exports.Line_input=["Droite"],e.exports["Nothing selected_combobox_input"]=["Pas de sélection"],e.exports["All items_combobox_input"]=["Tous les objets"],e.exports.Cancel_input=["Annuler"],e.exports.Open_input=["Ouvrir"]},54138:e=>{e.exports=["Inverser l'échelle"]},47807:e=>{e.exports=["Indexé sur 100"]},34727:e=>{e.exports=["Logarithmique"]},19238:e=>{e.exports=["Pas d'étiquettes superposées"]},70361:e=>{e.exports=["Pourcents"]},72116:e=>{e.exports=["Normal"]},33021:e=>{e.exports="ETH"},75610:e=>{e.exports=["Horaires de trading électronique"]},97442:e=>{e.exports=["Horaires de trading étendus"]},32929:e=>{e.exports=["post"]},56137:e=>{e.exports=["pré"]},98801:e=>{e.exports=["Post-marché"]},56935:e=>{e.exports=["Pré-marché"]},63798:e=>{e.exports="RTH"},24380:e=>{e.exports=["Horaires de trading régulier"]},27991:e=>{e.exports=["Mai"]},68716:e=>{e.exports=Object.create(null),e.exports.Technicals_study=["Techniques"],e.exports["Average Day Range_study"]=["Plage de jours moyenne"],e.exports["Bull Bear Power_study"]=["Puissance Bull Bear"],e.exports["Capital expenditures_study"]=["Dépenses d'investissement"],e.exports["Cash to debt ratio_study"]=["Ratio trésorerie/dette"],e.exports["Debt to EBITDA ratio_study"]=["Ratio dette/ EBITDA"],e.exports["Directional Movement Index_study"]=["Indice de mouvement directionnel"],e.exports.DMI_study=["IMD"],e.exports["Dividend payout ratio %_study"]=["Ratio de distribution des dividendes %"],e.exports["Equity to assets ratio_study"]=["Ratio capitaux propres/actifs"],e.exports["Enterprise value to EBIT ratio_study"]=["Ratio valeur d'entreprise/ EBIT"],e.exports["Enterprise value to EBITDA ratio_study"]=["Ratio valeur d'entreprise/ EBITDA"],e.exports["Enterprise value to revenue ratio_study"]=["Ratio valeur d'entreprise/revenus"],e.exports["Goodwill, net_study"]="Goodwill, net",e.exports["Ichimoku Cloud_study"]=["Nuage Ichimoku"],e.exports.Ichimoku_study="Ichimoku",e.exports["Moving Average Convergence Divergence_study"]=["Convergence Divergence Moyenne Mobile"],e.exports["Operating income_study"]=["Bénéfice d'exploitation"],e.exports["Price to book ratio_study"]=["Ratio cours/valeur comptable"],
e.exports["Price to cash flow ratio_study"]=["Ratio prix/flux de trésorerie"],e.exports["Price to earnings ratio_study"]=["Ratio cours/bénéfice"],e.exports["Price to free cash flow ratio_study"]=["Ratio cours/flux de trésorerie disponibles"],e.exports["Price to sales ratio_study"]=["Rapport prix/ventes"],e.exports["Float shares outstanding_study"]=["Actions flottantes en circulation"],e.exports["Total common shares outstanding_study"]=["Total des actions ordinaires en circulation"],e.exports["Volume Weighted Average Price_study"]=["Prix moyen pondéré par le volume"],e.exports["Volume Weighted Moving Average_study"]=["Moyenne mobile pondérée par le volume"],e.exports["Williams Percent Range_study"]=["Fourchette de pourcentages de Williams"],e.exports.Doji_study="Doji",e.exports["Spinning Top Black_study"]=["Toupie Noire"],e.exports["Spinning Top White_study"]=["Toupie Blanche"],e.exports["Accounts payable_study"]=["Comptes créanciers"],e.exports["Accounts receivables, gross_study"]=["Créances clients, brutes"],e.exports["Accounts receivable - trade, net_study"]=["Comptes débiteurs - clients, nets"],e.exports.Accruals_study=["Charges à payer"],e.exports["Accrued payroll_study"]=["Paiements accumulés"],e.exports["Accumulated depreciation, total_study"]=["Dépréciation cumulée, total"],e.exports["Additional paid-in capital/Capital surplus_study"]=["Capital libéré additionnel/excédent de capital"],e.exports["After tax other income/expense_study"]=["Autres produits et charges après impôts"],e.exports["Altman Z-score_study"]="Altman Z-score",e.exports.Amortization_study=["Amortissement"],e.exports["Amortization of intangibles_study"]=["Amortissement des actifs immatériels"],e.exports["Amortization of deferred charges_study"]=["Amortissement des charges différées"],e.exports["Asset turnover_study"]=["Rotation des actifs"],e.exports["Average basic shares outstanding_study"]=["Nombre moyen d'actions de base en circulation"],e.exports["Bad debt / Doubtful accounts_study"]=["Créances irrécouvrables / comptes douteux"],e.exports["Basic EPS_study"]=["Bénéfice par action de base"],e.exports["Basic earnings per share (Basic EPS)_study"]=["Résultat de base par action (BPA de base)"],e.exports["Beneish M-score_study"]="Beneish M-score",e.exports["Book value per share_study"]=["Valeur comptable par action"],e.exports["Buyback yield %_study"]=["Rendement des rachats %"],e.exports["Capital and operating lease obligations_study"]=["Engagements résultant de contrats de financement et de location-exploitation"],e.exports["Capital expenditures - fixed assets_study"]=["Dépenses d'investissement - immobilisations"],e.exports["Capital expenditures - other assets_study"]=["Dépenses en capital - autres actifs"],e.exports["Capitalized lease obligations_study"]=["Obligations de location capitalisées"],e.exports["Cash and short term investments_study"]=["Liquidités et investissements à court terme"],e.exports["Cash conversion cycle_study"]=["Cycle de conversion des liquidités"],e.exports["Cash & equivalents_study"]=["Liquidités et équivalents"],
e.exports["Cash from financing activities_study"]=["Cash lié aux activités de financement"],e.exports["Cash from investing activities_study"]=["Cash lié aux activités d'investissement"],e.exports["Cash from operating activities_study"]=["Cash lié aux activités d'exploitation"],e.exports["Change in accounts payable_study"]=["Variation des comptes créditeurs"],e.exports["Change in accounts receivable_study"]=["Variation des comptes débiteurs"],e.exports["Change in accrued expenses_study"]=["Variation des charges à payer"],e.exports["Change in inventories_study"]=["Variation des inventaires"],e.exports["Change in other assets/liabilities_study"]=["Variation des autres actifs/passifs"],e.exports["Change in taxes payable_study"]=["Variation des taxes à payer"],e.exports["Changes in working capital_study"]=["Évolution du fonds de roulement"],e.exports["COGS to revenue ratio_study"]=["Ratio COGS/revenus"],e.exports["Common dividends paid_study"]=["Dividendes ordinaires versés"],e.exports["Common equity, total_study"]=["Fonds propres ordinaires, total"],e.exports["Common stock par/Carrying value_study"]=["Valeur nominale/valeur comptable des actions ordinaires"],e.exports["Cost of goods_study"]=["Coût des biens"],e.exports["Cost of goods sold_study"]=["Coût des marchandises vendues"],e.exports["Current portion of LT debt and capital leases_study"]=["Partie courante de la dette LT et des contrats de location-acquisition"],e.exports["Current ratio_study"]=["Ratio actuel"],e.exports["Days inventory_study"]=["Jours en inventaire"],e.exports["Days payable_study"]=["Jours d'échéance"],e.exports["Days sales outstanding_study"]=["Jours d'encours des ventes"],e.exports["Debt to assets ratio_study"]=["Ratio dette/actif"],e.exports["Debt to equity ratio_study"]=["Ratio dette/fonds propres"],e.exports["Debt to revenue ratio_study"]=["Ratio dette/recettes"],e.exports["Deferred income, current_study"]=["Revenu différé, courant"],e.exports["Deferred income, non-current_study"]=["Produits différés, non courants"],e.exports["Deferred tax assets_study"]=["Actifs d'impôts différés"],e.exports["Deferred taxes (cash flow)_study"]=["Impôts différés (cash-flow)"],e.exports["Deferred tax liabilities_study"]=["Passifs d'impôts différés"],e.exports.Depreciation_study=["Dépréciation"],e.exports["Deprecation and amortization_study"]=["Dépréciation et amortissement"],e.exports["Depreciation & amortization (cash flow)_study"]=["Dépréciation et amortissement (cash flow)"],e.exports["Depreciation/depletion_study"]=["Amortissement/épuisement"],e.exports["Diluted EPS_study"]="Diluted EPS",e.exports["Diluted earnings per share (Diluted EPS)_study"]=["Résultat dilué par action (BPA dilué)"],e.exports["Diluted net income available to common stockholders_study"]=["Résultat net dilué disponible pour les actionnaires ordinaires"],e.exports["Diluted shares outstanding_study"]=["Actions diluées en circulation"],e.exports["Dilution adjustment_study"]=["Ajustement de dilution"],e.exports["Discontinued operations_study"]=["Opérations abandonnées"],
e.exports["Dividends payable_study"]=["Dividendes à payer"],e.exports["Dividends per share - common stock primary issue_study"]=["Dividendes par action - émission primaire d'actions ordinaires"],e.exports["Dividend yield %_study"]=["Rendement du dividende %"],e.exports["Earnings yield_study"]=["Rendement des bénéfices"],e.exports.EBIT_study="EBIT",e.exports.EBITDA_study="EBITDA",e.exports["EBITDA margin %_study"]=["Marge EBITDA %"],e.exports["Effective interest rate on debt %_study"]=["Taux d'intérêt effectif sur la dette"],e.exports["Enterprise value_study"]=["Valeur d'entreprise"],e.exports["EPS basic one year growth_study"]=["Croissance de base du BPA sur un an"],e.exports["EPS diluted one year growth_study"]=["Croissance du BPA dilué sur un an"],e.exports["EPS estimates_study"]=["Estimations du BPA"],e.exports["Equity in earnings_study"]=["Participation aux bénéfices"],e.exports["Financing activities – other sources_study"]=["Activités de financement - autres sources"],e.exports["Financing activities – other uses_study"]=["Activités de financement - autres usages"],e.exports["Free cash flow_study"]=["Free Cash Flow"],e.exports["Free cash flow margin %_study"]=["Marge de cash-flow libre %"],e.exports["Fulmer H factor_study"]=["Facteur Fulmer H"],e.exports["Funds from operations_study"]=["Fonds provenant des opérations"],e.exports["Goodwill to assets ratio_study"]=["Ratio goodwill/actif"],e.exports["Graham's number_study"]=["Nombre de Graham"],e.exports["Gross margin %_study"]=["Marge brute %"],e.exports["Gross profit_study"]=["Profit brut"],e.exports["Gross profit to assets ratio_study"]=["Ratio marge brute/actif"],e.exports["Gross property/plant/equipment_study"]=["Immobilier/usine/équipement brut"],e.exports.Impairments_study=["Dépréciations d'actifs"],e.exports["Income Tax Credits_study"]=["Crédits d'impôt sur le revenu"],e.exports["Income tax, current_study"]=["Impôt sur le revenu, courant"],e.exports["Income tax, current - domestic_study"]=["Impôt sur le revenu, courant - national"],e.exports["Income Tax, current - foreign_study"]=["Impôt sur le revenu, courant - étranger"],e.exports["Income tax, deferred_study"]=["Impôt sur le revenu, différé"],e.exports["Income tax, deferred - domestic_study"]=["Impôt sur le revenu, différé - national"],e.exports["Income tax, deferred - foreign_study"]=["Impôt sur le revenu, différé - étranger"],e.exports["Income tax payable_study"]=["Impôt sur le revenu à payer"],e.exports["Interest capitalized_study"]=["Intérêt capitalisé"],e.exports["Interest coverage_study"]=["Couverture d'intérêt"],e.exports["Interest expense, net of interest capitalized_study"]=["Charges d'intérêts, après déduction des intérêts capitalisés"],e.exports["Interest expense on debt_study"]=["Charges d'intérêts sur la dette"],e.exports["Inventories - finished goods_study"]=["Inventaires - produits finis"],e.exports["Inventories - progress payments & other_study"]=["Inventaires - paiements échelonnés et autres"],e.exports["Inventories - raw materials_study"]=["Inventaires - matières premières"],
e.exports["Inventories - work in progress_study"]=["Inventaires - travaux en cours"],e.exports["Inventory to revenue ratio_study"]=["Ratio inventaire/recettes"],e.exports["Inventory turnover_study"]=["Rotation des stocks"],e.exports["Investing activities – other sources_study"]=["Activités d'investissement - autres sources"],e.exports["Investing activities – other uses_study"]=["Activités d'investissement - autres utilisations"],e.exports["Investments in unconsolidated subsidiaries_study"]=["Investissements dans des filiales non consolidées"],e.exports["Issuance of long term debt_study"]=["Émission de la dette à long terme"],e.exports["Issuance/retirement of debt, net_study"]=["Émission/retrait de la dette, nette"],e.exports["Issuance/retirement of long term debt_study"]=["Émission/retrait de la dette à long terme"],e.exports["Issuance/retirement of other debt_study"]=["Émission/retrait d'autres dettes"],e.exports["Issuance/retirement of short term debt_study"]=["Émission/retrait de la dette à court terme"],e.exports["Issuance/retirement of stock, net_study"]=["Émission/retraite d'actions, nette"],e.exports["KZ index_study"]=["Indice KZ"],e.exports["Legal claim expense_study"]=["Frais de contentieux"],e.exports["Long term debt_study"]=["Dette à long terme"],e.exports["Long term debt excl. lease liabilities_study"]=["Dettes à long terme, hors loyers"],e.exports["Long term debt to total assets ratio_study"]=["Ratio de la dette à long terme sur l'actif total"],e.exports["Long term debt to total equity ratio_study"]=["Ratio dette à long terme/fonds propres totaux"],e.exports["Long term investments_study"]=["Investissements à long terme"],e.exports["Market capitalization_study"]=["Capitalisation boursière"],e.exports["Minority interest_study"]=["Participation minoritaire"],e.exports["Miscellaneous non-operating expense_study"]=["Charges diverses hors exploitation"],e.exports["Net current asset value per share_study"]=["Valeur de l'actif net courant par action"],e.exports["Net debt_study"]=["Dette nette"],e.exports["Net income_study"]=["Revenu net"],e.exports["Net income before discontinued operations_study"]=["Résultat net avant activités abandonnées"],e.exports["Net income (cash flow)_study"]=["Revenu net (cash flow)"],e.exports["Net income per employee_study"]=["Revenu net par employé"],e.exports["Net intangible assets_study"]=["Immobilisations incorporelles nettes"],e.exports["Net margin %_study"]=["Marge nette %"],e.exports["Net property/plant/equipment_study"]=["Immeubles/usines/équipements nets"],e.exports["Non-cash items_study"]=["Éléments hors trésorerie"],e.exports["Non-controlling/minority interest_study"]=["Intérêt minoritaire/non-contrôlant"],e.exports["Non-operating income, excl. interest expenses_study"]=["Produits hors exploitation, hors charges d'intérêt"],e.exports["Non-operating income, total_study"]=["Revenu hors exploitation, total"],e.exports["Non-operating interest income_study"]=["Revenus d'intérêts hors exploitation"],e.exports["Note receivable - long term_study"]=["Note à recevoir - long terme"],
e.exports["Notes payable_study"]=["Effets à payer"],e.exports["Number of employees_study"]=["Nombre d'employés"],e.exports["Number of shareholders_study"]=["Nombre d'actionnaires"],e.exports["Operating earnings yield %_study"]=["Rendement du bénéfice d'exploitation %"],e.exports["Operating expenses (excl. COGS)_study"]=["Dépenses d'exploitation (hors COGS)"],e.exports["Operating lease liabilities_study"]=["Dettes de location opérationnelle"],e.exports["Operating margin %_study"]=["Marge opérationnelle %"],e.exports["Other COGS_study"]=["Autres COGS"],e.exports["Other common equity_study"]=["Autres fonds propres ordinaires"],e.exports["Other current assets, total_study"]=["Autres actifs courants, total"],e.exports["Other current liabilities_study"]=["Autres dettes à court terme"],e.exports["Other cost of goods sold_study"]=["Autres coûts des marchandises vendues"],e.exports["Other exceptional charges_study"]=["Autres charges exceptionnelles"],e.exports["Other financing cash flow items, total_study"]=["Autres éléments financiers du cash flow, total"],e.exports["Other intangibles, net_study"]=["Autres actifs incorporels, net"],e.exports["Other investing cash flow items, total_study"]=["Autres éléments d'investissement du cash flow, total"],e.exports["Other investments_study"]=["Autres investissements"],e.exports["Other liabilities, total_study"]=["Autres dettes, total"],e.exports["Other long term assets, total_study"]=["Autres actifs à long terme, total"],e.exports["Other non-current liabilities, total_study"]=["Autres passifs non courants, total"],e.exports["Other operating expenses, total_study"]=["Autres dépenses de fonctionnement, total"],e.exports["Other receivables_study"]=["Autres créances"],e.exports["Other short term debt_study"]=["Autres dettes à court terme"],e.exports["Paid in capital_study"]=["Capital payé"],e.exports["PEG ratio_study"]=["Ratio PEG"],e.exports["Piotroski F-score_study"]=["Score-F Piotroski"],e.exports["Preferred dividends_study"]=["Dividendes préférentiels"],e.exports["Preferred dividends paid_study"]=["Dividendes préférentiels versés"],e.exports["Preferred stock, carrying value_study"]=["Actions préférentielles, valeur comptable"],e.exports["Prepaid expenses_study"]=["Dépenses prépayées"],e.exports["Pretax equity in earnings_study"]=["Participation aux bénéfices avant impôts"],e.exports["Pretax income_study"]=["Revenu avant impôts"],e.exports["Price earnings ratio forward_study"]=["Ratio prix/bénéfices à terme"],e.exports["Price sales ratio forward_study"]=["Rapport prix-vente à terme"],e.exports["Price to tangible book ratio_study"]=["Ratio cours/valeur comptable tangible"],e.exports["Provision for risks & charge_study"]=["Provision pour risques et charges"],e.exports["Purchase/acquisition of business_study"]=["Achat/acquisition d'une entreprise"],e.exports["Purchase of investments_study"]=["Achat d'investissements"],e.exports["Purchase/sale of business, net_study"]=["Achat/vente d'entreprise, net"],e.exports["Purchase/sale of investments, net_study"]=["Achat/vente de placements, net"],
e.exports["Quality ratio_study"]=["Ratio de qualité"],e.exports["Quick ratio_study"]="Quick ratio",e.exports["Reduction of long term debt_study"]=["Réduction de la dette à long terme"],e.exports["Repurchase of common & preferred stock_study"]=["Rachat d'actions ordinaires et privilégiées"],e.exports["Research & development_study"]=["Recherche & Développement"],e.exports["Research & development to revenue ratio_study"]=["Rapport entre la recherche et le développement et les recettes"],e.exports["Restructuring charge_study"]=["Charges de restructuration"],e.exports["Retained earnings_study"]=["Bénéfices non distribués"],e.exports["Return on assets %_study"]=["Rendement des actifs %"],e.exports["Return on equity %_study"]=["Rendement des capitaux propres %"],e.exports["Return on equity adjusted to book value %_study"]=["Rendement des fonds propres ajustés à la valeur comptable %"],e.exports["Return on invested capital %_study"]=["Rendement du capital investi %"],e.exports["Return on tangible assets %_study"]=["Rendement des actifs corporels %"],e.exports["Return on tangible equity %_study"]=["Rendement des fonds propres tangibles %"],e.exports["Revenue estimates_study"]=["Estimation des recettes"],e.exports["Revenue one year growth_study"]=["Croissance du chiffre d'affaires sur un an"],e.exports["Revenue per employee_study"]=["Revenu par employé"],e.exports["Sale/maturity of investments_study"]=["Vente/maturité des investissements"],e.exports["Sale of common & preferred stock_study"]=["Vente d'actions ordinaires et privilégiées"],e.exports["Sale of fixed assets & businesses_study"]=["Vente d'actifs immobilisés et d'entreprises"],e.exports["Selling/general/admin expenses, other_study"]=["Frais de vente/généraux/d'administration, autres"],e.exports["Selling/general/admin expenses, total_study"]=["Frais de vente, frais généraux et frais d'administration, total"],e.exports["Shareholders' equity_study"]=["Capitaux propres des actionnaires"],e.exports["Shares buyback ratio %_study"]=["Ratio de rachat d'actions %"],e.exports["Short term debt_study"]=["Dette à court terme"],e.exports["Short term debt excl. current portion of LT debt_study"]=["Dette à court terme excluant la partie à court terme de la dette à long terme"],e.exports["Short term investments_study"]=["Investissements à court terme"],e.exports["Sloan ratio %_study"]=["Ratio de Sloan %"],e.exports["Springate score_study"]="Springate score",e.exports["Sustainable growth rate_study"]=["Taux de croissance durable"],e.exports["Tangible book value per share_study"]=["Valeur comptable tangible par action"],e.exports["Tangible common equity ratio_study"]=["Ratio de fonds propres tangibles"],e.exports.Taxes_study="Taxes",e.exports["Tobin's Q (approximate)_study"]=["Tobin's Q (approximatif)"],e.exports["Total assets_study"]=["Total des actifs"],e.exports["Total cash dividends paid_study"]=["Total des dividendes versés en espèces"],e.exports["Total current assets_study"]=["Total des actifs courants"],e.exports["Total current liabilities_study"]=["Total passif actuel"],
e.exports["Total debt_study"]=["Dette totale"],e.exports["Total equity_study"]=["Total des avoirs"],e.exports["Total inventory_study"]=["Inventaire total"],e.exports["Total liabilities_study"]=["Total des passifs"],e.exports["Total liabilities & shareholders' equities_study"]=["Total du passif et des capitaux propres"],e.exports["Total non-current assets_study"]=["Total des actifs immobilisés"],e.exports["Total non-current liabilities_study"]=["Total des passifs immobilisés"],e.exports["Total operating expenses_study"]=["Total des dépenses d'exploitation"],e.exports["Total receivables, net_study"]=["Total des créances, nettes"],e.exports["Total revenue_study"]=["Revenu total"],e.exports["Treasury stock - common_study"]=["Actions de trésorerie - ordinaires"],e.exports["Unrealized gain/loss_study"]=["Gain/perte non réalisé(e)"],e.exports["Unusual income/expense_study"]=["Produits et charges exceptionnels"],e.exports["Zmijewski score_study"]=["Score de Zmijewski"],e.exports["Valuation ratios_study"]=["Ratios de valorisation"],e.exports["Profitability ratios_study"]=["Ratios de rentabilité"],e.exports["Liquidity ratios_study"]=["Ratios de liquidités"],e.exports["Solvency ratios_study"]=["Ratios de solvabilité"],e.exports["Key stats_study"]=["Statistiques clés"],e.exports["Accumulation/Distribution_study"]=["Accumulation/Répartition"],e.exports["Accumulative Swing Index_study"]=["Indice d'oscillation cumulative"],e.exports["Advance/Decline_study"]="Advance/Decline",e.exports["Arnaud Legoux Moving Average_study"]=["Arnaud Legoux Moyenne Mobile"],e.exports.Aroon_study="Aroon",e.exports.ASI_study="ASI",e.exports["Average Directional Index_study"]="Average Directional Index",e.exports["Average True Range_study"]=["Moyenne de la vraie amplitude"],e.exports["Awesome Oscillator_study"]=["Oscillateur merveilleux"],e.exports["Balance of Power_study"]=["Équilibre des forces"],e.exports["Bollinger Bands %B_study"]="Bollinger Bands %B",e.exports["Bollinger Bands Width_study"]=["Largeur Bollinger Bands"],e.exports["Bollinger Bands_study"]="Bollinger Bands",e.exports["Chaikin Money Flow_study"]="Chaikin Money Flow",e.exports["Chaikin Oscillator_study"]="Chaikin Oscillator",e.exports["Chande Kroll Stop_study"]=["Stop Chande Kroll"],e.exports["Chande Momentum Oscillator_study"]="Chande Momentum Oscillator",e.exports["Chop Zone_study"]="Chop Zone",e.exports["Choppiness Index_study"]="Choppiness Index",e.exports["Commodity Channel Index_study"]="Commodity Channel Index",e.exports["Connors RSI_study"]=["RSI de Connors"],e.exports["Coppock Curve_study"]=["Courbe Coppock"],e.exports["Correlation Coefficient_study"]=["Coefficient de Corellation"],e.exports.CRSI_study="CRSI",e.exports["Detrended Price Oscillator_study"]="Detrended Price Oscillator",e.exports["Directional Movement_study"]="Directional Movement",e.exports["Donchian Channels_study"]="Donchian Channels",e.exports["Double EMA_study"]=["EMA Double"],e.exports["Ease Of Movement_study"]="Ease Of Movement",e.exports["Elder Force Index_study"]=["Index de Force Elder"],
e.exports["EMA Cross_study"]="EMA Cross",e.exports.Envelopes_study=["Enveloppes"],e.exports["Fisher Transform_study"]="Fisher Transform",e.exports["Fixed Range_study"]=["Gamme fixe"],e.exports["Fixed Range Volume Profile_study"]=["Profil de volume à gamme fixe"],e.exports["Guppy Multiple Moving Average_study"]=["Moyenne mobile multiple de Guppy"],e.exports["Historical Volatility_study"]=["Volatilité Historique"],e.exports["Hull Moving Average_study"]=["Moyenne Mobile de Hull"],e.exports["Keltner Channels_study"]=["Canaux de Keltner"],e.exports["Klinger Oscillator_study"]="Klinger Oscillator",e.exports["Know Sure Thing_study"]="Know Sure Thing",e.exports["Least Squares Moving Average_study"]=["Moyenne Mobile Least Squares"],e.exports["Linear Regression Curve_study"]=["Courbe de régression linéaire"],e.exports["MA Cross_study"]=["Croisement MA"],e.exports["MA with EMA Cross_study"]=["MA avec EMA Cross"],e.exports["MA/EMA Cross_study"]="MA/EMA Cross",e.exports.MACD_study="MACD",e.exports["Mass Index_study"]=["Index de masse"],e.exports["McGinley Dynamic_study"]="McGinley Dynamic",e.exports.Median_study=["Médiane"],e.exports.Momentum_study="Momentum",e.exports["Money Flow_study"]=["Flux d'argent"],e.exports["Moving Average Channel_study"]=["Canal de moyenne mobile"],e.exports["Moving Average Exponential_study"]=["Moyenne mobile exponentielle"],e.exports["Moving Average Weighted_study"]=["Moyenne mobile pondérée"],e.exports["Moving Average Simple_study"]=["Moyenne mobile simple"],e.exports["Net Volume_study"]=["Volume net"],e.exports["On Balance Volume_study"]=["Volume On Balance"],e.exports["Parabolic SAR_study"]=["Parabolique SAR"],e.exports["Pivot Points Standard_study"]=["Points Pivots Standard"],e.exports["Periodic Volume Profile_study"]=["Profil de volume périodique"],e.exports["Price Channel_study"]=["Canal de prix"],e.exports["Price Oscillator_study"]="Price Oscillator",e.exports["Price Volume Trend_study"]=["Tendance volume-prix"],e.exports["Rate Of Change_study"]=["Taux de changement"],e.exports["Relative Strength Index_study"]="Relative Strength Index",e.exports["Relative Vigor Index_study"]="Relative Vigor Index",e.exports["Relative Volatility Index_study"]="Relative Volatility Index",e.exports["Session Volume_study"]=["Volume de la session"],e.exports["Session Volume HD_study"]=["Volume de la session HD"],e.exports["Session Volume Profile_study"]=["Profil de volume des sessions"],e.exports["Session Volume Profile HD_study"]=["Profil de volume des sessions HD"],e.exports["SMI Ergodic Indicator/Oscillator_study"]=["SMI Ergodic Indicateur/Oscillateur"],e.exports["Smoothed Moving Average_study"]="Smoothed Moving Average",e.exports.Stoch_study="Stoch",e.exports["Stochastic RSI_study"]="Stochastic RSI",e.exports.Stochastic_study="Stochastic",e.exports["Triple EMA_study"]=["EMA Triple"],e.exports.TRIX_study="TRIX",e.exports["True Strength Indicator_study"]=["Indicateur True Strength"],e.exports["Ultimate Oscillator_study"]=["Oscillateur Ultimate"],e.exports["Visible Range_study"]=["Gamme visible"],
e.exports["Visible Range Volume Profile_study"]=["Profil de volume de la gamme visible"],e.exports["Volume Oscillator_study"]=["Oscillateur de volume"],e.exports.Volume_study="Volume",e.exports.Vol_study="Vol",e.exports["Vortex Indicator_study"]=["Indicateur Vortex"],e.exports.VWAP_study="VWAP",e.exports.VWMA_study="VWMA",e.exports["Williams %R_study"]="Williams %R",e.exports["Williams Alligator_study"]=["Alligator Williams"],e.exports["Williams Fractal_study"]=["Fractale de Williams"],e.exports["Zig Zag_study"]="Zig Zag",e.exports["24-hour Volume_study"]=["Volume sur 24 heures"],e.exports["Ease of Movement_study"]=["Ease Of Movement"],e.exports["Elders Force Index_study"]=["Indice de force d'Elder"],e.exports.Envelope_study=["Enveloppe"],e.exports.Gaps_study="Gaps",e.exports["Linear Regression Channel_study"]=["Chaîne de regression Linéaire"],e.exports["Moving Average Ribbon_study"]=["Ruban de la Moyenne Mouvante"],e.exports["Multi-Time Period Charts_study"]=["Graphiques de périodes de temps multiples"],e.exports["Open Interest_study"]=["Intérêt ouvert"],e.exports["Rob Booker - Intraday Pivot Points_study"]=["Rob Booker - Points Pivots intraday"],e.exports["Rob Booker - Knoxville Divergence_study"]=["Rob Booker - Divergence de Knoxville"],e.exports["Rob Booker - Missed Pivot Points_study"]=["Rob Booker - Points Pivots Manqués"],e.exports["Rob Booker - Reversal_study"]=["Rob Booker - Inversion"],e.exports["Rob Booker - Ziv Ghost Pivots_study"]=["Rob Booker - Pivots Ziv Ghost"],e.exports.Supertrend_study=["Supertendance"],e.exports["Technical Ratings_study"]=["Notations Techniques"],e.exports["True Strength Index_study"]=["Indice de force réelle"],e.exports["Up/Down Volume_study"]=["Volume Haut/Bas"],e.exports["Visible Average Price_study"]=["Prix Moyen Visible"],e.exports["Williams Fractals_study"]=["Fractales de Williams"],e.exports["Keltner Channels Strategy_study"]=["Stratégie des Canaux de Keltner"],e.exports["Rob Booker - ADX Breakout_study"]="Rob Booker - ADX Breakout",e.exports["Supertrend Strategy_study"]=["Stratégie Super tendance"],e.exports["Technical Ratings Strategy_study"]=["Stratégie de notation technique"],e.exports["Auto Anchored Volume Profile_study"]=["Profil de volume ancré automatique"],e.exports["Auto Fib Extension_study"]=["Extension Auto Fib"],e.exports["Auto Fib Retracement_study"]=["Retracement Auto Fib"],e.exports["Auto Pitchfork_study"]="Auto Pitchfork",e.exports["Bearish Flag Chart Pattern_study"]=["Schéma graphique à drapeau baissier"],e.exports["Bullish Flag Chart Pattern_study"]=["Graphique en drapeau haussier"],e.exports["Bearish Pennant Chart Pattern_study"]=["Graphique en forme de fanion baissier"],e.exports["Bullish Pennant Chart Pattern_study"]=["Schéma graphique à fanion haussier"],e.exports["Double Bottom Chart Pattern_study"]=["Schéma graphique à double bas"],e.exports["Double Top Chart Pattern_study"]=["Schéma graphique à double haut"],e.exports["Elliott Wave Chart Pattern_study"]=["Schéma graphique des vagues d'Elliott"],
e.exports["Falling Wedge Chart Pattern_study"]=["Schéma graphique à biseau descendant"],e.exports["Head And Shoulders Chart Pattern_study"]=["Schéma graphique tête et épaules"],e.exports["Inverse Head And Shoulders Chart Pattern_study"]=["Schéma graphique tête et épaules inversées"],e.exports["Rectangle Chart Pattern_study"]=["Schéma graphique rectangle"],e.exports["Rising Wedge Chart Pattern_study"]=["Schéma graphique à biseau ascendant"],e.exports["Triangle Chart Pattern_study"]=["Schéma graphique en triangle"],e.exports["Triple Bottom Chart Pattern_study"]=["Schéma graphique à triple bas"],e.exports["Triple Top Chart Pattern_study"]=["Schéma graphique à triple haut"],e.exports["VWAP Auto Anchored_study"]=["VWAP Auto Ancré"],e.exports["*All Candlestick Patterns*_study"]=["*Tous les schémas en chandelier*"],e.exports["Abandoned Baby - Bearish_study"]=["Bébé abandonné - Baissier"],e.exports["Abandoned Baby - Bullish_study"]=["Bébé abandonné - Haussier"],e.exports["Dark Cloud Cover - Bearish_study"]=["Couverture nuageuse sombre - Baissier"],e.exports["Doji Star - Bearish_study"]=["Etoile Doji - Baissier"],e.exports["Doji Star - Bullish_study"]=["Etoile Doji - Haussier"],e.exports["Downside Tasuki Gap - Bearish_study"]=["Gap Tasuki descendant - Baissier"],e.exports["Dragonfly Doji - Bullish_study"]=["Dragonfly Doji - Haussier"],e.exports["Engulfing - Bearish_study"]=["Enlacement - Baissier"],e.exports["Engulfing - Bullish_study"]=["Enlacement - Haussier"],e.exports["Evening Doji Star - Bearish_study"]=["Etoile Doji du soir - Baissier"],e.exports["Evening Star - Bearish_study"]=["Etoile du soir - Baissier"],e.exports["Falling Three Methods - Bearish_study"]=["Méthode des trois chutes - Baissier"],e.exports["Falling Window - Bearish_study"]=["Fenêtre descendante - Baissier"],e.exports["Gravestone Doji - Bearish_study"]=["Doji en pierre tombale - Baissier"],e.exports["Hammer - Bullish_study"]=["Marteau - Haussier"],e.exports["Hanging Man - Bearish_study"]=["Homme suspendu - Baissier"],e.exports["Harami - Bearish_study"]=["Harami - Baissier"],e.exports["Harami - Bullish_study"]=["Harami - Haussier"],e.exports["Inverted Hammer - Bullish_study"]=["Marteau inversé - Haussier"],e.exports["Kicking - Bearish_study"]=["Coup de pied - Baissier"],e.exports["Kicking - Bullish_study"]=["Coup de pied - Haussier"],e.exports["Long Lower Shadow - Bullish_study"]=["Longue ombre inférieure - Haussier"],e.exports["Long Upper Shadow - Bearish_study"]=["Longue ombre supérieure - Baissier"],e.exports["Marubozu Black - Bearish_study"]=["Marubozu Noir - Baissier"],e.exports["Marubozu White - Bullish_study"]=["Marubozu Blanc - Haussier"],e.exports["Morning Doji Star - Bullish_study"]=["Étoile Doji du matin - Haussier"],e.exports["Morning Star - Bullish_study"]=["Étoile du matin - Haussier"],e.exports["On Neck - Bearish_study"]=["Sur le cou - Baissier"],e.exports["Piercing - Bullish_study"]=["Piercing - Haussier"],e.exports["Rising Three Methods - Bullish_study"]=["Méthodes des trois hausses - Haussier"],
e.exports["Rising Window - Bullish_study"]=["Fenêtre ascendante - Haussier"],e.exports["Shooting Star - Bearish_study"]=["Étoile filante - Baissier"],e.exports["Three Black Crows - Bearish_study"]=["Trois corbeaux noirs - Baissier"],e.exports["Three White Soldiers - Bullish_study"]=["Trois Soldats Blancs - Haussier"],e.exports["Tri-Star - Bearish_study"]=["Tri-Star - Baissier"],e.exports["Tri-Star - Bullish_study"]=["Tri-Star - Haussier"],e.exports["Tweezer Top - Bearish_study"]=["Tweezer Top - Baissier"],e.exports["Upside Tasuki Gap - Bullish_study"]=["Gap Tasuki ascendant - Haussier"],e.exports.SuperTrend_study="SuperTrend",e.exports["Average Price_study"]=["Prix moyen"],e.exports["Typical Price_study"]=["Prix typique"],e.exports["Median Price_study"]=["Prix médian"],e.exports["Money Flow Index_study"]=["Indice Money Flow"],e.exports["Moving Average Double_study"]=["Moyenne mobile Double"],e.exports["Moving Average Triple_study"]=["Moyenne mobile Triple"],e.exports["Moving Average Adaptive_study"]=["Moyenne mobile Adaptative"],e.exports["Moving Average Hamming_study"]=["Moyenne mobile Hamming"],e.exports["Moving Average Modified_study"]=["Moyenne mobile Modifiée"],e.exports["Moving Average Multiple_study"]=["Moyenne mobile Multiple"],e.exports["Linear Regression Slope_study"]=["Pente de régression linéaire"],e.exports["Standard Error_study"]=["Erreur Standard"],e.exports["Standard Error Bands_study"]=["Bandes d'erreur standard"],e.exports["Correlation - Log_study"]=["Corrélation - Log"],e.exports["Standard Deviation_study"]=["Déviation Standard"],e.exports["Chaikin Volatility_study"]=["Volatilité de Chaikin"],e.exports["Volatility Close-to-Close_study"]=["Volatilité fermeture à fermeture"],e.exports["Volatility Zero Trend Close-to-Close_study"]=["Volatilité zéro tendance fermeture à fermeture"],e.exports["Volatility O-H-L-C_study"]=["Volatilité O-H-L-C"],e.exports["Volatility Index_study"]=["Indice de volatilité"],e.exports["Trend Strength Index_study"]=["Indice de force de tendance"],e.exports["Majority Rule_study"]=["Règle de la majorité"],e.exports["Advance Decline Line_study"]=["Ligne Hausse Baisse"],e.exports["Advance Decline Ratio_study"]=["Ratio Hausse Baisse"],e.exports["Advance/Decline Ratio (Bars)_study"]=["Ratio Hausse/Baisse (barres)"],e.exports["BarUpDn Strategy_study"]=["Stratégie BarUpDn"],e.exports["Bollinger Bands Strategy directed_study"]=["Stratégie dirigée Bollinger Bands"],e.exports["Bollinger Bands Strategy_study"]=["Stratégie Bollinger Bands"],e.exports.ChannelBreakOutStrategy_study="ChannelBreakOutStrategy",e.exports.Compare_study=["Comparer"],e.exports["Conditional Expressions_study"]=["Expressions Conditionnelles"],e.exports.ConnorsRSI_study="ConnorsRSI",e.exports["Consecutive Up/Down Strategy_study"]=["Stratégie ascendante/descendante consécutive"],e.exports["Cumulative Volume Index_study"]=["Index Volume Cumulé"],e.exports["Divergence Indicator_study"]=["Indicateur de Divergence"],e.exports["Greedy Strategy_study"]="Greedy Strategy",
e.exports["InSide Bar Strategy_study"]=["Stratégie InSide Bar"],e.exports["Keltner Channel Strategy_study"]=["Stratégie du canal de Keltner"],e.exports["Linear Regression_study"]=["Régression linéaire"],e.exports["MACD Strategy_study"]=["Stratégie MACD"],e.exports["Momentum Strategy_study"]="Momentum Strategy",e.exports["Moon Phases_study"]=["Phases de la lune"],e.exports["Moving Average Convergence/Divergence_study"]=["Convergence/ Divergence Moyenne Mobile"],e.exports["MovingAvg Cross_study"]=["Croisement MovingAvg"],e.exports["MovingAvg2Line Cross_study"]=["Croisement MovingAvg2Line"],e.exports["OutSide Bar Strategy_study"]=["Stratégie OutSide Bar"],e.exports.Overlay_study=["Superposition"],e.exports["Parabolic SAR Strategy_study"]=["Stratégie SAR parabolique"],e.exports["Pivot Extension Strategy_study"]=["Stratégie Extension Pivot"],e.exports["Pivot Points High Low_study"]=["Points Pivots Haut Bas"],e.exports["Pivot Reversal Strategy_study"]=["Stratégie Pivot Reversal"],e.exports["Price Channel Strategy_study"]=["Stratégie canal de prix"],e.exports["RSI Strategy_study"]=["Stratégie RSI"],e.exports["SMI Ergodic Indicator_study"]=["Indicateur SMI Ergodic"],e.exports["SMI Ergodic Oscillator_study"]=["Oscillateur SMI Ergodic"],e.exports["Stochastic Slow Strategy_study"]=["Stratégie Slow Stochastic"],e.exports["Volatility Stop_study"]=["Stop selon volatilité"],e.exports["Volty Expan Close Strategy_study"]=["Stratégie Volty Expan Close"],e.exports["Woodies CCI_study"]=["CCI Woodies"]},59791:e=>{e.exports="Anchored Volume Profile"},40434:e=>{e.exports=["Profil de volume à gamme fixe"]},32819:e=>{e.exports="Vol"},66051:e=>{e.exports=["Mineure"]},86054:e=>{e.exports="Minute"},20936:e=>{e.exports=["Texte"]},98478:e=>{e.exports=["Impossible de copier"]},34004:e=>{e.exports=["Impossible de couper"]},96260:e=>{e.exports=["Impossible de coller"]},94370:e=>{e.exports=["Compte à rebours jusqu'à la fermeture de la barre"]},15168:e=>{e.exports="Colombo"},36018:e=>{e.exports=["Colonnes"]},19372:e=>{e.exports=["Commentaire"]},20229:e=>{e.exports=["Comparer ou Ajouter un Symbole"]},46689:e=>{e.exports=["Confirmer les entrées"]},43432:e=>{e.exports=["Copenhague"]},35216:e=>{e.exports=["Copier"]},87898:e=>{e.exports=["Copier la mise en page du graphique"]},28851:e=>{e.exports=["Copier le prix"]},94099:e=>{e.exports=["Le Caire"]},64149:e=>{e.exports="Callout"},63528:e=>{e.exports=["Bougies"]},46837:e=>{e.exports="Caracas"},53705:e=>{e.exports="Casablanca"},49329:e=>{e.exports=["Variation"]},28089:e=>{e.exports=["Changer le Symbole"]},13737:e=>{e.exports="Change alerts color"},99374:e=>{e.exports=["Changer l’intervalle"]},14412:e=>{e.exports=["Propriétés du graphique"]},26619:e=>{e.exports=["Graphique par TradingView"]},12011:e=>{e.exports=["Image du graphique copiée dans le presse-papiers {emoji}"]},79393:e=>{e.exports=["Code d'intégration de l'image du graphique copié dans le presse-papiers {emoji}"]},59884:e=>{e.exports=["Îles Chatham"]},28244:e=>{e.exports="Chicago"},49648:e=>{e.exports="Chongqing"},90068:e=>{e.exports=["Cercle"]},
32234:e=>{e.exports=["Cliquer pour établir un point"]},52977:e=>{e.exports=["Cloner"]},31691:e=>{e.exports=["Fermeture"]},50493:e=>{e.exports=["Créer un ordre"]},52302:e=>{e.exports=["Créer un ordre de limite"]},29908:e=>{e.exports="Cross"},60997:e=>{e.exports=["Ligne de croisement"]},81520:e=>{e.exports=["Devises"]},98486:e=>{e.exports=["Intervalle actuel et supérieur"]},73106:e=>{e.exports=["Intervalle actuel et inférieur"]},85964:e=>{e.exports=["Intervalle actuel uniquement"]},17206:e=>{e.exports=["Courbe"]},95176:e=>{e.exports="Cycle"},87761:e=>{e.exports=["Lignes cycliques"]},27891:e=>{e.exports=["Modèle Cypher"]},56996:e=>{e.exports=["Une mise en page portant ce nom existe déjà"]},30192:e=>{e.exports=["Une mise en page portant ce nom existe déjà. Voulez-vous l'écraser?"]},32852:e=>{e.exports=["Figure en ABCD"]},88010:e=>{e.exports="Amsterdam"},37422:e=>{e.exports=["Analyser la Configuration du Trade"]},99873:e=>{e.exports=["Ancrage"]},66828:e=>{e.exports=["Note ancrée"]},94782:e=>{e.exports=["Texte ancré"]},61704:e=>{e.exports=["VWAP ancré"]},63597:e=>{e.exports=["Ajouter une ligne horizontale"]},45743:e=>{e.exports=["Ajouter un Symbole"]},8700:e=>{e.exports=["Ajouter une alerte"]},64615:e=>{e.exports=["Ajouter une alerte pour {title}"]},7005:e=>{e.exports=["Ajouter une alerte sur {title} à {price}"]},3612:e=>{e.exports=["Ajouter une métrique financière pour {instrumentName}"]},92206:e=>{e.exports=["Ajouter un indicateur/une stratégie à {studyTitle}.."]},34810:e=>{e.exports=["Ajouter une note de texte pour {symbol}"]},75669:e=>{e.exports=["Ajouter cette métrique financière à l'ensemble de la mise en page"]},64288:e=>{e.exports=["Ajouter cet indicateur à l'ensemble de la mise en page"]},77920:e=>{e.exports=["Ajouter cette stratégie à l'ensemble de la mise en page"]},34059:e=>{e.exports=["Ajouter ce symbole à l'ensemble de la mise en page"]},17365:e=>{e.exports=["Adélaïde"]},9408:e=>{e.exports=["Toujours invisible"]},71997:e=>{e.exports=["Toujours visible"]},97305:e=>{e.exports=["Tous les Indicateurs Et Outils de Dessin"]},59192:e=>{e.exports=["Tous les intervalles"]},14452:e=>{e.exports="Almaty"},5716:e=>{e.exports=["Appliquer une Vague d'Eliliot"]},19263:e=>{e.exports=["Appliquer Vague d'Elliot Majeure"]},15818:e=>{e.exports=["Appliquer Vague d'Elliot Mineure"]},50352:e=>{e.exports=["Appliquer Vague d'Elliot Intermédiaire"]},66631:e=>{e.exports=["Appliquer Point de Décision Manuel"]},15682:e=>{e.exports=["Appliquer Risque/Rendement Manuel"]},15644:e=>{e.exports=["Appliquer Vague WPT vers le Bas"]},5897:e=>{e.exports=["Appliquer Vague WPT vers le Haut"]},13345:e=>{e.exports=["Appliquer paramètres par Défaut"]},95910:e=>{e.exports=["Appliquer ces indicateurs à l'ensemble de la mise en page"]},42762:e=>{e.exports=["Avr"]},45104:e=>{e.exports="Arc"},42097:e=>{e.exports=["Région"]},96237:e=>{e.exports=["Flèche"]},48732:e=>{e.exports=["Flèche vers le bas"]},82473:e=>{e.exports=["Marqueur fléché"]},8738:e=>{e.exports=["Flèche vers le Bas"]},35062:e=>{e.exports=["Flèche vers la Gauche"]},92163:e=>{
e.exports=["Flèche vers la Droite"]},33196:e=>{e.exports=["Flèche vers le Haut"]},10650:e=>{e.exports=["Flèche vers le haut"]},59340:e=>{e.exports=["Ashkhabad"]},13468:e=>{e.exports=["A la fermeture"]},21983:e=>{e.exports=["Athènes"]},86951:e=>{e.exports="Auto"},50834:e=>{e.exports=["Auto (adapte les données à l'écran)"]},38465:e=>{e.exports=["Août"]},8975:e=>{e.exports=["Étiquette du prix moyen de clôture"]},87899:e=>{e.exports=["Ligne de prix de clôture moyen"]},22554:e=>{e.exports=["Moy"]},54173:e=>{e.exports="Bogota"},53260:e=>{e.exports=["Bahrein"]},40664:e=>{e.exports=["Ballon"]},32376:e=>{e.exports="Bangkok"},19149:e=>{e.exports=["Bar Replay n'est pas disponible pour ce type de graphique. Voulez-vous quitter Bar Replay ?"]},38660:e=>{e.exports=["Bar Replay n'est pas disponible pour cet intervalle de temps. Voulez-vous quitter Bar Replay?"]},16812:e=>{e.exports=["Barres"]},98838:e=>{e.exports=["Configuration de barres"]},17712:e=>{e.exports=["Ligne de base"]},54861:e=>{e.exports="Belgrade"},26825:e=>{e.exports="Berlin"},30251:e=>{e.exports=["Pinceau"]},90204:e=>{e.exports=["Bruxelles"]},5262:e=>{e.exports="Bratislava"},59901:e=>{e.exports=["Mettre en avant"]},26354:e=>{e.exports=["Mettre au premier plan"]},11741:e=>{e.exports="Brisbane"},37728:e=>{e.exports=["Bucarest"]},87143:e=>{e.exports="Budapest"},82446:e=>{e.exports="Buenos Aires"},82128:e=>{e.exports=["Par TradingView"]},75190:e=>{e.exports=["Aller à cette date"]},38342:e=>{e.exports=["Aller à {lineToolName}"]},75139:e=>{e.exports=["Compris"]},81180:e=>{e.exports=["Boite de Gan"]},68102:e=>{e.exports=["Éventail de Gann"]},66321:e=>{e.exports=["Carré de Gann"]},87107:e=>{e.exports=["Carré de Gann fixe"]},7914:e=>{e.exports=["Flux fantôme d'informations"]},18367:e=>{e.exports=["Grand Supercycle"]},97065:e=>{e.exports=["Voulez-vous vraiment supprimer le modèle d'étude '{name}' ?"]},59368:e=>{e.exports=["Double Courbe"]},35273:e=>{e.exports=["Double-cliquez sur n'importe quel bord pour réinitialiser la grille de mise en page"]},5828:e=>{e.exports=["Double-cliquez pour terminer le tracé"]},63898:e=>{e.exports=["Double-cliquez pour terminer Polyline"]},42660:e=>{e.exports=["Vague Baissière 1 ou A"]},44788:e=>{e.exports=["Vague Baissière 2 ou B"]},71263:e=>{e.exports=["Vague Baissière 3"]},70573:e=>{e.exports=["Vague Baissière 4"]},59560:e=>{e.exports=["Vague Baissière 5"]},70437:e=>{e.exports=["Vague Baissière C"]},53831:e=>{e.exports=["Ouvrir la fenêtre des données"]},93345:e=>{e.exports=["Données fournies par"]},76912:e=>{e.exports="Date"},60222:e=>{e.exports=["Plage de dates"]},79859:e=>{e.exports=["Plage de dates et de prix"]},92203:e=>{e.exports=["Déc"]},69479:e=>{e.exports=["Degré"]},57701:e=>{e.exports="Denver"},24477:e=>{e.exports="Dhaka"},73720:e=>{e.exports=["Diamant"]},3556:e=>{e.exports=["Canal disjoint"]},62764:e=>{e.exports=["Déplacement"]},22903:e=>{e.exports=["Barre d'outils de dessin"]},8338:e=>{e.exports=["Tracez une ligne horizontale sur"]},22429:e=>{e.exports=["Dubaï"]},9497:e=>{e.exports="Dublin"},85223:e=>{e.exports="Emoji"},24435:e=>{
e.exports=["Entrer un nouveau nom de configuration graphique"]},93512:e=>{e.exports=["Éditer {title} alerte"]},91215:e=>{e.exports=["Vague Elliott de correction (ABC)"]},80983:e=>{e.exports=["Vague Elliott Double Combo (WXY)"]},74118:e=>{e.exports=["Vague Elliott d'impulsion (12345)"]},95840:e=>{e.exports=["Vague Triangle Elliott (ABCDE)"]},66637:e=>{e.exports=["Vague Triple Combo Elliott (WXYXZ)"]},69418:e=>{e.exports="Ellipse"},27558:e=>{e.exports=["Étendre les lignes d'alerte"]},2578:e=>{e.exports=["Ligne étendue"]},77295:e=>{e.exports=["Marché"]},2899:e=>{e.exports=["Volet existant au-dessus"]},53387:e=>{e.exports=["Volet existant au-dessous"]},36972:e=>{e.exports=["Prévision"]},17994:e=>{e.exports=["Échec de la sauvegarde de la bibliothèque"]},87375:e=>{e.exports=["Échec de l'enregistrement du script"]},35050:e=>{e.exports=["Févr"]},82719:e=>{e.exports=["Canal de Fibonacci"]},64192:e=>{e.exports=["Cercles de Fibonacci"]},63835:e=>{e.exports=["Retracement de Fibonacci"]},18072:e=>{e.exports=["Arcs de Résistance de la vitesse de Fibonacci"]},20877:e=>{e.exports=["Éventail de Résistance de la Vitesse de Fibonacci"]},76783:e=>{e.exports=["Spirale de Fibonacci"]},89037:e=>{e.exports=["Zone Temporelle de Fibonacci"]},72489:e=>{e.exports=["Coin de Fibonacci"]},21524:e=>{e.exports=["Drapeau"]},55678:e=>{e.exports=["Marque de Drapeau"]},29230:e=>{e.exports=["Haut/Bas Plat"]},92754:e=>{e.exports=["Basculé"]},42015:e=>{e.exports=["La partie fractionnelle n'est pas valide."]},47542:e=>{e.exports=["Les études fondamentales ne sont plus disponibles sur les graphiques"]},16245:e=>{e.exports=["Calcuta"]},3155:e=>{e.exports=["Katmandou"]},92901:e=>{e.exports="Kagi"},2693:e=>{e.exports="Karachi"},72374:e=>{e.exports=["Koweit"]},34911:e=>{e.exports=["Zone HLC"]},87338:e=>{e.exports="Ho Chi Minh"},61582:e=>{e.exports=["Bougies Creuses"]},32918:e=>{e.exports="Hong Kong"},61351:e=>{e.exports="Honolulu"},60049:e=>{e.exports=["Ligne Horizontale"]},76604:e=>{e.exports=["Rayon Horizontal"]},42616:e=>{e.exports=["Tête et épaules"]},40530:e=>{e.exports="Heikin Ashi"},99820:e=>{e.exports="Helsinki"},31971:e=>{e.exports=["Cacher"]},33911:e=>{e.exports=["Tout masquer"]},95551:e=>{e.exports=["Masquer tous les outils de dessin"]},44312:e=>{e.exports=["Masquer tous les dessins et indicateurs"]},67927:e=>{e.exports=["Masquer tous les dessins, indicateurs, positions & ordres"]},86306:e=>{e.exports=["Masquer tous les indicateurs"]},70803:e=>{e.exports=["Masquer toutes les positions & ordres"]},13277:e=>{e.exports=["Masquer les dessins"]},8251:e=>{e.exports=["Cacher les événements sur le graphique"]},44177:e=>{e.exports=["Masquer les indicateurs"]},2441:e=>{e.exports=["Cacher les marques de la barre"]},90540:e=>{e.exports=["Masquer les positions et les ordres"]},30777:e=>{e.exports=["Haut"]},31994:e=>{e.exports=["Haut-Bas"]},60259:e=>{e.exports=["Étiquettes de prix haut et bas"]},21803:e=>{e.exports=["Lignes de prix haut et bas"]},31895:e=>{e.exports=["En vedette"]},69085:e=>{
e.exports=["L'histogramme est trop grand, veuillez augmenter l'entrée \"Taille de la rangée\"."]},8122:e=>{e.exports=["L'histogramme est trop grand, veuillez réduire l'entrée \" Taille de la rangée \"."]},23450:e=>{e.exports="Image"},71778:e=>{e.exports=["Intermédiaire"]},14177:e=>{e.exports=["Symbole invalide"]},53239:e=>{e.exports=["Inverser l'échelle"]},20062:e=>{e.exports=["Indexé sur 100"]},81584:e=>{e.exports=["Étiquettes de valeur des indicateurs"]},31485:e=>{e.exports=["Libellés des indicateurs"]},27677:e=>{e.exports=["Ligne info"]},98767:e=>{e.exports=["Ajouter un indicateur"]},9114:e=>{e.exports=["À l'intérieur"]},12354:e=>{e.exports=["Fourchette Interne"]},26579:e=>{e.exports=["Icône"]},37885:e=>{e.exports="Istanbul"},87469:e=>{e.exports="Johannesburg"},52707:e=>{e.exports=["Djakarta"]},95425:e=>{e.exports=["Janv"]},42890:e=>{e.exports=["Jérusalem"]},6215:e=>{e.exports=["Juill"]},15224:e=>{e.exports=["Juin"]},36253:e=>{e.exports="Juneau"},15241:e=>{e.exports=["Sur la gauche"]},29404:e=>{e.exports=["Sur la droite"]},850:e=>{e.exports=["Oups!"]},675:e=>{e.exports=["Arborescence des objets"]},73546:e=>{e.exports="Oct"},39280:e=>{e.exports=["Ouverture"]},25595:e=>{e.exports=["Initial"]},82906:e=>{e.exports="Oslo"},8136:e=>{e.exports=["Bas"]},42284:e=>{e.exports=["Verrouiller"]},1441:e=>{e.exports=["Verrouiller/Déverrouiller"]},82232:e=>{e.exports=["Verrouiller la ligne du curseur vertical en fonction du temps"]},18219:e=>{e.exports=["Verrouiller le rapport prix / barre"]},12285:e=>{e.exports=["Logarithmique"]},50286:e=>{e.exports=["Londres"]},44604:e=>{e.exports=["Position Longue"]},87604:e=>{e.exports="Los Angeles"},18528:e=>{e.exports=["Etiquette vers le bas"]},13046:e=>{e.exports=["Etiquette vers le haut"]},94420:e=>{e.exports=["Étiquettes"]},89155:e=>{e.exports="Lagos"},25846:e=>{e.exports="Lima"},1277:e=>{e.exports=["Droite"]},38397:e=>{e.exports=["Ligne avec marqueurs"]},63492:e=>{e.exports=["Saut de ligne"]},83182:e=>{e.exports=["Lignes"]},78104:e=>{e.exports=["Lien vers l'image du graphique copié dans le presse-papiers {emoji}"]},50091:e=>{e.exports=["Lisbonne"]},64352:e=>{e.exports="Luxembourg"},11156:e=>{e.exports=["MTPrédicteur"]},67861:e=>{e.exports=["Déplacez le point pour positionner l'ancre puis touchez pour le placer"]},45828:e=>{e.exports=["Déplacer vers"]},44302:e=>{e.exports=["Déplacer l'échelle vers la gauche"]},94338:e=>{e.exports=["Déplacer l'échelle vers la droite"]},66276:e=>{e.exports=["Schiff modifié"]},18559:e=>{e.exports=["Fourchette de Schiff Modifiée"]},18665:e=>{e.exports=["Moscou"]},58038:e=>{e.exports="Madrid"},34190:e=>{e.exports=["Malte"]},90271:e=>{e.exports=["Manille"]},51369:e=>{e.exports=["Mars"]},85095:e=>{e.exports=["Ville de Mexico"]},75633:e=>{e.exports=["Fusionner toutes les échelles en une"]},95093:e=>{e.exports=["Mélangé"]},10931:e=>{e.exports="Micro"},58397:e=>{e.exports=["Millénaire"]},85884:e=>{e.exports=["Menuet"]},9632:e=>{e.exports="Minuscule"},63158:e=>{e.exports=["Reflété"]},42769:e=>{e.exports="Muscat"},43088:e=>{e.exports=["non disponible"]},3485:e=>{
e.exports=["Pas d'échelle (plein écran)"]},8886:e=>{e.exports=["Pas de sync"]},16971:e=>{e.exports=["Pas de données de volume"]},75549:e=>{e.exports="Note"},71230:e=>{e.exports="Nov"},99203:e=>{e.exports="Norfolk Island"},79023:e=>{e.exports="Nairobi"},91203:e=>{e.exports="New York"},24143:e=>{e.exports=["Nouvelle-Zélande"]},40887:e=>{e.exports=["Nouveau volet ci-dessus"]},96712:e=>{e.exports=["Nouveau volet ci-dessous"]},33566:e=>{e.exports=["Nicosie"]},56670:e=>{e.exports=["Quelque chose n'a pas fonctionné"]},64968:e=>{e.exports=["Quelque chose n'a pas fonctionné. Veuillez réessayer plus tard."]},10520:e=>{e.exports=["Enregistrer la nouvelle configuration graphique"]},9908:e=>{e.exports=["Sauvegarder Sous"]},68553:e=>{e.exports="San Salvador"},65412:e=>{e.exports="Santiago"},13538:e=>{e.exports=["São Paulo"]},37207:e=>{e.exports=["Mise à l’échelle des prix du graphique uniquement"]},51464:e=>{e.exports="Schiff"},98114:e=>{e.exports=["Fourchette de Schiff"]},1535:e=>{e.exports=["Le script peut ne pas être mis à jour si vous quittez la page."]},89517:e=>{e.exports=["Configurations"]},43247:e=>{e.exports=["La deuxième partie de fraction n'est pas valide."]},19796:e=>{e.exports=["Mettre au Fond"]},23221:e=>{e.exports=["Mettre vers l'Arrière"]},5961:e=>{e.exports=["Séoul"]},57902:e=>{e.exports=["Sept"]},25866:e=>{e.exports=["Séance"]},59827:e=>{e.exports=["Arrêts de Session"]},69240:e=>{e.exports="Shanghai"},37819:e=>{e.exports=["Position Short"]},81428:e=>{e.exports=["Montrer"]},98116:e=>{e.exports=["Montrer tous les dessins"]},39046:e=>{e.exports=["Montrer tous les dessins et indicateurs"]},38293:e=>{e.exports=["Masquer les positions et les ordresAfficher tous les dessins, indicateurs, positions & ordres"]},49982:e=>{e.exports=["Montrer tous les indicateurs"]},48284:e=>{e.exports=["Montrer toutes les idées"]},62632:e=>{e.exports=["Afficher toutes les positions & ordres"]},24620:e=>{e.exports=["Afficher le switch du contrat continu"]},84813:e=>{e.exports=["Afficher l'expiration des contrats"]},66263:e=>{e.exports=["Montrer les Dividendes"]},46771:e=>{e.exports=["Montrer les résultats"]},87933:e=>{e.exports=["Afficher les idées des utilisateurs suivis"]},72973:e=>{e.exports=["Afficher les dernières mises à jour"]},58669:e=>{e.exports=["Afficher mes idées uniquement"]},30816:e=>{e.exports=["Montrer les fractionnements d'actions"]},68161:e=>{e.exports=["Panneau"]},56683:e=>{e.exports=["Singapour"]},69502:e=>{e.exports=["Ligne sinusoïdale"]},44904:e=>{e.exports=["Carré"]},70213:e=>{e.exports=["Limite d’études dépassée: {number} études par mise en page.\nVeuillez retirer quelques études."]},32733:e=>{e.exports="Style"},65323:e=>{e.exports=["Empiler à gauche"]},14113:e=>{e.exports=["Empiler à droite"]},93161:e=>{e.exports=["Rester en Mode Dessin"]},79511:e=>{e.exports=["Ligne en escalier"]},84573:e=>{e.exports="Sticker"},48767:e=>{e.exports="Stockholm"},29662:e=>{e.exports="Submicro"},9753:e=>{e.exports=["Sous-millenaire"]},71722:e=>{e.exports=["Sous-menuet"]},91889:e=>{e.exports="Supercycle"},33820:e=>{
e.exports=["Super millénaire"]},11020:e=>{e.exports="Sydney"},89659:e=>{e.exports=["Erreur de symbole"]},90932:e=>{e.exports=["Étiquette de nom du symbole"]},65986:e=>{e.exports=["Info du Symbole"]},52054:e=>{e.exports=["Étiquette de la dernière valeur du symbole"]},33606:e=>{e.exports=["Synchronisation globale"]},18008:e=>{e.exports=["Synchroniser tous les graphiques"]},99969:e=>{e.exports=["Point et Figure"]},53047:e=>{e.exports=["Ensemble de Lignes"]},34402:e=>{e.exports=["Tracé"]},70394:e=>{e.exports=["Canal parallèle"]},95995:e=>{e.exports="Paris"},29682:e=>{e.exports=["Coller"]},51102:e=>{e.exports=["Pourcent"]},35590:e=>{e.exports="Perth"},19093:e=>{e.exports="Phoenix"},22293:e=>{e.exports=["Éventail"]},43852:e=>{e.exports=["Fourchette"]},37680:e=>{e.exports=["Épingler à la nouvelle échelle de gauche"]},43707:e=>{e.exports=["Épingler à la nouvelle échelle de droite"]},91130:e=>{e.exports=["Épingler à l'échelle de gauche"]},61201:e=>{e.exports=["Épingler à l'échelle de gauche (masqué)"]},764:e=>{e.exports=["épingler à l'échelle de droite"]},20207:e=>{e.exports=["Épingler à l'échelle de droite (masqué)"]},66156:e=>{e.exports=["Epingler à l'échelle (maintenant à gauche)"]},54727:e=>{e.exports=["Épingler à l'échelle (maintenant pas d'échelle)"]},76598:e=>{e.exports=["Epingler à l'échelle (maintenant à droite)"]},39065:e=>{e.exports=["Épingler à l'échelle (maintenant {label})"]},97324:e=>{e.exports=["Épingler à l'échelle {label}"]},56948:e=>{e.exports=["Épingler à l'échelle {label} (masqué)"]},32156:e=>{e.exports=["Épinglé à l'échelle de gauche"]},8128:e=>{e.exports=["Épinglé à l'échelle de gauche (masqué)"]},3822:e=>{e.exports=["Épinglé à l'échelle de droite"]},44538:e=>{e.exports=["Épingler à l'échelle de droite (masqué)"]},65810:e=>{e.exports=["Épinglé à l'échelle {label}"]},14125:e=>{e.exports=["Épinglé à l'échelle {label} (masqué)"]},97378:e=>{e.exports=["Bouton Plus"]},46669:e=>{e.exports=["Veuillez nous donner une autorisation d'écriture dans le presse-papiers de votre navigateur ou appuyez sur {keystroke}"]},35963:e=>{e.exports=["Appuyez et maintenez {key} enfoncé pendant le zoom pour maintenir la position du graphique"]},95921:e=>{e.exports=["Étiquette de Prix"]},28625:e=>{e.exports=["Note de prix"]},2032:e=>{e.exports=["Intervalle de Prix"]},32061:e=>{e.exports=["Le format du prix n'est pas valide."]},91492:e=>{e.exports=["Ligne de Prix"]},48404:e=>{e.exports=["Primaire"]},87086:e=>{e.exports="Projection"},10160:e=>{e.exports=["A publié sur {customer}, {date}"]},19056:e=>{e.exports="Qatar"},9998:e=>{e.exports=["Rectangle pivoté"]},74214:e=>{e.exports="Rome"},50470:e=>{e.exports=["Rayon"]},90357:e=>{e.exports=["Plage"]},26833:e=>{e.exports="Reykjavik"},328:e=>{e.exports="Rectangle"},41615:e=>{e.exports=["Recommencer"]},35001:e=>{e.exports=["Tendance de la Régression"]},34596:e=>{e.exports=["Retirer"]},1434:e=>{e.exports=["Supprimer les dessins"]},13951:e=>{e.exports=["Supprimer les indicateurs"]},4142:e=>{e.exports=["Renommer la configuration du graphique"]},20801:e=>{e.exports="Renko"},34301:e=>{
e.exports=["Réinitialiser l'affichage du graphique"]},18001:e=>{e.exports=["Réinitialiser les points"]},17258:e=>{e.exports=["Réinitialiser l'échelle de prix"]},25333:e=>{e.exports=["Réinitialiser l'échelle de temps"]},52588:e=>{e.exports="Riyadh"},5871:e=>{e.exports="Riga"},33603:e=>{e.exports=["Avertissement"]},48474:e=>{e.exports=["Varsovie"]},20466:e=>{e.exports="Tokelau"},94284:e=>{e.exports="Tokyo"},83836:e=>{e.exports="Toronto"},38788:e=>{e.exports="Taipei"},39108:e=>{e.exports="Tallinn"},37229:e=>{e.exports=["Texte"]},16267:e=>{e.exports=["Téhéran"]},19611:e=>{e.exports=["Espace de Travail"]},29198:e=>{e.exports=["Le fournisseur de données ne fournit pas de données de volume pour ce symbole."]},8162:e=>{e.exports=["L'aperçu de la publication n'a pas pu être chargé. Veuillez désactiver les extensions de votre navigateur et réessayer."]},65943:e=>{e.exports=["Cet indicateur ne peut pas être appliqué à un autre indicateur."]},74986:e=>{e.exports=["Ce script est sur invitation seulement. Pour y avoir accès, veuillez contacter son auteur."]},58018:e=>{e.exports=["Symbole disponible uniquement sur {linkStart}TradingView{linkEnd}."]},98538:e=>{e.exports=["Modèle Three Drives"]},30973:e=>{e.exports="Ticks"},31976:e=>{e.exports=["Heure"]},64375:e=>{e.exports=["Fuseau Horaire"]},95005:e=>{e.exports=["Cycles de temps"]},87085:e=>{e.exports="Trade"},94770:e=>{e.exports=["Angle de la Tendance"]},23104:e=>{e.exports=["Droite de Tendance"]},15501:e=>{e.exports=["Prolongation de Fibonacci selon la Tendance"]},31196:e=>{e.exports=["Temps de Fibonacci selon la Tendance"]},29245:e=>{e.exports="Triangle"},83356:e=>{e.exports=["Triangle vers le bas"]},12390:e=>{e.exports=["Figure en Triangle"]},28340:e=>{e.exports=["Triangle vers le haut"]},93855:e=>{e.exports="Tunis"},50406:e=>{e.exports="UTC"},81320:e=>{e.exports=["Annuler"]},25933:e=>{e.exports=["Unités"]},15101:e=>{e.exports=["Déverrouiller"]},34150:e=>{e.exports=["Vague Haussière 4"]},83927:e=>{e.exports=["Vague Haussière 5"]},58976:e=>{e.exports=["Vague Haussière 1 ou A"]},11661:e=>{e.exports=["Vague Haussière 2 ou B"]},53958:e=>{e.exports=["Vague Haussière 3"]},66560:e=>{e.exports=["Vague Haussière C"]},18426:e=>{e.exports=["Profil de volume Gamme fixe"]},61022:e=>{e.exports=["L'indicateur Volume Profile n'est disponible que sur nos plans upgradés."]},15771:e=>{e.exports="Vancouver"},56211:e=>{e.exports=["Droite Verticale"]},75354:e=>{e.exports="Vilnius"},21852:e=>{e.exports=["Visibilité"]},27557:e=>{e.exports=["Visibilité des intervalles"]},89960:e=>{e.exports=["Visible avec déplacement de la souris"]},22198:e=>{e.exports=["Ordre de visualisation"]},7050:e=>{e.exports="X Cross"},66527:e=>{e.exports=["Figure en XABCD"]},17126:e=>{e.exports=["Vous ne pouvez pas voir cette période de pivot avec cette résolution"]},69293:e=>{e.exports="Yangon"},84301:e=>{e.exports="Zurich"},76020:e=>{e.exports=["changer le degré d'Elliott"]},83935:e=>{e.exports=["modifier les étiquettes qui ne se chevauchent pas"]},39402:e=>{
e.exports=["changer la visibilité de l'étiquette du prix moyen de clôture"]},98866:e=>{e.exports=["changer la visibilité de la ligne du prix moyen de clôture"]},5100:e=>{e.exports=["changer la visibilité des étiquettes d'offre et de demande"]},32311:e=>{e.exports=["changer la visibilité des lignes d'offre et de demande"]},22641:e=>{e.exports=["changer la devise"]},30501:e=>{e.exports=["changer la mise en page du graphique en {title}"]},7017:e=>{e.exports=["changer la visibilité du switch du contrat continu"]},58108:e=>{e.exports=["changer la visibilité du compte à rebours pour la fermeture de la barre"]},7151:e=>{e.exports=["changer la plage de dates"]},84944:e=>{e.exports=["changement de la visibilité des dividendes"]},79574:e=>{e.exports=["changer la visibilité des événements sur le graphique"]},88217:e=>{e.exports=["changer la visibilité des gains"]},28288:e=>{e.exports=["modifier la visibilité de l'expiration des contrats à terme"]},66805:e=>{e.exports=["modifier la visibilité des étiquettes de prix haut et bas"]},92556:e=>{e.exports=["modifier la visibilité des lignes de prix haut et bas"]},87027:e=>{e.exports=["visibilité des étiquettes de noms des indicateurs de changement"]},14922:e=>{e.exports=["modifier la visibilité des étiquettes de valeur des indicateurs"]},19839:e=>{e.exports=["changer la visibilité des dernières mises à jour"]},23783:e=>{e.exports=["changer le groupe de liaison"]},87510:e=>{e.exports=["changer la hauteur du volet"]},50190:e=>{e.exports=["changer la visibilité du bouton plus"]},49889:e=>{e.exports=["changer la visibilité des étiquettes de prix pré/post marché"]},16750:e=>{e.exports=["changer la visibilité des lignes de prix pré/post marché"]},59883:e=>{e.exports=["changer la visibilité de la ligne de prix de clôture précédente"]},67761:e=>{e.exports=["Changer la ligne de prix"]},69510:e=>{e.exports=["modifier le rapport prix/barre"]},32303:e=>{e.exports=["Changer la Résolution"]},526:e=>{e.exports=["changer le symbole"]},9402:e=>{e.exports=["modifier la visibilité des étiquettes des symboles"]},53150:e=>{e.exports=["changer la visibilité de la dernière valeur du symbole"]},12707:e=>{e.exports=["changer la visibilité de la précédente valeur de clôture du symbole"]},65303:e=>{e.exports=["changer de session"]},15403:e=>{e.exports=["modifier la visibilité des interruptions de session"]},53438:e=>{e.exports=["changer de style de série"]},74488:e=>{e.exports=["changement de la visibilité des splits"]},20505:e=>{e.exports=["changer de fuseau horaire"]},39028:e=>{e.exports=["changer l'unité"]},21511:e=>{e.exports=["Changer la visibilité"]},16698:e=>{e.exports=["changer la visibilité à l'intervalle actuel"]},78422:e=>{e.exports=["changer la visibilité à l'intervalle actuel et au-dessus"]},49529:e=>{e.exports=["changer la visibilité à l'intervalle actuel et en-dessous"]},66927:e=>{e.exports=["changer la visibilité à tous les intervalles"]},74428:e=>{e.exports=["changer le style de {title}"]},72032:e=>{e.exports=["changer le point {pointIndex}"]},65911:e=>{e.exports=["graphiques par TradingView"]
},5179:e=>{e.exports=["Clôner les outils de ligne"]},3195:e=>{e.exports=["créer un groupe d'outils de ligne"]},92659:e=>{e.exports=["Créer un groupe d’outils de ligne à partir de la sélection"]},81791:e=>{e.exports=["créer {tool}"]},63649:e=>{e.exports=["couper sources"]},78755:e=>{e.exports=["couper {title}"]},99113:e=>{e.exports=["Ajouter l'outil de ligne {lineTool} au groupe {name}"]},40242:e=>{e.exports=["ajouter outil(s) de ligne au groupe {group}"]},22856:e=>{e.exports=["Ajouter cette métrique financière à l'ensemble de la mise en page"]},82388:e=>{e.exports=["Ajouter cet indicateur à l'ensemble de la mise en page"]},94292:e=>{e.exports=["ajouter cette stratégie à l'ensemble de la mise en page"]},27982:e=>{e.exports=["ajouter ce symbole à l'ensemble de la mise en page"]},66568:e=>{e.exports=["appliquer le thème des graphiques"]},64034:e=>{e.exports=["appliquer toutes les propriétés du graphique"]},49037:e=>{e.exports=["Appliquer un modèle de dessin"]},96996:e=>{e.exports=["appliquer les valeurs d'usine par défaut aux sources sélectionnées"]},44547:e=>{e.exports=["appliquer des indicateurs à l'ensemble de la mise en page"]},26065:e=>{e.exports=["Appliquer le modèle d'étude {template}"]},58570:e=>{e.exports=["appliquer le thème des barres d'outils"]},27195:e=>{e.exports=["mettre le groupe {title} à l'avant"]},78246:e=>{e.exports=["mettre {title} en avant"]},56763:e=>{e.exports=["Mettre en avant {title}"]},5607:e=>{e.exports=["par TradingView"]},90621:e=>{e.exports=["verrouillage de la plage de dates"]},12962:e=>{e.exports=["effacer la ligne de niveau"]},63391:e=>{e.exports=["Exclure les outils de ligne du groupe {group}"]},59942:e=>{e.exports=["motif de barres flottantes"]},70301:e=>{e.exports=["Masquer {title}"]},91842:e=>{e.exports=["Cacher les lignes d'étiquette d'alerte"]},54781:e=>{e.exports=["Cacher tous les Outils de Dessin"]},44974:e=>{e.exports=["Cacher les marques de la barre"]},28916:e=>{e.exports=["verrouillage de l'intervalle"]},94245:e=>{e.exports=["Inverser l'échelle"]},90743:e=>{e.exports=["insérer {title}"]},53146:e=>{e.exports=["insérer {title} après {targetTitle}"]},74055:e=>{e.exports=["insérer {title} après {target}"]},11231:e=>{e.exports=["Insérer {title} avant {target}"]},67176:e=>{e.exports=["Insérer {title} avant {targetTitle}"]},54597:e=>{e.exports=["charger le modèle de dessin par défaut"]},30295:e=>{e.exports=["chargement..."]},50193:e=>{e.exports=["Verrouiller {title}"]},4963:e=>{e.exports=["verrouiller le groupe {group}"]},68163:e=>{e.exports=["verrouiller les objets"]},47107:e=>{e.exports=["déplacer"]},11303:e=>{e.exports=["Déplacer {title} vers la nouvelle échelle de gauche"]},45544:e=>{e.exports=["déplacer {title} vers la nouvelle échelle de droite"]},81898:e=>{e.exports=["Déplacer toutes les échelles vers la gauche"]},22863:e=>{e.exports=["Déplacer toutes les échelles vers la droite"]},45356:e=>{e.exports=["Déplacer le(s) dessin(s)"]},15086:e=>{e.exports=["déplacer à gauche"]},61711:e=>{e.exports=["déplacer à droite"]},4184:e=>{e.exports=["Déplacer l'échelle"]},74642:e=>{
e.exports=["rendre {title} sans échelle (Plein écran)"]},45223:e=>{e.exports=["Rendre le groupe {group} invisible"]},87927:e=>{e.exports=["rendre le groupe {group} visible"]},62153:e=>{e.exports=["fusionner vers le bas"]},70746:e=>{e.exports=["fusionner vers le volet"]},66143:e=>{e.exports=["fusionner vers le haut"]},81870:e=>{e.exports=["motif de barres en miroir"]},16542:e=>{e.exports="n/a"},47222:e=>{e.exports=["prix des échelles"]},99042:e=>{e.exports=["Mise à l’échelle des prix du graphique uniquement"]},35962:e=>{e.exports=["temps de l'échelle"]},68193:e=>{e.exports=["faire défiler"]},70009:e=>{e.exports=["défilement temporel"]},69485:e=>{e.exports=["fixer la stratégie de sélection du barème de prix sur {title}"]},16259:e=>{e.exports=["envoyer {title} en arrière"]},66781:e=>{e.exports=["mettre {title} à l'arrière"]},4998:e=>{e.exports=["mettre le groupe {title} à l'arrière"]},64704:e=>{e.exports=["partager les outils de ligne dans leur ensemble"]},77554:e=>{e.exports=["partager les outils de ligne dans la mise en page"]},16237:e=>{e.exports=["afficher les lignes d'étiquette d'alerte"]},13622:e=>{e.exports=["afficher toutes les idées"]},26267:e=>{e.exports=["afficher les idées des utilisateurs suivis"]},40061:e=>{e.exports=["afficher mes idées uniquement"]},52010:e=>{e.exports=["rester en mode dessin"]},98784:e=>{e.exports=["arrêter la synchronisation des dessins"]},57011:e=>{e.exports=["arrêter la synchronisation des outils de ligne"]},92831:e=>{e.exports=["verrouillage du symbole"]},60635:e=>{e.exports=["synchroniser l'heure"]},99769:e=>{e.exports=["propulsé par"]},68111:e=>{e.exports=["fourni par TradingView"]},96916:e=>{e.exports=["coller le dessin"]},80611:e=>{e.exports=["coller l'indicateur"]},41601:e=>{e.exports=["coller {title}"]},84018:e=>{e.exports=["épingler à l'échelle de gauche"]},22615:e=>{e.exports=["Épingler à l'échelle de droite"]},56015:e=>{e.exports=["épingler à l'échelle {label}"]},33348:e=>{e.exports=["réarranger les volets"]},15516:e=>{e.exports=["Supprimer toutes les études"]},80171:e=>{e.exports=["Supprimer tous les outils de dessin et études"]},59211:e=>{e.exports=["supprimer les outils de ligne vide désélectionnés"]},44656:e=>{e.exports=["Supprimer les dessins"]},70653:e=>{e.exports=["supprimer le groupe de dessins"]},66414:e=>{e.exports=["supprimer les sources de données de la ligne"]},47637:e=>{e.exports=["supprimer le volet"]},39859:e=>{e.exports=["supprimer {title}"]},78811:e=>{e.exports=["suppression du groupe d'outils de ligne {name}"]},16338:e=>{e.exports=["Renommez le groupe {group} en {newName}"]},30910:e=>{e.exports=["réinitialiser les tailles de mise en page"]},21948:e=>{e.exports=["réinitialiser les échelles"]},55064:e=>{e.exports=["Réinitialiser l'échelle de temps"]},13034:e=>{e.exports=["redimensionner la mise en page"]},9608:e=>{e.exports=["restaurer les valeurs par défaut"]},63060:e=>{e.exports=["basculer vers l'échelle automatique"]},98860:e=>{e.exports=["activer l'échelle indexée sur 100"]},21203:e=>{e.exports=["activer le verrouillage de l'échelle"]},60166:e=>{
e.exports=["basculer vers l'échelle logarithmique"]},68642:e=>{e.exports=["Commuter l'échelle de pourcentage"]},33714:e=>{e.exports=["activer l'échelle standard"]},47122:e=>{e.exports=["suivre le temps"]},28068:e=>{e.exports=["désactiver le partage des outils de ligne"]},66824:e=>{e.exports=["déverrouiller les objets"]},51114:e=>{e.exports=["déverrouiller le groupe {group}"]},92421:e=>{e.exports=["déverrouiller {title}"]},20057:e=>{e.exports=["défusionner vers le nouveau volet inférieur"]},52540:e=>{e.exports=["défusionner vers le haut"]},86949:e=>{e.exports=["défusionner vers le bas"]},50728:e=>{e.exports=["Mettre à jour le script {title}"]},33355:e=>{e.exports=["{count} barres"]},88841:e=>{e.exports=["{symbol} données financières par TradingView"]},38641:e=>{e.exports=["{userName} a publié sur {customer}, {date}"]},59833:e=>{e.exports=["zoomer"]},19813:e=>{e.exports=["zoomer"]},9645:e=>{e.exports=["dézoomer"]},30572:e=>{e.exports=["jour","jours"]},52254:e=>{e.exports=["heure","heures"]},99062:e=>{e.exports=["mois","mois"]},69143:e=>{e.exports="minute"},71787:e=>{e.exports=["seconde","secondes"]},82797:e=>{e.exports=["plage","plages"]},47966:e=>{e.exports=["semaine","semaines"]},99136:e=>{e.exports="tick"},18562:e=>{e.exports=Object.create(null),e.exports["#AAPL-symbol-description"]="Apple Inc",e.exports["#AUDCAD-symbol-description"]=["Dollar australien/Dollar canadien"],e.exports["#AUDCHF-symbol-description"]=["Dollar australien/Franc suisse"],e.exports["#AUDJPY-symbol-description"]=["Dollar australien/Yen japonais"],e.exports["#AUDNZD-symbol-description"]=["Dollar Australien/Dollar Néo-zélandais"],e.exports["#AUDRUB-symbol-description"]=["Dollar Australien/Rouble russe"],e.exports["#AUDUSD-symbol-description"]=["Dollar Australien/Dollar Américain"],e.exports["#BRLJPY-symbol-description"]=["Réal brésilien / Yen japonais"],e.exports["#BTCCAD-symbol-description"]=["Bitcoin / Dollar Canadien"],e.exports["#BTCCNY-symbol-description"]=["Bitcoin / Yuan Chinois"],e.exports["#BTCEUR-symbol-description"]="Bitcoin / Euro",e.exports["#BTCKRW-symbol-description"]=["Bitcoin / Won sud-coréen"],e.exports["#BTCRUR-symbol-description"]=["Bitcoin / Rouble"],e.exports["#BTCUSD-symbol-description"]=["Bitcoin / Dollar"],e.exports["#BVSP-symbol-description"]=["Indice Bovespa Brésil"],e.exports["#CADJPY-symbol-description"]=["Dollar canadien / Yen japonais"],e.exports["#CB1!-symbol-description"]=["Pétrole brut Brent"],e.exports["#CHFJPY-symbol-description"]=["Franc suisse / Yen japonais"],e.exports["#COPPER-symbol-description"]=["CFD sur Cuivre"],e.exports["#ES1-symbol-description"]=["S&P 500 Contrats à terme E-Mini"],e.exports["#ESP35-symbol-description"]=["Indice IBEX 35"],e.exports["#EUBUND-symbol-description"]="Euro Bund",e.exports["#EURAUD-symbol-description"]=["Euro / Dollar Australien"],e.exports["#EURBRL-symbol-description"]=["Euro / Réal brésilien"],e.exports["#EURCAD-symbol-description"]=["Euro / Dollar Canadien"],e.exports["#EURCHF-symbol-description"]=["Euro / Franc Suisse"],
e.exports["#EURGBP-symbol-description"]=["Euro / Livre Sterling"],e.exports["#EURJPY-symbol-description"]=["Euro / Yen Japonais"],e.exports["#EURNZD-symbol-description"]=["Euro / Dollar Néo-Zélandais"],e.exports["#EURRUB-symbol-description"]=["Euro / Rouble russe"],e.exports["#EURRUB_TOM-symbol-description"]=["Euro / Rouble russe TOM"],e.exports["#EURSEK-symbol-description"]=["EUR/SEK"],e.exports["#EURTRY-symbol-description"]=["Euro / Livre Turque"],e.exports["#EURUSD-symbol-description"]=["Euro / Dollar Américain"],e.exports["#EUSTX50-symbol-description"]=["Indice Euro Stoxx 5"],e.exports["#FRA40-symbol-description"]=["Indice CAC40"],e.exports["#GB10-symbol-description"]=["Royaume-Uni Obligations gouvernementales 10 ans"],e.exports["#GBPAUD-symbol-description"]=["Livre sterling / Dollar australien"],e.exports["#GBPCAD-symbol-description"]=["Livre sterling / Dollar canadien"],e.exports["#GBPCHF-symbol-description"]=["Livre sterling / Franc suisse"],e.exports["#GBPEUR-symbol-description"]=["Livre Sterling / Euro"],e.exports["#GBPJPY-symbol-description"]=["Livre sterling / Yen japonais"],e.exports["#GBPNZD-symbol-description"]=["Livre sterling / Dollar Néo-Zélandais"],e.exports["#GBPRUB-symbol-description"]=["Livre sterling / Rouble russe"],e.exports["#GBPUSD-symbol-description"]=["Livre sterling / Dollar américain"],e.exports["#GER30-symbol-description"]=["Indice DAX des actions allemandes cotées"],e.exports["#GOOGL-symbol-description"]=["Alphabet Inc (Google) Classe A"],e.exports["#ITA40-symbol-description"]=["Indice FTSE MIB"],e.exports["#JPN225-symbol-description"]=["NIKKEI 225"],e.exports["#JPYKRW-symbol-description"]=["YEN / WON"],e.exports["#JPYRUB-symbol-description"]=["Yen / Rouble russe"],e.exports["#KA1-symbol-description"]=["Contrats à terme sur sucre #11"],e.exports["#KG1-symbol-description"]=["Contrats à terme sur coton"],e.exports["#KT1-symbol-description"]=["Key Tronic Corp."],e.exports["#LKOH-symbol-description"]="LUKOIL",e.exports["#LTCBTC-symbol-description"]="Litecoin / Bitcoin",e.exports["#MGNT-symbol-description"]="Magnit",e.exports["#MICEX-symbol-description"]=["Indice MICEX"],e.exports["#MNOD_ME.EQRP-symbol-description"]="ADR GMK NORILSKIYNIKEL ORD SHS [REPO]",e.exports["#MSFT-symbol-description"]=["MICROSOFT CORP"],e.exports["#NAS100-symbol-description"]="US 100 Cash CFD",e.exports["#NGAS-symbol-description"]=["Gaz naturel (Henry Hub)"],e.exports["#NKY-symbol-description"]=["Indice Nikkei 225"],e.exports["#NZDJPY-symbol-description"]=["Dollar Néo-Zélandais / Yen Japonais"],e.exports["#NZDUSD-symbol-description"]=["Dollar Néo-Zélandais / Dollar Américain"],e.exports["#RB1-symbol-description"]=["Contrats à terme sur essence RBOB"],e.exports["#RTS-symbol-description"]=["Indice Russe RTS"],e.exports["#SBER-symbol-description"]="SBERBANK",e.exports["#SPX500-symbol-description"]=["Indice S&P 500"],e.exports["#TWTR-symbol-description"]=["TWITTER INC"],e.exports["#UK100-symbol-description"]=["Indice FTSE 100 des compagnies britanniques cotées"],
e.exports["#USDBRL-symbol-description"]=["Dollar US / Réal Brésilien"],e.exports["#USDCAD-symbol-description"]=["Dollar Américain / Dollar Canadien"],e.exports["#USDCHF-symbol-description"]=["Dollar américain / Franc suisse"],e.exports["#USDCNY-symbol-description"]=["Dollar Américain / Renminbi Yuan"],e.exports["#USDDKK-symbol-description"]=["Dollar Américain / Couronne danoise"],e.exports["#USDHKD-symbol-description"]=["Dollar Américain / Dollar de Hong Kong"],e.exports["#USDIDR-symbol-description"]=["Dollar Américain / Roupie"],e.exports["#USDINR-symbol-description"]=["Dollar américain / Roupie indienne"],e.exports["#USDJPY-symbol-description"]=["Dollar Américain / Yen japonais"],e.exports["#USDKRW-symbol-description"]=["Dollar Américain / Won"],e.exports["#USDMXN-symbol-description"]=["USD/MXN"],e.exports["#USDPHP-symbol-description"]=["Dollar U.S. / Peso philippin"],e.exports["#USDRUB-symbol-description"]=["Dollar Américain / Rouble Russe"],e.exports["#USDRUB_TOM-symbol-description"]=["USD/RUB TOM"],e.exports["#USDSEK-symbol-description"]=["Dollar américain / Couronne suédoise"],e.exports["#USDSGD-symbol-description"]=["DOLLAR AMÉRICAIN / DOLLAR DE SINGAPOUR"],e.exports["#USDTRY-symbol-description"]=["Dollar Américain / Nouvelle Livre Turque"],e.exports["#VTBR-symbol-description"]="VTB",e.exports["#XAGUSD-symbol-description"]=["Argent / Dollar Américain"],e.exports["#XAUUSD-symbol-description"]=["Or / Dollar Américain"],e.exports["#XPDUSD-symbol-description"]=["CFD sur Palladium"],e.exports["#XPTUSD-symbol-description"]=["Platine / Dollar Américain"],e.exports["#ZS1-symbol-description"]=["Contrats à terme sur germes de soja - ECBT"],e.exports["#ZW1-symbol-description"]=["Contrats à terme sur blé ECBT"],e.exports["#BTCGBP-symbol-description"]=["Bitcoin / Livre Sterling"],e.exports["#MICEXINDEXCF-symbol-description"]=["Indice MOEX Russie"],e.exports["#BTCAUD-symbol-description"]=["Bitcoin / Dollar australien"],e.exports["#BTCJPY-symbol-description"]=["Bitcoin / Yen japonais"],e.exports["#BTCBRL-symbol-description"]=["Bitcoin / Réal Brésilien"],e.exports["#PT10-symbol-description"]=["Obligations du gouvernement portugais 10 ans"],e.exports["#TXSX-symbol-description"]=["Indice TSX 60"],e.exports["#VIXC-symbol-description"]=["Indice VIX TSX 60"],e.exports["#USDPLN-symbol-description"]=["Dollar Américain / Zloty Polonais"],e.exports["#EURPLN-symbol-description"]=["Euro / Zloty Polonais"],e.exports["#BTCPLN-symbol-description"]=["Bitcoin / Zloty polonais"],e.exports["#CAC40-symbol-description"]=["Indice CAC 40"],e.exports["#XBTCAD-symbol-description"]=["Bitcoin / Dollar Canadien"],e.exports["#ITI2!-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIF2018-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIF2019-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIF2020-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIG2018-symbol-description"]=["Contrats à terme sur minerai de fer"],
e.exports["#ITIG2019-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIG2020-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIH2018-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIH2019-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIH2020-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIJ2018-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIJ2019-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIJ2020-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIK2018-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIK2019-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIK2020-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIM2017-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIM2018-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIM2019-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIM2020-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIN2017-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIN2018-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIN2019-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIN2020-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIQ2017-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIQ2018-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIQ2019-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIQ2020-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIU2017-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIU2018-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIU2019-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIU2020-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIV2017-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIV2018-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIV2019-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIV2020-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIX2017-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIX2018-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIX2019-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIX2020-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIZ2017-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIZ2018-symbol-description"]=["Contrats à terme sur minerai de fer"],
e.exports["#ITIZ2019-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#ITIZ2020-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#AMEX:GXF-symbol-description"]=["ETF Région Nordique Global x FTSE"],e.exports["#ASX:XAF-symbol-description"]=["Indice S&P/ASX All Australian 50"],e.exports["#ASX:XAT-symbol-description"]=["Indice S&P/ASX All Australian 200"],e.exports["#BIST:XU100-symbol-description"]=["Indice BIST 100"],e.exports["#GPW:WIG20-symbol-description"]=["Indice WIG20"],e.exports["#INDEX:JKSE-symbol-description"]=["Indice Composite Djakarta"],e.exports["#INDEX:KLSE-symbol-description"]=["Indice KLCI Bourse de Malaisie"],e.exports["#INDEX:NZD-symbol-description"]=["Indice NZX 50"],e.exports["#INDEX:STI-symbol-description"]=["Indice STI"],e.exports["#INDEX:XLY0-symbol-description"]=["Indice Composite Shanghai"],e.exports["#MOEX:MICEXINDEXCF-symbol-description"]=["Indice MOEX Russie"],e.exports["#NYMEX:KT1!-symbol-description"]=["Contrats à terme sur café"],e.exports["#OANDA:NATGASUSD-symbol-description"]=["CFD sur Gaz naturel"],e.exports["#OANDA:USDPLN-symbol-description"]=["Dollar Américain / Zloty Polonais"],e.exports["#TSX:TX60-symbol-description"]=["Indice S&P/TSX 60"],e.exports["#TSX:VBU-symbol-description"]=["Indice ETF Vanguard US Aggregate BND (couvert en CAD-)UN"],e.exports["#TSX:VIXC-symbol-description"]=["Indice S&P/TSX 60 VIX"],e.exports["#TVC:CAC40-symbol-description"]=["Indice CAC40"],e.exports["#TVC:ES10-symbol-description"]=["Espagne Obligations du gouvernement 10 ans"],e.exports["#TVC:EUBUND-symbol-description"]="Euro Bund",e.exports["#TVC:GB02-symbol-description"]=["Obligations du gouvernement Britannique 2 ans"],e.exports["#TVC:GB10-symbol-description"]=["Obligations du gouvernement Britannique 10 ans"],e.exports["#TVC:GOLD-symbol-description"]=["CFD sur Or (US$/OZ)"],e.exports["#TVC:ID03-symbol-description"]=["Indonésie Obligations du gouvernement 3 ans"],e.exports["#TVC:ID10-symbol-description"]=["Indonésie Obligations du gouvernement 10 ans"],e.exports["#TVC:PALLADIUM-symbol-description"]=["CFD sur PALLADIUM (US$/OZ)"],e.exports["#TVC:PT10-symbol-description"]=["Portugal, Obligations du gouvernement 10 ans"],e.exports["#TVC:SILVER-symbol-description"]=["CFD sur Argent (US$/OZ)"],e.exports["#TVC:RUT-symbol-description"]=["Indice Russell 2000"],e.exports["#TSX:TSX-symbol-description"]=["Indice Composite S&P/TSX"],e.exports["#OANDA:CH20CHF-symbol-description"]=["Indice Swiss 20"],e.exports["#TVC:SHCOMP-symbol-description"]=["Indice Composite Shanghai"],e.exports["#NZX:ALLC-symbol-description"]=["Indice S&P/NZX ALL ( Indice de capital )"],e.exports["#AMEX:SHYG-symbol-description"]=["Actions 0-5 ans Obligations d'entreprise à rendement élevé ETF"],e.exports["#TVC:AU10-symbol-description"]=["Australie Obligations du gouvernement 10 ans"],e.exports["#TVC:CN10-symbol-description"]=["Chine Obligations du gouvernement 10 ans"],e.exports["#TVC:KR10-symbol-description"]=["Corée Obligations du gouvernement 10 ans"],
e.exports["#NYMEX:RB1!-symbol-description"]=["Contrats à terme sur essence RBOB"],e.exports["#NYMEX:HO1!-symbol-description"]=["Contrats à terme NY Harbor ULSD"],e.exports["#NYMEX:AEZ1!-symbol-description"]=["Contrats à terme sur Ethanol NY"],e.exports["#OANDA:XCUUSD-symbol-description"]=["CFD sur Cuivre (US$ / lb)"],e.exports["#COMEX:ZA1!-symbol-description"]=["Contrats à terme sur zinc"],e.exports["#CBOT:ZW1!-symbol-description"]=["Contrats à terme sur blé"],e.exports["#NYMEX:KA1!-symbol-description"]=["Contrats à terme sur sucre #11"],e.exports["#CBOT:QBC1!-symbol-description"]=["Contrats à terme sur maïs"],e.exports["#CME:E61!-symbol-description"]=["Contrats à terme Euro"],e.exports["#CME:B61!-symbol-description"]=["Contrats à terme Livre britannique"],e.exports["#CME:QJY1!-symbol-description"]=["Contrats à terme Yen japonais"],e.exports["#CME:A61!-symbol-description"]=["Contrats à terme Dollar australien"],e.exports["#CME:D61!-symbol-description"]=["Contrats à terme Dollar canadien"],e.exports["#CME:SP1!-symbol-description"]=["Contrats à terme S&P 500"],e.exports["#CME_MINI:NQ1!-symbol-description"]=["Contrats à terme NASDAQ 100 E-MINI"],e.exports["#CBOT_MINI:YM1!-symbol-description"]=["Contrats à terme E-MINI DOW JONES ($5)"],e.exports["#CME:NY1!-symbol-description"]=["Contrats à terme NIKKEI 225"],e.exports["#EUREX:DY1!-symbol-description"]=["Indice DAX"],e.exports["#CME:IF1!-symbol-description"]=["Indice IBOVESPA Contrats à terme USD"],e.exports["#CBOT:TY1!-symbol-description"]=["Contrats à terme 10 ans Bons du Trésor"],e.exports["#CBOT:FV1!-symbol-description"]=["Contrats à terme 5 ans Bons du Trésor"],e.exports["#CBOT:ZE1!-symbol-description"]=["Contrats à terme 3 ans Bons du Trésor"],e.exports["#CBOT:TU1!-symbol-description"]=["Contrats à terme 2 ans Bons du Trésor"],e.exports["#CBOT:FF1!-symbol-description"]=["Contrats à terme 30 jours Taux d'intérêt des Fonds FED"],e.exports["#CBOT:US1!-symbol-description"]=["Contrats à terme Obligations du Trésor"],e.exports["#TVC:EXY-symbol-description"]=["Indice de devise EURO"],e.exports["#TVC:JXY-symbol-description"]=["Indice de devise YEN japonais"],e.exports["#TVC:BXY-symbol-description"]=["Indice de devise LIVRE britannique"],e.exports["#TVC:AXY-symbol-description"]=["Indice de devise DOLLAR australien"],e.exports["#TVC:CXY-symbol-description"]=["Indice de devise DOLLAR canadien"],e.exports["#FRED:GDP-symbol-description"]=["Produit intérieur brut, 1 décimale"],e.exports["#FRED:UNRATE-symbol-description"]=["Taux de chômage civil"],e.exports["#FRED:POP-symbol-description"]=["Population Totale : Tous âges, incluant les forces armées outremer"],e.exports["#ETHUSD-symbol-description"]=["Ethereum / Dollar"],e.exports["#BMFBOVESPA:IBOV-symbol-description"]=["Indice IBovespa"],e.exports["#BMFBOVESPA:IBRA-symbol-description"]=["Indice IBrasil"],e.exports["#BMFBOVESPA:IBXL-symbol-description"]=["Indice IBRX 50"],e.exports["#COMEX:HG1!-symbol-description"]=["Contrats à terme sur cuivre"],
e.exports["#INDEX:HSCE-symbol-description"]=["Indice Hang Seng des entreprises chinoises"],e.exports["#NYMEX:CL1!-symbol-description"]=["Contrats à terme sur pétrole brut léger"],e.exports["#OTC:IHRMF-symbol-description"]=["ISHARES MSCI JAPAN SHS"],e.exports["#TVC:DAX-symbol-description"]=["Indice des 30 plus importantes compagnies allemandes"],e.exports["#TVC:DE10-symbol-description"]=["Allemagne Obligations du gouvernement 10 ans"],e.exports["#TVC:DJI-symbol-description"]=["L'indice industriel moyen DOW JONES"],e.exports["#TVC:DXY-symbol-description"]=["Indice devise Dollar U.S."],e.exports["#TVC:FR10-symbol-description"]=["France Obligations du gouvernement 10 ans"],e.exports["#TVC:HSI-symbol-description"]=["Indice Hang Seng"],e.exports["#TVC:IBEX35-symbol-description"]=["Indice IBEX35"],e.exports["#FX:AUS200-symbol-description"]=["Indice S&P/ASX"],e.exports["#AMEX:SHY-symbol-description"]=["Ishares Bons du Trésor ETF 1-3 ans"],e.exports["#ASX:XJO-symbol-description"]=["Indice S&P/ASX 200"],e.exports["#BSE:SENSEX-symbol-description"]=["Indice S&P BSE SENSEX"],e.exports["#INDEX:MIB-symbol-description"]=["Indice MIB"],e.exports["#INDEX:MOY0-symbol-description"]=["Indice Euro STOXX 50"],e.exports["#MOEX:RTSI-symbol-description"]=["Indice RTS"],e.exports["#NSE:NIFTY-symbol-description"]=["Indice NIFTY 50"],e.exports["#NYMEX:NG1!-symbol-description"]=["Contrats à terme sur Gaz Naturel"],e.exports["#NYMEX:ZC1!-symbol-description"]=["Contrats à terme sur maïs"],e.exports["#TVC:IN10-symbol-description"]=["Inde Obligations du gouvernement 10 ans"],e.exports["#TVC:IT10-symbol-description"]=["Italie Obligations du gouvernement 10 ans"],e.exports["#TVC:JP10-symbol-description"]=["Japon Obligations du gouvernement 10 ans"],e.exports["#TVC:NDX-symbol-description"]=["Nasdaq 100"],e.exports["#TVC:NI225-symbol-description"]=["Nikkei 225"],e.exports["#TVC:SPX-symbol-description"]=["S&P 500"],e.exports["#TVC:SX5E-symbol-description"]=["Indice Euro Stoxx 50"],e.exports["#TVC:TR10-symbol-description"]=["Turquie Obligations du gouvernement 10 ans"],e.exports["#TVC:UKOIL-symbol-description"]=["CFD sur Pétrole brut Brent"],e.exports["#TVC:UKX-symbol-description"]="UK 100 Index",e.exports["#TVC:US02-symbol-description"]=["USA Obligations du gouvernement 2 ans"],e.exports["#TVC:US05-symbol-description"]=["USA Obligations du gouvernement 5 ans"],e.exports["#TVC:US10-symbol-description"]=["USA Obligations du gouvernement 10 ans"],e.exports["#TVC:USOIL-symbol-description"]=["CFD sur Pétrole brut WTI"],e.exports["#NYMEX:ITI1!-symbol-description"]=["Contrats à terme sur minerai de fer"],e.exports["#NASDAQ:SHY-symbol-description"]=["Obligations du Trésor Américain 1-3 ans ISHARES ETF"],e.exports["#AMEX:ALD-symbol-description"]="WisdomTree Asia Local Debt ETF",e.exports["#NASDAQ:AMD-symbol-description"]="Advanced Micro Devices Inc",e.exports["#NYSE:BABA-symbol-description"]="Alibaba Group Holdings Ltd.",e.exports["#ICEEUR:CB-symbol-description"]=["Pétrole brut Brent"],e.exports["#ICEEUR:CB1!-symbol-description"]=["Pétrole brut Brent"],
e.exports["#ICEUSA:CC-symbol-description"]=["Cacao"],e.exports["#NYMEX:CL-symbol-description"]=["Pétrole brut WTI"],e.exports["#ICEUSA:CT-symbol-description"]=["Coton #2"],e.exports["#NASDAQ:CTRV-symbol-description"]="ContraVir Pharmaceuticals Inc",e.exports["#CME:DL-symbol-description"]=["Lait Classe III"],e.exports["#NYSE:F-symbol-description"]="FORD MTR CO DEL",e.exports["#MOEX:GAZP-symbol-description"]="GAZPROM",e.exports["#COMEX:GC-symbol-description"]=["Or"],e.exports["#CME:GF-symbol-description"]=["Bétail nourricier"],e.exports["#CME:HE-symbol-description"]=["Porcs maigres"],e.exports["#NASDAQ:IEF-symbol-description"]=["IShares obligations de trésor 7-10 ans ETF"],e.exports["#NASDAQ:IEI-symbol-description"]=["IShares obligations de trésor 3-7 ans ETF"],e.exports["#NYMEX:KA1-symbol-description"]=["Contrats à terme sur sucre #11"],e.exports["#ICEUSA:KC-symbol-description"]=["Café"],e.exports["#NYMEX:KG1-symbol-description"]=["Contrats à terme sur coton"],e.exports["#FWB:KT1-symbol-description"]=["Key Tronic Corp."],e.exports["#CME:LE-symbol-description"]=["Bétail vivant"],e.exports["#ICEEUR:LO-symbol-description"]=["Huile de chauffage ICE"],e.exports["#CME:LS-symbol-description"]=["Bois"],e.exports["#MOEX:MGNT-symbol-description"]="MAGNIT",e.exports["#LSIN:MNOD-symbol-description"]="ADR GMK NORILSKIYNIKEL ORD SHS [REPO]",e.exports["#NYMEX:NG-symbol-description"]=["Gaz Naturel"],e.exports["#ICEUSA:OJ-symbol-description"]=["Jus d'orange"],e.exports["#NYMEX:PA-symbol-description"]="Palladium",e.exports["#NYSE:PBR-symbol-description"]="PETROLEO BRASILEIRO SA PETROBR",e.exports["#NYMEX:PL-symbol-description"]=["Platine"],e.exports["#COMEX_MINI:QC-symbol-description"]=["Cuivre E-Mini"],e.exports["#NYMEX:RB-symbol-description"]=["Essence RBOB"],e.exports["#NYMEX:RB1-symbol-description"]=["Contrats à terme sur essence RBOB"],e.exports["#MOEX:SBER-symbol-description"]="SBERBANK",e.exports["#AMEX:SCHO-symbol-description"]=["ETF Schwab Trésor Américain court terme"],e.exports["#COMEX:SI-symbol-description"]=["Argent"],e.exports["#NASDAQ:TLT-symbol-description"]=["Obligations du Trésor Américain 20+ ISHARES ETF"],e.exports["#TVC:VIX-symbol-description"]=["Indice de volatilité S&P 500"],e.exports["#MOEX:VTBR-symbol-description"]="VTB",e.exports["#COMEX:ZA-symbol-description"]="Zinc",e.exports["#CBOT:ZC-symbol-description"]=["Maïs"],e.exports["#CBOT:ZK-symbol-description"]=["Contrats à termes sur éthanol"],e.exports["#CBOT:ZL-symbol-description"]=["Huile de germes de soja"],e.exports["#CBOT:ZO-symbol-description"]=["Avoine"],e.exports["#CBOT:ZR-symbol-description"]=["Riz Brut"],e.exports["#CBOT:ZS-symbol-description"]=["Germes de soja"],e.exports["#CBOT:ZS1-symbol-description"]=["Contrats à terme sur germes de soja"],e.exports["#CBOT:ZW-symbol-description"]=["Blé"],e.exports["#CBOT:ZW1-symbol-description"]=["Contrats à terme sur blé ECBT"],e.exports["#NASDAQ:ITI-symbol-description"]="Iteris Inc",e.exports["#NYMEX:ITI2!-symbol-description"]=["Contrats à terme sur minerai de fer"],
e.exports["#CADUSD-symbol-description"]=["Dollar Canadien / U.S. Dollar"],e.exports["#CHFUSD-symbol-description"]=["Franc Suisse / U. S. Dollar"],e.exports["#GPW:ACG-symbol-description"]="Acautogaz",e.exports["#JPYUSD-symbol-description"]=["Yen Japonais  / U.S. Dollar"],e.exports["#USDAUD-symbol-description"]=["U.S. Dollar / Dollar Australien"],e.exports["#USDEUR-symbol-description"]="U.S. Dollar / Euro",e.exports["#USDGBP-symbol-description"]=["U.S. Dollar / Livre Sterling"],e.exports["#USDNZD-symbol-description"]=["U.S. Dollar / Dollar néo-zélandais"],e.exports["#UKOIL-symbol-description"]=["CFDs sur Pétrole brut (Brent)"],e.exports["#USOIL-symbol-description"]=["CFDs sur Pétrole brut (WTI)"],e.exports["#US30-symbol-description"]=["Indice Dow Jones Industriel Moyen"],e.exports["#BCHUSD-symbol-description"]=["Bitcoin Cash / Dollar"],e.exports["#ETCUSD-symbol-description"]=["Ethereum Classic / Dollar"],e.exports["#GOOG-symbol-description"]="Alphabet Inc (Google) Class C",e.exports["#LTCUSD-symbol-description"]=["Litecoin / Dollar"],e.exports["#XRPUSD-symbol-description"]="XRP / U.S. Dollar",e.exports["#SP:SPX-symbol-description"]=["L'indice S&P 500"],e.exports["#ETCBTC-symbol-description"]="Ethereum Classic / Bitcoin",e.exports["#ETHBTC-symbol-description"]="Ethereum / Bitcoin",e.exports["#XRPBTC-symbol-description"]="XRP / Bitcoin",e.exports["#TVC:US30-symbol-description"]=["Obligations du gouvernement américain 30 ans"],e.exports["#COMEX:SI1!-symbol-description"]=["Contrats à terme sur Argent"],e.exports["#BTGUSD-symbol-description"]="Bitcoin Gold / U.S. Dollar",e.exports["#IOTUSD-symbol-description"]="IOTA / U.S. Dollar",e.exports["#CME:BTC1!-symbol-description"]=["Contrats à terme sur Bitcoin CME"],e.exports["#COMEX:GC1!-symbol-description"]=["Contrats à terme sur Or"],e.exports["#CORNUSD-symbol-description"]=["Contrats à terme sur maïs"],e.exports["#COTUSD-symbol-description"]=["Contrats à terme sur Coton"],e.exports["#DJ:DJA-symbol-description"]=["Indice Dow Jones Composite Moyen"],e.exports["#DJ:DJI-symbol-description"]=["Indice Dow Jones Industriel Moyen"],e.exports["#ETHEUR-symbol-description"]="Ethereum / Euro",e.exports["#ETHGBP-symbol-description"]=["Ethereum / Livre britannique"],e.exports["#ETHJPY-symbol-description"]=["Ethereum / Yen japonais"],e.exports["#EURNOK-symbol-description"]=["Euro / Couronne Norvégienne"],e.exports["#GBPPLN-symbol-description"]=["Livre britannique / Zloty polonais"],e.exports["#MOEX:BR1!-symbol-description"]=["Contrats à terme sur Pétrole Brent"],e.exports["#NYMEX:KG1!-symbol-description"]=["Contrats à terme sur coton"],e.exports["#NYMEX:PL1!-symbol-description"]=["Contrats à terme sur Platine"],e.exports["#SOYBNUSD-symbol-description"]=["CFD sur Germes de soja"],e.exports["#SUGARUSD-symbol-description"]=["CFD sur Sucre"],e.exports["#TVC:IXIC-symbol-description"]=["Indice Composite US"],e.exports["#TVC:RU-symbol-description"]=["Indice Russell 1000"],e.exports["#USDZAR-symbol-description"]=["Dollar américain / Rand sud-africain"],
e.exports["#WHEATUSD-symbol-description"]=["CFD sur Blé"],e.exports["#XRPEUR-symbol-description"]="XRP / Euro",e.exports["#CBOT:S1!-symbol-description"]=["Contrats à terme sur soja"],e.exports["#SP:MID-symbol-description"]=["Indice S&P 400"],e.exports["#TSX:XCUUSD-symbol-description"]=["CFD sur Cuivre"],e.exports["#TVC:NYA-symbol-description"]=["Indice Composite NYSE"],e.exports["#TVC:PLATINUM-symbol-description"]=["CFD sur Platine (US$ / OZ)"],e.exports["#TVC:SSMI-symbol-description"]="Swiss Market Index",e.exports["#TVC:SXY-symbol-description"]=["Indice de devise Franc suisse"],e.exports["#TVC:RUI-symbol-description"]=["Indice Russell 1000"],e.exports["#MOEX:RI1!-symbol-description"]=["Contrats à terme sur indice RTS"],e.exports["#MOEX:MX1!-symbol-description"]=["Contrats à terme sur indice MICEX"],e.exports["#CBOE:BG1!-symbol-description"]=["Contrats à terme Bitcoin CBOE"],e.exports["#TVC:MY10-symbol-description"]=["Obligations du Gouvernement Malaisien 10 YR"],e.exports["#CME:S61!-symbol-description"]=["Contrats à terme Francs Suisses"],e.exports["#TVC:DEU30-symbol-description"]=["Indice DAX"],e.exports["#BCHEUR-symbol-description"]="Bitcoin Cash / Euro",e.exports["#TVC:ZXY-symbol-description"]=["Indice de devise Dollar Néo-Zélandais"],e.exports["#MIL:FTSEMIB-symbol-description"]=["Indice FTSE MIB"],e.exports["#XETR:DAX-symbol-description"]=["Indice DAX"],e.exports["#MOEX:IMOEX-symbol-description"]=["Indice MOEX Russie"],e.exports["#FX:US30-symbol-description"]=["Indice moyen Dow Jones Industriel"],e.exports["#MOEX:RUAL-symbol-description"]="United Company RUSAL PLC",e.exports["#MOEX:MX2!-symbol-description"]=["Contrats à terme sur indice MICEX"],e.exports["#NEOUSD-symbol-description"]="NEO / U.S. Dollar",e.exports["#XMRUSD-symbol-description"]="Monero / U.S. Dollar",e.exports["#ZECUSD-symbol-description"]="Zcash / U.S. Dollar",e.exports["#TVC:CAC-symbol-description"]=["Indice CAC40"],e.exports["#NASDAQ:ZS-symbol-description"]="Zscaler Inc",e.exports["#TVC:GB10Y-symbol-description"]=["Obligations du gouvernement Britannique 10 ans"],e.exports["#TVC:AU10Y-symbol-description"]=["Australie Obligations du gouvernement 10 ans"],e.exports["#TVC:CN10Y-symbol-description"]=["Chine Obligations du gouvernement 10 ans"],e.exports["#TVC:DE10Y-symbol-description"]=["Allemagne Obligations du gouvernement 10 ans"],e.exports["#TVC:ES10Y-symbol-description"]=["Espagne Obligations du gouvernement 10 ans"],e.exports["#TVC:FR10Y-symbol-description"]=["France Obligations du gouvernement 10 ans"],e.exports["#TVC:IN10Y-symbol-description"]=["Inde Obligations gouvernementales 10 ans"],e.exports["#TVC:IT10Y-symbol-description"]=["Italie Obligations gouvernementales 10 ans"],e.exports["#TVC:JP10Y-symbol-description"]=["Japon Obligations gouvernementales 10 ans"],e.exports["#TVC:KR10Y-symbol-description"]=["Corée Obligations du gouvernement 10 ans"],e.exports["#TVC:MY10Y-symbol-description"]=["Obligations du Gouvernement Malaisien 10 YR"],e.exports["#TVC:PT10Y-symbol-description"]=["Portugal, Obligations du gouvernement 10 ans"],
e.exports["#TVC:TR10Y-symbol-description"]=["Turkey Government Bonds 10 YR"],e.exports["#TVC:US02Y-symbol-description"]=["Obligations gouvernement américain 2 ans"],e.exports["#TVC:US05Y-symbol-description"]=["Obligations gouvernement américain 5 ans"],e.exports["#TVC:US10Y-symbol-description"]=["Obligations gouvernement américain 10 ans"],e.exports["#INDEX:TWII-symbol-description"]=["Indice pondéré Taiwan"],e.exports["#CME:J61!-symbol-description"]=["Contrats à terme Yen Japonais"],e.exports["#CME_MINI:J71!-symbol-description"]=["Contrats à terme E-mini Yen Japonais"],e.exports["#CME_MINI:WM1!-symbol-description"]=["E-micro contrats à terme Yen / US dollar"],e.exports["#CME:M61!-symbol-description"]=["Contrats à terme Peso mexicain"],e.exports["#CME:T61!-symbol-description"]=["Contrats à terme Rand Sud-Africain"],e.exports["#CME:SK1!-symbol-description"]=["Contrats à terme Couronne suédoise"],e.exports["#CME:QT1!-symbol-description"]=["Contrats à terme Renminbi chinois / US dollar"],e.exports["#COMEX:AUP1!-symbol-description"]=["Contrats à terme Aluminum MW U.S. Transaction Premium Platts (25MT)"],e.exports["#CME:L61!-symbol-description"]=["Contrats à terme Real brésilien"],e.exports["#CME:WP1!-symbol-description"]=["Contrats à terme Zloty polonais"],e.exports["#CME:N61!-symbol-description"]=["Contrats à terme Dollar néo-zélandais"],e.exports["#CME_MINI:MG1!-symbol-description"]=["Contrats à terme E-micro Dollar australien / US dollar"],e.exports["#CME_MINI:WN1!-symbol-description"]=["Contrats à terme E-micro Franc suisse / US dollar"],e.exports["#CME_MINI:MF1!-symbol-description"]=["Contrats à terme E-micro Euro / US dollar"],e.exports["#CME_MINI:E71!-symbol-description"]=["Contrats à terme E-mini Euro"],e.exports["#CBOT:ZK1!-symbol-description"]=["Contrats à terme Fuel dénaturé Ethanol"],e.exports["#CME_MINI:MB1!-symbol-description"]=["Contrats à terme Livre britannique / US dollar"],e.exports["#NYMEX_MINI:QU1!-symbol-description"]=["Contrats à terme E-mini Gasoline"],e.exports["#NYMEX_MINI:QX1!-symbol-description"]=["Contrats à terme E-mini fuel domestique"],e.exports["#COMEX_MINI:QC1!-symbol-description"]=["Contrats à terme E-mini Cuivre"],e.exports["#NYMEX_MINI:QG1!-symbol-description"]=["Contrats à terme E-mini Gaz naturel"],e.exports["#CME:E41!-symbol-description"]=["Contrats à terme US dollar / Livre turque"],e.exports["#COMEX_MINI:QI1!-symbol-description"]=["(Mini) Contrats à terme Argent"],e.exports["#CME:DL1!-symbol-description"]=["Contrats à terme Lait, Classe III"],e.exports["#NYMEX:UX1!-symbol-description"]=["Contrats à terme Uranium"],e.exports["#CBOT:BO1!-symbol-description"]=["Contrats à terme Huile de soja"],e.exports["#CME:HE1!-symbol-description"]=["Contrats à terme porc maigre"],e.exports["#NYMEX:IAC1!-symbol-description"]=["Contrats à terme Charbon de Newcastle"],e.exports["#NYMEX_MINI:QM1!-symbol-description"]=["Contrats à terme E-mini Pétrole brut léger"],e.exports["#NYMEX:JMJ1!-symbol-description"]=["Contrats à terme financiers Mini Brent"],
e.exports["#COMEX:AEP1!-symbol-description"]=["Contrats à terme Aluminium Européen Premium"],e.exports["#CBOT:ZQ1!-symbol-description"]=["Contrats à terme à 30 jours Taux d'intérêt des fonds fédéraux"],e.exports["#CME:LE1!-symbol-description"]=["Contrats à terme bétail sur pied"],e.exports["#CME:UP1!-symbol-description"]=["Contrats à terme Franc suisse / Yen Japonais"],e.exports["#CBOT:ZN1!-symbol-description"]=["Contrats à terme T-Note 10 ans"],e.exports["#CBOT:ZB1!-symbol-description"]=["Contrats à terme T-Bond"],e.exports["#CME:GF1!-symbol-description"]=["Contrats à terme Bovins d'engraissement"],e.exports["#CBOT:UD1!-symbol-description"]=["Contrats à terme Ultra T-Bond"],e.exports["#CME:I91!-symbol-description"]=["Contrats à terme CME Housing - Washington DC"],e.exports["#CBOT:ZO1!-symbol-description"]=["Contrats à terme Avoine"],e.exports["#CBOT:ZM1!-symbol-description"]=["Contrats à terme Farine de soja"],e.exports["#CBOT_MINI:XN1!-symbol-description"]=["Contrats à terme mini Maïs"],e.exports["#CBOT:ZC1!-symbol-description"]=["Contrats à terme Maïs"],e.exports["#CME:LS1!-symbol-description"]=["Contrats à terme Bois de charpente"],e.exports["#CBOT_MINI:XW1!-symbol-description"]=["Contrats à terme Blé mini"],e.exports["#CBOT_MINI:XK1!-symbol-description"]=["Contrats à terme Soja mini"],e.exports["#CBOT:ZS1!-symbol-description"]=["Contrats à terme Soja"],e.exports["#NYMEX:PA1!-symbol-description"]=["Contrats à terme Palladium"],e.exports["#CME:FTU1!-symbol-description"]=["Contrats à terme E-mini Indice FTSE 100 USD"],e.exports["#CBOT:ZR1!-symbol-description"]=["Contrats à terme Riz"],e.exports["#COMEX_MINI:GR1!-symbol-description"]=["Contrats à terme Or (E-micro)"],e.exports["#COMEX_MINI:QO1!-symbol-description"]=["Contrats à terme Or (mini)"],e.exports["#CME_MINI:RL1!-symbol-description"]=["Contrats à terme E-mini Russell 1000"],e.exports["#CME_MINI:EW1!-symbol-description"]=["Contrats à terme E-mini S&P 400 Midcap"],e.exports["#COMEX:LD1!-symbol-description"]=["Contrats à terme Plomb"],e.exports["#CME_MINI:ES1!-symbol-description"]=["Contrats à terme E-mini S&P 500"],e.exports["#TVC:SA40-symbol-description"]=["Indice Top 40 Afrique du Sud"],e.exports["#BMV:ME-symbol-description"]=["Indice IPC Mexique"],e.exports["#BCBA:IMV-symbol-description"]=["Indice MERVAL"],e.exports["#HSI:HSI-symbol-description"]=["Indice Hang Seng"],e.exports["#BVL:SPBLPGPT-symbol-description"]=["Indice Général Pérou S&P / BVL (PEN)"],e.exports["#EGX:EGX30-symbol-description"]=["Indice de rendement EGX 30"],e.exports["#BVC:IGBC-symbol-description"]=["Indice Général de la Bolsa de Valores de Colombie"],e.exports["#TWSE:TAIEX-symbol-description"]=["Indice boursier pondéré de la capitalisation taïwanaise"],e.exports["#QSE:GNRI-symbol-description"]=["Indice QE"],e.exports["#BME:IBC-symbol-description"]=["Indice IBEX 35"],e.exports["#NZX:NZ50G-symbol-description"]=["Indice brut S&P / NZX"],e.exports["#SIX:SMI-symbol-description"]=["Indice du marché Suisse"],e.exports["#SZSE:399001-symbol-description"]=["Indice des composants SZSE"],
e.exports["#TADAWUL:TASI-symbol-description"]=["Indice toutes actions Tadawul"],e.exports["#IDX:COMPOSITE-symbol-description"]=["Indice Composite IDX"],e.exports["#EURONEXT:PX1-symbol-description"]=["Indice CAC 40"],e.exports["#OMXHEX:OMXH25-symbol-description"]=["Indice OMX Helsinki 25"],e.exports["#EURONEXT:BEL20-symbol-description"]=["Indice BEL 20"],e.exports["#TVC:STI-symbol-description"]=["Indice Straits Times"],e.exports["#DFM:DFMGI-symbol-description"]=["Indice DFM"],e.exports["#TVC:KOSPI-symbol-description"]=["Indice de prix Actions coréennes composites"],e.exports["#FTSEMYX:FBMKLCI-symbol-description"]=["Indice FTSE Bourse de Malaisie KLCI"],e.exports["#TASE:TA35-symbol-description"]=["Indice TA-35"],e.exports["#OMXSTO:OMXS30-symbol-description"]=["Indice OMX Stockholm 30"],e.exports["#OMXICE:OMXI8-symbol-description"]=["Indice OMX Iceland 8"],e.exports["#NSENG:NSE30-symbol-description"]=["Indice NSE 30"],e.exports["#BAHRAIN:BSEX-symbol-description"]=["Indice Toutes actions Bahrein"],e.exports["#OMXTSE:OMXTGI-symbol-description"]="OMX Tallinn GI",e.exports["#OMXCOP:OMXC25-symbol-description"]=["Indice OMX Copenhague 25"],e.exports["#OMXRSE:OMXRGI-symbol-description"]="OMX Riga GI",e.exports["#BELEX:BELEX15-symbol-description"]=["Indice BELEX 15"],e.exports["#OMXVSE:OMXVGI-symbol-description"]="OMX Vilnius GI",e.exports["#EURONEXT:AEX-symbol-description"]=["Indice AEX"],e.exports["#CBOE:VIX-symbol-description"]=["Indice de volatilité S&P 500"],e.exports["#NASDAQ:XAU-symbol-description"]=["Indice PHLX Secteur Or et Argent"],e.exports["#DJ:DJUSCL-symbol-description"]=["Indice Dow Jones Charbon US"],e.exports["#DJ:DJCIKC-symbol-description"]=["Indice du Dow Jones Commodity Café"],e.exports["#DJ:DJCIEN-symbol-description"]=["Indice Dow Jones Commodity Energy"],e.exports["#NASDAQ:OSX-symbol-description"]=["Indice Secteur des Services PHLX Oil"],e.exports["#DJ:DJCISB-symbol-description"]=["Indice Dow Jones Commodity Sucre"],e.exports["#DJ:DJCICC-symbol-description"]=["Indice Dow Jones Commodity Cacao"],e.exports["#DJ:DJCIGR-symbol-description"]=["Indice Dow Jones Commodity Grains"],e.exports["#DJ:DJCIAGC-symbol-description"]=["Indice Dow Jones Commodity Agriculture Capped Component"],e.exports["#DJ:DJCISI-symbol-description"]=["Indice Dow Jones Commodity Argent"],e.exports["#DJ:DJCIIK-symbol-description"]=["Indice Dow Jones Commodity Nickel"],e.exports["#NASDAQ:HGX-symbol-description"]=["Indice PHLX Secteur du logement"],e.exports["#DJ:DJCIGC-symbol-description"]=["Indice Dow Jones Commodity Or"],e.exports["#SP:SPGSCI-symbol-description"]=["Indice Commodity S&P Goldman Sachs"],e.exports["#NASDAQ:UTY-symbol-description"]=["Indice PHLX secteur des services publics"],e.exports["#DJ:DJU-symbol-description"]=["Indice moyen Dow Jones Services publics"],e.exports["#SP:SVX-symbol-description"]=["Indice de valeur S&P 500"],e.exports["#SP:OEX-symbol-description"]=["Indice S&P 100"],e.exports["#CBOE:OEX-symbol-description"]=["Indice S&P 100"],e.exports["#NASDAQ:SOX-symbol-description"]=["Indice Philadelphia Semiconductor"],
e.exports["#RUSSELL:RUI-symbol-description"]=["Indice Russell 1000"],e.exports["#RUSSELL:RUA-symbol-description"]=["Indice Russell 3000"],e.exports["#RUSSELL:RUT-symbol-description"]=["Indice Russell 2000"],e.exports["#NYSE:XMI-symbol-description"]=["Indice principal de marché NYSE ARCA"],e.exports["#NYSE:XAX-symbol-description"]=["Indice Composite AMEX"],e.exports["#NASDAQ:NDX-symbol-description"]=["Indice Nasdaq 100"],e.exports["#NASDAQ:IXIC-symbol-description"]=["Indice Composite Nasdaq"],e.exports["#DJ:DJT-symbol-description"]=["Indice Dow Jones Transportation Average"],e.exports["#NYSE:NYA-symbol-description"]=["Indice Composite NYSE"],e.exports["#NYMEX:CJ1!-symbol-description"]=["Contrats à terme cacao"],e.exports["#USDILS-symbol-description"]=["U.S. Dollar / Shekel Israélien"],e.exports["#TSXV:F-symbol-description"]="Fiore Gold Inc",e.exports["#SIX:F-symbol-description"]="Ford Motor Company",e.exports["#BMV:F-symbol-description"]="Ford Motor Company",e.exports["#TWII-symbol-description"]=["Indice pondéré Taiwan"],e.exports["#TVC:PL10Y-symbol-description"]=["Rendement Obligations Gouvernement Polonais 10Y"],e.exports["#TVC:PL05Y-symbol-description"]=["Rendement Obligations Gouvernement Polonais 5Y"],e.exports["#SET:GC-symbol-description"]=["Global Connections Public  Company"],e.exports["#TSX:GC-symbol-description"]="Great Canadian Gaming Corporation",e.exports["#TVC:FTMIB-symbol-description"]="Milano Italia Borsa Index",e.exports["#OANDA:SPX500USD-symbol-description"]=["Indice S&P 500"],e.exports["#BMV:CT-symbol-description"]="China SX20 RT",e.exports["#TSXV:CT-symbol-description"]="Centenera Mining Corporation",e.exports["#BYBIT:ETHUSD-symbol-description"]=["Contrat perpétuel ETHUSD"],e.exports["#BYBIT:XRPUSD-symbol-description"]=["Contrat perpétuel XRPUSD"],e.exports["#BYBIT:BTCUSD-symbol-description"]=["Contrat perpétuel BTCUSD"],e.exports["#BITMEX:ETHUSD-symbol-description"]=["ETHUSD Contrats à terme perpétuel"],e.exports["#DERIBIT:BTCUSD-symbol-description"]=["BTCUSD Contrats à terme perpétuel"],e.exports["#DERIBIT:ETHUSD-symbol-description"]=["ETHUSD Contrats à terme perpétuel"],e.exports["#USDHUF-symbol-description"]=["Dollar US / Forint Hongrois"],e.exports["#USDTHB-symbol-description"]=["Dollar US / Baht Thaïlandais"],e.exports["#FOREXCOM:US2000-symbol-description"]="US Small Cap 2000",e.exports["#TSXV:PBR-symbol-description"]="Para Resources Inc",e.exports["#NYSE:SI-symbol-description"]="Silvergate Capital Corporation",e.exports["#NASDAQ:LE-symbol-description"]="Lands' End Inc",e.exports["#CME:CB1!-symbol-description"]=["Butter Futures-Cash (Continu : contrat actuel en premier)"],e.exports["#LSE:SCHO-symbol-description"]="Scholium Group Plc Ord 1P",e.exports["#NEO:HE-symbol-description"]="Hanwei Energy Services Corp.",e.exports["#NYSE:HE-symbol-description"]="Hawaiian Electric Industries",e.exports["#OMXCOP:SCHO-symbol-description"]="Schouw & Co A/S",e.exports["#TSX:HE-symbol-description"]="Hanwei Energy Services Corp.",e.exports["#BSE:ITI-symbol-description"]="ITI Ltd",
e.exports["#NSE:ITI-symbol-description"]="Indian Telephone Industries Limited",e.exports["#TSX:LS-symbol-description"]="Middlefield Healthcare & Life Sciences Dividend Fund",e.exports["#BITMEX:XBT-symbol-description"]=["Indice Bitcoin / U.S. Dollar"],e.exports["#CME_MINI:RTY1!-symbol-description"]="E-Mini Russell 2000 Index Futures",e.exports["#CRYPTOCAP:TOTAL-symbol-description"]=["Capitalisation boursière totale Crypto, $"],e.exports["#ICEUS:DX1!-symbol-description"]="U.S. Dollar Index Futures",e.exports["#NYMEX:TT1!-symbol-description"]=["Contrats à terme sur coton"],e.exports["#PHEMEX:BTCUSD-symbol-description"]=["Contrats à terme perpétuels BTC"],e.exports["#PHEMEX:ETHUSD-symbol-description"]=["Contrats à terme perpétuels ETH"],e.exports["#PHEMEX:XRPUSD-symbol-description"]=["Contrats à terme perpétuels XRP"],e.exports["#PHEMEX:LTCUSD-symbol-description"]=["Contrats à terme perpétuels LTC"],e.exports["#BITCOKE:BCHUSD-symbol-description"]="BCH Quanto Swap",e.exports["#BITCOKE:BTCUSD-symbol-description"]="BTC Quanto Swap",e.exports["#BITCOKE:ETHUSD-symbol-description"]="ETH Quanto Swap",e.exports["#BITCOKE:LTCUSD-symbol-description"]="LTC Quanto Swap",e.exports["#TVC:CA10-symbol-description"]="Canadian Government Bonds, 10 YR",e.exports["#TVC:CA10Y-symbol-description"]=["Rendement des obligations du gouvernement canadien à 10 ans"],e.exports["#TVC:ID10Y-symbol-description"]=["Rendement des obligations du gouvernement indonésien à 10 ans"],e.exports["#TVC:NL10-symbol-description"]=["Obligations du gouvernement néerlandais à 10 ans"],e.exports["#TVC:NL10Y-symbol-description"]=["Rendement des obligations du gouvernement néerlandais à 10 ans"],e.exports["#TVC:NZ10-symbol-description"]=["Obligations du gouvernement néo-zélandais à 10 ans"],e.exports["#TVC:NZ10Y-symbol-description"]=["Rendement des obligations du gouvernement néo-zélandais à 10 ans"],e.exports["#SOLUSD-symbol-description"]="Solana / U.S. Dollar",e.exports["#LUNAUSD-symbol-description"]="Luna / U.S. Dollar",e.exports["#UNIUSD-symbol-description"]="Uniswap / U.S. Dollar",e.exports["#LTCBRL-symbol-description"]=["Litecoin / Real Brésilien"],e.exports["#ETCEUR-symbol-description"]="Ethereum Classic / Euro",e.exports["#ETHKRW-symbol-description"]=["Ethereum / Won sud-coréen"],e.exports["#BTCRUB-symbol-description"]=["Bitcoin / Rouble russe"],e.exports["#BTCTHB-symbol-description"]=["Bitcoin / Baht Thai"],e.exports["#ETHTHB-symbol-description"]=["Ethereum / Baht Thai"],e.exports["#TVC:EU10YY-symbol-description"]="Euro Government Bonds 10 YR Yield"}}]);
(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[3732],{20747:e=>{e.exports=["ulangi"]},9846:e=>{e.exports="A"},55765:e=>{e.exports="L"},14642:e=>{e.exports=["Gelap"]},69841:e=>{e.exports=["Terang"]},673:e=>{e.exports=Object.create(null),e.exports.d_dates="d",e.exports.h_dates="h",e.exports.m_dates="m",e.exports.s_dates="s",e.exports.in_dates=["dalam"]},97840:e=>{e.exports="d"},64302:e=>{e.exports="h"},79442:e=>{e.exports="m"},22448:e=>{e.exports="s"},16493:e=>{e.exports=["Salinan {title}"]},13395:e=>{e.exports="D"},37720:e=>{e.exports="M"},69838:e=>{e.exports="R"},59231:e=>{e.exports="T"},85521:e=>{e.exports="W"},13994:e=>{e.exports="h"},6791:e=>{e.exports="m"},2949:e=>{e.exports="s"},77297:e=>{e.exports="C"},56723:e=>{e.exports="H"},5801:e=>{e.exports="HL2"},98865:e=>{e.exports="HLC3"},42659:e=>{e.exports="OHLC4"},4292:e=>{e.exports="L"},78155:e=>{e.exports="O"},88601:e=>{e.exports=Object.create(null),e.exports.Close_input=["Tutup"],e.exports.Back_input=["Kembali"],e.exports.Minimize_input=["Perkecil"],e.exports["Hull MA_input"]=["MA Hull"],e.exports["{number} item_combobox_input"]="{number} item",e.exports.Length_input=["Panjang"],e.exports.Plot_input="Plot",e.exports.Zero_input=["Nol"],e.exports.Signal_input=["Sinyal"],e.exports.Long_input=["Pembelian"],e.exports.Short_input=["Penjualan"],e.exports.UpperLimit_input=["LimitAtas"],e.exports.LowerLimit_input=["LimitBawah"],e.exports.Offset_input="Offset",e.exports.length_input=["panjang"],e.exports.mult_input="mult",e.exports.short_input=["penjualan"],e.exports.long_input=["pembelian"],e.exports.Limit_input="Limit",e.exports.Move_input=["Pindah"],e.exports.Value_input=["Nilai"],e.exports.Method_input=["Metode"],e.exports["Values in status line_input"]=["Nilai dalam baris status"],e.exports["Labels on price scale_input"]=["Label pada skala harga"],e.exports["Accumulation/Distribution_input"]=["Akumulasi/Distribusi"],e.exports.ADR_B_input="ADR_B",e.exports["Equality Line_input"]=["Garis Kesetaraan"],e.exports["Window Size_input"]=["Besar Jendela"],e.exports.Sigma_input="Sigma",e.exports["Aroon Up_input"]=["Aroon Naik"],e.exports["Aroon Down_input"]=["Aroon Turun"],e.exports.Upper_input=["Atas"],e.exports.Lower_input=["Bawah"],e.exports.Deviation_input=["Deviasi"],e.exports["Levels Format_input"]=["Format Level"],e.exports["Labels Position_input"]=["Posisi Label"],e.exports["0 Level Color_input"]=["Warna Level 0"],e.exports["0.236 Level Color_input"]=["Warna Level 0.236"],e.exports["0.382 Level Color_input"]=["Warna Level 0.382"],e.exports["0.5 Level Color_input"]=["Warna Level 0.5"],e.exports["0.618 Level Color_input"]=["Warna Level 0.618"],e.exports["0.65 Level Color_input"]=["Warna Level 0.65"],e.exports["0.786 Level Color_input"]=["Warna Level 0.786"],e.exports["1 Level Color_input"]=["Warna Level 1"],e.exports["1.272 Level Color_input"]=["Warna Level 1.272"],e.exports["1.414 Level Color_input"]=["Warna Level 1.414"],e.exports["1.618 Level Color_input"]=["Warna Level 1.618"],e.exports["1.65 Level Color_input"]=["Warna Level 1.65"],
e.exports["2.618 Level Color_input"]=["Warna Level 2.618"],e.exports["2.65 Level Color_input"]=["Warna Level 2.65"],e.exports["3.618 Level Color_input"]=["Warna Level 3.618"],e.exports["3.65 Level Color_input"]=["Warna Level 3.65"],e.exports["4.236 Level Color_input"]=["Warna Level 4.236"],e.exports["-0.236 Level Color_input"]=["Warna Level -0.236"],e.exports["-0.382 Level Color_input"]=["Warna Level -0.382"],e.exports["-0.618 Level Color_input"]=["Warna Level -0.618"],e.exports["-0.65 Level Color_input"]=["Warna Level -0.65"],e.exports.ADX_input="ADX",e.exports["ADX Smoothing_input"]=["Penghalusan ADX"],e.exports["DI Length_input"]=["Panjang DI"],e.exports.Smoothing_input=["Penghalusan"],e.exports.ATR_input="ATR",e.exports.Growing_input=["Berkembang"],e.exports.Falling_input=["Jatuh"],e.exports["Color 0_input"]=["Warna 0"],e.exports["Color 1_input"]=["Warna 1"],e.exports.Source_input=["Sumber"],e.exports.StdDev_input="StdDev",e.exports.Basis_input="Basis",e.exports.Median_input="Median",e.exports["Bollinger Bands %B_input"]=["%B Ikat Bollinger"],e.exports.Overbought_input="Overbought",e.exports.Oversold_input="Oversold",e.exports["Bollinger Bands Width_input"]=["Lebar Ikat Bollinger"],e.exports["RSI Length_input"]=["Panjang RSI"],e.exports["UpDown Length_input"]=["Panjang UpDown"],e.exports["ROC Length_input"]=["Panjang ROC"],e.exports.MF_input="MF",e.exports.resolution_input=["resolusi"],e.exports["Fast Length_input"]=["Panjang Cepat"],e.exports["Slow Length_input"]=["Panjang Lambat"],e.exports["Chaikin Oscillator_input"]=["Osilator Chaikin"],e.exports.P_input="P",e.exports.X_input="X",e.exports.Q_input="Q",e.exports.p_input="p",e.exports.x_input="x",e.exports.q_input="q",e.exports.Price_input=["Harga"],e.exports["Chande MO_input"]=["MO Chande"],e.exports["Zero Line_input"]=["Garis Nol"],e.exports["Color 2_input"]=["Warna 2"],e.exports["Color 3_input"]=["Warna 3"],e.exports["Color 4_input"]=["Warna 4"],e.exports["Color 5_input"]=["Warna 5"],e.exports["Color 6_input"]=["Warna 6"],e.exports["Color 7_input"]=["Warna 7"],e.exports["Color 8_input"]=["Warna 8"],e.exports.CHOP_input="CHOP",e.exports["Upper Band_input"]=["Ikat Atas"],e.exports["Lower Band_input"]=["Ikat Bawah"],e.exports.CCI_input="CCI",e.exports["Smoothing Line_input"]=["Garis Diperhalus"],e.exports["Smoothing Length_input"]=["Panjang Diperhalus"],e.exports["WMA Length_input"]=["Panjang WMA"],e.exports["Long RoC Length_input"]=["Panjang RoC Pembelian"],e.exports["Short RoC Length_input"]=["Panjang RoC Penjualan"],e.exports.sym_input=["sim"],e.exports.Symbol_input=["Simbol"],e.exports.Correlation_input=["Korelasi"],e.exports.Period_input=["Periode"],e.exports.Centered_input=["Dipusatkan"],e.exports["Detrended Price Oscillator_input"]=["Osilator Harga Detrended / Detrended Price Oscillator"],e.exports.isCentered_input=["Ditengahkan"],e.exports.DPO_input="DPO",e.exports["ADX smoothing_input"]=["Penghalusan ADX"],e.exports["+DI_input"]="+DI",e.exports["-DI_input"]="-DI",e.exports.DEMA_input="DEMA",
e.exports["Multi timeframe_input"]=["Beberapa kerangka waktu"],e.exports.Timeframe_input=["Kerangka waktu"],e.exports["Wait for timeframe closes_input"]=["Tunggu kerangka waktu ditutup"],e.exports.Divisor_input=["Pembagi"],e.exports.EOM_input="EOM",e.exports["Elder's Force Index_input"]=["Indeks Kekuatan Elder / Elder's Force Index"],e.exports.Percent_input=["Persen"],e.exports.Exponential_input=["Eksponensial"],e.exports.Average_input=["Rata-Rata"],e.exports["Upper Percentage_input"]=["Persentase Atas"],e.exports["Lower Percentage_input"]=["Persentase Bawah"],e.exports.Fisher_input="Fisher",e.exports.Trigger_input=["Pemicu"],e.exports.Level_input="Level",e.exports["Trader EMA 1 length_input"]=["EMA Trader panjang 1"],e.exports["Trader EMA 2 length_input"]=["EMA Trader panjang 2"],e.exports["Trader EMA 3 length_input"]=["EMA Trader panjang 3"],e.exports["Trader EMA 4 length_input"]=["EMA Trader panjang 4"],e.exports["Trader EMA 5 length_input"]=["EMA Trader panjang 5"],e.exports["Trader EMA 6 length_input"]=["EMA Trader panjang 6"],e.exports["Investor EMA 1 length_input"]=["EMA Investor panjang 1"],e.exports["Investor EMA 2 length_input"]=["EMA Investor panjang 2"],e.exports["Investor EMA 3 length_input"]=["EMA Investor panjang 3"],e.exports["Investor EMA 4 length_input"]=["EMA Investor panjang 4"],e.exports["Investor EMA 5 length_input"]=["EMA Investor panjang 5"],e.exports["Investor EMA 6 length_input"]=["EMA Investor panjang 6"],e.exports.HV_input="HV",e.exports["Conversion Line Periods_input"]=["Periode Garis Konversi"],e.exports["Base Line Periods_input"]=["Periode Garis Dasar"],e.exports["Lagging Span_input"]="Lagging Span",e.exports["Conversion Line_input"]=["Garis Konversi"],e.exports["Base Line_input"]=["Garis Dasar"],e.exports["Leading Span A_input"]="Leading Span A",e.exports["Leading Span B_input"]="Leading Span B",e.exports["Plots Background_input"]=["Latar Belakang Plot"],e.exports["yay Color 0_input"]=["Warna yay 0"],e.exports["yay Color 1_input"]=["Warna yay 1"],e.exports.Multiplier_input=["Pengali"],e.exports["Bands style_input"]=["Corak Pita"],e.exports.Middle_input=["Tengah"],e.exports.useTrueRange_input=["gunakanRentangSebenarnya"],e.exports.ROCLen1_input=["PjgROC1"],e.exports.ROCLen2_input=["PjgROC2"],e.exports.ROCLen3_input=["PjgROC3"],e.exports.ROCLen4_input=["PjgROC4"],e.exports.SMALen1_input=["PjgSMA1"],e.exports.SMALen2_input=["PjgSMA2"],e.exports.SMALen3_input=["PjgSMA3"],e.exports.SMALen4_input=["PjgSMA4"],e.exports.SigLen_input=["PjgSig"],e.exports.KST_input="KST",e.exports.Sig_input="Sig",e.exports.roclen1_input=["pjgroc1"],e.exports.roclen2_input=["pjgroc2"],e.exports.roclen3_input=["pjgroc3"],e.exports.roclen4_input=["pjgroc4"],e.exports.smalen1_input=["pjgsma1"],e.exports.smalen2_input=["pjgsma2"],e.exports.smalen3_input=["pjgsma3"],e.exports.smalen4_input=["pjgsma4"],e.exports.siglen_input=["pjgsin"],e.exports["Upper Deviation_input"]=["Deviasi Atas"],e.exports["Lower Deviation_input"]=["Deviasi Bawah"],e.exports["Use Upper Deviation_input"]=["Gunakan Deviasi Atas"],
e.exports["Use Lower Deviation_input"]=["Gunakan Deviasi Bawah"],e.exports.Count_input=["Hitung"],e.exports.Crosses_input=["Persilangan"],e.exports.MOM_input="MOM",e.exports.MA_input="MA",e.exports["Length EMA_input"]=["Panjang EMA"],e.exports["Length MA_input"]=["Panjang MA"],e.exports["Fast length_input"]=["Panjang Cepat"],e.exports["Slow length_input"]=["Panjang lambat"],e.exports["Signal smoothing_input"]=["Penghalusan sinyal"],e.exports["Simple ma(oscillator)_input"]=["MA sederhana(osilator)"],e.exports["Simple ma(signal line)_input"]=["MA sederhana (garis sinyal)"],e.exports.Histogram_input="Histogram",e.exports.MACD_input="MACD",e.exports.fastLength_input=["Panjangcepat"],e.exports.slowLength_input=["Panjanglambat"],e.exports.signalLength_input=["Panjangsinyal"],e.exports.NV_input="NV",e.exports.OnBalanceVolume_input=["VolumeKeseimbangan"],e.exports.Start_input="Start",e.exports.Increment_input=["Kenaikan"],e.exports["Max value_input"]=["Nilai Max"],e.exports.ParabolicSAR_input=["SAR Parabolis"],e.exports.start_input=["mulai"],e.exports.increment_input=["kenaikan"],e.exports.maximum_input=["maksimum"],e.exports["Short length_input"]=["Panjang penjualan"],e.exports["Long length_input"]=["Panjang pembelian"],e.exports.OSC_input="OSC",e.exports.shortlen_input=["pjgpenjualan"],e.exports.longlen_input=["pjgpembelian"],e.exports.PVT_input="PVT",e.exports.ROC_input="ROC",e.exports.RSI_input="RSI",e.exports.RVGI_input="RVGI",e.exports.RVI_input="RVI",e.exports["Long period_input"]=["Periode pembelian"],e.exports["Short period_input"]=["Periode penjualan"],e.exports["Signal line period_input"]=["Periode garis sinyal"],e.exports.SMI_input="SMI",e.exports["SMI Ergodic Oscillator_input"]=["Osilator SMI Ergodic / SMI Ergodic Oscillator"],e.exports.Indicator_input=["Indikator"],e.exports.Oscillator_input=["Osilator"],e.exports.K_input="K",e.exports.D_input="D",e.exports.smoothK_input=["Khalus"],e.exports.smoothD_input=["Dhalus"],e.exports["%K_input"]="%K",e.exports["%D_input"]="%D",e.exports["Stochastic Length_input"]=["Panjang Stochastic"],e.exports["RSI Source_input"]=["Sumber RSI"],e.exports.lengthRSI_input=["panjangRSI"],e.exports.lengthStoch_input=["panjangStoch"],e.exports.TRIX_input="TRIX",e.exports.TEMA_input="TEMA",e.exports["Long Length_input"]=["Panjang Pembelian"],e.exports["Short Length_input"]=["Panjang Penjualan"],e.exports["Signal Length_input"]=["Panjang Sinyal"],e.exports.Length1_input=["Panjang1"],e.exports.Length2_input=["Panjang2"],e.exports.Length3_input=["Panjang3"],e.exports.length7_input=["panjang7"],e.exports.length14_input=["panjang14"],e.exports.length28_input=["panjang28"],e.exports.UO_input="UO",e.exports.VWMA_input="VWMA",e.exports.len_input=["pjg"],e.exports["VI +_input"]="VI +",e.exports["VI -_input"]="VI -",e.exports["%R_input"]="%R",e.exports["Jaw Length_input"]=["Panjang Rahang"],e.exports["Teeth Length_input"]=["Panjang Gigi"],e.exports["Lips Length_input"]=["Panjang Bibir"],e.exports.Jaw_input=["Rahang"],e.exports.Teeth_input=["Gigi"],e.exports.Lips_input=["Bibir"],
e.exports["Jaw Offset_input"]=["Offset Jaw"],e.exports["Teeth Offset_input"]=["Offset Teeth"],e.exports["Lips Offset_input"]=["Offset Lips"],e.exports["Down fractals_input"]=["Fraktal Turun"],e.exports["Up fractals_input"]=["Fraktal naik"],e.exports.Periods_input=["Periode"],e.exports.Shapes_input=["Bentuk"],e.exports["show MA_input"]=["tampilkan MA"],e.exports["MA Length_input"]=["Panjang MA"],e.exports["Color based on previous close_input"]=["Warna dengan basis penutupan sebelumnya"],e.exports["Rows Layout_input"]=["Layout Baris"],e.exports["Row Size_input"]=["Besar Baris"],e.exports.Volume_input="Volume",e.exports["Value Area volume_input"]=["Volume Area Nilai"],e.exports["Extend Right_input"]=["Perpanjang Kanan"],e.exports["Extend POC Right_input"]=["Perpanjang POC Kekanan"],e.exports["Extend VAH Right_input"]=["Perpanjangan VAH ke Kanan"],e.exports["Extend VAL Right_input"]=["Perpanjangan VAL ke Kanan"],e.exports["Value Area Volume_input"]=["Volume Area Nilai"],e.exports.Placement_input=["Penempatan"],e.exports.POC_input="POC",e.exports["Developing Poc_input"]=["POC Berjalan"],e.exports["Up Volume_input"]=["Volume Naik"],e.exports["Down Volume_input"]=["Volume Turun"],e.exports["Value Area_input"]=["Area Nilai"],e.exports["Histogram Box_input"]=["Kotak Histogram"],e.exports["Value Area Up_input"]=["Area Nilai Naik"],e.exports["Value Area Down_input"]=["Area Nilai Turun"],e.exports["Number Of Rows_input"]=["Jumlah Baris"],e.exports["Ticks Per Row_input"]=["Tick Per Baris"],e.exports["Up/Down_input"]=["Naik/Turun"],e.exports.Total_input="Total",e.exports.Delta_input="Delta",e.exports.Bar_input="Bar",e.exports.Day_input=["Hari"],e.exports["Deviation (%)_input"]=["Deviasi (%)"],e.exports.Depth_input=["Kedalaman"],e.exports["Extend to last bar_input"]=["Perpanjang hingga bar terakhir"],e.exports.Simple_input=["Sederhana"],e.exports.Weighted_input=["Terbebani"],e.exports["Wilder's Smoothing_input"]="Wilder's Smoothing",e.exports["1st Period_input"]=["Periode ke-1"],e.exports["2nd Period_input"]=["Periode ke-2"],e.exports["3rd Period_input"]=["Periode ke-3"],e.exports["4th Period_input"]=["Periode ke-4"],e.exports["5th Period_input"]=["Periode ke-5"],e.exports["6th Period_input"]=["Periode ke-6"],e.exports["Rate of Change Lookback_input"]=["Kilas balik Kecepatan Perubahan"],e.exports["Instrument 1_input"]=["Instrumen 1"],e.exports["Instrument 2_input"]=["Instrumen 2"],e.exports["Rolling Period_input"]=["Periode Bergulir"],e.exports["Standard Errors_input"]=["Standar Error"],e.exports["Averaging Periods_input"]=["Periode Perata-Rataan"],e.exports["Days Per Year_input"]=["Hari Per Tahun"],e.exports["Market Closed Percentage_input"]=["Persentasi Penutupan Pasar"],e.exports["ATR Mult_input"]="ATR Mult",e.exports.VWAP_input="VWAP",e.exports["Anchor Period_input"]=["Periode Jangkar"],e.exports.Session_input=["Sesi"],e.exports.Week_input=["Minggu"],e.exports.Month_input=["Bulan"],e.exports.Year_input=["Tahun"],e.exports.Decade_input=["Dekade"],e.exports.Century_input=["Abad"],e.exports.Sessions_input=["Sesi"],
e.exports["Each (pre-market, market, post-market)_input"]=["Masing-masing (pra-pasar, pasar, pasca-pasar)"],e.exports["Pre-market only_input"]=["Hanya Pra-pasar"],e.exports["Market only_input"]=["Hanya jam pasar"],e.exports["Post-market only_input"]=["Hanya Pasca-pasar"],e.exports["Main chart symbol_input"]=["Simbol chart utama"],e.exports["Another symbol_input"]=["Simbol lainnya"],e.exports.Line_input=["Garis"],e.exports["Nothing selected_combobox_input"]=["Tidak ada yang dipilih"],e.exports["All items_combobox_input"]=["Seluruh item"],e.exports.Cancel_input=["Batalkan"],e.exports.Open_input=["Buka"]},54138:e=>{e.exports=["Inversikan Skala"]},47807:e=>{e.exports=["Diindeks ke 100"]},34727:e=>{e.exports=["Logaritmik"]},19238:e=>{e.exports=["Tidak ada label yang tertumpuk"]},70361:e=>{e.exports=["Persen"]},72116:e=>{e.exports=["Reguler"]},33021:e=>{e.exports="ETH"},75610:e=>{e.exports=["Jam Trading Elektronik"]},97442:e=>{e.exports=["Jam trading perpanjangan"]},32929:e=>{e.exports=["pasca"]},56137:e=>{e.exports=["pra"]},98801:e=>{e.exports=["Pasca pasar"]},56935:e=>{e.exports=["Pra pasar"]},63798:e=>{e.exports="RTH"},24380:e=>{e.exports=["Jam Trading Reguler"]},27991:e=>{e.exports=["Mei"]},68716:e=>{e.exports=Object.create(null),e.exports.Technicals_study=["Teknikal"],e.exports["Average Day Range_study"]=["Rata-Rata Rentang Harian"],e.exports["Bull Bear Power_study"]=["Kekuatan Bull Bear"],e.exports["Capital expenditures_study"]=["Pengeluaran modal / Capital expenditures"],e.exports["Cash to debt ratio_study"]="Cash to debt ratio",e.exports["Debt to EBITDA ratio_study"]="Debt to EBITDA ratio",e.exports["Directional Movement Index_study"]=["Indeks Arah Pergerakan"],e.exports.DMI_study="DMI",e.exports["Dividend payout ratio %_study"]="Dividend payout ratio %",e.exports["Equity to assets ratio_study"]="Equity to assets ratio",e.exports["Enterprise value to EBIT ratio_study"]="Enterprise value to EBIT ratio",e.exports["Enterprise value to EBITDA ratio_study"]="Enterprise value to EBITDA ratio",e.exports["Enterprise value to revenue ratio_study"]="Enterprise value to revenue ratio",e.exports["Goodwill, net_study"]="Goodwill, net",e.exports["Ichimoku Cloud_study"]=["Awan Ichimoku"],e.exports.Ichimoku_study="Ichimoku",e.exports["Moving Average Convergence Divergence_study"]=["Rata-Rata Pergerakan Konvergen Divergen / Moving Average Convergence Divergence"],e.exports["Operating income_study"]=["Pemasukan operasional"],e.exports["Price to book ratio_study"]="Price to book ratio",e.exports["Price to cash flow ratio_study"]="Price to cash flow ratio",e.exports["Price to earnings ratio_study"]="Price to earnings ratio",e.exports["Price to free cash flow ratio_study"]="Price to free cash flow ratio",e.exports["Price to sales ratio_study"]="Price to sales ratio",e.exports["Float shares outstanding_study"]="Float shares outstanding",e.exports["Total common shares outstanding_study"]=["Total saham umum beredar / Total common shares outstanding"],e.exports["Volume Weighted Average Price_study"]=["Harga Rata-Rata Terbebani Volume"],
e.exports["Volume Weighted Moving Average_study"]=["Rata-Rata Pergerakan Terbebani Volume"],e.exports["Williams Percent Range_study"]=["Rentang Persentase Williams / Williams Percent Range"],e.exports.Doji_study="Doji",e.exports["Spinning Top Black_study"]=["Spinning Top Hitam"],e.exports["Spinning Top White_study"]=["Spinning Top Putih"],e.exports["Accounts payable_study"]="Accounts payable",e.exports["Accounts receivables, gross_study"]=["Piutang, bruto"],e.exports["Accounts receivable - trade, net_study"]="Accounts receivable - trade, net",e.exports.Accruals_study="Accruals",e.exports["Accrued payroll_study"]="Accrued payroll",e.exports["Accumulated depreciation, total_study"]="Accumulated depreciation, total",e.exports["Additional paid-in capital/Capital surplus_study"]="Additional paid-in capital/Capital surplus",e.exports["After tax other income/expense_study"]="After tax other income/expense",e.exports["Altman Z-score_study"]="Altman Z-score",e.exports.Amortization_study=["Amortisasi / Amortization"],e.exports["Amortization of intangibles_study"]=["Amortisasi Barang Tak Berwujud"],e.exports["Amortization of deferred charges_study"]=["Amortisasi Biaya Tertangguh"],e.exports["Asset turnover_study"]="Asset turnover",e.exports["Average basic shares outstanding_study"]="Average basic shares outstanding",e.exports["Bad debt / Doubtful accounts_study"]=["Piutang macet / Piutang ragu-ragu"],e.exports["Basic EPS_study"]="Basic EPS",e.exports["Basic earnings per share (Basic EPS)_study"]=["Perolehan dasar per saham / basic Earnings per share (Basic EPS)"],e.exports["Beneish M-score_study"]="Beneish M-score",e.exports["Book value per share_study"]="Book value per share",e.exports["Buyback yield %_study"]="Buyback yield %",e.exports["Capital and operating lease obligations_study"]="Capital and operating lease obligations",e.exports["Capital expenditures - fixed assets_study"]="Capital expenditures - fixed assets",e.exports["Capital expenditures - other assets_study"]="Capital expenditures - other assets",e.exports["Capitalized lease obligations_study"]="Capitalized lease obligations",e.exports["Cash and short term investments_study"]="Cash and short term investments",e.exports["Cash conversion cycle_study"]="Cash conversion cycle",e.exports["Cash & equivalents_study"]="Cash & equivalents",e.exports["Cash from financing activities_study"]=["Kas dari aktivitas pembiayaan"],e.exports["Cash from investing activities_study"]=["Kas dari aktivitas investasi"],e.exports["Cash from operating activities_study"]=["Kas dari aktivitas operasional"],e.exports["Change in accounts payable_study"]="Change in accounts payable",e.exports["Change in accounts receivable_study"]="Change in accounts receivable",e.exports["Change in accrued expenses_study"]="Change in accrued expenses",e.exports["Change in inventories_study"]="Change in inventories",e.exports["Change in other assets/liabilities_study"]="Change in other assets/liabilities",e.exports["Change in taxes payable_study"]="Change in taxes payable",
e.exports["Changes in working capital_study"]=["Perubahan pada kapital berjalan"],e.exports["COGS to revenue ratio_study"]="COGS to revenue ratio",e.exports["Common dividends paid_study"]="Common dividends paid",e.exports["Common equity, total_study"]="Common equity, total",e.exports["Common stock par/Carrying value_study"]="Common stock par/Carrying value",e.exports["Cost of goods_study"]=["Biaya barang"],e.exports["Cost of goods sold_study"]=["Harga pokok penjualan"],e.exports["Current portion of LT debt and capital leases_study"]="Current portion of LT debt and capital leases",e.exports["Current ratio_study"]="Current ratio",e.exports["Days inventory_study"]="Days inventory",e.exports["Days payable_study"]="Days payable",e.exports["Days sales outstanding_study"]="Days sales outstanding",e.exports["Debt to assets ratio_study"]="Debt to assets ratio",e.exports["Debt to equity ratio_study"]="Debt to equity ratio",e.exports["Debt to revenue ratio_study"]="Debt to revenue ratio",e.exports["Deferred income, current_study"]="Deferred income, current",e.exports["Deferred income, non-current_study"]="Deferred income, non-current",e.exports["Deferred tax assets_study"]="Deferred tax assets",e.exports["Deferred taxes (cash flow)_study"]="Deferred taxes (cash flow)",e.exports["Deferred tax liabilities_study"]="Deferred tax liabilities",e.exports.Depreciation_study=["Depresiasi"],e.exports["Deprecation and amortization_study"]="Deprecation and amortization",e.exports["Depreciation & amortization (cash flow)_study"]="Depreciation & amortization (cash flow)",e.exports["Depreciation/depletion_study"]="Depreciation/depletion",e.exports["Diluted EPS_study"]=["EPS Terdilusi"],e.exports["Diluted earnings per share (Diluted EPS)_study"]=["Laba Dilusian per Saham (Diluted EPS)"],e.exports["Diluted net income available to common stockholders_study"]="Diluted net income available to common stockholders",e.exports["Diluted shares outstanding_study"]="Diluted shares outstanding",e.exports["Dilution adjustment_study"]="Dilution adjustment",e.exports["Discontinued operations_study"]="Discontinued operations",e.exports["Dividends payable_study"]="Dividends payable",e.exports["Dividends per share - common stock primary issue_study"]="Dividends per share - common stock primary issue",e.exports["Dividend yield %_study"]="Dividend yield %",e.exports["Earnings yield_study"]=["Hasil perolehan / Earnings yield"],e.exports.EBIT_study="EBIT",e.exports.EBITDA_study="EBITDA",e.exports["EBITDA margin %_study"]="EBITDA margin %",e.exports["Effective interest rate on debt %_study"]="Effective interest rate on debt %",e.exports["Enterprise value_study"]="Enterprise value",e.exports["EPS basic one year growth_study"]="EPS basic one year growth",e.exports["EPS diluted one year growth_study"]="EPS diluted one year growth",e.exports["EPS estimates_study"]="EPS estimates",e.exports["Equity in earnings_study"]="Equity in earnings",e.exports["Financing activities – other sources_study"]="Financing activities – other sources",
e.exports["Financing activities – other uses_study"]="Financing activities – other uses",e.exports["Free cash flow_study"]=["Arus kas bebas"],e.exports["Free cash flow margin %_study"]="Free cash flow margin %",e.exports["Fulmer H factor_study"]="Fulmer H factor",e.exports["Funds from operations_study"]=["Dana dari operasi"],e.exports["Goodwill to assets ratio_study"]="Goodwill to assets ratio",e.exports["Graham's number_study"]="Graham's number",e.exports["Gross margin %_study"]="Gross margin %",e.exports["Gross profit_study"]=["Profit bruto / Gross profit"],e.exports["Gross profit to assets ratio_study"]="Gross profit to assets ratio",e.exports["Gross property/plant/equipment_study"]="Gross property/plant/equipment",e.exports.Impairments_study=["Penurunan"],e.exports["Income Tax Credits_study"]=["Kredit Pajak Penghasilan"],e.exports["Income tax, current_study"]=["Pajak penghasilan, saat ini"],e.exports["Income tax, current - domestic_study"]=["Pajak penghasilan, saat ini - domestik"],e.exports["Income Tax, current - foreign_study"]=["Pajak penghasilan, saat ini - luar negeri"],e.exports["Income tax, deferred_study"]=["Pajak penghasilan, ditangguhkan"],e.exports["Income tax, deferred - domestic_study"]=["Pajak penghasilan, ditangguhkan - domestik"],e.exports["Income tax, deferred - foreign_study"]=["Pajak penghasilan, ditangguhkan - luar negeri"],e.exports["Income tax payable_study"]="Income tax payable",e.exports["Interest capitalized_study"]="Interest capitalized",e.exports["Interest coverage_study"]="Interest coverage",e.exports["Interest expense, net of interest capitalized_study"]="Interest expense, net of interest capitalized",e.exports["Interest expense on debt_study"]="Interest expense on debt",e.exports["Inventories - finished goods_study"]="Inventories - finished goods",e.exports["Inventories - progress payments & other_study"]="Inventories - progress payments & other",e.exports["Inventories - raw materials_study"]="Inventories - raw materials",e.exports["Inventories - work in progress_study"]="Inventories - work in progress",e.exports["Inventory to revenue ratio_study"]="Inventory to revenue ratio",e.exports["Inventory turnover_study"]="Inventory turnover",e.exports["Investing activities – other sources_study"]="Investing activities – other sources",e.exports["Investing activities – other uses_study"]="Investing activities – other uses",e.exports["Investments in unconsolidated subsidiaries_study"]="Investments in unconsolidated subsidiaries",e.exports["Issuance of long term debt_study"]="Issuance of long term debt",e.exports["Issuance/retirement of debt, net_study"]="Issuance/retirement of debt, net",e.exports["Issuance/retirement of long term debt_study"]="Issuance/retirement of long term debt",e.exports["Issuance/retirement of other debt_study"]="Issuance/retirement of other debt",e.exports["Issuance/retirement of short term debt_study"]="Issuance/retirement of short term debt",e.exports["Issuance/retirement of stock, net_study"]="Issuance/retirement of stock, net",
e.exports["KZ index_study"]="KZ index",e.exports["Legal claim expense_study"]=["Biaya klaim hukum"],e.exports["Long term debt_study"]=["Hutang jangka panjang"],e.exports["Long term debt excl. lease liabilities_study"]="Long term debt excl. lease liabilities",e.exports["Long term debt to total assets ratio_study"]="Long term debt to total assets ratio",e.exports["Long term debt to total equity ratio_study"]=["Rasio utang jangka panjang terhadap total ekuitas"],e.exports["Long term investments_study"]="Long term investments",e.exports["Market capitalization_study"]=["Kapitalisasi pasar"],e.exports["Minority interest_study"]=["Suku bunga minoritas / Minority interest"],e.exports["Miscellaneous non-operating expense_study"]="Miscellaneous non-operating expense",e.exports["Net current asset value per share_study"]="Net current asset value per share",e.exports["Net debt_study"]=["Hutang Bersih"],e.exports["Net income_study"]=["Pendapatan netto"],e.exports["Net income before discontinued operations_study"]="Net income before discontinued operations",e.exports["Net income (cash flow)_study"]="Net income (cash flow)",e.exports["Net income per employee_study"]="Net income per employee",e.exports["Net intangible assets_study"]="Net intangible assets",e.exports["Net margin %_study"]="Net margin %",e.exports["Net property/plant/equipment_study"]="Net property/plant/equipment",e.exports["Non-cash items_study"]="Non-cash items",e.exports["Non-controlling/minority interest_study"]="Non-controlling/minority interest",e.exports["Non-operating income, excl. interest expenses_study"]="Non-operating income, excl. interest expenses",e.exports["Non-operating income, total_study"]="Non-operating income, total",e.exports["Non-operating interest income_study"]="Non-operating interest income",e.exports["Note receivable - long term_study"]="Note receivable - long term",e.exports["Notes payable_study"]="Notes payable",e.exports["Number of employees_study"]="Number of employees",e.exports["Number of shareholders_study"]=["Jumlah pemegang saham"],e.exports["Operating earnings yield %_study"]="Operating earnings yield %",e.exports["Operating expenses (excl. COGS)_study"]="Operating expenses (excl. COGS)",e.exports["Operating lease liabilities_study"]="Operating lease liabilities",e.exports["Operating margin %_study"]="Operating margin %",e.exports["Other COGS_study"]="Other COGS",e.exports["Other common equity_study"]="Other common equity",e.exports["Other current assets, total_study"]="Other current assets, total",e.exports["Other current liabilities_study"]="Other current liabilities",e.exports["Other cost of goods sold_study"]=["Harga pokok penjualan lainnya"],e.exports["Other exceptional charges_study"]=["Biaya eksepsional lainnya"],e.exports["Other financing cash flow items, total_study"]=["Barang arus kas pembiayaan lainnya, total"],e.exports["Other intangibles, net_study"]="Other intangibles, net",e.exports["Other investing cash flow items, total_study"]=["Barang arus kas investai lainnya, total"],
e.exports["Other investments_study"]="Other investments",e.exports["Other liabilities, total_study"]="Other liabilities, total",e.exports["Other long term assets, total_study"]="Other long term assets, total",e.exports["Other non-current liabilities, total_study"]=["Kewajiban tidak lancar lainnya, total"],e.exports["Other operating expenses, total_study"]="Other operating expenses, total",e.exports["Other receivables_study"]="Other receivables",e.exports["Other short term debt_study"]="Other short term debt",e.exports["Paid in capital_study"]="Paid in capital",e.exports["PEG ratio_study"]="PEG ratio",e.exports["Piotroski F-score_study"]="Piotroski F-score",e.exports["Preferred dividends_study"]="Preferred dividends",e.exports["Preferred dividends paid_study"]="Preferred dividends paid",e.exports["Preferred stock, carrying value_study"]="Preferred stock, carrying value",e.exports["Prepaid expenses_study"]="Prepaid expenses",e.exports["Pretax equity in earnings_study"]="Pretax equity in earnings",e.exports["Pretax income_study"]="Pretax income",e.exports["Price earnings ratio forward_study"]="Price earnings ratio forward",e.exports["Price sales ratio forward_study"]="Price sales ratio forward",e.exports["Price to tangible book ratio_study"]="Price to tangible book ratio",e.exports["Provision for risks & charge_study"]="Provision for risks & charge",e.exports["Purchase/acquisition of business_study"]="Purchase/acquisition of business",e.exports["Purchase of investments_study"]="Purchase of investments",e.exports["Purchase/sale of business, net_study"]=["Pembelian/penjualan dari bisnis, netto"],e.exports["Purchase/sale of investments, net_study"]=["Pembelian/Penjualan dari investasi, netto"],e.exports["Quality ratio_study"]="Quality ratio",e.exports["Quick ratio_study"]="Quick ratio",e.exports["Reduction of long term debt_study"]="Reduction of long term debt",e.exports["Repurchase of common & preferred stock_study"]="Repurchase of common & preferred stock",e.exports["Research & development_study"]=["Riset & pengembangan"],e.exports["Research & development to revenue ratio_study"]="Research & development to revenue ratio",e.exports["Restructuring charge_study"]=["Biaya restrukturisasi"],e.exports["Retained earnings_study"]="Retained earnings",e.exports["Return on assets %_study"]="Return on assets %",e.exports["Return on equity %_study"]="Return on equity %",e.exports["Return on equity adjusted to book value %_study"]="Return on equity adjusted to book value %",e.exports["Return on invested capital %_study"]="Return on invested capital %",e.exports["Return on tangible assets %_study"]="Return on tangible assets %",e.exports["Return on tangible equity %_study"]="Return on tangible equity %",e.exports["Revenue estimates_study"]="Revenue estimates",e.exports["Revenue one year growth_study"]="Revenue one year growth",e.exports["Revenue per employee_study"]="Revenue per employee",e.exports["Sale/maturity of investments_study"]="Sale/maturity of investments",
e.exports["Sale of common & preferred stock_study"]="Sale of common & preferred stock",e.exports["Sale of fixed assets & businesses_study"]="Sale of fixed assets & businesses",e.exports["Selling/general/admin expenses, other_study"]=["Pengeluaran penjualan/umum/admin, lainnya"],e.exports["Selling/general/admin expenses, total_study"]=["Pengeluaran penjualan/umum/admin, total"],e.exports["Shareholders' equity_study"]="Shareholders' equity",e.exports["Shares buyback ratio %_study"]="Shares buyback ratio %",e.exports["Short term debt_study"]="Short term debt",e.exports["Short term debt excl. current portion of LT debt_study"]="Short term debt excl. current portion of LT debt",e.exports["Short term investments_study"]="Short term investments",e.exports["Sloan ratio %_study"]="Sloan ratio %",e.exports["Springate score_study"]="Springate score",e.exports["Sustainable growth rate_study"]="Sustainable growth rate",e.exports["Tangible book value per share_study"]="Tangible book value per share",e.exports["Tangible common equity ratio_study"]="Tangible common equity ratio",e.exports.Taxes_study="Taxes",e.exports["Tobin's Q (approximate)_study"]="Tobin's Q (approximate)",e.exports["Total assets_study"]=["Total aset"],e.exports["Total cash dividends paid_study"]=["Total dividen tunai dibayarkan / Total cash dividends paid"],e.exports["Total current assets_study"]=["Total aset saat ini"],e.exports["Total current liabilities_study"]=["Total liabilitas saat ini"],e.exports["Total debt_study"]=["Total hutang"],e.exports["Total equity_study"]=["Total ekuitas"],e.exports["Total inventory_study"]="Total inventory",e.exports["Total liabilities_study"]=["Total liabilitas"],e.exports["Total liabilities & shareholders' equities_study"]="Total liabilities & shareholders' equities",e.exports["Total non-current assets_study"]=["Total aset tidak lancar"],e.exports["Total non-current liabilities_study"]=["Total liabilitas tidak lancar"],e.exports["Total operating expenses_study"]=["Total pengeluaran operasi"],e.exports["Total receivables, net_study"]="Total receivables, net",e.exports["Total revenue_study"]=["Total pendapatan"],e.exports["Treasury stock - common_study"]="Treasury stock - common",e.exports["Unrealized gain/loss_study"]=["Keuntungan/kerugian yang belum terealisasi"],e.exports["Unusual income/expense_study"]=["Unusual income/expense / Pemasukan/pengeluaran tidak biasa"],e.exports["Zmijewski score_study"]=["Zmijewski score / Skor Zmijewski"],e.exports["Valuation ratios_study"]=["Rasio valuasi"],e.exports["Profitability ratios_study"]=["Rasio profitabilitas"],e.exports["Liquidity ratios_study"]=["Rasio likuiditas"],e.exports["Solvency ratios_study"]=["Rasio solvabilitas"],e.exports["Key stats_study"]=["Statistik kunci"],e.exports["Accumulation/Distribution_study"]=["Akumulasi/Distribusi"],e.exports["Accumulative Swing Index_study"]=["Indeks Swing Akumulatif / Accumulative Swing Index"],e.exports["Advance/Decline_study"]=["Kemajuan/Kemunduran / Advance/Decline"],
e.exports["Arnaud Legoux Moving Average_study"]=["Rata-Rata Pergerakan Arnaud Legoux / Arnaud Legoux Moving Average"],e.exports.Aroon_study="Aroon",e.exports.ASI_study="ASI",e.exports["Average Directional Index_study"]=["Indeks Arah Rata-Rata / Average Directional Index"],e.exports["Average True Range_study"]=["Rata-Rata Rentang Sebenarnya / Average True Range"],e.exports["Awesome Oscillator_study"]=["Osilator Awesome / Awesome Oscillator"],e.exports["Balance of Power_study"]=["Keseimbangan dari Kekuatan / Balance of Power"],e.exports["Bollinger Bands %B_study"]=["%B Ikat Bollinger"],e.exports["Bollinger Bands Width_study"]=["Lebar Ikat Bollinger"],e.exports["Bollinger Bands_study"]=["Ikat Bollinger / Bollinger Bands"],e.exports["Chaikin Money Flow_study"]=["Arus Uang Chaikin / Chaikin Money Flow"],e.exports["Chaikin Oscillator_study"]=["Osilator Chaikin"],e.exports["Chande Kroll Stop_study"]="Chande Kroll Stop",e.exports["Chande Momentum Oscillator_study"]=["Osilator Momentum Chande"],e.exports["Chop Zone_study"]=["Zona Chop"],e.exports["Choppiness Index_study"]=["Indeks Choppiness / Choppiness Index"],e.exports["Commodity Channel Index_study"]=["Indeks Kanal Komoditas / Commodity Channel Index"],e.exports["Connors RSI_study"]=["RSI Connors"],e.exports["Coppock Curve_study"]=["Kurva Coppock"],e.exports["Correlation Coefficient_study"]=["Koefisien Korelasi"],e.exports.CRSI_study="CRSI",e.exports["Detrended Price Oscillator_study"]=["Osilator Harga Detrended / Detrended Price Oscillator"],e.exports["Directional Movement_study"]=["Pergerakan Terarah/ Directional Movement"],e.exports["Donchian Channels_study"]=["Kanal Donchian"],e.exports["Double EMA_study"]="Double EMA",e.exports["Ease Of Movement_study"]="Ease Of Movement",e.exports["Elder Force Index_study"]=["Indeks Kekuatan Elder / Elder's Force Index"],e.exports["EMA Cross_study"]=["Persilangan EMA"],e.exports.Envelopes_study=["Envelope"],e.exports["Fisher Transform_study"]=["Transformasi Fisher / Fisher Transform"],e.exports["Fixed Range_study"]=["Rentang Tetap"],e.exports["Fixed Range Volume Profile_study"]=["Profil Volume Rentang Tetap"],e.exports["Guppy Multiple Moving Average_study"]=["Rata-Rata Pergerakan Berganda Guppy / Guppy Multiple Moving Average"],e.exports["Historical Volatility_study"]=["Volatilitas Historis"],e.exports["Hull Moving Average_study"]=["Rata-Rata Pergerakan Hull / Hull Moving Average"],e.exports["Keltner Channels_study"]=["Kanal Keltner"],e.exports["Klinger Oscillator_study"]=["Osilator Klinger"],e.exports["Know Sure Thing_study"]="Know Sure Thing",e.exports["Least Squares Moving Average_study"]=["Rata-Rata Pergerakan Kuadrat Terkecil / Least Squares Moving Average"],e.exports["Linear Regression Curve_study"]=["Kurva Regresi Linear"],e.exports["MA Cross_study"]=["Persilangan MA / MA Cross"],e.exports["MA with EMA Cross_study"]=["Persilangan MA dengan EMA"],e.exports["MA/EMA Cross_study"]=["Persilangan MA/EMA"],e.exports.MACD_study="MACD",e.exports["Mass Index_study"]=["Indeks Massa / Mass Index"],
e.exports["McGinley Dynamic_study"]=["Dinamik McGinley / McGinley Dynamic"],e.exports.Median_study="Median",e.exports.Momentum_study="Momentum",e.exports["Money Flow_study"]=["Arus Uang / Money Flow"],e.exports["Moving Average Channel_study"]=["Kanal Rata-Rata Pergerakan / Moving Average Channel"],e.exports["Moving Average Exponential_study"]=["Rata-Rata Pergerakan Eksponensial / Moving Average Exponential"],e.exports["Moving Average Weighted_study"]=["Rata-Rata Pergerakan Terbebani / Moving Average Weighted"],e.exports["Moving Average Simple_study"]=["Simple Moving Average"],e.exports["Net Volume_study"]=["Volume Bersih"],e.exports["On Balance Volume_study"]=["Volume Keseimbangan / On Balance Volume"],e.exports["Parabolic SAR_study"]=["SAR Parabolis"],e.exports["Pivot Points Standard_study"]=["Poin Pivot Standar"],e.exports["Periodic Volume Profile_study"]=["Profil Volume Periodik"],e.exports["Price Channel_study"]=["Kanal Harga"],e.exports["Price Oscillator_study"]=["Osilator Harga"],e.exports["Price Volume Trend_study"]=["Tren Volume Harga / Price Volume Trend"],e.exports["Rate Of Change_study"]=["Kecepatan Perubahan"],e.exports["Relative Strength Index_study"]=["Indeks Kekuatan Relatif / Relative Strength Index"],e.exports["Relative Vigor Index_study"]=["Indeks Vigor Relatif / Relative Vigor Index"],e.exports["Relative Volatility Index_study"]=["Indeks Volatilitas Relatif / Relative Volatility Index"],e.exports["Session Volume_study"]=["Volume Sesi"],e.exports["Session Volume HD_study"]=["Volume Sesi HD"],e.exports["Session Volume Profile_study"]=["Profil Volume Sesi"],e.exports["Session Volume Profile HD_study"]=["Profil Volume Sesi HD"],e.exports["SMI Ergodic Indicator/Oscillator_study"]=["Indikator/Osilator SMI Ergodic"],e.exports["Smoothed Moving Average_study"]=["Rata-Rata Pergerakan Terhaluskan / Smoothed Moving Average"],e.exports.Stoch_study="Stoch",e.exports["Stochastic RSI_study"]="Stochastic RSI",e.exports.Stochastic_study="Stochastic",e.exports["Triple EMA_study"]="Triple EMA",e.exports.TRIX_study="TRIX",e.exports["True Strength Indicator_study"]=["Indikator Kekuatan Sebenarnya / True Strength Indicator"],e.exports["Ultimate Oscillator_study"]=["Osilator Ultimate"],e.exports["Visible Range_study"]=["Rentang Terlihat"],e.exports["Visible Range Volume Profile_study"]=["Profil Volume Rentang Terlihat"],e.exports["Volume Oscillator_study"]=["Osilator Volume"],e.exports.Volume_study="Volume",e.exports.Vol_study="Vol",e.exports["Vortex Indicator_study"]=["Indikator Vortex"],e.exports.VWAP_study="VWAP",e.exports.VWMA_study="VWMA",e.exports["Williams %R_study"]=["%R Williams / Williams %R"],e.exports["Williams Alligator_study"]=["Aligator Williams / Williams Alligator"],e.exports["Williams Fractal_study"]=["Fraktal Williams / Williams Fractal"],e.exports["Zig Zag_study"]="Zig Zag",e.exports["24-hour Volume_study"]=["Volume 24 Jam"],e.exports["Ease of Movement_study"]=["Ease Of Movement"],e.exports["Elders Force Index_study"]=["Indeks Kekuatan Elder"],e.exports.Envelope_study="Envelope",
e.exports.Gaps_study=["Gap"],e.exports["Linear Regression Channel_study"]=["Kanal Regresi Linier"],e.exports["Moving Average Ribbon_study"]=["Pita Pergerakan Rata-rata"],e.exports["Multi-Time Period Charts_study"]=["Chart Periode Waktu Berganda"],e.exports["Open Interest_study"]=["Minat Terbuka/Open Interest"],e.exports["Rob Booker - Intraday Pivot Points_study"]=["Rob Booker - Poin Pivot Intrahari"],e.exports["Rob Booker - Knoxville Divergence_study"]=["Rob Booker - Divergen Knoxville"],e.exports["Rob Booker - Missed Pivot Points_study"]=["Rob Booker - Poin Pivot Terlewati"],e.exports["Rob Booker - Reversal_study"]="Rob Booker - Reversal",e.exports["Rob Booker - Ziv Ghost Pivots_study"]=["Rob Booker - Pivot Bayangan / Ziv Ghost Pivots"],e.exports.Supertrend_study="Supertrend",e.exports["Technical Ratings_study"]=["Peringkat Teknis"],e.exports["True Strength Index_study"]=["Indeks Kekuatan Sebenarnya / True Strength Index"],e.exports["Up/Down Volume_study"]=["Volume Naik/Turun"],e.exports["Visible Average Price_study"]=["Rata-rata Harga Terlihat"],e.exports["Williams Fractals_study"]=["Fraktal Williams"],e.exports["Keltner Channels Strategy_study"]=["Strategi Kanal Keltner"],e.exports["Rob Booker - ADX Breakout_study"]=["Rob Booker - Breakout ADX"],e.exports["Supertrend Strategy_study"]=["Strategi Supertrend"],e.exports["Technical Ratings Strategy_study"]=["Strategi Peringkat Teknis"],e.exports["Auto Anchored Volume Profile_study"]=["Profil Volume Terjangkar Otomatis"],e.exports["Auto Fib Extension_study"]=["Fibonacci Ekstension Otomatis"],e.exports["Auto Fib Retracement_study"]=["Retracemen Fib Auto"],e.exports["Auto Pitchfork_study"]=["Pitchfork Otomatis"],e.exports["Bearish Flag Chart Pattern_study"]=["Motif Chart Bendera / Flag Bearish"],e.exports["Bullish Flag Chart Pattern_study"]=["Motif Chart Bendera Bullish"],e.exports["Bearish Pennant Chart Pattern_study"]=["Motif Chart Pennant/Panji Bearish"],e.exports["Bullish Pennant Chart Pattern_study"]=["Motif Chart Bendera / Flag Bullish"],e.exports["Double Bottom Chart Pattern_study"]=["Motif Chart Double Bottom"],e.exports["Double Top Chart Pattern_study"]=["Motif Chart Double Top"],e.exports["Elliott Wave Chart Pattern_study"]=["Motif Chart Gelombang Elliott"],e.exports["Falling Wedge Chart Pattern_study"]=["Motif Chart Baji / Wedge Menurun"],e.exports["Head And Shoulders Chart Pattern_study"]=["Motif Chart Head Dan Shoulder"],e.exports["Inverse Head And Shoulders Chart Pattern_study"]=["Motif Chart Head Dan Shoulder Terbalik"],e.exports["Rectangle Chart Pattern_study"]=["Motif Chart Persegi"],e.exports["Rising Wedge Chart Pattern_study"]=["Motif Chart Baji / Wedge Menaik"],e.exports["Triangle Chart Pattern_study"]=["Motif Chart Segitiga"],e.exports["Triple Bottom Chart Pattern_study"]=["Motif Chart Triple Bottom"],e.exports["Triple Top Chart Pattern_study"]=["Motif Chart Triple Top"],e.exports["VWAP Auto Anchored_study"]=["VWAP Terjangkar Otomatis"],e.exports["*All Candlestick Patterns*_study"]=["*Seluruh Motif Candlestick*"],
e.exports["Abandoned Baby - Bearish_study"]="Abandoned Baby - Bearish",e.exports["Abandoned Baby - Bullish_study"]="Abandoned Baby - Bullish",e.exports["Dark Cloud Cover - Bearish_study"]="Dark Cloud Cover - Bearish",e.exports["Doji Star - Bearish_study"]="Doji Star - Bearish",e.exports["Doji Star - Bullish_study"]="Doji Star - Bullish",e.exports["Downside Tasuki Gap - Bearish_study"]="Downside Tasuki Gap - Bearish",e.exports["Dragonfly Doji - Bullish_study"]="Dragonfly Doji - Bullish",e.exports["Engulfing - Bearish_study"]="Engulfing - Bearish",e.exports["Engulfing - Bullish_study"]="Engulfing - Bullish",e.exports["Evening Doji Star - Bearish_study"]="Evening Doji Star - Bearish",e.exports["Evening Star - Bearish_study"]="Evening Star - Bearish",e.exports["Falling Three Methods - Bearish_study"]="Falling Three Methods - Bearish",e.exports["Falling Window - Bearish_study"]="Falling Window - Bearish",e.exports["Gravestone Doji - Bearish_study"]="Gravestone Doji - Bearish",e.exports["Hammer - Bullish_study"]="Hammer - Bullish",e.exports["Hanging Man - Bearish_study"]="Hanging Man - Bearish",e.exports["Harami - Bearish_study"]="Harami - Bearish",e.exports["Harami - Bullish_study"]="Harami - Bullish",e.exports["Inverted Hammer - Bullish_study"]="Inverted Hammer - Bullish",e.exports["Kicking - Bearish_study"]="Kicking - Bearish",e.exports["Kicking - Bullish_study"]="Kicking - Bullish",e.exports["Long Lower Shadow - Bullish_study"]="Long Lower Shadow - Bullish",e.exports["Long Upper Shadow - Bearish_study"]="Long Upper Shadow - Bearish",e.exports["Marubozu Black - Bearish_study"]="Marubozu Black - Bearish",e.exports["Marubozu White - Bullish_study"]="Marubozu White - Bullish",e.exports["Morning Doji Star - Bullish_study"]="Morning Doji Star - Bullish",e.exports["Morning Star - Bullish_study"]="Morning Star - Bullish",e.exports["On Neck - Bearish_study"]="On Neck - Bearish",e.exports["Piercing - Bullish_study"]="Piercing - Bullish",e.exports["Rising Three Methods - Bullish_study"]="Rising Three Methods - Bullish",e.exports["Rising Window - Bullish_study"]="Rising Window - Bullish",e.exports["Shooting Star - Bearish_study"]="Shooting Star - Bearish",e.exports["Three Black Crows - Bearish_study"]="Three Black Crows - Bearish",e.exports["Three White Soldiers - Bullish_study"]="Three White Soldiers - Bullish",e.exports["Tri-Star - Bearish_study"]="Tri-Star - Bearish",e.exports["Tri-Star - Bullish_study"]="Tri-Star - Bullish",e.exports["Tweezer Top - Bearish_study"]="Tweezer Top - Bearish",e.exports["Upside Tasuki Gap - Bullish_study"]="Upside Tasuki Gap - Bullish",e.exports.SuperTrend_study="SuperTrend",e.exports["Average Price_study"]=["Harga Rata-Rata"],e.exports["Typical Price_study"]=["Harga Tipikal"],e.exports["Median Price_study"]=["Harga Median"],e.exports["Money Flow Index_study"]=["Indeks Arus Uang"],e.exports["Moving Average Double_study"]=["Rata-Rata Pergerakan Ganda"],e.exports["Moving Average Triple_study"]=["Rata-Rata Pergerakan Tripel"],e.exports["Moving Average Adaptive_study"]=["Rata-Rata Pergerakan Adaptif"],
e.exports["Moving Average Hamming_study"]=["Rata-Rata Pergerakan Hamming"],e.exports["Moving Average Modified_study"]=["Rata-Rata Pergerakan Termodifikasi"],e.exports["Moving Average Multiple_study"]=["Rata-Rata Pergerakan Berganda"],e.exports["Linear Regression Slope_study"]=["Kemiringan Regresi Linear"],e.exports["Standard Error_study"]=["Standar Error"],e.exports["Standard Error Bands_study"]=["Ikat Standar Error"],e.exports["Correlation - Log_study"]=["Korelasi - Log"],e.exports["Standard Deviation_study"]=["Standar Deviasi"],e.exports["Chaikin Volatility_study"]=["Volatilitas Chaikin"],e.exports["Volatility Close-to-Close_study"]=["Volatilitas Penutupan-ke-Penutupan"],e.exports["Volatility Zero Trend Close-to-Close_study"]=["Volatilitas Zero Trend Penutupan-ke-Penutupan"],e.exports["Volatility O-H-L-C_study"]=["Volatilitas O-H-L-C"],e.exports["Volatility Index_study"]=["Indeks Volatilitas"],e.exports["Trend Strength Index_study"]=["Indeks Kekuatan Tren"],e.exports["Majority Rule_study"]=["Aturan Mayoritas"],e.exports["Advance Decline Line_study"]=["Garis Kemajuan Kemunduran / Advance Decline Line"],e.exports["Advance Decline Ratio_study"]=["Rasio Kemajuan Kemunduran / Advance Decline Ratio"],e.exports["Advance/Decline Ratio (Bars)_study"]=["Rasio Kemajuan/Kemunduran / Advance/Decline Ratio (Bars)"],e.exports["BarUpDn Strategy_study"]=["Strategi BarUpDn"],e.exports["Bollinger Bands Strategy directed_study"]=["Strategi Ikat Bollinger terarahkan"],e.exports["Bollinger Bands Strategy_study"]=["Strategi Ikat Bollinger"],e.exports.ChannelBreakOutStrategy_study=["StrategiBreakOutKanal"],e.exports.Compare_study=["Bandingkan"],e.exports["Conditional Expressions_study"]=["Ekspresi Bersyarat"],e.exports.ConnorsRSI_study="ConnorsRSI",e.exports["Consecutive Up/Down Strategy_study"]=["Strategi Naik/Turun Berurutan"],e.exports["Cumulative Volume Index_study"]=["Indeks Volume Kumulatif"],e.exports["Divergence Indicator_study"]=["Indikator Divergen"],e.exports["Greedy Strategy_study"]=["Strategi Greedy"],e.exports["InSide Bar Strategy_study"]=["Strategi InSide Bar"],e.exports["Keltner Channel Strategy_study"]=["Strategi Kanal Keltner"],e.exports["Linear Regression_study"]=["Regresi Linier"],e.exports["MACD Strategy_study"]=["Strategi MACD"],e.exports["Momentum Strategy_study"]=["Strategi Momentum"],e.exports["Moon Phases_study"]=["Fase Bulan"],e.exports["Moving Average Convergence/Divergence_study"]=["Rata-Rata Pergerakan Konvergen/Divergen / Moving Average Convergence/Divergence"],e.exports["MovingAvg Cross_study"]=["Persilangan Rata-RataPerg"],e.exports["MovingAvg2Line Cross_study"]=["Persilangan 2GarisRata-RataPerg"],e.exports["OutSide Bar Strategy_study"]=["Strategi OutSide Bar"],e.exports.Overlay_study="Overlay",e.exports["Parabolic SAR Strategy_study"]=["Strategi SAR Parabolis"],e.exports["Pivot Extension Strategy_study"]=["Strategi Ekstensi Pivot"],e.exports["Pivot Points High Low_study"]=["Poin Pivot High Low"],e.exports["Pivot Reversal Strategy_study"]=["Strategi Pembalikan Pivot"],
e.exports["Price Channel Strategy_study"]=["Strategi Kanal Harga"],e.exports["RSI Strategy_study"]=["Strategi RSI"],e.exports["SMI Ergodic Indicator_study"]=["Indikator SMI Ergodic"],e.exports["SMI Ergodic Oscillator_study"]=["Osilator SMI Ergodic"],e.exports["Stochastic Slow Strategy_study"]=["Strategi Stochastic Lambat"],e.exports["Volatility Stop_study"]=["Stop Volatilitas"],e.exports["Volty Expan Close Strategy_study"]=["Strategi Volty Expan Close"],e.exports["Woodies CCI_study"]=["CCI Woodies"]},59791:e=>{e.exports="Anchored Volume Profile"},40434:e=>{e.exports=["Profil Volume Rentang Tetap"]},32819:e=>{e.exports="Vol"},66051:e=>{e.exports="Minor"},86054:e=>{e.exports=["Menit"]},20936:e=>{e.exports=["Teks"]},98478:e=>{e.exports=["Tidak dapat menyalin"]},34004:e=>{e.exports=["Tidak dapat memotong"]},96260:e=>{e.exports=["Tidak dapat menempel"]},94370:e=>{e.exports=["Hitung Mundur Ke Penutupan Bar"]},15168:e=>{e.exports="Colombo"},36018:e=>{e.exports=["Kolom-Kolom"]},19372:e=>{e.exports=["Komentar"]},20229:e=>{e.exports=["Bandingkan atau Tambahkan Simbol"]},46689:e=>{e.exports=["Konfirmasi Input"]},43432:e=>{e.exports="Copenhagen"},35216:e=>{e.exports=["Salin"]},87898:e=>{e.exports=["Salin Layout Chart"]},28851:e=>{e.exports=["Salin harga"]},94099:e=>{e.exports=["Kairo"]},64149:e=>{e.exports=["Label"]},63528:e=>{e.exports=["Candle"]},46837:e=>{e.exports="Caracas"},53705:e=>{e.exports="Casablanca"},49329:e=>{e.exports=["Perubahan"]},28089:e=>{e.exports=["Ubah Simbol"]},13737:e=>{e.exports="Change alerts color"},99374:e=>{e.exports=["Ubah interval"]},14412:e=>{e.exports=["Properti Chart"]},26619:e=>{e.exports=["Chart oleh TradingView"]},12011:e=>{e.exports=["Gambar chart yang disalin ke clipboard {emoji}"]},79393:e=>{e.exports=["Kode embed gambar chart disalin ke clipboard {emoji}"]},59884:e=>{e.exports=["Kepulauan Chatham"]},28244:e=>{e.exports="Chicago"},49648:e=>{e.exports="Chongqing"},90068:e=>{e.exports=["Lingkaran"]},32234:e=>{e.exports=["Klik untuk menentukan sebuah poin"]},52977:e=>{e.exports=["Gandakan"]},31691:e=>{e.exports=["Penutupan"]},50493:e=>{e.exports=["Buat Order"]},52302:e=>{e.exports=["Buat order baru"]},29908:e=>{e.exports="Cross"},60997:e=>{e.exports=["Garis Perpotongan"]},81520:e=>{e.exports=["Mata Uang"]},98486:e=>{e.exports=["Interval saat ini dan di atasnya"]},73106:e=>{e.exports=["Interval saat ini dan di bawahnya"]},85964:e=>{e.exports=["Hanya interval saat ini"]},17206:e=>{e.exports=["Kurva"]},95176:e=>{e.exports=["Siklus"]},87761:e=>{e.exports=["Garis Siklus"]},27891:e=>{e.exports=["Motif Cypher"]},56996:e=>{e.exports=["Layout dengan nama tersebut sudah ada"]},30192:e=>{e.exports=["Layout dengan nama tersebut sudah ada. Apakah anda ingin menimpanya?"]},32852:e=>{e.exports=["Motif ABCD"]},88010:e=>{e.exports="Amsterdam"},37422:e=>{e.exports=["Analisis Setup Trade"]},99873:e=>{e.exports=["Penjangkaran"]},66828:e=>{e.exports=["Catatan Terjangkar"]},94782:e=>{e.exports=["Teks Terjangkar"]},61704:e=>{e.exports=["VWAP Terjangkar"]},63597:e=>{e.exports=["Tambah Garis Horizontal"]},45743:e=>{
e.exports=["Tambah Simbol"]},8700:e=>{e.exports=["Tambah peringatan"]},64615:e=>{e.exports=["Tambah Peringatan untuk {title}"]},7005:e=>{e.exports=["Tambahkan peringatan untuk {title} pada {price}"]},3612:e=>{e.exports=["Tambah metrik Finansial untuk {instrumentName}"]},92206:e=>{e.exports=["Tambah Indikator/Strategi pada {studyTitle}"]},34810:e=>{e.exports=["Tambahkan Catatan Teks untuk {symbol}"]},75669:e=>{e.exports=["Tambah Metrik Finansial ini ke Seluruh Layout"]},64288:e=>{e.exports=["Tambah Indikator ini ke Seluruh Layout"]},77920:e=>{e.exports=["Tambah Strategi ini ke Seluruh Layout"]},34059:e=>{e.exports=["Tambah Simbol ini ke Seluruh Layout"]},17365:e=>{e.exports="Adelaide"},9408:e=>{e.exports=["Selalu Tidak Terlihat"]},71997:e=>{e.exports=["Selalu Terlihat"]},97305:e=>{e.exports=["Seluruh Indikator dan Alat Gambar"]},59192:e=>{e.exports=["Seluruh interval"]},14452:e=>{e.exports="Almaty"},5716:e=>{e.exports=["Terapkan Gelombang Elliot"]},19263:e=>{e.exports=["Terapkan Gelombang Elliot Mayor"]},15818:e=>{e.exports=["Terapkan Gelombang Elliot Minor"]},50352:e=>{e.exports=["Terapkan Gelombang Elliot Menengah"]},66631:e=>{e.exports=["Terapkan Titik Keputusan Manual"]},15682:e=>{e.exports=["Terapkan Risiko/Perolehan Manual"]},15644:e=>{e.exports=["Terapkan WPT Down Wave"]},5897:e=>{e.exports=["Terapkan WPT Up Wave"]},13345:e=>{e.exports=["Terapkan Bawaan"]},95910:e=>{e.exports=["Terapkan Indikator berikut ini ke Seluruh Layout"]},42762:e=>{e.exports="Apr"},45104:e=>{e.exports=["Busur"]},42097:e=>{e.exports="Area"},96237:e=>{e.exports=["Panah"]},48732:e=>{e.exports=["Panah Turun"]},82473:e=>{e.exports=["Penanda panah"]},8738:e=>{e.exports=["Tanda Panah Turun"]},35062:e=>{e.exports=["Tanda Panah Kiri"]},92163:e=>{e.exports=["Tanda Panah Kanan"]},33196:e=>{e.exports=["Tanda Panah Naik"]},10650:e=>{e.exports=["Panah Naik"]},59340:e=>{e.exports=["Ashkhabad"]},13468:e=>{e.exports=["Pada penutupan"]},21983:e=>{e.exports=["Athena"]},86951:e=>{e.exports="Auto"},50834:e=>{e.exports=["Auto (Mengepaskan Data Pada Layar)"]},38465:e=>{e.exports=["Agst"]},8975:e=>{e.exports=["Label rata-rata harga penutupan"]},87899:e=>{e.exports=["Garis harga rata-rata penutupan"]},22554:e=>{e.exports=["Rata-rata"]},54173:e=>{e.exports="Bogota"},53260:e=>{e.exports="Bahrain"},40664:e=>{e.exports=["Balon"]},32376:e=>{e.exports="Bangkok"},19149:e=>{e.exports=["Putar Ulang Bar tidak tersedia untuk tipe chart ini. Apakah anda ingin keluar dari Putar Ulang Bar?"]},38660:e=>{e.exports=["Putar Ulang Bar tidak tersedia untuk interval waktu ini. Apakah anda ingin keluar dari Putar Ulang Bar?"]},16812:e=>{e.exports=["Bar"]},98838:e=>{e.exports=["Motif Bar"]},17712:e=>{e.exports=["Garis dasar"]},54861:e=>{e.exports="Belgrade"},26825:e=>{e.exports="Berlin"},30251:e=>{e.exports=["Kuas"]},90204:e=>{e.exports="Brussels"},5262:e=>{e.exports="Bratislava"},59901:e=>{e.exports=["Bawa Maju"]},26354:e=>{e.exports=["Bawa ke Depan"]},11741:e=>{e.exports="Brisbane"},37728:e=>{e.exports="Bucharest"},87143:e=>{e.exports="Budapest"},82446:e=>{e.exports="Buenos Aires"},
82128:e=>{e.exports=["Oleh TradingView"]},75190:e=>{e.exports=["Menuju ke tanggal"]},38342:e=>{e.exports=["Menuju ke {lineToolName}"]},75139:e=>{e.exports=["Mengerti"]},81180:e=>{e.exports=["Kotak Gann"]},68102:e=>{e.exports=["Kipas Gann"]},66321:e=>{e.exports=["Persegi Gann"]},87107:e=>{e.exports=["Kotak Gann Paten"]},7914:e=>{e.exports="Ghost Feed"},18367:e=>{e.exports=["Supercycle Besar"]},97065:e=>{e.exports=["Apakah benar anda ingin menghapus Template Studi '{name}' ?"]},59368:e=>{e.exports=["Kurva Ganda"]},35273:e=>{e.exports=["Dobel-klik di tepi mana pun untuk mengatur ulang kisi layout"]},5828:e=>{e.exports=["Klik dua kali untuk menyelesaikan Jalur"]},63898:e=>{e.exports=["Klik dua kali untuk menyelesaikan Polyline"]},42660:e=>{e.exports=["Gelombang Turun 1 atau A"]},44788:e=>{e.exports=["Gelombang Turun 2 atau B"]},71263:e=>{e.exports=["Gelombang Turun 3"]},70573:e=>{e.exports=["Gelombang Turun 4"]},59560:e=>{e.exports=["Gelombang Turun 5"]},70437:e=>{e.exports=["Gelombang Turun C"]},53831:e=>{e.exports=["Buka jendela Data"]},93345:e=>{e.exports=["Data Disediakan oleh"]},76912:e=>{e.exports=["Tanggal"]},60222:e=>{e.exports=["Rentang Tanggal"]},79859:e=>{e.exports=["Rentang Tanggal dan Harga"]},92203:e=>{e.exports=["Des"]},69479:e=>{e.exports=["Derajat"]},57701:e=>{e.exports="Denver"},24477:e=>{e.exports="Dhaka"},73720:e=>{e.exports=["Berlian"]},3556:e=>{e.exports=["Kanal Disjoint"]},62764:e=>{e.exports=["Pemindahan"]},22903:e=>{e.exports=["Toolbar Alat Gambar"]},8338:e=>{e.exports=["Menggambar Garis Horizontal menyala"]},22429:e=>{e.exports="Dubai"},9497:e=>{e.exports="Dublin"},85223:e=>{e.exports="Emoji"},24435:e=>{e.exports=["Masukkan nama layout chart yang baru"]},93512:e=>{e.exports=["Edit Peringatan {title}"]},91215:e=>{e.exports=["Gelombang Koreksi Elliott (ABC)"]},80983:e=>{e.exports=["Gelombang Kombinasi Dobel Elliott (WXY)"]},74118:e=>{e.exports=["Gelombang Impulse Elliott (12345)"]},95840:e=>{e.exports=["Gelombang Segitiga Elliott / Elliott Triangle Wave (ABCDE)"]},66637:e=>{e.exports=["Gelombang Kombinasi Tripel Elliott (WXYXZ)"]},69418:e=>{e.exports=["Elips"]},27558:e=>{e.exports=["Perpanjang Garis Peringatan"]},2578:e=>{e.exports=["Garis Perpanjangan"]},77295:e=>{e.exports=["Bursa"]},2899:e=>{e.exports=["Pane Yang Telah Ada Diatas"]},53387:e=>{e.exports=["Pane Yang Telah Ada Dibawah"]},36972:e=>{e.exports=["Prakiraan"]},17994:e=>{e.exports=["Kegagalan untuk menyimpan perpustakaan"]},87375:e=>{e.exports=["Gagal menyimpan skrip"]},35050:e=>{e.exports="Feb"},82719:e=>{e.exports=["Kanal Fib"]},64192:e=>{e.exports=["Lingkaran Fib"]},63835:e=>{e.exports=["Retracemen Fib"]},18072:e=>{e.exports=["Busur Resisten Kecepatan Fib"]},20877:e=>{e.exports=["Kipas Resisten Kecepatan Fib"]},76783:e=>{e.exports=["Spiral Fib"]},89037:e=>{e.exports=["Zona Waktu Fib"]},72489:e=>{e.exports=["Baji Fib"]},21524:e=>{e.exports=["Bendera"]},55678:e=>{e.exports=["Tanda Bendera"]},29230:e=>{e.exports=["Puncak/Dasar Datar"]},92754:e=>{e.exports=["Membalik"]},42015:e=>{e.exports=["Bagian fraksi tidak valid."]},47542:e=>{
e.exports=["Studi-Studi Fundamental tidak lagi tersedia pada chart"]},16245:e=>{e.exports="Kolkata"},3155:e=>{e.exports="Kathmandu"},92901:e=>{e.exports="Kagi"},2693:e=>{e.exports="Karachi"},72374:e=>{e.exports="Kuwait"},34911:e=>{e.exports=["Area HLC"]},87338:e=>{e.exports="Ho Chi Minh"},61582:e=>{e.exports=["Candle Kosong"]},32918:e=>{e.exports="Hong Kong"},61351:e=>{e.exports="Honolulu"},60049:e=>{e.exports=["Garis Horisontal"]},76604:e=>{e.exports=["Sinar Horisontal"]},42616:e=>{e.exports=["Head dan Shoulders"]},40530:e=>{e.exports="Heikin Ashi"},99820:e=>{e.exports="Helsinki"},31971:e=>{e.exports=["Sembunyikan"]},33911:e=>{e.exports=["Sembunyikan seluruhnya"]},95551:e=>{e.exports=["Sembunyikan seluruh gambar"]},44312:e=>{e.exports=["Sembunyikan seluruh gambar dan indikator"]},67927:e=>{e.exports=["Sembunyikan seluruh gambar, indikator, posisi & order"]},86306:e=>{e.exports=["Sembunyikan seluruh indikator"]},70803:e=>{e.exports=["Sembunyikan seluruh posisi & order"]},13277:e=>{e.exports=["Sembunyikan gambar"]},8251:e=>{e.exports=["Sembunyikan Peristiwa di Chart"]},44177:e=>{e.exports=["Sembunyikan indikator"]},2441:e=>{e.exports=["Sembunyikan Tanda-Tanda pada Bar"]},90540:e=>{e.exports=["Sembunyikan posisi & order"]},30777:e=>{e.exports=["Tertinggi"]},31994:e=>{e.exports=["Tertinggi-Terendah"]},60259:e=>{e.exports=["Label harga tertinggi dan terendah"]},21803:e=>{e.exports=["Garis harga tertinggi dan terendah"]},31895:e=>{e.exports=["Penanda"]},69085:e=>{e.exports=['Histogram terlalu besar, silakan tingkatkan input "Ukuran Row".']},8122:e=>{e.exports=['Histogram terlalu besar, silakan kurangi input "Ukuran Row".']},23450:e=>{e.exports=["Gambar"]},71778:e=>{e.exports=["Menengah"]},14177:e=>{e.exports=["Simbol Tidak Valid"]},53239:e=>{e.exports=["Inversikan Skala"]},20062:e=>{e.exports=["Diindeks ke 100"]},81584:e=>{e.exports=["Label nilai indikator"]},31485:e=>{e.exports=["Label nama indikator"]},27677:e=>{e.exports=["Garis Info"]},98767:e=>{e.exports=["Masukkan Indikator"]},9114:e=>{e.exports=["Di Dalam"]},12354:e=>{e.exports=["Pitchfork Bagian Dalam"]},26579:e=>{e.exports=["Ikon"]},37885:e=>{e.exports="Istanbul"},87469:e=>{e.exports="Johannesburg"},52707:e=>{e.exports="Jakarta"},95425:e=>{e.exports="Jan"},42890:e=>{e.exports=["Yerusalem"]},6215:e=>{e.exports="Jul"},15224:e=>{e.exports="Jun"},36253:e=>{e.exports="Juneau"},15241:e=>{e.exports=["Di Sebelah Kiri"]},29404:e=>{e.exports=["Di Sebelah Kanan"]},850:e=>{e.exports="Oops!"},675:e=>{e.exports=["Pohon Objek"]},73546:e=>{e.exports=["Okt"]},39280:e=>{e.exports=["Pembukaan"]},25595:e=>{e.exports="Original"},82906:e=>{e.exports="Oslo"},8136:e=>{e.exports=["Terendah"]},42284:e=>{e.exports=["Kunci"]},1441:e=>{e.exports=["Kunci/Buka Kunci"]},82232:e=>{e.exports=["Kunci garis kursor vertikal berdasarkan waktu"]},18219:e=>{e.exports=["Kunci Harga Ke Rasio Bar"]},12285:e=>{e.exports=["Logaritma"]},50286:e=>{e.exports="London"},44604:e=>{e.exports=["Posisi Pembelian"]},87604:e=>{e.exports="Los Angeles"},18528:e=>{e.exports=["Label Turun"]},13046:e=>{
e.exports=["Label Naik"]},94420:e=>{e.exports=["Label"]},89155:e=>{e.exports="Lagos"},25846:e=>{e.exports="Lima"},1277:e=>{e.exports=["Garis"]},38397:e=>{e.exports=["Garis dengan penanda"]},63492:e=>{e.exports=["Garis Jeda"]},83182:e=>{e.exports=["Garis"]},78104:e=>{e.exports=["Tautan ke gambar chart yang disalin ke clipboard {emoji}"]},50091:e=>{e.exports="Lisbon"},64352:e=>{e.exports="Luxembourg"},11156:e=>{e.exports="MTPredictor"},67861:e=>{e.exports=["Pindahkan titiknya untuk memposisikan jangkar lalu tap untuk meletakkan"]},45828:e=>{e.exports=["Pindah Ke"]},44302:e=>{e.exports=["Pindahkan Skala ke Kiri"]},94338:e=>{e.exports=["Pindahkan Skala ke Kanan"]},66276:e=>{e.exports=["Schiff Termodifikasi"]},18559:e=>{e.exports=["Pitchfork Schiff Termodifikasi"]},18665:e=>{e.exports="Moscow"},58038:e=>{e.exports="Madrid"},34190:e=>{e.exports="Malta"},90271:e=>{e.exports="Manila"},51369:e=>{e.exports="Mar"},85095:e=>{e.exports=["Kota Meksiko"]},75633:e=>{e.exports=["Gabungkan Seluruh Skala Menjadi Satu"]},95093:e=>{e.exports=["Campuran"]},10931:e=>{e.exports=["Mikro"]},58397:e=>{e.exports=["Milenium"]},85884:e=>{e.exports="Minuette"},9632:e=>{e.exports=["Amat kecil"]},63158:e=>{e.exports=["Dicerminkan"]},42769:e=>{e.exports="Muscat"},43088:e=>{e.exports=["Tidak Tersedia"]},3485:e=>{e.exports=["Tanpa Skala (Layar Penuh)"]},8886:e=>{e.exports=["Tidak ada sinkronisasi"]},16971:e=>{e.exports=["Tidak ada data volume"]},75549:e=>{e.exports=["Catatan"]},71230:e=>{e.exports="Nov"},99203:e=>{e.exports=["Pulai Norfolk"]},79023:e=>{e.exports="Nairobi"},91203:e=>{e.exports="New York"},24143:e=>{e.exports=["Selandia Baru"]},40887:e=>{e.exports=["Panel baru diatas"]},96712:e=>{e.exports=["Panel baru dibawah"]},33566:e=>{e.exports=["Nikosia"]},56670:e=>{e.exports=["Terjadi masalah"]},64968:e=>{e.exports=["Terjadi kesalahan. Harap coba kembali nanti."]},10520:e=>{e.exports=["Simpan Layout Chart Baru"]},9908:e=>{e.exports=["Simpan Sebagai"]},68553:e=>{e.exports="San Salvador"},65412:e=>{e.exports="Santiago"},13538:e=>{e.exports="Sao Paulo"},37207:e=>{e.exports=["Skalakan Chart Harga Saja"]},51464:e=>{e.exports="Schiff"},98114:e=>{e.exports=["Pitchfork Schiff"]},1535:e=>{e.exports=["Skrip mungkin tidak diperbaharui apabila anda menginggalkan halaman ini."]},89517:e=>{e.exports=["Pengaturan"]},43247:e=>{e.exports=["Bagian pecahan kedua tidak valid."]},19796:e=>{e.exports=["Kirim ke Belakang"]},23221:e=>{e.exports=["Kirim Mundur"]},5961:e=>{e.exports="Seoul"},57902:e=>{e.exports="Sep"},25866:e=>{e.exports=["Sesi"]},59827:e=>{e.exports=["Jeda Sesi"]},69240:e=>{e.exports="Shanghai"},37819:e=>{e.exports=["Posisi Penjualan"]},81428:e=>{e.exports=["Perlihatkan"]},98116:e=>{e.exports=["Tampilkan seluruh gambar"]},39046:e=>{e.exports=["Tampilkan seluruh gambar dan indikator"]},38293:e=>{e.exports=["Tampilkan seluruh gambar, indikator, posisi & order"]},49982:e=>{e.exports=["Tampilkan seluruh indikator"]},48284:e=>{e.exports=["Tampilkan seluruh ide"]},62632:e=>{e.exports=["Tampilkan seluruh posisi & order"]},24620:e=>{
e.exports=["Tampilkan perubahan kontrak berlanjut"]},84813:e=>{e.exports=["Tampilkan kedaluwarsa kontrak"]},66263:e=>{e.exports=["Perlihatkan dividen"]},46771:e=>{e.exports=["Perlihatkan perolehan"]},87933:e=>{e.exports=["Tampilkan ide dari pengguna yang diikuti"]},72973:e=>{e.exports=["Tampilkan update terkini"]},58669:e=>{e.exports=["Tampilkan ide saya saja"]},30816:e=>{e.exports=["Perlihatkan pemecahan"]},68161:e=>{e.exports="Signpost"},56683:e=>{e.exports=["Singapura"]},69502:e=>{e.exports=["Garis Sinus"]},44904:e=>{e.exports=["Persegi"]},70213:e=>{e.exports=["Batas studi terlewati. {number} studi per layout.\nHarap menghilangkan beberapa studi."]},32733:e=>{e.exports=["Corak"]},65323:e=>{e.exports=["Susun di Kiri"]},14113:e=>{e.exports=["Susun di Kanan"]},93161:e=>{e.exports=["Tetap Dalam Mode Menggambar"]},79511:e=>{e.exports=["Garis tahap"]},84573:e=>{e.exports=["Stiker"]},48767:e=>{e.exports="Stockholm"},29662:e=>{e.exports=["Submikro"]},9753:e=>{e.exports=["Submilenium"]},71722:e=>{e.exports="Subminuette"},91889:e=>{e.exports="Supercycle"},33820:e=>{e.exports=["Supermilenium"]},11020:e=>{e.exports="Sydney"},89659:e=>{e.exports=["Simbol Error"]},90932:e=>{e.exports=["Label Nama Simbol"]},65986:e=>{e.exports=["Info Simbol"]},52054:e=>{e.exports=["Label Nilai Terakhir Simbol"]},33606:e=>{e.exports=["Sinkronisasikan secara global"]},18008:e=>{e.exports=["Sinkronisasi pada layout"]},99969:e=>{e.exports=["Poin & Figur"]},53047:e=>{e.exports="Polyline"},34402:e=>{e.exports=["Jalur"]},70394:e=>{e.exports=["Kanal Paralel"]},95995:e=>{e.exports="Paris"},29682:e=>{e.exports="Paste"},51102:e=>{e.exports=["Persen"]},35590:e=>{e.exports="Perth"},19093:e=>{e.exports="Phoenix"},22293:e=>{e.exports="Pitchfan"},43852:e=>{e.exports="Pitchfork"},37680:e=>{e.exports=["Pin ke Skala Kiri Baru"]},43707:e=>{e.exports=["Pin ke Skala Kanan Baru"]},91130:e=>{e.exports=["Pin ke Skala Kiri"]},61201:e=>{e.exports=["Pin ke Skala Kiri (Tersembunyi)"]},764:e=>{e.exports=["Pin ke skala kanan"]},20207:e=>{e.exports=["Pin ke Skala Kanan (Tersembunyi)"]},66156:e=>{e.exports=["Pin ke Skala (Saat ini Kiri)"]},54727:e=>{e.exports=["Pin ke Skala (Saat ini Tanpa Skala)"]},76598:e=>{e.exports=["Pin ke Skala (Saat ini Kanan)"]},39065:e=>{e.exports=["Pin ke Skala ({label} Saat ini)"]},97324:e=>{e.exports=["Pin ke Skala {label}"]},56948:e=>{e.exports=["Pin ke Skala {label} (Tersembunyi)"]},32156:e=>{e.exports=["Di Pin ke Skala Kiri"]},8128:e=>{e.exports=["Di Pin ke Skala Kiri (Tersembunyi)"]},3822:e=>{e.exports=["Di Pin ke Skala Kanan"]},44538:e=>{e.exports=["Pin ke Skala Kanan (Tersembunyi)"]},65810:e=>{e.exports=["Di Pin ke Skala {label}"]},14125:e=>{e.exports=["Di Pin ke Skala {label} (Tersembunyi)"]},97378:e=>{e.exports=["Tanda tambah"]},46669:e=>{e.exports=["Harap beri kami izin menulis clipboard di browser anda atau tekan {keystroke}"]},35963:e=>{e.exports=["Tekan dan tahan {key} saat melakukan zoom untuk mempertahankan posisi chart"]},95921:e=>{e.exports=["Label Harga"]},28625:e=>{e.exports=["Catatan Harga"]},2032:e=>{e.exports=["Rentang Harga"]},
32061:e=>{e.exports=["Format harga tidak valid."]},91492:e=>{e.exports=["Garis Harga"]},48404:e=>{e.exports=["Primer"]},87086:e=>{e.exports=["Proyeksi"]},10160:e=>{e.exports=["Dipublikasikan pada {customer}, {date}"]},19056:e=>{e.exports="Qatar"},9998:e=>{e.exports=["Persegi Terputar"]},74214:e=>{e.exports=["Roma"]},50470:e=>{e.exports=["Sinar"]},90357:e=>{e.exports=["Rentang"]},26833:e=>{e.exports="Reykjavik"},328:e=>{e.exports=["Persegi"]},41615:e=>{e.exports=["Ulangi"]},35001:e=>{e.exports=["Tren Regresi"]},34596:e=>{e.exports=["Hilangkan"]},1434:e=>{e.exports=["Hilangkan Gambar"]},13951:e=>{e.exports=["Hilangkan Indikator"]},4142:e=>{e.exports=["Ganti Nama Layout Chart"]},20801:e=>{e.exports="Renko"},34301:e=>{e.exports=["Atur ulang tampilan chart"]},18001:e=>{e.exports=["Reset poin"]},17258:e=>{e.exports=["Atur ulang skala harga"]},25333:e=>{e.exports=["Reset Skala Waktu"]},52588:e=>{e.exports="Riyadh"},5871:e=>{e.exports="Riga"},33603:e=>{e.exports=["Peringatan"]},48474:e=>{e.exports=["Warsawa"]},20466:e=>{e.exports="Tokelau"},94284:e=>{e.exports="Tokyo"},83836:e=>{e.exports="Toronto"},38788:e=>{e.exports="Taipei"},39108:e=>{e.exports="Tallinn"},37229:e=>{e.exports=["Teks"]},16267:e=>{e.exports=["Teheran"]},19611:e=>{e.exports="Template"},29198:e=>{e.exports=["Vendor data tidak menyediakan data volume untuk simbol ini."]},8162:e=>{e.exports=["Kilasan publikasi tidak dapat dimuat. Harap matikan ekstensi browser anda lalu coba kembali."]},65943:e=>{e.exports=["Indikator ini tidak dapat diterapkan pada indikator lain"]},74986:e=>{e.exports=["Skrip ini hanya-undangan. Untuk meminta akses, silakan hubungi penulisnya."]},58018:e=>{e.exports=["Simbol tersebut hanya tersedia di {linkStart}TradingView{linkEnd}."]},98538:e=>{e.exports=["Motif Three Drives"]},30973:e=>{e.exports=["Tick"]},31976:e=>{e.exports=["Waktu"]},64375:e=>{e.exports=["Zona Waktu"]},95005:e=>{e.exports=["Siklus Waktu"]},87085:e=>{e.exports="Trade"},94770:e=>{e.exports=["Sudut Tren"]},23104:e=>{e.exports=["Garis Tren"]},15501:e=>{e.exports=["Ekstensi Fib Berbasis Tren"]},31196:e=>{e.exports=["Waktu Fib Berbasis Tren"]},29245:e=>{e.exports=["Segitiga"]},83356:e=>{e.exports=["Segitiga Turun"]},12390:e=>{e.exports=["Motif Segitiga"]},28340:e=>{e.exports=["Segitiga Naik"]},93855:e=>{e.exports="Tunis"},50406:e=>{e.exports="UTC"},81320:e=>{e.exports=["Kembalikan"]},25933:e=>{e.exports=["Unit"]},15101:e=>{e.exports=["Buka Kunci"]},34150:e=>{e.exports=["Gelombang Naik 4"]},83927:e=>{e.exports=["Gelombang Naik 5"]},58976:e=>{e.exports=["Gelombang Naik 1 atau A"]},11661:e=>{e.exports=["Gelombang Naik 2 atau B"]},53958:e=>{e.exports=["Gelombang Naik 3"]},66560:e=>{e.exports=["Gelombang Naik C"]},18426:e=>{e.exports=["Profil Volume Rentang Tetap"]},61022:e=>{e.exports=["Indikator Profil Volume hanya tersedia pada skema terupgrade kami."]},15771:e=>{e.exports="Vancouver"},56211:e=>{e.exports=["Garis Vertikal"]},75354:e=>{e.exports="Vilnius"},21852:e=>{e.exports=["Visibilitas"]},27557:e=>{e.exports=["Visibilitas interval"]},89960:e=>{
e.exports=["Terlihat saat Mouse Diatas"]},22198:e=>{e.exports=["Urutan visual"]},7050:e=>{e.exports=["Persilangan X"]},66527:e=>{e.exports=["Motif XABCD"]},17126:e=>{e.exports=["Anda tidak dapat melihat kerangka waktu pivot pada resolusi ini"]},69293:e=>{e.exports="Yangon"},84301:e=>{e.exports="Zurich"},76020:e=>{e.exports=["ubah derajat Elliott"]},83935:e=>{e.exports=["ubah label tidak tumpang tindih"]},39402:e=>{e.exports=["Ubah visibilitas label rata-rata harga penutupan"]},98866:e=>{e.exports=["Ubah visibilitas garis rata-rata harga penutupan"]},5100:e=>{e.exports=["Ubah visibilitas label penawaran dan permintaan"]},32311:e=>{e.exports=["Ubah visibilitas garis penawaran dan permintaan"]},22641:e=>{e.exports=["ubah mata uang"]},30501:e=>{e.exports=["ubah layout chart ke {title}"]},7017:e=>{e.exports=["ubah visibilitas perubahan kontrak berlanjut"]},58108:e=>{e.exports=["Ubah visibilitas perhitungan mundur ke penutupan bar"]},7151:e=>{e.exports=["ubah rentang tanggal"]},84944:e=>{e.exports=["Ubah visibilitas dividen"]},79574:e=>{e.exports=["Ubah visibilitas peristiwa pada chart"]},88217:e=>{e.exports=["Ubah visibilitas laba"]},28288:e=>{e.exports=["ubah visibilitas kedaluwarsa kontrak berjangka"]},66805:e=>{e.exports=["Ubah visibilitas label harga tertinggi dan terendah"]},92556:e=>{e.exports=["Ubah visibilitas garis harga tertinggi dan terendah"]},87027:e=>{e.exports=["Ubah visibilitas label nama indikator"]},14922:e=>{e.exports=["Ubah visibilitas label nilai indikator"]},19839:e=>{e.exports=["Ubah visibilitas update terkini"]},23783:e=>{e.exports=["Ubah penghubungan grup"]},87510:e=>{e.exports=["ubah ketinggian panel"]},50190:e=>{e.exports=["Ubah visibilitas tombol plus"]},49889:e=>{e.exports=["Ubah visibilitas label harga pra/pasca pasar"]},16750:e=>{e.exports=["Ubah visibilitas garis harga pra/pasca pasar"]},59883:e=>{e.exports=["Ubah visibilitas garis penutupan harga sebelumnya"]},67761:e=>{e.exports=["Ubah Garis Harga"]},69510:e=>{e.exports=["Ubah harga ke rasio bar"]},32303:e=>{e.exports=["Ubah Resolusi"]},526:e=>{e.exports=["Ubah simbol"]},9402:e=>{e.exports=["Ubah visibilitas label simbol"]},53150:e=>{e.exports=["Ubah visibilitas nilai terakhir simbol"]},12707:e=>{e.exports=["Ubah visibilitas nilai penutupan simbol sebelumnya"]},65303:e=>{e.exports=["Ubah sesi"]},15403:e=>{e.exports=["ubah visibilitas jeda sesi"]},53438:e=>{e.exports=["ubah corak seri"]},74488:e=>{e.exports=["Ubah visibilitas pemecahan"]},20505:e=>{e.exports=["Ubah zona waktu"]},39028:e=>{e.exports=["ubah unit"]},21511:e=>{e.exports=["Ubah Visibilitas"]},16698:e=>{e.exports=["Ubah visibilitas pada interval saat ini"]},78422:e=>{e.exports=["Ubah visibilitas pada interval saat ini dan di atasnya"]},49529:e=>{e.exports=["Ubah visibilitas pada interval saat ini dan di bawahnya"]},66927:e=>{e.exports=["Ubah visibilitas pada semua interval"]},74428:e=>{e.exports=["ubah corak {title}"]},72032:e=>{e.exports=["ubah poin {pointIndex}"]},65911:e=>{e.exports=["chart oleh TradingView"]},5179:e=>{e.exports=["Klon peralatan garis"]},3195:e=>{
e.exports=["Buat kelompok alat garis"]},92659:e=>{e.exports=["Buat kelompok alat garis dari pilihan"]},81791:e=>{e.exports=["buat {tool}"]},63649:e=>{e.exports=["cut sumber"]},78755:e=>{e.exports="cut {title}"},99113:e=>{e.exports=["Tambahkan alat garis {lineTool} ke kelompok {name}"]},40242:e=>{e.exports=["tambahkan alat garis ke kelompok {group}"]},22856:e=>{e.exports=["Tambah Metrik Finansial ini ke Seluruh Layout"]},82388:e=>{e.exports=["Tambah Indikator ini ke Seluruh Layout"]},94292:e=>{e.exports=["Tambah Strategi ini ke Seluruh Layout"]},27982:e=>{e.exports=["Tambah Simbol ini ke Seluruh Layout"]},66568:e=>{e.exports=["terapkan tema chart"]},64034:e=>{e.exports=["terapkan semua properti chart"]},49037:e=>{e.exports=["Terapkan Template Gambar"]},96996:e=>{e.exports=["terapkan setelan pabrik ke sumber yang dipilih"]},44547:e=>{e.exports=["Terapkan indikator ke seluruh layout"]},26065:e=>{e.exports=["Terapkan template studi {template}"]},58570:e=>{e.exports=["terapkan tema toolbar"]},27195:e=>{e.exports=["bawa ke depan {title} grup"]},78246:e=>{e.exports=["bawa {title} ke depan"]},56763:e=>{e.exports=["Bawa {title} kedepan"]},5607:e=>{e.exports=["oleh TradingView"]},90621:e=>{e.exports=["kunci rentang tanggal"]},12962:e=>{e.exports=["hapus haris level"]},63391:e=>{e.exports=["Keluarkan alat garis dari kelompok {group}"]},59942:e=>{e.exports=["balik motif bar"]},70301:e=>{e.exports=["sembunyikan {title}"]},91842:e=>{e.exports=["Sembunyikan garis label peringatan"]},54781:e=>{e.exports=["Sembunyikan Semua Peralatan Gambar"]},44974:e=>{e.exports=["Sembunyikan Tanda-Tanda pada Bar"]},28916:e=>{e.exports=["pengunci interval"]},94245:e=>{e.exports=["Inversikan Skala"]},90743:e=>{e.exports=["masukkan {title}"]},53146:e=>{e.exports=["masukkan {title} setelah {targetTitle}"]},74055:e=>{e.exports=["Masukkan {title} setelah {target}"]},11231:e=>{e.exports=["Masukkan {title} sebelum {target}"]},67176:e=>{e.exports=["Masukkan {title} sebelum {targetTitle}"]},54597:e=>{e.exports=["memuat template gambar default"]},30295:e=>{e.exports=["memuat..."]},50193:e=>{e.exports=["Kunci {title}"]},4963:e=>{e.exports=["Kunci kelompok {group}"]},68163:e=>{e.exports=["kunci objek"]},47107:e=>{e.exports=["pindah"]},11303:e=>{e.exports=["Pindahkan {title} ke Skala Kiri Baru"]},45544:e=>{e.exports=["pindahkan {title} ke skala kanan yang baru"]},81898:e=>{e.exports=["Pindahkan Seluruh Skala ke Kiri"]},22863:e=>{e.exports=["Pindahkan Seluruh Skala ke Kanan"]},45356:e=>{e.exports=["Pindahkan Gambar"]},15086:e=>{e.exports=["Pindahkan ke kiri"]},61711:e=>{e.exports=["Pindahkan ke kanan"]},4184:e=>{e.exports=["Pindahkan skala"]},74642:e=>{e.exports=["Jadikan {title} tanpa skala (Layar Penuh)"]},45223:e=>{e.exports=["Jadikan kelompok {group} tidak terlihat"]},87927:e=>{e.exports=["Jadikan kelompok {group} terlihat"]},62153:e=>{e.exports=["gabungkan ke bawah"]},70746:e=>{e.exports=["gabungkan ke panel"]},66143:e=>{e.exports=["gabungkan ke atas"]},81870:e=>{e.exports=["cerminkan motif bar"]},16542:e=>{e.exports=["Tidak Tersedia"]},47222:e=>{
e.exports=["skala harga"]},99042:e=>{e.exports=["Skalakan Chart Harga Saja"]},35962:e=>{e.exports=["skala waktu"]},68193:e=>{e.exports=["gulir"]},70009:e=>{e.exports=["gulirkan waktu"]},69485:e=>{e.exports=["atur skala harga strategi yang dipilih ke {title}"]},16259:e=>{e.exports=["Kirim {title} kebelakang"]},66781:e=>{e.exports=["kirim {title} ke belakang"]},4998:e=>{e.exports=["bawa mundur {title} grup"]},64704:e=>{e.exports=["Bagikan peralatan garis secara global"]},77554:e=>{e.exports=["Bagikan peralatan garis pada layout"]},16237:e=>{e.exports=["tampilkan garis label peringatan"]},13622:e=>{e.exports=["tampilkan seluruh ide"]},26267:e=>{e.exports=["tampilkan ide dari pengguna yang diikuti"]},40061:e=>{e.exports=["tampilkan ide saya saja"]},52010:e=>{e.exports=["Tetap dalam mode menggambar"]},98784:e=>{e.exports=["hentikan sinkronisasi gambar"]},57011:e=>{e.exports=["hentikan sinkronisasi peralatan menggaris"]},92831:e=>{e.exports=["pengunci simbol"]},60635:e=>{e.exports=["waktu sinkronisasi"]},99769:e=>{e.exports=["diberdayakan oleh"]},68111:e=>{e.exports=["diberdayakan oleh TradingView"]},96916:e=>{e.exports=["paste gambar"]},80611:e=>{e.exports=["paste indikator"]},41601:e=>{e.exports="paste {title}"},84018:e=>{e.exports=["Pin ke skala kiri"]},22615:e=>{e.exports=["Pin ke Skala Kanan"]},56015:e=>{e.exports=["Pin ke Skala {label}"]},33348:e=>{e.exports=["atur ulang pane"]},15516:e=>{e.exports=["Hilangkan seluruh studi"]},80171:e=>{e.exports=["Hilangkan seluruh studi dan peralatan gambar"]},59211:e=>{e.exports=["hapus alat garis kosong yang tidak dipilih"]},44656:e=>{e.exports=["Hilangkan Gambar"]},70653:e=>{e.exports=["lepaskan kelompok gambar"]},66414:e=>{e.exports=["hapus garis sumber data"]},47637:e=>{e.exports=["lepaskan panel"]},39859:e=>{e.exports=["lepaskan {title}"]},78811:e=>{e.exports=["Hilangkan kelompok alat garis {name}"]},16338:e=>{e.exports=["Ubah nama kelompok {group} menjadi {newName}"]},30910:e=>{e.exports=["atur ulang ukuran layout"]},21948:e=>{e.exports=["atur ulang skala"]},55064:e=>{e.exports=["Reset Skala Waktu"]},13034:e=>{e.exports=["ubah ukuran layout"]},9608:e=>{e.exports=["kembali ke bawaan"]},63060:e=>{e.exports=["toggle skala otomatis"]},98860:e=>{e.exports=["toggle skala diindeks ke 100"]},21203:e=>{e.exports=["toggle pengunci skala"]},60166:e=>{e.exports=["toggle skala Log"]},68642:e=>{e.exports=["toggle skala persentase"]},33714:e=>{e.exports=["toggle skala reguler"]},47122:e=>{e.exports=["waktu pelacakan"]},28068:e=>{e.exports=["matikan pembagian peralatan garis"]},66824:e=>{e.exports=["buka kunci objek"]},51114:e=>{e.exports=["Buka kunci kelompok {group}"]},92421:e=>{e.exports=["Buka kunci {title}"]},20057:e=>{e.exports=["pisahkan ke panel bawah yang baru"]},52540:e=>{e.exports=["pisahkan ke atas"]},86949:e=>{e.exports=["pisahkan ke bawah"]},50728:e=>{e.exports=["Update Skrip {title}"]},33355:e=>{e.exports=["{count} bar"]},88841:e=>{e.exports=["{symbol} finansial oleh TradingView"]},38641:e=>{e.exports=["{userName} dipublikasikan pada {customer}, {date}"]},59833:e=>{
e.exports="zoom"},19813:e=>{e.exports=["perbesar"]},9645:e=>{e.exports=["perkecil"]},30572:e=>{e.exports=["hari"]},52254:e=>{e.exports=["jam"]},99062:e=>{e.exports=["bulan"]},69143:e=>{e.exports=["menit"]},71787:e=>{e.exports=["detik"]},82797:e=>{e.exports=["rentang"]},47966:e=>{e.exports=["minggu"]},99136:e=>{e.exports="tick"},18562:e=>{e.exports=Object.create(null),e.exports["#AAPL-symbol-description"]="Apple Inc",e.exports["#AUDCAD-symbol-description"]=["Dollar Australia/Dollar Kanada"],e.exports["#AUDCHF-symbol-description"]=["Dollar Australia / Franc Swiss"],e.exports["#AUDJPY-symbol-description"]=["Dollar Australia / Yen Jepang"],e.exports["#AUDNZD-symbol-description"]=["Dollar Australia / Dollar New Zealand"],e.exports["#AUDRUB-symbol-description"]=["Dollar Australia / Ruble Rusia"],e.exports["#AUDUSD-symbol-description"]=["Dollar Australia / Dollar AS"],e.exports["#BRLJPY-symbol-description"]=["Real Brazil / Yen Jepang"],e.exports["#BTCCAD-symbol-description"]=["Bitcoin / Dollar Kanada"],e.exports["#BTCCNY-symbol-description"]=["Bitcoin / Yuan Cina"],e.exports["#BTCEUR-symbol-description"]="Bitcoin / Euro",e.exports["#BTCKRW-symbol-description"]=["Bitcoin / Won Korea Selatan"],e.exports["#BTCRUR-symbol-description"]="Bitcoin / Ruble",e.exports["#BTCUSD-symbol-description"]=["Bitcoin / Dollar AS"],e.exports["#BVSP-symbol-description"]=["Index Bovespa Brazil"],e.exports["#CADJPY-symbol-description"]=["Dollar Kanada / Yen Jepang"],e.exports["#CB1!-symbol-description"]=["Minyak Mentah Brent"],e.exports["#CHFJPY-symbol-description"]=["Franc Swiss / Yen Jepang"],e.exports["#COPPER-symbol-description"]=["CFD pada Tembaga"],e.exports["#ES1-symbol-description"]=["Kontrak Berjangka S&P 500 E-Mini"],e.exports["#ESP35-symbol-description"]=["Indeks IBEX 35"],e.exports["#EUBUND-symbol-description"]=["Bund Euro"],e.exports["#EURAUD-symbol-description"]=["Euro / Dollar Australia"],e.exports["#EURBRL-symbol-description"]=["Euro / Real Brazil"],e.exports["#EURCAD-symbol-description"]=["Euro / Dollar Kanada"],e.exports["#EURCHF-symbol-description"]=["Euro / Franc Swiss"],e.exports["#EURGBP-symbol-description"]=["Euro / Pound Inggris"],e.exports["#EURJPY-symbol-description"]=["Euro / Yen Jepang"],e.exports["#EURNZD-symbol-description"]=["Euro / Dollar New Zealand"],e.exports["#EURRUB-symbol-description"]=["Euro / Ruble Rusia"],e.exports["#EURRUB_TOM-symbol-description"]=["Euro /Ruble TOM Rusia"],e.exports["#EURSEK-symbol-description"]=["Euro / Krona Swedia"],e.exports["#EURTRY-symbol-description"]=["Euro / Lira Turki"],e.exports["#EURUSD-symbol-description"]=["Euro / Dollar AS"],e.exports["#EUSTX50-symbol-description"]=["Indeks Euro Stoxx 50"],e.exports["#FRA40-symbol-description"]=["Indeks CAC 40"],e.exports["#GB10-symbol-description"]=["Obligasi Pemerintah Inggris 10th"],e.exports["#GBPAUD-symbol-description"]=["Pound Inggris / Dollar Australia"],e.exports["#GBPCAD-symbol-description"]=["Pound Inggris / Dollar Kanada"],e.exports["#GBPCHF-symbol-description"]=["Pound Inggris / Franc Swiss"],
e.exports["#GBPEUR-symbol-description"]=["Pound Inggris / Euro"],e.exports["#GBPJPY-symbol-description"]=["Pound Inggris / Yen Jepang"],e.exports["#GBPNZD-symbol-description"]=["Pound Inggris / Dollar New Zealand"],e.exports["#GBPRUB-symbol-description"]=["Pound Inggris / Ruble Rusia"],e.exports["#GBPUSD-symbol-description"]=["Pound Inggris / Dollar AS"],e.exports["#GER30-symbol-description"]=["Indeks DAX"],e.exports["#GOOGL-symbol-description"]=["Alphabet Inc (Google) Kelas A"],e.exports["#ITA40-symbol-description"]=["Indeks FTSE MIB"],e.exports["#JPN225-symbol-description"]=["Indeks Nikkei 225"],e.exports["#JPYKRW-symbol-description"]=["Yen Jepang / Won Korea Selatan"],e.exports["#JPYRUB-symbol-description"]=["Yen Jepang / Ruble Rusia"],e.exports["#KA1-symbol-description"]=["Kontrak Berjangka Gula #11"],e.exports["#KG1-symbol-description"]=["Kontrak Berjangka Kapas"],e.exports["#KT1-symbol-description"]="Key Tronic Corр.",e.exports["#LKOH-symbol-description"]="LUKOIL",e.exports["#LTCBTC-symbol-description"]="Litecoin / Bitcoin",e.exports["#MGNT-symbol-description"]="Magnit",e.exports["#MICEX-symbol-description"]=["Indeks MICEX"],e.exports["#MNOD_ME.EQRP-symbol-description"]="ADR GMK NORILSKIYNIKEL ORD SHS [REPO]",e.exports["#MSFT-symbol-description"]="Microsoft Corp.",e.exports["#NAS100-symbol-description"]=["CFD Cash US 100"],e.exports["#NGAS-symbol-description"]=["Gas Alam (Henry Hub)"],e.exports["#NKY-symbol-description"]=["Indeks Nikkei 225"],e.exports["#NZDJPY-symbol-description"]=["Dollar New Zealand / Yen Jepang"],e.exports["#NZDUSD-symbol-description"]=["Dollar New Zealand / Dollar AS"],e.exports["#RB1-symbol-description"]=["Kontrak Berjangka Bensin RBOB"],e.exports["#RTS-symbol-description"]=["Indeks RTS Rusia"],e.exports["#SBER-symbol-description"]="SBERBANK",e.exports["#SPX500-symbol-description"]=["Indeks S&P 500"],e.exports["#TWTR-symbol-description"]="Twitter Inc",e.exports["#UK100-symbol-description"]=["Indeks FTSE 100"],e.exports["#USDBRL-symbol-description"]=["Dollar A.S. / Real Brazil"],e.exports["#USDCAD-symbol-description"]=["Dollar A.S. / Dollar Kanada"],e.exports["#USDCHF-symbol-description"]=["Dollar A.S. / Franc Swiss"],e.exports["#USDCNY-symbol-description"]=["Dollar A.S. / Yuan Cina"],e.exports["#USDDKK-symbol-description"]=["Dollar A.S. / Krona Denmark"],e.exports["#USDHKD-symbol-description"]=["Dollar A.S. / Dollar Hong Kong"],e.exports["#USDIDR-symbol-description"]=["Dollar A.S. / Rupiah"],e.exports["#USDINR-symbol-description"]=["Dollar A.S. / Rupee India"],e.exports["#USDJPY-symbol-description"]=["Dollar A.S. / Yen Jepang"],e.exports["#USDKRW-symbol-description"]=["Dollar A.S. / Won Korea Selatan"],e.exports["#USDMXN-symbol-description"]=["Dollar A.S. / Peso Meksiko"],e.exports["#USDPHP-symbol-description"]=["Dollar A.S. / Peso Filipina"],e.exports["#USDRUB-symbol-description"]=["Dollar A.S. / Ruble Rusia"],e.exports["#USDRUB_TOM-symbol-description"]=["Dollar A.S. / Ruble TOM Rusia"],e.exports["#USDSEK-symbol-description"]=["Dollar A.S. / Krona Swedia"],
e.exports["#USDSGD-symbol-description"]=["Dollar A.S. / Dollar Singapura"],e.exports["#USDTRY-symbol-description"]=["Dollar A.S. / Lira Turki"],e.exports["#VTBR-symbol-description"]="VTB",e.exports["#XAGUSD-symbol-description"]=["Perak / Dollar A.S."],e.exports["#XAUUSD-symbol-description"]=["Emas / Dollar A.S."],e.exports["#XPDUSD-symbol-description"]=["CFD pada Palladium"],e.exports["#XPTUSD-symbol-description"]=["Platinum / Dollar A.S."],e.exports["#ZS1-symbol-description"]=["Kontrak Berjangka Kacang Kedelai - ECBT"],e.exports["#ZW1-symbol-description"]=["Kontrak Berjangka Gandum - ECBT"],e.exports["#BTCGBP-symbol-description"]=["Bitcoin/Pound Inggris"],e.exports["#MICEXINDEXCF-symbol-description"]=["Indeks MOEX Rusia"],e.exports["#BTCAUD-symbol-description"]=["Bitcoin/Dollar Australia"],e.exports["#BTCJPY-symbol-description"]=["Bitcoin/Yen Jepang"],e.exports["#BTCBRL-symbol-description"]=["Bitcoin/Real Brazil"],e.exports["#PT10-symbol-description"]=["Obligasi Pemerintah Portugal 10 th"],e.exports["#TXSX-symbol-description"]=["Indeks TSX 60"],e.exports["#VIXC-symbol-description"]=["Indeks TSX 60 VIX"],e.exports["#USDPLN-symbol-description"]=["USD/PLN"],e.exports["#EURPLN-symbol-description"]=["EUR/PLN"],e.exports["#BTCPLN-symbol-description"]=["Bitcoin/Zloty Polandia"],e.exports["#CAC40-symbol-description"]=["Indeks CAC 40"],e.exports["#XBTCAD-symbol-description"]=["Bitcoin / Dollar Kanada"],e.exports["#ITI2!-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIF2018-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIF2019-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIF2020-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIG2018-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIG2019-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIG2020-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIH2018-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIH2019-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIH2020-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIJ2018-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIJ2019-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIJ2020-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIK2018-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIK2019-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIK2020-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIM2017-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIM2018-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIM2019-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIM2020-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIN2017-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIN2018-symbol-description"]=["Kontrak Berjangka Bijih Besi"],
e.exports["#ITIN2019-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIN2020-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIQ2017-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIQ2018-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIQ2019-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIQ2020-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIU2017-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIU2018-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIU2019-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIU2020-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIV2017-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIV2018-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIV2019-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIV2020-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIX2017-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIX2018-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIX2019-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIX2020-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIZ2017-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIZ2018-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIZ2019-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#ITIZ2020-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#AMEX:GXF-symbol-description"]=["ETF Global x FTSE Wilayah Nordik"],e.exports["#ASX:XAF-symbol-description"]=["Indeks S&P/ASX Seluruh Australia 50"],e.exports["#ASX:XAT-symbol-description"]=["Indeks S&P/ASX Seluruh Australia 200"],e.exports["#BIST:XU100-symbol-description"]=["Indeks BIST 100"],e.exports["#GPW:WIG20-symbol-description"]=["Indeks WIG20"],e.exports["#INDEX:JKSE-symbol-description"]=["Indeks Komposit Jakarta"],e.exports["#INDEX:KLSE-symbol-description"]=["Indeks KLCI Bursa Malaysia"],e.exports["#INDEX:NZD-symbol-description"]=["Indeks NZX 50"],e.exports["#INDEX:STI-symbol-description"]=["Indeks STI"],e.exports["#INDEX:XLY0-symbol-description"]=["Indeks Komposit Shanghai"],e.exports["#MOEX:MICEXINDEXCF-symbol-description"]=["Indeks MOEX Rusia"],e.exports["#NYMEX:KT1!-symbol-description"]=["Kontrak Berjangka Kopi"],e.exports["#OANDA:NATGASUSD-symbol-description"]=["CFD pada Gas Alam"],e.exports["#OANDA:USDPLN-symbol-description"]=["USD/PLN"],e.exports["#TSX:TX60-symbol-description"]=["Indeks S&P/TSX"],e.exports["#TSX:VBU-symbol-description"]=["Indeks Agregat Obligasi ETF Vanguard US (CAD-ter-hedge) UN"],e.exports["#TSX:VIXC-symbol-description"]=["Indeks S&P/TSX 60 VIX"],e.exports["#TVC:CAC40-symbol-description"]=["Indeks CAC 40"],e.exports["#TVC:ES10-symbol-description"]=["Obligasi Pemerintah Spanyol 10 TH"],e.exports["#TVC:EUBUND-symbol-description"]=["Bund Euro"],
e.exports["#TVC:GB02-symbol-description"]=["Obligasi Pemerintah Inggris 2 TH"],e.exports["#TVC:GB10-symbol-description"]=["Obligasi Pemerintah Inggris 10 TH"],e.exports["#TVC:GOLD-symbol-description"]=["CFD pada Emas ($AS/OZ)"],e.exports["#TVC:ID03-symbol-description"]=["Obligasi Pemerintah Indonesia 3 TH"],e.exports["#TVC:ID10-symbol-description"]=["Obligasi Pemerintah Indonesia 10 TH"],e.exports["#TVC:PALLADIUM-symbol-description"]=["CFD pada Paladium ($AS/OZ)"],e.exports["#TVC:PT10-symbol-description"]=["Obligasi Pemerintah Portugal 10 TH"],e.exports["#TVC:SILVER-symbol-description"]=["CFD pada Perak ($AS/OZ)"],e.exports["#TVC:RUT-symbol-description"]=["Indeks Russell 2000"],e.exports["#TSX:TSX-symbol-description"]=["Indeks Komposit S&P/TSX"],e.exports["#OANDA:CH20CHF-symbol-description"]=["Indeks Swiss 20"],e.exports["#TVC:SHCOMP-symbol-description"]=["Indeks Komposit Shanghai"],e.exports["#NZX:ALLC-symbol-description"]=["SELURUH Indeks S&P/NZX (Indeks Kapital)"],e.exports["#AMEX:SHYG-symbol-description"]=["Saham 0-5 TAHUN ETF Obligasi Perusahan Dengan Hasil Tinggi"],e.exports["#TVC:AU10-symbol-description"]=["Obligasi Pemerintah Australia 10 TH"],e.exports["#TVC:CN10-symbol-description"]=["Obligasi Pemerintah Cina 10 TH"],e.exports["#TVC:KR10-symbol-description"]=["Obligasi Pemerintah Korea 10 TH"],e.exports["#NYMEX:RB1!-symbol-description"]=["Kontrak Berjangka Bensin RBOB"],e.exports["#NYMEX:HO1!-symbol-description"]=["Kontrak Berjangka Pelabuhan NY ULSD"],e.exports["#NYMEX:AEZ1!-symbol-description"]=["Kontrak Berjangka Ethanol NY"],e.exports["#OANDA:XCUUSD-symbol-description"]=["CFD pada Tembaga (US / lb)"],e.exports["#COMEX:ZA1!-symbol-description"]=["Kontrak Berjangka Seng"],e.exports["#CBOT:ZW1!-symbol-description"]=["Kontrak Berjangka Gandum"],e.exports["#NYMEX:KA1!-symbol-description"]=["Kontrak Berjangka Gula #11"],e.exports["#CBOT:QBC1!-symbol-description"]=["Kontrak Berjangka Jagung"],e.exports["#CME:E61!-symbol-description"]=["Kontrak Berjangka Euro"],e.exports["#CME:B61!-symbol-description"]=["Kontrak Berjangka Pound Inggris"],e.exports["#CME:QJY1!-symbol-description"]=["Kontrak Berjangka Yen Jepang"],e.exports["#CME:A61!-symbol-description"]=["Kontrak Berjangka Dollar Australia"],e.exports["#CME:D61!-symbol-description"]=["Kontrak Berjangka Dollar Kanada"],e.exports["#CME:SP1!-symbol-description"]=["Kontrak Berjangka S&P 500"],e.exports["#CME_MINI:NQ1!-symbol-description"]=["Kontrak Berjangka NASDAQ 100 E-MINI"],e.exports["#CBOT_MINI:YM1!-symbol-description"]=["Kontrak Berjangka E-MINI DOW JONES ($5)"],e.exports["#CME:NY1!-symbol-description"]=["Kontrak Berjangka NIKKEI 225"],e.exports["#EUREX:DY1!-symbol-description"]=["Indeks DAX"],e.exports["#CME:IF1!-symbol-description"]=["Kontrak Berjangka Indeks IBOVESPA-$AS"],e.exports["#CBOT:TY1!-symbol-description"]=["Kontrak Berjangka T-Note 10 Tahun"],e.exports["#CBOT:FV1!-symbol-description"]=["Kontrak Berjangka T-Note 5 Tahun"],e.exports["#CBOT:ZE1!-symbol-description"]=["Catatan Departemen Keuangan - Kontrak Berjangka 3 Tahun"],
e.exports["#CBOT:TU1!-symbol-description"]=["Kontrak Berjangka T-Note 2 Tahun"],e.exports["#CBOT:FF1!-symbol-description"]=["Kontrak Berjangka Suku Bunga Dana FED 30-Hari"],e.exports["#CBOT:US1!-symbol-description"]=["Kontrak Berjangka T-Bond"],e.exports["#TVC:EXY-symbol-description"]=["Indeks Mata Uang Euro"],e.exports["#TVC:JXY-symbol-description"]=["Indeks Mata Uang Yen Jepang"],e.exports["#TVC:BXY-symbol-description"]=["Indeks Mata Uang Pound Inggris"],e.exports["#TVC:AXY-symbol-description"]=["Indeks Mata Uang Dollar Australia"],e.exports["#TVC:CXY-symbol-description"]=["Indeks Mata Uang Dollar Kanada"],e.exports["#FRED:GDP-symbol-description"]=["Produk Domestik Bruto, 1 Desimal"],e.exports["#FRED:UNRATE-symbol-description"]=["Tingkat Pengangguran Warga Sipil"],e.exports["#FRED:POP-symbol-description"]=["Total Populasi. Seluruh Usia Termasuk Pasukan Bersenjata Di Luar Negeri"],e.exports["#ETHUSD-symbol-description"]=["Ethereum / Dollar"],e.exports["#BMFBOVESPA:IBOV-symbol-description"]=["Indeks IBovespa"],e.exports["#BMFBOVESPA:IBRA-symbol-description"]=["Indeks IBrasil"],e.exports["#BMFBOVESPA:IBXL-symbol-description"]=["Indeks IBRX 50"],e.exports["#COMEX:HG1!-symbol-description"]=["Kontrak Berjangka Tembaga"],e.exports["#INDEX:HSCE-symbol-description"]=["Indeks Perusahaan Cina Hang Seng"],e.exports["#NYMEX:CL1!-symbol-description"]=["Kontrak Berjangka Minyak Mentah Ringan"],e.exports["#OTC:IHRMF-symbol-description"]=["Ishares MSCI Jepang SHS"],e.exports["#TVC:DAX-symbol-description"]=["Indeks dari 30 Perusahaan Mayor Jerman"],e.exports["#TVC:DE10-symbol-description"]=["Obligasi Pemerintah Jerman 10 TH"],e.exports["#TVC:DJI-symbol-description"]=["Indeks Rata-rata Industri Dow Jones"],e.exports["#TVC:DXY-symbol-description"]=["Indeks Mata Uang Dollar A.S."],e.exports["#TVC:FR10-symbol-description"]=["Obligasi Pemerintah Perancis 10 TH"],e.exports["#TVC:HSI-symbol-description"]=["Indeks Hang Seng"],e.exports["#TVC:IBEX35-symbol-description"]=["Indeks IBEX 35"],e.exports["#FX:AUS200-symbol-description"]=["Indeks S&P/ASX"],e.exports["#AMEX:SHY-symbol-description"]=["ETF Obligasi Departemen Keuangan 1-3 Tahun Ishares"],e.exports["#ASX:XJO-symbol-description"]=["Indeks S&P/ASX 200"],e.exports["#BSE:SENSEX-symbol-description"]=["Indeks S&P BSE Sensex"],e.exports["#INDEX:MIB-symbol-description"]=["Indeks MIB"],e.exports["#INDEX:MOY0-symbol-description"]=["Indeks Euro STOXX 50"],e.exports["#MOEX:RTSI-symbol-description"]=["Indeks RTS"],e.exports["#NSE:NIFTY-symbol-description"]=["Indeks Nifty 50"],e.exports["#NYMEX:NG1!-symbol-description"]=["Kontrak Berjangka Gas Alam"],e.exports["#NYMEX:ZC1!-symbol-description"]=["Kontrak Berjangka Jagung"],e.exports["#TVC:IN10-symbol-description"]=["Obligasi Pemerintah India 10 TH"],e.exports["#TVC:IT10-symbol-description"]=["Obligasi Pemerintah Italia 10 TH"],e.exports["#TVC:JP10-symbol-description"]=["Obligasi Pemerintah Jepang 10 TH"],e.exports["#TVC:NDX-symbol-description"]=["Indeks US 100"],e.exports["#TVC:NI225-symbol-description"]=["NIKKEI 225"],
e.exports["#TVC:SPX-symbol-description"]=["S&P 500"],e.exports["#TVC:SX5E-symbol-description"]=["Indeks STOXX 50"],e.exports["#TVC:TR10-symbol-description"]=["Obligasi Pemerintah Turki 10 TH"],e.exports["#TVC:UKOIL-symbol-description"]=["CFD pada Minyak Mentah Brent"],e.exports["#TVC:UKX-symbol-description"]=["Indeks UK 100"],e.exports["#TVC:US02-symbol-description"]=["Obligasi Pemerintah AS 2 TH"],e.exports["#TVC:US05-symbol-description"]=["Obligasi Pemerintah AS 5 TH"],e.exports["#TVC:US10-symbol-description"]=["Obligasi Pemerintah AS 10 TH"],e.exports["#TVC:USOIL-symbol-description"]=["CFD pada Minyak Mentah WTI"],e.exports["#NYMEX:ITI1!-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#NASDAQ:SHY-symbol-description"]=["ETF Obligasi Departemen Keuangan 1-3 Tahun Ishares"],e.exports["#AMEX:ALD-symbol-description"]=["ETF Hutang Lokal Asia WisdomTree"],e.exports["#NASDAQ:AMD-symbol-description"]="Advanced Micro Devices Inc",e.exports["#NYSE:BABA-symbol-description"]="Alibaba Group Holdings Ltd.",e.exports["#ICEEUR:CB-symbol-description"]=["Brent Minyak Mentah"],e.exports["#ICEEUR:CB1!-symbol-description"]=["Minyak Mentah Brent"],e.exports["#ICEUSA:CC-symbol-description"]=["Kakao"],e.exports["#NYMEX:CL-symbol-description"]=["WTI Minyak Mentah"],e.exports["#ICEUSA:CT-symbol-description"]=["Kapas #2"],e.exports["#NASDAQ:CTRV-symbol-description"]="ContraVir Pharmaceuticals Inc",e.exports["#CME:DL-symbol-description"]=["Susu Kelas III"],e.exports["#NYSE:F-symbol-description"]="FORD MTR CO DEL",e.exports["#MOEX:GAZP-symbol-description"]="GAZPROM",e.exports["#COMEX:GC-symbol-description"]=["Emas"],e.exports["#CME:GF-symbol-description"]=["Ternak Pengumpan"],e.exports["#CME:HE-symbol-description"]="Lean Hogs",e.exports["#NASDAQ:IEF-symbol-description"]=["ETF Obligasi Departemen Keuangan Ishares 7-10 Tahun"],e.exports["#NASDAQ:IEI-symbol-description"]=["ETF Obligasi Departemen Keuangan Ishares 3-7 Tahun"],e.exports["#NYMEX:KA1-symbol-description"]=["Kontrak Berjangka Gula #11"],e.exports["#ICEUSA:KC-symbol-description"]=["Kopi"],e.exports["#NYMEX:KG1-symbol-description"]=["Kontrak Berjangka Kapas"],e.exports["#FWB:KT1-symbol-description"]=["Key Tronic Corр"],e.exports["#CME:LE-symbol-description"]=["Ternak Hidup"],e.exports["#ICEEUR:LO-symbol-description"]=["Minyak Pemanas ICE"],e.exports["#CME:LS-symbol-description"]=["Kayu"],e.exports["#MOEX:MGNT-symbol-description"]="MAGNIT",e.exports["#LSIN:MNOD-symbol-description"]="ADR GMK NORILSKIYNIKEL ORD SHS [REPO]",e.exports["#NYMEX:NG-symbol-description"]=["Gas Alam"],e.exports["#ICEUSA:OJ-symbol-description"]=["Jus Jeruk"],e.exports["#NYMEX:PA-symbol-description"]="Palladium",e.exports["#NYSE:PBR-symbol-description"]="PETROLEO BRASILEIRO SA PETROBR",e.exports["#NYMEX:PL-symbol-description"]="Platinum",e.exports["#COMEX_MINI:QC-symbol-description"]=["Tembaga E-Mini"],e.exports["#NYMEX:RB-symbol-description"]=["Bensin RBOB"],e.exports["#NYMEX:RB1-symbol-description"]=["Kontrak Berjangka Bensin RBOB"],e.exports["#MOEX:SBER-symbol-description"]="SBERBANK",
e.exports["#AMEX:SCHO-symbol-description"]=["ETF Departemen Keuangan AS Jangka Pendek Schwab"],e.exports["#COMEX:SI-symbol-description"]=["Perak"],e.exports["#NASDAQ:TLT-symbol-description"]=["ETF Obligasi Departemen Keuangan 20+ Tahun Ishares"],e.exports["#TVC:VIX-symbol-description"]=["Indeks Volatilitas S&P 500"],e.exports["#MOEX:VTBR-symbol-description"]="VTB",e.exports["#COMEX:ZA-symbol-description"]=["Seng"],e.exports["#CBOT:ZC-symbol-description"]=["Jagung"],e.exports["#CBOT:ZK-symbol-description"]=["Kontrak Berjangka Ethanol"],e.exports["#CBOT:ZL-symbol-description"]=["Minyak Kedelai"],e.exports["#CBOT:ZO-symbol-description"]="Oats",e.exports["#CBOT:ZR-symbol-description"]=["Beras Gabah"],e.exports["#CBOT:ZS-symbol-description"]=["Kacang Kedelai"],e.exports["#CBOT:ZS1-symbol-description"]=["Kontrak Berjangka Kacang Kedelai"],e.exports["#CBOT:ZW-symbol-description"]=["Gandum"],e.exports["#CBOT:ZW1-symbol-description"]=["Kontrak Berjangka Gandum - ECBT"],e.exports["#NASDAQ:ITI-symbol-description"]="Iteris Inc",e.exports["#NYMEX:ITI2!-symbol-description"]=["Kontrak Berjangka Bijih Besi"],e.exports["#CADUSD-symbol-description"]=["Dollar Kanada / Dollar AS"],e.exports["#CHFUSD-symbol-description"]=["Franc Swiss / Dollar AS"],e.exports["#GPW:ACG-symbol-description"]="Acautogaz",e.exports["#JPYUSD-symbol-description"]=["Yen Jepang / Dollar AS"],e.exports["#USDAUD-symbol-description"]=["Dollar AS / Dollar Australia"],e.exports["#USDEUR-symbol-description"]=["Dollar AS / Euro"],e.exports["#USDGBP-symbol-description"]=["Dollar AS / Pound Sterling"],e.exports["#USDNZD-symbol-description"]=["Dollar AS / Dollar New Zealand"],e.exports["#UKOIL-symbol-description"]=["CFD pada Minyak Mentah (Brent)"],e.exports["#USOIL-symbol-description"]=["CFD pada Minyak Mentah (WTI)"],e.exports["#US30-symbol-description"]=["Indeks Rata-Rata Industri Dow Jones"],e.exports["#BCHUSD-symbol-description"]=["Bitcoin Cash / Dollar"],e.exports["#ETCUSD-symbol-description"]=["Ethereum Klasik / Dollar"],e.exports["#GOOG-symbol-description"]=["Alphabet Inc (Google) Kelas C"],e.exports["#LTCUSD-symbol-description"]=["Litecoin / Dollar"],e.exports["#XRPUSD-symbol-description"]=["XRP / Dollar A.S."],e.exports["#SP:SPX-symbol-description"]=["Indeks S&P500"],e.exports["#ETCBTC-symbol-description"]=["Ethereum Klasik / Bitcoin"],e.exports["#ETHBTC-symbol-description"]="Ethereum / Bitcoin",e.exports["#XRPBTC-symbol-description"]="XRP / Bitcoin",e.exports["#TVC:US30-symbol-description"]=["Obligasi Pemerintah AS 30 Th"],e.exports["#COMEX:SI1!-symbol-description"]=["Kontrak Berjangka Perak"],e.exports["#BTGUSD-symbol-description"]=["Emas Bitcoin / Dollar A.S."],e.exports["#IOTUSD-symbol-description"]=["IOTA / Dollar A.S."],e.exports["#CME:BTC1!-symbol-description"]=["Kontrak Berjangka Bitcoin CME"],e.exports["#COMEX:GC1!-symbol-description"]=["Kontrak Berjangka Emas"],e.exports["#CORNUSD-symbol-description"]=["CFD pada Jagung"],e.exports["#COTUSD-symbol-description"]=["CFD pada Kapas"],
e.exports["#DJ:DJA-symbol-description"]=["Indeks Rata-Rata Komposit Dow Jones"],e.exports["#DJ:DJI-symbol-description"]=["Indeks Rata-Rata Industri Dow Jones"],e.exports["#ETHEUR-symbol-description"]="Ethereum / Euro",e.exports["#ETHGBP-symbol-description"]=["Ethereum / Pound Inggris"],e.exports["#ETHJPY-symbol-description"]=["Ethereum / Yen Jepang"],e.exports["#EURNOK-symbol-description"]=["Euro / Krone Norwegia"],e.exports["#GBPPLN-symbol-description"]=["Pound Inggris / Zloty Polandia"],e.exports["#MOEX:BR1!-symbol-description"]=["Kontrak Berjangka Brent Oil"],e.exports["#NYMEX:KG1!-symbol-description"]=["Kontrak Berjangka Kapas"],e.exports["#NYMEX:PL1!-symbol-description"]=["Kontrak Berjangka Platinum"],e.exports["#SOYBNUSD-symbol-description"]=["CFD pada Kacang Kedelai"],e.exports["#SUGARUSD-symbol-description"]=["CFD pada Gula"],e.exports["#TVC:IXIC-symbol-description"]=["Indeks Komposit US"],e.exports["#TVC:RU-symbol-description"]=["Indeks Russell 1000"],e.exports["#USDZAR-symbol-description"]=["Dollar A.S / Rand Afrika Selatan"],e.exports["#WHEATUSD-symbol-description"]=["CFD pada Gandum"],e.exports["#XRPEUR-symbol-description"]="XRP / Euro",e.exports["#CBOT:S1!-symbol-description"]=["Kontrak Berjangka Kedelai"],e.exports["#SP:MID-symbol-description"]=["Indeks S&P 400"],e.exports["#TSX:XCUUSD-symbol-description"]=["CFD pada Tembaga"],e.exports["#TVC:NYA-symbol-description"]=["Indeks Komposit NYSE"],e.exports["#TVC:PLATINUM-symbol-description"]=["CFD pada Platimun ($AS / OZ)"],e.exports["#TVC:SSMI-symbol-description"]=["Indeks Pasar Swiss"],e.exports["#TVC:SXY-symbol-description"]=["Indeks Mata Uang Franc Swiss"],e.exports["#TVC:RUI-symbol-description"]=["Indeks Russell 1000"],e.exports["#MOEX:RI1!-symbol-description"]=["Kontrak Berjangka Indeks RTS"],e.exports["#MOEX:MX1!-symbol-description"]=["Kontrak Berjangka Indeks MICEX"],e.exports["#CBOE:BG1!-symbol-description"]=["Kontrak Berjangka CBOE Bitcoin"],e.exports["#TVC:MY10-symbol-description"]=["Obligasi Pemerintah Malaysia 10 TH"],e.exports["#CME:S61!-symbol-description"]=["Kontrak Berjangka Franc Swiss"],e.exports["#TVC:DEU30-symbol-description"]=["Indeks DAX"],e.exports["#BCHEUR-symbol-description"]="Bitcoin Cash / Euro",e.exports["#TVC:ZXY-symbol-description"]=["Indeks Mata Uang Dollar New Zealand"],e.exports["#MIL:FTSEMIB-symbol-description"]=["Indeks FTSE MIB"],e.exports["#XETR:DAX-symbol-description"]=["Indeks DAX"],e.exports["#MOEX:IMOEX-symbol-description"]=["Indeks MOEX Rusia"],e.exports["#FX:US30-symbol-description"]=["Indeks Rata-Rata Industri Dow Jones"],e.exports["#MOEX:RUAL-symbol-description"]="United Company RUSAL PLC",e.exports["#MOEX:MX2!-symbol-description"]=["Kontrak Berjangka Indeks MICEX"],e.exports["#NEOUSD-symbol-description"]=["NEO / Dollar A.S."],e.exports["#XMRUSD-symbol-description"]=["Monero  / Dollar A.S."],e.exports["#ZECUSD-symbol-description"]=["Zcash / Dollar A.S."],e.exports["#TVC:CAC-symbol-description"]=["Indeks CAC 40"],e.exports["#NASDAQ:ZS-symbol-description"]="Zscaler Inc",
e.exports["#TVC:GB10Y-symbol-description"]=["Obligasi Pemerintah Inggris Yield 10 TH"],e.exports["#TVC:AU10Y-symbol-description"]=["Obligasi Pemerintah Australia Yield 10 TH"],e.exports["#TVC:CN10Y-symbol-description"]=["Obligasi Pemerintah Cina Yield 10 TH"],e.exports["#TVC:DE10Y-symbol-description"]=["Obligasi Pemerintah Jerman Yield 10 TH"],e.exports["#TVC:ES10Y-symbol-description"]=["Obligasi Pemerintah Spanyol Yield 10 TH"],e.exports["#TVC:FR10Y-symbol-description"]=["Obligasi Pemerintah Perancis Yield 10 TH"],e.exports["#TVC:IN10Y-symbol-description"]=["Obligasi Pemerintah India Yield 10 TH"],e.exports["#TVC:IT10Y-symbol-description"]=["Obligasi Pemerintah Italia Yield 10 TH"],e.exports["#TVC:JP10Y-symbol-description"]=["Obligasi Pemerintah Jepang Yield 10 TH"],e.exports["#TVC:KR10Y-symbol-description"]=["Obligasi Pemerintah Korea Yield 10 TH"],e.exports["#TVC:MY10Y-symbol-description"]=["Obligasi Pemerintah Malaysia Yield 10 TH"],e.exports["#TVC:PT10Y-symbol-description"]=["Obligasi Pemerintah Portugis Yield 10 TH"],e.exports["#TVC:TR10Y-symbol-description"]=["Obligasi Pemerintah Turki Yield 10 TH"],e.exports["#TVC:US02Y-symbol-description"]=["Obligasi Pemerintah A.S Yield 2 TH"],e.exports["#TVC:US05Y-symbol-description"]=["Obligasi Pemerintah A.S Yield 5 TH"],e.exports["#TVC:US10Y-symbol-description"]=["Obligasi Pemerintah A.S Yield 10 TH"],e.exports["#INDEX:TWII-symbol-description"]=["Indeks Terbebani Taiwan"],e.exports["#CME:J61!-symbol-description"]=["Kontrak Berjangka Yen Jepang"],e.exports["#CME_MINI:J71!-symbol-description"]=["Kontrak Berjangka E-mini Yen Jepang"],e.exports["#CME_MINI:WM1!-symbol-description"]=["Kontrak Berjangka E-micro Yen Jepang / Dollar A.S."],e.exports["#CME:M61!-symbol-description"]=["Kontrak Berjangka Peso Meksiko"],e.exports["#CME:T61!-symbol-description"]=["Kontrak Berjangka Rand Afrika Selatan"],e.exports["#CME:SK1!-symbol-description"]=["Kontrak Berjangka Krona Swedia"],e.exports["#CME:QT1!-symbol-description"]=["Kontrak Berjangka Renminbi Cina / Dollar A.S"],e.exports["#COMEX:AUP1!-symbol-description"]=["Kontrak Berjangka Aluminum MW U.S Transaction Premium Platts (25MT)"],e.exports["#CME:L61!-symbol-description"]=["Kontrak Berjangka Real Brazil"],e.exports["#CME:WP1!-symbol-description"]=["Kontrak Berjangka Zloty Polandia"],e.exports["#CME:N61!-symbol-description"]=["Kontrak Berjangka Dollar New Zealand"],e.exports["#CME_MINI:MG1!-symbol-description"]=["Kontrak Berjangka E-micro Australian Dollar / Dollar A.S"],e.exports["#CME_MINI:WN1!-symbol-description"]=["Kontrak Berjangka E-micro Franc Swiss / Dollar A.S"],e.exports["#CME_MINI:MF1!-symbol-description"]=["Kontrak Berjangka E-micro Euro / Dollar A.S"],e.exports["#CME_MINI:E71!-symbol-description"]=["Kontrak Berjangka E-mini Euro"],e.exports["#CBOT:ZK1!-symbol-description"]=["Kontrak Berjangka Bahan Bakar Etanol Denaturisasi"],e.exports["#CME_MINI:MB1!-symbol-description"]=["Kontrak Berjangka E-micro Pound Inggris / Dollar A.S"],e.exports["#NYMEX_MINI:QU1!-symbol-description"]=["Kontrak Berjangka Bensin E-mini"],
e.exports["#NYMEX_MINI:QX1!-symbol-description"]=["Kontrak Berjangka Minyak Pemanas E-mini"],e.exports["#COMEX_MINI:QC1!-symbol-description"]=["Kontrak Berjangka Tembaga E-mini"],e.exports["#NYMEX_MINI:QG1!-symbol-description"]=["Kontrak Berjangka Gas Alam E-mini"],e.exports["#CME:E41!-symbol-description"]=["Kontrak Berjangka Dollar A.S / Lira Turki"],e.exports["#COMEX_MINI:QI1!-symbol-description"]=["Kontrak Berjangka Perak (Mini)"],e.exports["#CME:DL1!-symbol-description"]=["Kontrak Berjangka Susu, Kelas III"],e.exports["#NYMEX:UX1!-symbol-description"]=["Kontrak Berjangka Uranium"],e.exports["#CBOT:BO1!-symbol-description"]=["Kontrak Berjangka Minyak Kedelai"],e.exports["#CME:HE1!-symbol-description"]=["Kontrak Berjangka Daging Babi"],e.exports["#NYMEX:IAC1!-symbol-description"]=["Kontrak Berjangka Batu Bara New Castle"],e.exports["#NYMEX_MINI:QM1!-symbol-description"]=["Kontrak Berjangka Minyak Mentah Ringan E-mini"],e.exports["#NYMEX:JMJ1!-symbol-description"]=["Kontrak Berjangka Finansial Brent Mini"],e.exports["#COMEX:AEP1!-symbol-description"]=["Kontrak Berjangka Aluminium Premium Eropa"],e.exports["#CBOT:ZQ1!-symbol-description"]=["Kontrak Berjangka Suku Bunga Dana Federal 30 Hari"],e.exports["#CME:LE1!-symbol-description"]=["Kontrak Berjangka Ternak Hidup"],e.exports["#CME:UP1!-symbol-description"]=["Kontrak Berjangka Franc Swiss / Yen Jepang"],e.exports["#CBOT:ZN1!-symbol-description"]=["Kontrak Berjangka T-Note 10 Tahun"],e.exports["#CBOT:ZB1!-symbol-description"]=["Kontrak Berjangka T-Bond"],e.exports["#CME:GF1!-symbol-description"]=["Kontrak Berjangka Pengumpan Ternak"],e.exports["#CBOT:UD1!-symbol-description"]=["Kontrak Berjangka Ultra T-Bond"],e.exports["#CME:I91!-symbol-description"]=["Kontrak Berjangka Perumahan CME - Washington DC"],e.exports["#CBOT:ZO1!-symbol-description"]=["Kontrak Berjangka Oat"],e.exports["#CBOT:ZM1!-symbol-description"]=["Kontrak Berjangka Makanan Kedelai"],e.exports["#CBOT_MINI:XN1!-symbol-description"]=["Kontrak Berjangka Mini Jagung"],e.exports["#CBOT:ZC1!-symbol-description"]=["Kontrak Berjangka Jagung"],e.exports["#CME:LS1!-symbol-description"]=["Kontrak Berjangka Kayu"],e.exports["#CBOT_MINI:XW1!-symbol-description"]=["Kontrak Berjangka Gandum"],e.exports["#CBOT_MINI:XK1!-symbol-description"]=["Kontrak Berjangka Mini Kedelai"],e.exports["#CBOT:ZS1!-symbol-description"]=["Kontrak Berjangka Kedelai"],e.exports["#NYMEX:PA1!-symbol-description"]=["Kontrak Berjangka Palladium"],e.exports["#CME:FTU1!-symbol-description"]=["Kontrak Berjangka Indeks E-mini FTSE 100 USD"],e.exports["#CBOT:ZR1!-symbol-description"]=["Kontrak Berjangka Beras"],e.exports["#COMEX_MINI:GR1!-symbol-description"]=["Kontrak Berjangka Emas (E-micro)"],e.exports["#COMEX_MINI:QO1!-symbol-description"]=["Kontrak Berjangka Emas (mini)"],e.exports["#CME_MINI:RL1!-symbol-description"]=["E-mini Russell 1000"],e.exports["#CME_MINI:EW1!-symbol-description"]=["Kontrak Berjangka E-mini S&P 400"],e.exports["#COMEX:LD1!-symbol-description"]=["Kontrak Berjangka Timah"],
e.exports["#CME_MINI:ES1!-symbol-description"]=["Kontrak Berjangka E-mini S&P 500"],e.exports["#TVC:SA40-symbol-description"]=["Indeks 40 Teratas Afrika Selatan"],e.exports["#BMV:ME-symbol-description"]=["Indeks IPC Meksiko"],e.exports["#BCBA:IMV-symbol-description"]=["Indeks MERVAL"],e.exports["#HSI:HSI-symbol-description"]=["Indeks Hang Seng"],e.exports["#BVL:SPBLPGPT-symbol-description"]=["Indeks Umum S&P / BVL Peru (PEN)"],e.exports["#EGX:EGX30-symbol-description"]=["Indeks Pengembalian Harga EGX 30"],e.exports["#BVC:IGBC-symbol-description"]=["Indeks Umum dari Bursa Saham Kolombia"],e.exports["#TWSE:TAIEX-symbol-description"]=["Indeks Saham Terbebani Kapitalisasi Taiwan"],e.exports["#QSE:GNRI-symbol-description"]=["Indeks QE"],e.exports["#BME:IBC-symbol-description"]=["Indeks IBEX 35"],e.exports["#NZX:NZ50G-symbol-description"]=["Indeks S&P / NZX 50 Bruto"],e.exports["#SIX:SMI-symbol-description"]=["Indeks Pasar Swiss"],e.exports["#SZSE:399001-symbol-description"]=["Indeks Komponen SZSE"],e.exports["#TADAWUL:TASI-symbol-description"]=["Indeks Seluruh Saham Tadawul"],e.exports["#IDX:COMPOSITE-symbol-description"]=["Indeks Harga Saham Gabungan IDX"],e.exports["#EURONEXT:PX1-symbol-description"]=["Indeks CAC 40"],e.exports["#OMXHEX:OMXH25-symbol-description"]=["Indeks OMX Helsinki 25"],e.exports["#EURONEXT:BEL20-symbol-description"]=["Indeks BEL 20"],e.exports["#TVC:STI-symbol-description"]=["Indeks Straits Times"],e.exports["#DFM:DFMGI-symbol-description"]=["Indeks DFM"],e.exports["#TVC:KOSPI-symbol-description"]=["Indeks Harga Saham Komposit Korea"],e.exports["#FTSEMYX:FBMKLCI-symbol-description"]=["Indeks KLCI FTSE Bursa Malaysia"],e.exports["#TASE:TA35-symbol-description"]=["Indeks TA-35"],e.exports["#OMXSTO:OMXS30-symbol-description"]=["Indeks OMX Stockholm 30"],e.exports["#OMXICE:OMXI8-symbol-description"]=["Indeks OMX Islandia 8"],e.exports["#NSENG:NSE30-symbol-description"]=["Indeks NSE 30"],e.exports["#BAHRAIN:BSEX-symbol-description"]=["Indeks Seluruh Saham Bahrain"],e.exports["#OMXTSE:OMXTGI-symbol-description"]=["Indeks OMX Tallinn Global"],e.exports["#OMXCOP:OMXC25-symbol-description"]=["Indeks OMX Copenhagen 25"],e.exports["#OMXRSE:OMXRGI-symbol-description"]=["Indeks OMX Riga Global"],e.exports["#BELEX:BELEX15-symbol-description"]=["Indeks BELEX 15"],e.exports["#OMXVSE:OMXVGI-symbol-description"]=["Indeks OMX Vilnius Global"],e.exports["#EURONEXT:AEX-symbol-description"]=["Indeks AEX"],e.exports["#CBOE:VIX-symbol-description"]=["Indeks Volatilitas S&P 500"],e.exports["#NASDAQ:XAU-symbol-description"]=["Indeks Sektor Emas dan Perak PHLX"],e.exports["#DJ:DJUSCL-symbol-description"]=["Indeks Batu Bara Dow Jones A.S"],e.exports["#DJ:DJCIKC-symbol-description"]=["Indeks Komoditas Dow Jones Kopi"],e.exports["#DJ:DJCIEN-symbol-description"]=["Indeks Komoditas Dow Jones Energi"],e.exports["#NASDAQ:OSX-symbol-description"]=["Indeks Sektor Layanan Minyak PHLX"],e.exports["#DJ:DJCISB-symbol-description"]=["Indeks Komoditas Dow Jones Gula"],
e.exports["#DJ:DJCICC-symbol-description"]=["Indeks Komoditas Dow Jones Kokoa"],e.exports["#DJ:DJCIGR-symbol-description"]=["Indeks Komoditas Dow Jones Gandum"],e.exports["#DJ:DJCIAGC-symbol-description"]=["Indeks Komoditas Dow Jones Komponen Terbatas Pertanian"],e.exports["#DJ:DJCISI-symbol-description"]=["Indeks Komoditas Dow Jones Perak"],e.exports["#DJ:DJCIIK-symbol-description"]=["Indeks Komoditas Dow Jones Nikel"],e.exports["#NASDAQ:HGX-symbol-description"]=["Indeks Sektor Perumahan PHLX"],e.exports["#DJ:DJCIGC-symbol-description"]=["Indeks Komoditas Dow Jones Emas"],e.exports["#SP:SPGSCI-symbol-description"]=["Indeks Komoditas S&P Goldman Sachs"],e.exports["#NASDAQ:UTY-symbol-description"]=["Indeks Sektor Utilitas PHLX"],e.exports["#DJ:DJU-symbol-description"]=["Indeks Utilitas Rata-RataDow Jones"],e.exports["#SP:SVX-symbol-description"]=["Indeks Nilai S&P 500"],e.exports["#SP:OEX-symbol-description"]=["Indeks S&P 100"],e.exports["#CBOE:OEX-symbol-description"]=["Indeks S&P 100"],e.exports["#NASDAQ:SOX-symbol-description"]=["Indeks Semikonduktor Philadelphia"],e.exports["#RUSSELL:RUI-symbol-description"]=["Indeks Russell 1000"],e.exports["#RUSSELL:RUA-symbol-description"]=["Indeks Russell 3000"],e.exports["#RUSSELL:RUT-symbol-description"]=["Indeks Russell 2000"],e.exports["#NYSE:XMI-symbol-description"]=["Indeks Pasar Mayor NYSE ARCA"],e.exports["#NYSE:XAX-symbol-description"]=["Indeks Komposit AMEX"],e.exports["#NASDAQ:NDX-symbol-description"]=["Indeks 100 Nasdaq"],e.exports["#NASDAQ:IXIC-symbol-description"]=["Indeks Komposit Nasdaq"],e.exports["#DJ:DJT-symbol-description"]=["Indeks Rata-Rata Transportasi Dow Jones"],e.exports["#NYSE:NYA-symbol-description"]=["Indeks Komposit NYSE"],e.exports["#NYMEX:CJ1!-symbol-description"]=["Kontrak Berjangka Kokoa"],e.exports["#USDILS-symbol-description"]=["Dollar A.S. / Shekel Israel"],e.exports["#TSXV:F-symbol-description"]="Fiore Gold Inc",e.exports["#SIX:F-symbol-description"]=["Perusahaan Ford Motor"],e.exports["#BMV:F-symbol-description"]=["Perusahaan Ford Motor"],e.exports["#TWII-symbol-description"]=["Indeks Terbebani Taiwan"],e.exports["#TVC:PL10Y-symbol-description"]=["Obligasi Pemerintah Polandia Yield 10 TH"],e.exports["#TVC:PL05Y-symbol-description"]=["Obligasi Pemerintah Polandia Yield 5 TH"],e.exports["#SET:GC-symbol-description"]=["Perusahaan Publik Global Connections"],e.exports["#TSX:GC-symbol-description"]="Great Canadian Gaming Corporation",e.exports["#TVC:FTMIB-symbol-description"]=["Indeks Milano Italia Borsa"],e.exports["#OANDA:SPX500USD-symbol-description"]=["Indeks S&P 500"],e.exports["#BMV:CT-symbol-description"]="China SX20 RT",e.exports["#TSXV:CT-symbol-description"]=["Perusahaan Tambang Centenera"],e.exports["#BYBIT:ETHUSD-symbol-description"]=["Kontrak Berlanjut ETHUSD"],e.exports["#BYBIT:XRPUSD-symbol-description"]=["Kontrak Berlanjut XRPUSD"],e.exports["#BYBIT:BTCUSD-symbol-description"]=["Kontrak Berlanjut BTCUSD"],e.exports["#BITMEX:ETHUSD-symbol-description"]=["Kontrak Berjangka Berlanjut ETHUSD"],
e.exports["#DERIBIT:BTCUSD-symbol-description"]=["Kontrak Berjangka Berlanjut BTCUSD"],e.exports["#DERIBIT:ETHUSD-symbol-description"]=["Kontrak Berjangka Berlanjut ETHUSD"],e.exports["#USDHUF-symbol-description"]=["Dollar A.S / Forint Hungaria"],e.exports["#USDTHB-symbol-description"]=["Dollar A.S / Baht Thailand"],e.exports["#FOREXCOM:US2000-symbol-description"]=["Cap Kecil AS 2000"],e.exports["#TSXV:PBR-symbol-description"]="Para Resources Inc",e.exports["#NYSE:SI-symbol-description"]="Silvergate Capital Corporation",e.exports["#NASDAQ:LE-symbol-description"]="Lands' End Inc",e.exports["#CME:CB1!-symbol-description"]=["Kontrak Berjangka Mentega-Kas (Berlanjut: Kontrak saat ini didepan)"],e.exports["#LSE:SCHO-symbol-description"]="Scholium Group Plc Ord 1P",e.exports["#NEO:HE-symbol-description"]="Hanwei Energy Services Corp.",e.exports["#NYSE:HE-symbol-description"]=["Industri-Industri Elektrik Hawaii"],e.exports["#OMXCOP:SCHO-symbol-description"]="Schouw & Co A/S",e.exports["#TSX:HE-symbol-description"]="Hanwei Energy Services Corp.",e.exports["#BSE:ITI-symbol-description"]="ITI Ltd",e.exports["#NSE:ITI-symbol-description"]="Indian Telephone Industries Limited",e.exports["#TSX:LS-symbol-description"]=["Dana Dividen Middlefield Healthcare & Life Sciences"],e.exports["#BITMEX:XBT-symbol-description"]=["Indeks Bitcoin / Dollar A.S."],e.exports["#CME_MINI:RTY1!-symbol-description"]=["Indeks Kontrak Berjangka E-Mini Russell 2000"],e.exports["#CRYPTOCAP:TOTAL-symbol-description"]=["Total Cap Pasar Crypto, $"],e.exports["#ICEUS:DX1!-symbol-description"]=["Kontrak Berjangka Indeks Dollar A.S."],e.exports["#NYMEX:TT1!-symbol-description"]=["Kontrak Berjangka Kapas"],e.exports["#PHEMEX:BTCUSD-symbol-description"]=["Kortrak Berjangka Menerus BTC"],e.exports["#PHEMEX:ETHUSD-symbol-description"]=["Kortrak Berjangka Menerus ETH"],e.exports["#PHEMEX:XRPUSD-symbol-description"]=["Kortrak Berjangka Menerus XRP"],e.exports["#PHEMEX:LTCUSD-symbol-description"]=["Kortrak Berjangka Menerus LTC"],e.exports["#BITCOKE:BCHUSD-symbol-description"]="BCH Quanto Swap",e.exports["#BITCOKE:BTCUSD-symbol-description"]="BTC Quanto Swap",e.exports["#BITCOKE:ETHUSD-symbol-description"]="ETH Quanto Swap",e.exports["#BITCOKE:LTCUSD-symbol-description"]="LTC Quanto Swap",e.exports["#TVC:CA10-symbol-description"]=["Kanada - Obligasi Pemerintah 10 TH"],e.exports["#TVC:CA10Y-symbol-description"]=["Kanada - Obligasi Pemerintah Yield 10 TH"],e.exports["#TVC:ID10Y-symbol-description"]=["Indonesia - Obligasi Pemerintah Yield 10 TH"],e.exports["#TVC:NL10-symbol-description"]=["Belanda  - Obligasi Pemerintah 10 TH"],e.exports["#TVC:NL10Y-symbol-description"]=["Belanda - Obligasi Pemerintah Yield 10 TH"],e.exports["#TVC:NZ10-symbol-description"]=["New Zealand - Obligasi Pemerintah 10 TH"],e.exports["#TVC:NZ10Y-symbol-description"]=["New Zealand - Obligasi Pemerintah Yield 10 TH"],e.exports["#SOLUSD-symbol-description"]=["Solana / Dollar A.S."],e.exports["#LUNAUSD-symbol-description"]=["Luna / Dollar A.S."],
e.exports["#UNIUSD-symbol-description"]=["Uniswap / Dollar A.S."],e.exports["#LTCBRL-symbol-description"]=["Litecoin / Real Brazil"],e.exports["#ETCEUR-symbol-description"]="Ethereum Classic / Euro",e.exports["#ETHKRW-symbol-description"]=["Ethereum / Won Korea Selatan"],e.exports["#BTCRUB-symbol-description"]=["Bitcoin / Ruble Rusia"],e.exports["#BTCTHB-symbol-description"]=["Bitcoin / Baht Thailand"],e.exports["#ETHTHB-symbol-description"]=["Ethereum / Baht Thailand"],e.exports["#TVC:EU10YY-symbol-description"]=["Yield 10 TH Obligasi Pemerintah Euro"]}}]);
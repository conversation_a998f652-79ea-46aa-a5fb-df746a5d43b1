(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[6456],{1414:e=>{e.exports={button:"button-D4RPB3ZC",content:"content-D4RPB3ZC","icon-only":"icon-only-D4RPB3ZC",link:"link-D4RPB3ZC","color-brand":"color-brand-D4RPB3ZC","variant-primary":"variant-primary-D4RPB3ZC","variant-secondary":"variant-secondary-D4RPB3ZC","color-gray":"color-gray-D4RPB3ZC","color-green":"color-green-D4RPB3ZC","color-red":"color-red-D4RPB3ZC","color-black":"color-black-D4RPB3ZC","size-xsmall":"size-xsmall-D4RPB3ZC","start-icon-wrap":"start-icon-wrap-D4RPB3ZC","end-icon-wrap":"end-icon-wrap-D4RPB3ZC","with-start-icon":"with-start-icon-D4RPB3ZC","with-end-icon":"with-end-icon-D4RPB3ZC","size-small":"size-small-D4RPB3ZC","size-medium":"size-medium-D4RPB3ZC","size-large":"size-large-D4RPB3ZC","size-xlarge":"size-xlarge-D4RPB3ZC",animated:"animated-D4RPB3ZC",stretch:"stretch-D4RPB3ZC",grouped:"grouped-D4RPB3ZC","adjust-position":"adjust-position-D4RPB3ZC","first-row":"first-row-D4RPB3ZC","first-col":"first-col-D4RPB3ZC","no-corner-top-left":"no-corner-top-left-D4RPB3ZC","no-corner-top-right":"no-corner-top-right-D4RPB3ZC","no-corner-bottom-right":"no-corner-bottom-right-D4RPB3ZC","no-corner-bottom-left":"no-corner-bottom-left-D4RPB3ZC","text-wrap":"text-wrap-D4RPB3ZC","multiline-content":"multiline-content-D4RPB3ZC","secondary-text":"secondary-text-D4RPB3ZC","primary-text":"primary-text-D4RPB3ZC"}},88803:e=>{e.exports={"tablet-normal-breakpoint":"screen and (max-width: 768px)","small-height-breakpoint":"screen and (max-height: 360px)","tablet-small-breakpoint":"screen and (max-width: 430px)"}},55596:e=>{e.exports={dialog:"dialog-b8SxMnzX",wrapper:"wrapper-b8SxMnzX",separator:"separator-b8SxMnzX",bounded:"bounded-b8SxMnzX"}},69827:e=>{e.exports={"small-height-breakpoint":"screen and (max-height: 360px)",container:"container-BZKENkhT",unsetAlign:"unsetAlign-BZKENkhT",title:"title-BZKENkhT",subtitle:"subtitle-BZKENkhT",textWrap:"textWrap-BZKENkhT",ellipsis:"ellipsis-BZKENkhT",close:"close-BZKENkhT",icon:"icon-BZKENkhT"}},40281:e=>{e.exports={container:"container-qm7Rg5MB",inputContainer:"inputContainer-qm7Rg5MB",withCancel:"withCancel-qm7Rg5MB",input:"input-qm7Rg5MB",icon:"icon-qm7Rg5MB",cancel:"cancel-qm7Rg5MB"}},54829:e=>{e.exports={wrapper:"wrapper-nGEmjtaX",container:"container-nGEmjtaX",tab:"tab-nGEmjtaX",active:"active-nGEmjtaX",title:"title-nGEmjtaX",icon:"icon-nGEmjtaX",withoutIcon:"withoutIcon-nGEmjtaX",titleText:"titleText-nGEmjtaX",nested:"nested-nGEmjtaX",isTablet:"isTablet-nGEmjtaX",isMobile:"isMobile-nGEmjtaX",accessible:"accessible-nGEmjtaX"}},61098:e=>{e.exports={title:"title-z9fs4j4t",small:"small-z9fs4j4t",normal:"normal-z9fs4j4t",large:"large-z9fs4j4t"}},76797:e=>{e.exports={container:"container-XOHpda28"}},95988:e=>{e.exports={title:"title-cIIj4HrJ",disabled:"disabled-cIIj4HrJ",icon:"icon-cIIj4HrJ",locked:"locked-cIIj4HrJ",open:"open-cIIj4HrJ",actionIcon:"actionIcon-cIIj4HrJ",selected:"selected-cIIj4HrJ",codeIcon:"codeIcon-cIIj4HrJ",solutionIcon:"solutionIcon-cIIj4HrJ"}},60430:e=>{e.exports={
"tablet-small-breakpoint":"screen and (max-width: 430px)",container:"container-WeNdU0sq",selected:"selected-WeNdU0sq",disabled:"disabled-WeNdU0sq",favorite:"favorite-WeNdU0sq",highlighted:"highlighted-WeNdU0sq",light:"light-WeNdU0sq","highlight-animation-theme-light":"highlight-animation-theme-light-WeNdU0sq",dark:"dark-WeNdU0sq","highlight-animation-theme-dark":"highlight-animation-theme-dark-WeNdU0sq",badge:"badge-WeNdU0sq",main:"main-WeNdU0sq",paddingLeft:"paddingLeft-WeNdU0sq",author:"author-WeNdU0sq",likes:"likes-WeNdU0sq",actions:"actions-WeNdU0sq",isActive:"isActive-WeNdU0sq"}},60030:e=>{e.exports={container:"container-hrZZtP0J"}},4567:e=>{e.exports={"tablet-small-breakpoint":"screen and (max-width: 430px)",dialog:"dialog-I087YV6b",dialogLibrary:"dialogLibrary-I087YV6b",contentContainer:"contentContainer-I087YV6b",listContainer:"listContainer-I087YV6b",scroll:"scroll-I087YV6b",sidebarContainer:"sidebarContainer-I087YV6b",noContentBlock:"noContentBlock-I087YV6b",tabWithHint:"tabWithHint-I087YV6b",solution:"solution-I087YV6b"}},70722:e=>{e.exports={container:"container-QcG0kDOU",image:"image-QcG0kDOU",title:"title-QcG0kDOU",description:"description-QcG0kDOU",button:"button-QcG0kDOU"}},14877:e=>{e.exports={favorite:"favorite-_FRQhM5Y",hovered:"hovered-_FRQhM5Y",disabled:"disabled-_FRQhM5Y",active:"active-_FRQhM5Y",checked:"checked-_FRQhM5Y"}},75623:e=>{e.exports={highlighted:"highlighted-cwp8YRo6"}},45719:e=>{e.exports={separator:"separator-Pf4rIzEt"}},94720:(e,t,n)=>{"use strict";n.d(t,{Button:()=>_});var r=n(50959),i=n(97754),o=n(95604),a=n(9745),s=n(1414),l=n.n(s);function c(e){const{color:t="brand",size:n="medium",variant:r="primary",stretch:a=!1,icon:s,startIcon:c,endIcon:u,iconOnly:d=!1,className:h,isGrouped:p,cellState:f,disablePositionAdjustment:m=!1,primaryText:v,secondaryText:g,isAnchor:y=!1}=e,_=function(e){let t="";return 0!==e&&(1&e&&(t=i(t,l()["no-corner-top-left"])),2&e&&(t=i(t,l()["no-corner-top-right"])),4&e&&(t=i(t,l()["no-corner-bottom-right"])),8&e&&(t=i(t,l()["no-corner-bottom-left"]))),t}((0,o.getGroupCellRemoveRoundBorders)(f));return i(h,l().button,l()[`size-${n}`],l()[`color-${t}`],l()[`variant-${r}`],a&&l().stretch,(s||c)&&l()["with-start-icon"],u&&l()["with-end-icon"],d&&l()["icon-only"],_,p&&l().grouped,p&&!m&&l()["adjust-position"],p&&f.isTop&&l()["first-row"],p&&f.isLeft&&l()["first-col"],v&&g&&l()["multiline-content"],y&&l().link)}function u(e){const{startIcon:t,icon:n,iconOnly:i,children:o,endIcon:s,primaryText:c,secondaryText:u}=e,d=null!=t?t:n,h=!(t||n||s||i)&&!o&&c&&u;return r.createElement(r.Fragment,null,d&&r.createElement(a.Icon,{icon:d,className:l()["start-icon-wrap"]}),o&&r.createElement("span",{className:l().content},o),s&&!i&&r.createElement(a.Icon,{icon:s,className:l()["end-icon-wrap"]}),h&&function(e){return e.primaryText&&e.secondaryText&&r.createElement("div",{className:l()["text-wrap"]},r.createElement("span",{className:l()["primary-text"]}," ",e.primaryText," "),"string"==typeof e.secondaryText?r.createElement("span",{className:l()["secondary-text"]
}," ",e.secondaryText," "):r.createElement("span",{className:l()["secondary-text"]},r.createElement("span",null,e.secondaryText.firstLine),r.createElement("span",null,e.secondaryText.secondLine)))}(e))}var d=n(86332),h=n(90186);function p(e){const{className:t,color:n,variant:r,size:i,stretch:o,animated:a,icon:s,iconOnly:l,startIcon:c,endIcon:u,primaryText:d,secondaryText:p,...f}=e;return{...f,...(0,h.filterDataProps)(e),...(0,h.filterAriaProps)(e)}}function f(e){const{reference:t,...n}=e,{isGrouped:i,cellState:o,disablePositionAdjustment:a}=(0,r.useContext)(d.ControlGroupContext),s=c({...n,isGrouped:i,cellState:o,disablePositionAdjustment:a});return r.createElement("button",{...p(n),className:s,ref:t},r.createElement(u,{...n}))}function m(e="default"){switch(e){case"default":return"primary";case"stroke":return"secondary"}}function v(e="primary"){switch(e){case"primary":return"brand";case"success":return"green";case"default":return"gray";case"danger":return"red"}}function g(e="m"){switch(e){case"s":return"xsmall";case"m":return"small";case"l":return"large"}}function y(e){const{intent:t,size:n,appearance:r,useFullWidth:i,icon:o,...a}=e;return{...a,color:v(t),size:g(n),variant:m(r),stretch:i,startIcon:o}}function _(e){return r.createElement(f,{...y(e)})}},86332:(e,t,n)=>{"use strict";n.d(t,{ControlGroupContext:()=>r});const r=n(50959).createContext({isGrouped:!1,cellState:{isTop:!0,isRight:!0,isBottom:!0,isLeft:!0}})},95604:(e,t,n)=>{"use strict";function r(e){let t=0;return e.isTop&&e.isLeft||(t+=1),e.isTop&&e.isRight||(t+=2),e.isBottom&&e.isLeft||(t+=8),e.isBottom&&e.isRight||(t+=4),t}n.d(t,{getGroupCellRemoveRoundBorders:()=>r})},27267:(e,t,n)=>{"use strict";function r(e,t,n,r,i){function o(i){if(e>i.timeStamp)return;const o=i.target;void 0!==n&&null!==t&&null!==o&&o.ownerDocument===r&&(t.contains(o)||n(i))}return i.click&&r.addEventListener("click",o,!1),i.mouseDown&&r.addEventListener("mousedown",o,!1),i.touchEnd&&r.addEventListener("touchend",o,!1),i.touchStart&&r.addEventListener("touchstart",o,!1),()=>{r.removeEventListener("click",o,!1),r.removeEventListener("mousedown",o,!1),r.removeEventListener("touchend",o,!1),r.removeEventListener("touchstart",o,!1)}}n.d(t,{addOutsideEventListener:()=>r})},36383:(e,t,n)=>{"use strict";n.d(t,{useOutsideEvent:()=>o});var r=n(50959),i=n(27267);function o(e){const{click:t,mouseDown:n,touchEnd:o,touchStart:a,handler:s,reference:l,ownerDocument:c=document}=e,u=(0,r.useRef)(null),d=(0,r.useRef)(new CustomEvent("timestamp").timeStamp);return(0,r.useLayoutEffect)((()=>{const e={click:t,mouseDown:n,touchEnd:o,touchStart:a},r=l?l.current:u.current;return(0,i.addOutsideEventListener)(d.current,r,s,c,e)}),[t,n,o,a,s]),l||u}},9745:(e,t,n)=>{"use strict";n.d(t,{Icon:()=>i});var r=n(50959);const i=r.forwardRef(((e,t)=>{const{icon:n="",...i}=e;return r.createElement("span",{...i,ref:t,dangerouslySetInnerHTML:{__html:n}})}))},90186:(e,t,n)=>{"use strict";function r(e){return o(e,a)}function i(e){return o(e,s)}function o(e,t){const n=Object.entries(e).filter(t),r={};for(const[e,t]of n)r[e]=t
;return r}function a(e){const[t,n]=e;return 0===t.indexOf("data-")&&"string"==typeof n}function s(e){return 0===e[0].indexOf("aria-")}n.d(t,{filterAriaProps:()=>i,filterDataProps:()=>r,filterProps:()=>o,isAriaAttribute:()=>s,isDataAttribute:()=>a})},53017:(e,t,n)=>{"use strict";function r(e){return t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}}function i(e){return r([e])}n.d(t,{isomorphicRef:()=>i,mergeRefs:()=>r})},67961:(e,t,n)=>{"use strict";n.d(t,{OverlapManager:()=>o,getRootOverlapManager:()=>s});var r=n(50151);class i{constructor(){this._storage=[]}add(e){this._storage.push(e)}remove(e){this._storage=this._storage.filter((t=>e!==t))}has(e){return this._storage.includes(e)}getItems(){return this._storage}}class o{constructor(e=document){this._storage=new i,this._windows=new Map,this._index=0,this._document=e,this._container=e.createDocumentFragment()}setContainer(e){const t=this._container,n=null===e?this._document.createDocumentFragment():e;!function(e,t){Array.from(e.childNodes).forEach((e=>{e.nodeType===Node.ELEMENT_NODE&&t.appendChild(e)}))}(t,n),this._container=n}registerWindow(e){this._storage.has(e)||this._storage.add(e)}ensureWindow(e,t={position:"fixed",direction:"normal"}){const n=this._windows.get(e);if(void 0!==n)return n;this.registerWindow(e);const r=this._document.createElement("div");if(r.style.position=t.position,r.style.zIndex=this._index.toString(),r.dataset.id=e,void 0!==t.index){const e=this._container.childNodes.length;if(t.index>=e)this._container.appendChild(r);else if(t.index<=0)this._container.insertBefore(r,this._container.firstChild);else{const e=this._container.childNodes[t.index];this._container.insertBefore(r,e)}}else"reverse"===t.direction?this._container.insertBefore(r,this._container.firstChild):this._container.appendChild(r);return this._windows.set(e,r),++this._index,r}unregisterWindow(e){this._storage.remove(e);const t=this._windows.get(e);void 0!==t&&(null!==t.parentElement&&t.parentElement.removeChild(t),this._windows.delete(e))}getZindex(e){const t=this.ensureWindow(e);return parseInt(t.style.zIndex||"0")}moveToTop(e){if(this.getZindex(e)!==this._index){this.ensureWindow(e).style.zIndex=(++this._index).toString()}}removeWindow(e){this.unregisterWindow(e)}}const a=new WeakMap;function s(e=document){const t=e.getElementById("overlap-manager-root");if(null!==t)return(0,r.ensureDefined)(a.get(t));{const t=new o(e),n=function(e){const t=e.createElement("div");return t.style.position="absolute",t.style.zIndex=150..toString(),t.style.top="0px",t.style.left="0px",t.id="overlap-manager-root",t}(e);return a.set(n,t),t.setContainer(n),e.body.appendChild(n),t}}},99054:(e,t,n)=>{"use strict";n.d(t,{setFixedBodyState:()=>c});const r=(()=>{let e;return()=>{var t;if(void 0===e){const n=document.createElement("div"),r=n.style;r.visibility="hidden",r.width="100px",r.msOverflowStyle="scrollbar",document.body.appendChild(n);const i=n.offsetWidth;n.style.overflow="scroll";const o=document.createElement("div");o.style.width="100%",n.appendChild(o);const a=o.offsetWidth
;null===(t=n.parentNode)||void 0===t||t.removeChild(n),e=i-a}return e}})();function i(e,t,n){null!==e&&e.style.setProperty(t,n)}function o(e,t){return getComputedStyle(e,null).getPropertyValue(t)}function a(e,t){return parseInt(o(e,t))}let s=0,l=!1;function c(e){const{body:t}=document,n=t.querySelector(".widgetbar-wrap");if(e&&1==++s){const e=o(t,"overflow"),s=a(t,"padding-right");"hidden"!==e.toLowerCase()&&t.scrollHeight>t.offsetHeight&&(i(n,"right",`${r()}px`),t.style.paddingRight=`${s+r()}px`,l=!0),t.classList.add("i-no-scroll")}else if(!e&&s>0&&0==--s&&(t.classList.remove("i-no-scroll"),l)){i(n,"right","0px");let e=0;0,t.scrollHeight<=t.clientHeight&&(e-=r()),t.style.paddingRight=(e<0?0:e)+"px",l=!1}}},24437:(e,t,n)=>{"use strict";n.d(t,{DialogBreakpoints:()=>i});var r=n(88803);const i={SmallHeight:r["small-height-breakpoint"],TabletSmall:r["tablet-small-breakpoint"],TabletNormal:r["tablet-normal-breakpoint"]}},35057:(e,t,n)=>{"use strict";n.d(t,{AdaptivePopupDialog:()=>N});var r=n(50959),i=n(50151);var o=n(97754),a=n.n(o),s=n(68335),l=n(38223),c=n(35749),u=n(63016),d=n(1109),h=n(24437),p=n(90692),f=n(95711);var m=n(52092),v=n(76422),g=n(9745);const y=r.createContext({setHideClose:()=>{}});var _=n(7720),b=n(69827);function C(e){const{title:t,titleTextWrap:n=!1,subtitle:i,showCloseIcon:o=!0,onClose:s,onCloseButtonKeyDown:l,renderBefore:c,renderAfter:u,draggable:d,className:h,unsetAlign:p,closeAriaLabel:f,closeButtonReference:m}=e,[v,C]=(0,r.useState)(!1);return r.createElement(y.Provider,{value:{setHideClose:C}},r.createElement("div",{className:a()(b.container,h,(i||p)&&b.unsetAlign)},c,r.createElement("div",{"data-dragg-area":d,className:b.title},r.createElement("div",{className:a()(n?b.textWrap:b.ellipsis)},t),i&&r.createElement("div",{className:a()(b.ellipsis,b.subtitle)},i)),u,o&&!v&&r.createElement("button",{className:b.close,onClick:s,onKeyDown:l,"data-name":"close","aria-label":f,type:"button",ref:m},r.createElement(g.Icon,{className:b.icon,icon:_,"data-name":"close","data-role":"button"}))))}var E=n(53017),w=n(90186),k=n(55596);const x={vertical:20},S={vertical:0};class N extends r.PureComponent{constructor(){super(...arguments),this._controller=null,this._reference=null,this._orientationMediaQuery=null,this._renderChildren=(e,t)=>(this._controller=e,this.props.render({requestResize:this._requestResize,centerAndFit:this._centerAndFit,isSmallWidth:t})),this._handleReference=e=>this._reference=e,this._handleCloseBtnClick=()=>{this.props.onKeyboardClose&&this.props.onKeyboardClose(),this._handleClose()},this._handleClose=()=>{this.props.onClose()},this._handleOpen=()=>{void 0!==this.props.onOpen&&this.props.isOpened&&this.props.onOpen(this.props.fullScreen||window.matchMedia(h.DialogBreakpoints.TabletSmall).matches)},this._handleKeyDown=e=>{if(!e.defaultPrevented){if(this.props.onKeyDown&&this.props.onKeyDown(e),27===(0,s.hashFromEvent)(e)){if(e.defaultPrevented)return;if(this.props.forceCloseOnEsc&&this.props.forceCloseOnEsc())return this.props.onKeyboardClose&&this.props.onKeyboardClose(),
void this._handleClose();const{activeElement:n}=document,r=(0,i.ensureNotNull)(this._reference);if(null!==n){if(e.preventDefault(),"true"===(t=n).getAttribute("data-haspopup")&&"true"!==t.getAttribute("data-expanded"))return void this._handleClose();if((0,c.isTextEditingField)(n))return void r.focus();if(r.contains(n))return this.props.onKeyboardClose&&this.props.onKeyboardClose(),void this._handleClose()}}var t,n;(function(e){if("function"==typeof e)return e();return Boolean(e)})(this.props.disableTabNavigationContainment)||(n=e,[9,s.Modifiers.Shift+9].includes((0,s.hashFromEvent)(n))&&n.stopPropagation())}},this._requestResize=()=>{null!==this._controller&&this._controller.recalculateBounds()},this._centerAndFit=()=>{null!==this._controller&&this._controller.centerAndFit()},this._calculatePositionWithOffsets=(e,t)=>{const n=(0,i.ensureDefined)(this.props.fullScreenViewOffsets).value();return{top:n.top,left:(0,l.isRtl)()?-n.right:n.left,width:t.clientWidth-n.left-n.right,height:t.clientHeight-n.top-n.bottom}}}componentDidMount(){var e,t;this.props.ignoreClosePopupsAndDialog||v.subscribe(m.CLOSE_POPUPS_AND_DIALOGS_COMMAND,this._handleClose,null),this._handleOpen(),void 0!==this.props.onOpen&&(this._orientationMediaQuery=window.matchMedia("(orientation: portrait)"),e=this._orientationMediaQuery,t=this._handleOpen,(null==e?void 0:e.addEventListener)?e.addEventListener("change",t):e.addListener(t)),this.props.fullScreenViewOffsets&&this.props.fullScreen&&this.props.fullScreenViewOffsets.subscribe(this._requestResize)}componentWillUnmount(){var e,t;this.props.ignoreClosePopupsAndDialog||v.unsubscribe(m.CLOSE_POPUPS_AND_DIALOGS_COMMAND,this._handleClose,null),null!==this._orientationMediaQuery&&(e=this._orientationMediaQuery,t=this._handleOpen,(null==e?void 0:e.removeEventListener)?e.removeEventListener("change",t):e.removeListener(t)),this.props.fullScreenViewOffsets&&this.props.fullScreen&&this.props.fullScreenViewOffsets.unsubscribe(this._requestResize)}focus(){(0,i.ensureNotNull)(this._reference).focus()}getElement(){return this._reference}contains(e){var t,n;return null!==(n=null===(t=this._reference)||void 0===t?void 0:t.contains(e))&&void 0!==n&&n}render(){const{className:e,wrapperClassName:t,headerClassName:n,isOpened:i,title:o,titleTextWrap:s,dataName:l,onClickOutside:c,additionalElementPos:m,additionalHeaderElement:v,backdrop:g,shouldForceFocus:y=!0,shouldReturnFocus:_,onForceFocus:b,showSeparator:N,subtitle:P,draggable:I=!0,fullScreen:D=!1,showCloseIcon:R=!0,rounded:B=!0,isAnimationEnabled:T,growPoint:L,dialogTooltip:A,unsetHeaderAlign:M,onDragStart:z,dataDialogName:O,closeAriaLabel:F,containerAriaLabel:j,reference:W,containerTabIndex:H,closeButtonReference:q,onCloseButtonKeyDown:Z,shadowed:U,fullScreenViewOffsets:K}=this.props,V="after"!==m?v:void 0,G="after"===m?v:void 0,$="string"==typeof o?o:O||"",Y=(0,w.filterDataProps)(this.props),X=(0,E.mergeRefs)([this._handleReference,W]);return r.createElement(p.MatchMedia,{rule:h.DialogBreakpoints.SmallHeight},(m=>r.createElement(p.MatchMedia,{
rule:h.DialogBreakpoints.TabletSmall},(h=>r.createElement(u.PopupDialog,{rounded:!(h||D)&&B,className:a()(k.dialog,D&&K&&k.bounded,e),isOpened:i,reference:X,onKeyDown:this._handleKeyDown,onClickOutside:c,onClickBackdrop:c,fullscreen:h||D,guard:m?S:x,boundByScreen:h||D,shouldForceFocus:y,onForceFocus:b,shouldReturnFocus:_,backdrop:g,draggable:I,isAnimationEnabled:T,growPoint:L,name:this.props.dataName,dialogTooltip:A,onDragStart:z,containerAriaLabel:j,containerTabIndex:H,calculateDialogPosition:D&&K?this._calculatePositionWithOffsets:void 0,shadowed:U,...Y},r.createElement("div",{className:a()(k.wrapper,t),"data-name":l,"data-dialog-name":$},void 0!==o&&r.createElement(C,{draggable:I&&!(h||D),onClose:this._handleCloseBtnClick,renderAfter:G,renderBefore:V,subtitle:P,title:o,titleTextWrap:s,showCloseIcon:R,className:n,unsetAlign:M,closeAriaLabel:F,closeButtonReference:q,onCloseButtonKeyDown:Z}),N&&r.createElement(d.Separator,{className:k.separator}),r.createElement(f.PopupContext.Consumer,null,(e=>this._renderChildren(e,h||D)))))))))}}},69654:(e,t,n)=>{"use strict";n.d(t,{DialogSearch:()=>u});var r=n(50959),i=n(97754),o=n.n(i),a=n(44352),s=n(9745),l=n(69859),c=n(40281);function u(e){const{children:t,renderInput:i,onCancel:u,containerClassName:h,inputContainerClassName:p,iconClassName:f,...m}=e;return r.createElement("div",{className:o()(c.container,h)},r.createElement("div",{className:o()(c.inputContainer,p,u&&c.withCancel)},i||r.createElement(d,{...m})),t,r.createElement(s.Icon,{className:o()(c.icon,f),icon:l}),u&&r.createElement("div",{className:c.cancel,onClick:u},a.t(null,void 0,n(20036))))}function d(e){const{className:t,reference:n,value:i,onChange:a,onFocus:s,onBlur:l,onKeyDown:u,onSelect:d,placeholder:h,...p}=e;return r.createElement("input",{...p,ref:n,type:"text",className:o()(t,c.input),autoComplete:"off","data-role":"search",placeholder:h,value:i,onChange:a,onFocus:s,onBlur:l,onSelect:d,onKeyDown:u})}},22265:(e,t,n)=>{"use strict";n.d(t,{DialogSidebarContainer:()=>u,DialogSidebarItem:()=>h,DialogSidebarWrapper:()=>d});var r=n(50959),i=n(97754),o=n.n(i),a=n(9745),s=n(65631),l=n(68648),c=n(54829);function u(e){const{mode:t,className:n,...i}=e,{isMobile:a,isTablet:l}=(0,s.getSidebarMode)(t),u=o()(c.container,l&&c.isTablet,a&&c.isMobile,n);return r.createElement("div",{...i,className:u,"data-role":"dialog-sidebar"})}function d(e){return r.createElement("div",{className:c.wrapper,...e})}function h(e){const{mode:t,title:n,icon:i,isActive:u,onClick:d,tag:h="div",reference:p,className:f,...m}=e,{isMobile:v,isTablet:g}=(0,s.getSidebarMode)(t),y=u?null==i?void 0:i.active:null==i?void 0:i.default;return r.createElement(h,{...m,ref:p,className:o()(c.tab,g&&c.isTablet,v&&c.isMobile,u&&c.active,f),onClick:d},i&&r.createElement(a.Icon,{className:c.icon,icon:y}),!g&&r.createElement("span",{className:o()(c.title,!i&&c.withoutIcon)},r.createElement("span",{className:c.titleText},n),v&&r.createElement(a.Icon,{className:c.nested,icon:l})))}},65631:(e,t,n)=>{"use strict";function r(e){return{isMobile:"mobile"===e,
isTablet:"tablet"===e}}n.d(t,{getSidebarMode:()=>r})},57979:(e,t,n)=>{"use strict";n.r(t),n.d(t,{IndicatorsLibraryContainer:()=>ce});var r=n(50959),i=n(962),o=n(44352),a=n(88348);const s=o.t(null,void 0,n(44463));var l=n(42856),c=n(14483),u=n(92249);function d(e,t){const n=e.title.toLowerCase(),r=t.title.toLowerCase();return n<r?-1:n>r?1:0}const h={earning:new RegExp("EPS"),earnings:new RegExp("EPS"),"trailing twelve months":new RegExp("TTM")};function p(e){var t;const{id:r,description:i,shortDescription:a,description_localized:u,is_hidden_study:d,version:h,extra:p,tags:m}=e,v=c.enabled("graying_disabled_tools_enabled")&&(null===(t=window.ChartApiInstance)||void 0===t?void 0:t.studiesAccessController.isToolGrayed(i));return{id:r,title:u||o.t(i,{context:"study"},n(68716)),shortDescription:a,shortTitle:a,isStrategy:l.StudyMetaInfo.isScriptStrategy(e),isHidden:d,isNew:null==p?void 0:p.isNew,isUpdated:null==p?void 0:p.isUpdated,isBeta:null==p?void 0:p.isBeta,isPro:null==p?void 0:p.isPro,proBadgeTitle:s,isFundamental:!1,studyData:{id:r,version:h,descriptor:{type:"java",studyId:e.id},packageName:f(r,p)},isGrayed:v,tags:m}}function f(e,t){return(null==t?void 0:t.isChartPattern)?"tv-chart_patterns":(null==t?void 0:t.isAuto)?"auto-java":l.StudyMetaInfo.getPackageName(e)}var m=n(97754),v=n.n(m),g=n(63932),y=n(35057),_=n(49483),b=n(69654),C=n(22265),E=n(60030);function w(e){const{reference:t,className:n,...i}=e;return r.createElement("div",{ref:t,className:v()(E.container,n),...i,"data-role":"dialog-content"})}var k=n(95988);function x(e){const{children:t,className:n,disabled:i}=e;return r.createElement("span",{className:v()(k.title,i&&k.disabled,n)},t)}const S=r.createContext(null);var N=n(24637),P=n(77975),I=n(45345),D=n(26843),R=n(36189),B=n(68335),T=n(60430);function L(e){var t;const i=(0,r.useContext)(S),{style:a,layoutMode:s,item:l,query:c,regExpRules:u,isBeta:d,isNew:h,isUpdated:p,isSelected:f,isHighlighted:m,reference:g,onClick:y,renderActions:_,isPro:b,proBadgeTitle:C}=e,{isFavorite:E,isLocked:w,public:k,editorsPick:B}=l,L=void 0!==E,M=A(y,l),z=(0,r.useCallback)((e=>e.stopPropagation()),[]),O=null!==(t=e.favoriteClickHandler)&&void 0!==t?t:(null==i?void 0:i.toggleFavorite)?A(i.toggleFavorite,l):void 0,F=(0,P.useWatchedValueReadonly)({watchedValue:I.watchedTheme})===D.StdTheme.Dark?T.dark:T.light,j=v()(T.container,l.isGrayed&&T.disabled,f&&T.selected,m&&T.highlighted,m&&F);return r.createElement("div",{ref:g,className:j,onClick:M,style:a,"data-role":"list-item","data-disabled":l.isGrayed,"data-title":l.title,"data-id":l.id},r.createElement("div",{className:v()(T.main,!L&&T.paddingLeft)},L&&r.createElement(R.FavoriteButton,{className:v()(T.favorite,E&&T.isActive),isFilled:E,onClick:O}),r.createElement(x,{disabled:l.isGrayed},r.createElement(N.HighlightedText,{queryString:c,rules:u,text:l.title})),!1,d&&r.createElement(BadgeIndicator,{type:"beta",className:T.badge}),h&&r.createElement(BadgeIndicator,{type:"new",className:T.badge}),p&&r.createElement(BadgeIndicator,{type:"updated",className:T.badge
}),B&&r.createElement(BadgeIndicator,{type:"ep",className:T.badge,tooltip:o.t(null,void 0,n(92490))}),!1),k&&r.createElement("a",{href:k.authorLink,className:T.author,target:"_blank",onClick:z},k.authorName),"mobile"!==s&&k&&r.createElement("span",{className:T.likes},k.likesCount),!1)}function A(e,t){return n=>{const r=0===(0,B.modifiersFromEvent)(n)&&0===n.button;!n.defaultPrevented&&e&&r&&(n.preventDefault(),e(t))}}var M=n(61098);function z(e){const{title:t,type:n,className:i}=e;return r.createElement("h3",{className:v()(M.title,"Small"===n&&M.small,"Normal"===n&&M.normal,"Large"===n&&M.large,i)},t)}var O=n(76797);function F(e){const{style:t,children:n}=e;return r.createElement("div",{style:t,className:O.container},n)}var j=n(9745),W=n(94720),H=n(70722);function q(e){const{className:t,icon:n,title:i,description:o,buttonText:a,buttonAction:s}=e;return r.createElement("div",{className:v()(H.container,t)},n&&r.createElement(j.Icon,{icon:n,className:H.image}),i&&r.createElement("h3",{className:H.title},i),o&&r.createElement("p",{className:H.description},o),a&&s&&r.createElement(W.Button,{onClick:s,className:H.button},a))}function Z(e){const[t,n]=(0,r.useState)(null);function i(e){return e.findIndex((e=>(null==t?void 0:t.id)===e.id))}return[t,n,function(){n(function(){var n;const r=i(e),o=r===e.length-1;return null===t||-1===r?null!==(n=e[0])&&void 0!==n?n:null:o?e[r]:e[r+1]}())},function(){n(function(){var n;const r=i(e);return null===t||0===r||-1===r?null!==(n=e[0])&&void 0!==n?n:null:e[r-1]}())}]}var U=n(19785),K=n(4567),V=n(57898),G=n(56840);const $=new V.Delegate,Y=new V.Delegate,X=new V.Delegate;let J=[];function Q(e){return-1===te(e)?(function(e){!ee(e)&&(J.push(e),re(),$.fire(e))}(e),!0):(function(e){const t=te(e);-1!==t&&(J.splice(t,1),re(),Y.fire(e))}(e),!1)}function ee(e){return-1!==te(e)}function te(e){return J.indexOf(e)}function ne(){var e,t;J=[];const n=Boolean(void 0===(0,G.getValue)("chart.favoriteLibraryIndicators")),r=(0,G.getJSON)("chart.favoriteLibraryIndicators",[]);if(J.push(...r),0===J.length&&n&&"undefined"!=typeof window){const n=JSON.parse(null!==(t=null===(e=window.urlParams)||void 0===e?void 0:e.favorites)&&void 0!==t?t:"{}").indicators;n&&Array.isArray(n)&&J.push(...n)}X.fire()}function re(){const e=J.slice();(0,G.setJSON)("chart.favoriteLibraryIndicators",e)}function ie(e){const{reference:t,data:i,isOpened:a,onClose:s,applyStudy:l,shouldReturnFocus:c}=e,[u,d]=(0,r.useState)(""),p=(0,r.useMemo)((()=>(0,U.createRegExpList)(u,h)),[u]),f=(0,r.useMemo)((()=>u?(0,U.rankedSearch)({data:i,rules:p,queryString:u,primaryKey:"shortDescription",secondaryKey:"title",optionalPrimaryKey:"shortTitle",tertiaryKey:"tags"}):i),[u,p,i]),{highlightedItem:m,selectedItem:E,selectedNodeReference:k,scrollContainerRef:x,searchInputRef:S,onClickStudy:N,handleKeyDown:P}=function(e,t,n,i){let o=0;const[a,s]=(0,r.useState)(null),l=(0,r.useRef)(null),c=(0,r.useRef)(null),[u,d,h,p]=Z(t),f=(0,r.useRef)(null);return(0,r.useEffect)((()=>{e?m(0):d(null)}),[e]),(0,r.useEffect)((()=>{void 0!==i&&(m(0),d(null))}),[i]),(0,
r.useEffect)((()=>(a&&(o=setTimeout((()=>{s(null)}),1500)),()=>{clearInterval(o)})),[a]),{highlightedItem:a,scrollContainerRef:l,selectedNodeReference:c,selectedItem:u,searchInputRef:f,onClickStudy:function(e){n&&(n(e),d(e),s(e))},handleKeyDown:function(e){const[t,r]=function(e,t){if(null===e.current||null===t.current)return[0,0];const n=e.current.getBoundingClientRect(),r=t.current.getBoundingClientRect(),{height:i}=n,o=n.top-r.top,a=n.bottom-r.bottom+i<0?0:i,s=o-i>0?0:i,{scrollTop:l}=t.current;return[l-s,l+a]}(c,l);if(40===(0,B.hashFromEvent)(e)&&(e.preventDefault(),h(),m(r)),38===(0,B.hashFromEvent)(e)&&(e.preventDefault(),p(),m(t)),13===(0,B.hashFromEvent)(e)&&u){if(!n)return;n(u),s(u)}}};function m(e){null!==l.current&&l.current.scrollTo&&l.current.scrollTo(0,e)}}(a,f,l),I=""===u&&!f.length;return(0,r.useEffect)((()=>{var e;a||d(""),_.CheckMobile.any()||null===(e=S.current)||void 0===e||e.focus()}),[a]),r.createElement(y.AdaptivePopupDialog,{isOpened:a,onClose:s,onClickOutside:s,className:v()(K.dialogLibrary),render:function(){return r.createElement(r.Fragment,null,r.createElement(b.DialogSearch,{reference:S,placeholder:o.t(null,void 0,n(52298)),onChange:D,onFocus:R}),r.createElement(C.DialogSidebarWrapper,null,r.createElement(w,{reference:x,className:K.scroll},I?r.createElement(g.Spinner,null):f.length?r.createElement(r.Fragment,null,r.createElement(F,null,r.createElement(z,{title:o.t(null,void 0,n(15491))})),f.slice().sort(oe).map((e=>{const t=(null==E?void 0:E.id)===e.id;return r.createElement(L,{key:e.id,item:e,onClick:()=>N(e),query:u,regExpRules:p,reference:t?k:void 0,isSelected:(null==E?void 0:E.id)===e.id,isHighlighted:(null==m?void 0:m.id)===e.id,favoriteClickHandler:t=>{t.stopPropagation(),Q(e.title)}})}))):r.createElement(q,{className:K.noContentBlock,description:o.t(null,void 0,n(53170))}))))},title:o.t(null,void 0,n(61142)),dataName:"indicators-dialog",onKeyDown:P,shouldReturnFocus:c,ref:t});function D(e){d(e.target.value)}function R(){var e;u.length>0&&(null===(e=S.current)||void 0===e||e.select())}}function oe(e,t){return e.isFavorite===t.isFavorite?0:e.isFavorite?-1:1}ne(),G.onSync.subscribe(null,ne);var ae=n(76422),se=n(97145);function le(e,t){return e[t]||[]}class ce extends class{constructor(e){this._searchInputRef=r.createRef(),this._dialog=r.createRef(),this._visibility=new se.WatchedValue(!1),this._container=document.createElement("div"),this._isForceRender=!1,this._parentSources=[],this._isDestroyed=!1,this._deepFundamentalsHistoryNotificationHasBeenShown=!1,this._showDeepFundamentalsHistoryNotification=()=>{},this._chartWidgetCollection=e}isDestroyed(){return this._isDestroyed}visible(){return this._visibility.readonly()}resetAllStudies(){}updateFavorites(){}open(e,t,n,r,i){this._parentSources=e,this._updateSymbol(),this._setProps({isOpened:!0,shouldReturnFocus:null==i?void 0:i.shouldReturnFocus}),this._visibility.setValue(!0),ae.emit("indicators_dialog")}show(e){this.open([],void 0,void 0,void 0,e)}hide(){this._parentSources=[],this._setProps({isOpened:!1}),this._visibility.setValue(!1)}
destroy(){this._isDestroyed=!0,i.unmountComponentAtNode(this._container)}_shouldPreventRender(){return this._isDestroyed||!this._isForceRender&&!this._getProps().value().isOpened}_getRenderData(){return{props:this._getProps().value(),container:this._getContainer()}}_applyStudy(e,t){var n;e.isGrayed?ae.emit("onGrayedObjectClicked",{type:"study",name:e.shortDescription}):(_.CheckMobile.any()||null===(n=this._searchInputRef.current)||void 0===n||n.select(),async function(e,t,n,r,i){const o=e.activeChartWidget.value();if(!o)return null;const{studyData:s}=t;if(!s)return Promise.resolve(null);const l=s.descriptor;if("java"===l.type){const e=(0,u.tryFindStudyLineToolNameByStudyId)(l.studyId);if(null!==e)return a.tool.setValue(e),null}return o.insertStudy(s.descriptor,n,t.shortDescription)}(this._chartWidgetCollection,e,this._parentSources,0,this._symbol).then((()=>{var e;window.is_authenticated;_.CheckMobile.any()||(null===document.activeElement||document.activeElement===document.body||null!==this._dialog.current&&this._dialog.current.contains(document.activeElement))&&(null===(e=this._searchInputRef.current)||void 0===e||e.focus())})))}_setProps(e){const t=this._getProps().value(),{isOpened:n}=t;this._isForceRender=n&&"isOpened"in e&&!e.isOpened;const r={...t,...e};this._getProps().setValue(r)}_requestBuiltInJavaStudies(){return this._chartWidgetCollection.activeChartWidget.value().metaInfoRepository().findAllJavaStudies()}_focus(){var e;this._getProps().value().isOpened&&(null===(e=this._dialog.current)||void 0===e||e.focus())}_getContainer(){return this._container}_getDialog(){return this._dialog}_getSymbol(){return this._symbol}_updateSymbol(){this._symbol=void 0}}{constructor(e,t){super(e),this._options={onWidget:!1},this._indicatorData=[],t&&(this._options=t),this._props=new se.WatchedValue({data:[],applyStudy:this._applyStudy.bind(this),isOpened:!1,reference:this._getDialog(),onClose:this.hide.bind(this)}),this._getProps().subscribe(this._render.bind(this)),this._init()}_getProps(){return this._props}async _init(){const e=function(e){const t={};return e.forEach((e=>{const{studyData:n}=e;if(!n)return;const{packageName:r}=n;r in t?t[r].push(e):t[r]=[e]})),t}(function(e,t=!0){return e.filter((e=>{const n=!!t||!function(e){return e.isStrategy}(e);return!e.isHidden&&n}))}((await this._requestBuiltInJavaStudies()).map(p)));this._indicatorData=await async function(e,t){let n={...t};return[...le(n,"tv-basicstudies"),...le(n,"Script$STD"),...le(n,"tv-volumebyprice")].filter((e=>!e.isStrategy)).sort(d)}(this._options.onWidget,e),this._setFavorites(),this._setProps({data:this._indicatorData}),$.subscribe(null,(()=>this._refreshFavorites())),Y.subscribe(null,(()=>this._refreshFavorites()))}_setFavorites(){c.enabled("items_favoriting")&&this._indicatorData.forEach((e=>{e.isFavorite=ee(e.title)}))}_refreshFavorites(){this._setFavorites(),this._setProps({data:this._indicatorData})}_render(){if(this._shouldPreventRender())return;const{props:e,container:t}=this._getRenderData();i.render(r.createElement(ie,{...e}),t)}}},
36189:(e,t,n)=>{"use strict";n.d(t,{FavoriteButton:()=>d});var r=n(44352),i=n(50959),o=n(97754),a=n(9745),s=n(39146),l=n(48010),c=n(14877);const u={add:r.t(null,void 0,n(44629)),remove:r.t(null,void 0,n(72482))};function d(e){const{className:t,isFilled:n,isActive:r,onClick:d,...h}=e;return i.createElement(a.Icon,{...h,className:o(c.favorite,"apply-common-tooltip",n&&c.checked,r&&c.active,t),icon:n?s:l,onClick:d,title:n?u.remove:u.add})}},19785:(e,t,n)=>{"use strict";n.d(t,{createRegExpList:()=>o,getHighlightedChars:()=>a,rankedSearch:()=>i});var r=n(1722);function i(e){const{data:t,rules:n,queryString:i,isPreventedFromFiltering:o,primaryKey:a,secondaryKey:s=a,optionalPrimaryKey:l,tertiaryKey:c}=e;return t.map((e=>{const t=l&&e[l]?e[l]:e[a],o=e[s],u=c&&e[c];let d,h=0;return n.forEach((e=>{var n,a,s,l,c;const{re:p,fullMatch:f}=e;if(p.lastIndex=0,(0,r.isString)(t)&&t&&t.toLowerCase()===i.toLowerCase())return h=4,void(d=null===(n=t.match(f))||void 0===n?void 0:n.index);if((0,r.isString)(t)&&f.test(t))return h=3,void(d=null===(a=t.match(f))||void 0===a?void 0:a.index);if((0,r.isString)(o)&&f.test(o))return h=2,void(d=null===(s=o.match(f))||void 0===s?void 0:s.index);if((0,r.isString)(o)&&p.test(o))return h=2,void(d=null===(l=o.match(p))||void 0===l?void 0:l.index);if(Array.isArray(u))for(const e of u)if(f.test(e))return h=1,void(d=null===(c=e.match(f))||void 0===c?void 0:c.index)})),{matchPriority:h,matchIndex:d,item:e}})).filter((e=>o||e.matchPriority)).sort(((e,t)=>{if(e.matchPriority<t.matchPriority)return 1;if(e.matchPriority>t.matchPriority)return-1;if(e.matchPriority===t.matchPriority){if(void 0===e.matchIndex||void 0===t.matchIndex)return 0;if(e.matchIndex>t.matchIndex)return 1;if(e.matchIndex<t.matchIndex)return-1}return 0})).map((({item:e})=>e))}function o(e,t){const n=[],r=e.toLowerCase(),i=e.split("").map(((e,t)=>`(${0!==t?`[/\\s-]${s(e)}`:s(e)})`)).join("(.*?)")+"(.*)";return n.push({fullMatch:new RegExp(`(${s(e)})`,"i"),re:new RegExp(`^${i}`,"i"),reserveRe:new RegExp(i,"i"),fuzzyHighlight:!0}),t&&t.hasOwnProperty(r)&&n.push({fullMatch:t[r],re:t[r],fuzzyHighlight:!1}),n}function a(e,t,n){const r=[];return e&&n?(n.forEach((e=>{const{fullMatch:n,re:i,reserveRe:o}=e;n.lastIndex=0,i.lastIndex=0;const a=n.exec(t),s=a||i.exec(t)||o&&o.exec(t);if(e.fuzzyHighlight=!a,s)if(e.fuzzyHighlight){let e=s.index;for(let t=1;t<s.length;t++){const n=s[t],i=s[t].length;if(t%2){const t=n.startsWith(" ")||n.startsWith("/")||n.startsWith("-");r[t?e+1:e]=!0}e+=i}}else for(let e=0;e<s[0].length;e++)r[s.index+e]=!0})),r):r}function s(e){return e.replace(/[!-/[-^{-}?]/g,"\\$&")}},24637:(e,t,n)=>{"use strict";n.d(t,{HighlightedText:()=>s});var r=n(50959),i=n(97754),o=n(19785),a=n(75623);function s(e){const{queryString:t,rules:n,text:s,className:l}=e,c=(0,r.useMemo)((()=>(0,o.getHighlightedChars)(t,s,n)),[t,n,s]);return r.createElement(r.Fragment,null,c.length?s.split("").map(((e,t)=>r.createElement(r.Fragment,{key:t},c[t]?r.createElement("span",{className:i(a.highlighted,l)},e):r.createElement("span",null,e)))):s)}},77975:(e,t,n)=>{
"use strict";n.d(t,{useWatchedValueReadonly:()=>i});var r=n(50959);const i=(e,t=!1)=>{const n="watchedValue"in e?e.watchedValue:void 0,i="defaultValue"in e?e.defaultValue:e.watchedValue.value(),[o,a]=(0,r.useState)(n?n.value():i);return(t?r.useLayoutEffect:r.useEffect)((()=>{if(n){a(n.value());const e=e=>a(e);return n.subscribe(e),()=>n.unsubscribe(e)}return()=>{}}),[n]),o}},90692:(e,t,n)=>{"use strict";n.d(t,{MatchMedia:()=>i});var r=n(50959);class i extends r.PureComponent{constructor(e){super(e),this._handleChange=()=>{this.forceUpdate()},this.state={query:window.matchMedia(this.props.rule)}}componentDidMount(){this._subscribe(this.state.query)}componentDidUpdate(e,t){this.state.query!==t.query&&(this._unsubscribe(t.query),this._subscribe(this.state.query))}componentWillUnmount(){this._unsubscribe(this.state.query)}render(){return this.props.children(this.state.query.matches)}static getDerivedStateFromProps(e,t){return e.rule!==t.query.media?{query:window.matchMedia(e.rule)}:null}_subscribe(e){e.addListener(this._handleChange)}_unsubscribe(e){e.removeListener(this._handleChange)}}},1109:(e,t,n)=>{"use strict";n.d(t,{Separator:()=>a});var r=n(50959),i=n(97754),o=n(45719);function a(e){return r.createElement("div",{className:i(o.separator,e.className)})}},65718:(e,t,n)=>{"use strict";n.d(t,{Portal:()=>l,PortalContext:()=>c});var r=n(50959),i=n(962),o=n(36174),a=n(67961),s=n(60508);class l extends r.PureComponent{constructor(){super(...arguments),this._uuid=(0,o.guid)()}componentWillUnmount(){this._manager().removeWindow(this._uuid)}render(){const e=this._manager().ensureWindow(this._uuid,this.props.layerOptions);return e.style.top=this.props.top||"",e.style.bottom=this.props.bottom||"",e.style.left=this.props.left||"",e.style.right=this.props.right||"",e.style.pointerEvents=this.props.pointerEvents||"",i.createPortal(r.createElement(c.Provider,{value:this},this.props.children),e)}moveToTop(){this._manager().moveToTop(this._uuid)}_manager(){return null===this.context?(0,a.getRootOverlapManager)():this.context}}l.contextType=s.SlotContext;const c=r.createContext(null)},60508:(e,t,n)=>{"use strict";n.d(t,{Slot:()=>i,SlotContext:()=>o});var r=n(50959);class i extends r.Component{shouldComponentUpdate(){return!1}render(){return r.createElement("div",{style:{position:"fixed",zIndex:150,left:0,top:0},ref:this.props.reference})}}const o=r.createContext(null)},63932:(e,t,n)=>{"use strict";n.d(t,{Spinner:()=>a});var r=n(50959),i=n(97754),o=n(58096);n(83135);function a(e){const t=i(e.className,"tv-spinner","tv-spinner--shown",`tv-spinner--size_${o.spinnerSizeMap[e.size||o.DEFAULT_SIZE]}`);return r.createElement("div",{className:t,style:e.style,role:"progressbar"})}},95257:(e,t)=>{"use strict"
;var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),l=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),p=Symbol.iterator;var f={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,v={};function g(e,t,n){this.props=e,this.context=t,this.refs=v,this.updater=n||f}function y(){}function _(e,t,n){this.props=e,this.context=t,this.refs=v,this.updater=n||f}g.prototype.isReactComponent={},g.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},g.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=g.prototype;var b=_.prototype=new y;b.constructor=_,m(b,g.prototype),b.isPureReactComponent=!0;var C=Array.isArray,E=Object.prototype.hasOwnProperty,w={current:null},k={key:!0,ref:!0,__self:!0,__source:!0};function x(e,t,r){var i,o={},a=null,s=null;if(null!=t)for(i in void 0!==t.ref&&(s=t.ref),void 0!==t.key&&(a=""+t.key),t)E.call(t,i)&&!k.hasOwnProperty(i)&&(o[i]=t[i]);var l=arguments.length-2;if(1===l)o.children=r;else if(1<l){for(var c=Array(l),u=0;u<l;u++)c[u]=arguments[u+2];o.children=c}if(e&&e.defaultProps)for(i in l=e.defaultProps)void 0===o[i]&&(o[i]=l[i]);return{$$typeof:n,type:e,key:a,ref:s,props:o,_owner:w.current}}function S(e){return"object"==typeof e&&null!==e&&e.$$typeof===n}var N=/\/+/g;function P(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function I(e,t,i,o,a){var s=typeof e;"undefined"!==s&&"boolean"!==s||(e=null);var l=!1;if(null===e)l=!0;else switch(s){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case n:case r:l=!0}}if(l)return a=a(l=e),e=""===o?"."+P(l,0):o,C(a)?(i="",null!=e&&(i=e.replace(N,"$&/")+"/"),I(a,t,i,"",(function(e){return e}))):null!=a&&(S(a)&&(a=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(a,i+(!a.key||l&&l.key===a.key?"":(""+a.key).replace(N,"$&/")+"/")+e)),t.push(a)),1;if(l=0,o=""===o?".":o+":",C(e))for(var c=0;c<e.length;c++){var u=o+P(s=e[c],c);l+=I(s,t,i,u,a)}else if(u=function(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"==typeof u)for(e=u.call(e),c=0;!(s=e.next()).done;)l+=I(s=s.value,t,i,u=o+P(s,c++),a);else if("object"===s)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.")
;return l}function D(e,t,n){if(null==e)return e;var r=[],i=0;return I(e,r,"","",(function(e){return t.call(n,e,i++)})),r}function R(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var B={current:null},T={transition:null},L={ReactCurrentDispatcher:B,ReactCurrentBatchConfig:T,ReactCurrentOwner:w};t.Children={map:D,forEach:function(e,t,n){D(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return D(e,(function(){t++})),t},toArray:function(e){return D(e,(function(e){return e}))||[]},only:function(e){if(!S(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=g,t.Fragment=i,t.Profiler=a,t.PureComponent=_,t.StrictMode=o,t.Suspense=u,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=L,t.cloneElement=function(e,t,r){if(null==e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var i=m({},e.props),o=e.key,a=e.ref,s=e._owner;if(null!=t){if(void 0!==t.ref&&(a=t.ref,s=w.current),void 0!==t.key&&(o=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(c in t)E.call(t,c)&&!k.hasOwnProperty(c)&&(i[c]=void 0===t[c]&&void 0!==l?l[c]:t[c])}var c=arguments.length-2;if(1===c)i.children=r;else if(1<c){l=Array(c);for(var u=0;u<c;u++)l[u]=arguments[u+2];i.children=l}return{$$typeof:n,type:e.type,key:o,ref:a,props:i,_owner:s}},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:s,_context:e},e.Consumer=e},t.createElement=x,t.createFactory=function(e){var t=x.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=S,t.lazy=function(e){return{$$typeof:h,_payload:{_status:-1,_result:e},_init:R}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=T.transition;T.transition={};try{e()}finally{T.transition=t}},t.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")},t.useCallback=function(e,t){return B.current.useCallback(e,t)},t.useContext=function(e){return B.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return B.current.useDeferredValue(e)},t.useEffect=function(e,t){return B.current.useEffect(e,t)},t.useId=function(){return B.current.useId()},t.useImperativeHandle=function(e,t,n){return B.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return B.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return B.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return B.current.useMemo(e,t)},t.useReducer=function(e,t,n){return B.current.useReducer(e,t,n)},t.useRef=function(e){
return B.current.useRef(e)},t.useState=function(e){return B.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return B.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return B.current.useTransition()},t.version="18.2.0"},50959:(e,t,n)=>{"use strict";e.exports=n(95257)},68648:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentcolor" stroke-width="1.3" d="M12 9l5 5-5 5"/></svg>'},7720:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 17 17" width="17" height="17" fill="currentColor"><path d="m.58 1.42.82-.82 15 15-.82.82z"/><path d="m.58 15.58 15-15 .82.82-15 15z"/></svg>'},69859:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path stroke="currentColor" d="M12.4 12.5a7 7 0 1 0-4.9 2 7 7 0 0 0 4.9-2zm0 0l5.101 5"/></svg>'},39146:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path fill="currentColor" d="M9 1l2.35 4.76 5.26.77-3.8 3.7.9 5.24L9 13l-4.7 2.47.9-5.23-3.8-3.71 5.25-.77L9 1z"/></svg>'},48010:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" d="M9 2.13l1.903 3.855.116.236.26.038 4.255.618-3.079 3.001-.188.184.044.259.727 4.237-3.805-2L9 12.434l-.233.122-3.805 2.001.727-4.237.044-.26-.188-.183-3.079-3.001 4.255-.618.26-.038.116-.236L9 2.13z"/></svg>'},20036:e=>{e.exports={ar:["إلغاء"],ca_ES:["Cancel·la"],cs:["Zrušit"],de:["Abbrechen"],el:["Άκυρο"],en:"Cancel",es:["Cancelar"],fa:["لغو"],fr:["Annuler"],he_IL:["ביטול"],hu_HU:["Törlés"],id_ID:["Batal"],it:["Annulla"],ja:["キャンセル"],ko:["취소"],ms_MY:["Batal"],nl_NL:["Annuleren"],pl:["Anuluj"],pt:["Cancelar"],ro:"Cancel",ru:["Отмена"],sv:["Avbryt"],th:["ยกเลิก"],tr:["İptal"],vi:["Hủy bỏ"],zh:["取消"],zh_TW:["取消"]}},44629:e=>{e.exports={ar:["اضف إلى القائمة التفضيلات"],ca_ES:["Afegeix a preferits"],cs:["Přidat do oblíbených"],de:["Zu Favoriten hinzufügen"],el:["Προσθήκη στα αγαπημένα"],en:"Add to favorites",es:["Añadir a favoritos"],fa:["افزودن به موارد مورد علاقه"],fr:["Ajouter aux favoris"],he_IL:["הוסף למועדפים"],hu_HU:["Hozzáadás kedvencekhez"],id_ID:["Tambah ke daftar favorit"],it:["Aggiungi ai preferiti"],ja:["お気に入りに追加"],ko:["즐겨찾기에 넣기"],ms_MY:["Tambah kepada kegemaran"],nl_NL:["Voeg toe aan favorieten"],pl:["Dodaj do ulubionych"],pt:["Adicionar aos favoritos"],ro:"Add to favorites",ru:["Добавить в избранное"],sv:["Lägg till som favorit"],th:["เพิ่มลงรายการโปรด"],tr:["Favorilere ekle"],vi:["Thêm vào mục yêu thích"],zh:["添加到收藏"],zh_TW:["加入收藏"]}},44463:e=>{e.exports={ar:["متاح لحسابات Pro"],ca_ES:"Available for Pro accounts",cs:"Available for Pro accounts",de:["Erhältlich für Pro-Konten"],el:"Available for Pro accounts",en:"Available for Pro accounts",es:["Disponible para las cuentas Pro"],fa:"Available for Pro accounts",fr:["Disponible pour les comptes Pro"],he_IL:["זמין עבור חשבונות Pro"],hu_HU:"Available for Pro accounts",id_ID:["Tersedia untuk akun Pro"],
it:["Disponibile per gli account Pro"],ja:["Proアカウントでご利用いただけます"],ko:["프로 계정에서 사용 가능"],ms_MY:["Tersedia untuk akaun Pro"],nl_NL:"Available for Pro accounts",pl:["Dostępne dla kont Pro"],pt:["Disponível para contas Pro"],ro:"Available for Pro accounts",ru:["Доступно для подписок Pro"],sv:["Tillgängligt för Proabonnenter"],th:["มีให้สำหรับบัญชี Pro"],tr:["Pro hesaplar için kullanılabilir"],vi:["Dành cho các tài khoản Pro"],zh:["适用于Pro账户"],zh_TW:["適用於Pro帳戶"]}},92490:e=>{e.exports={ar:["مختارات المحرر"],ca_ES:["Seleccions dels editors"],cs:"Editors' picks",de:["Editor's Picks"],el:"Editors' picks",en:"Editors' picks",es:["Selecciones de los editores"],fa:"Editors' picks",fr:["Choix de la rédaction"],he_IL:["בחירות העורכים"],hu_HU:"Editors' picks",id_ID:["Pilihan editor"],it:["Selezione editoriale"],ja:["エディターズ・ピック"],ko:["에디터즈 픽"],ms_MY:["Pilihan Editor"],nl_NL:"Editors' picks",pl:["Wybór Redakcji"],pt:["Sugestão da Casa"],ro:"Editors' picks",ru:["Выбор редакции"],sv:["Redaktörens val"],th:["คัดสรรโดยบรรณาธิการ"],tr:["Editörün Seçtikleri"],vi:["Biên tập viên chọn"],zh:["编辑精选"],zh_TW:["編輯精選"]}},61142:e=>{e.exports={ar:["مؤشرات"],ca_ES:["Indicadors"],cs:["Indikátory"],de:["Indikatoren"],el:["Τέχν. Δείκτες"],en:"Indicators",es:["Indicadores"],fa:["اندیکاتورها"],fr:["Indicateurs"],he_IL:["אינדיקטורים"],hu_HU:["Indikátorok"],id_ID:["Indikator"],it:["Indicatori"],ja:["インジケーター"],ko:["지표"],ms_MY:["Penunjuk"],nl_NL:["Indicatoren"],pl:["Wskaźniki"],pt:["Indicadores"],ro:"Indicators",ru:["Индикаторы"],sv:["Indikatorer"],th:["อินดิเคเตอร์"],tr:["Göstergeler"],vi:["Các chỉ báo"],zh:["指标"],zh_TW:["技術指標"]}},53170:e=>{e.exports={ar:["لا توجد مؤشرات تتوافق مع معاييرك."],ca_ES:["Cap indicador coincideix amb els vostres criteris"],cs:["Indikátory nevyhovující vaším požadavkům"],de:["Keine passenden Indikatoren zu Ihren Kriterien gefunden"],el:["Δε βρέθηκαν Τέχνικο. Δείκτες που να ταιριάζουν με τα κριτήρια αναζήτησης"],en:"No indicators matched your criteria",es:["Ningún indicador coincide con sus criterios"],fa:["هیچ اندیکاتوری با شرط شما مطابقت ندارد."],fr:["Aucuns indicateurs ne correspondent à vos critères."],he_IL:["לא נמצאו התאמות לאינדקטור"],hu_HU:["Egyetlen indikátor se felel meg a kritériumoknak."],id_ID:["Tidak ada indikator yang cocok dengan kriteria anda."],it:["Nessun indicatore corrisponde ai criteri"],ja:["条件に合致するインジケーターはありません"],ko:["찾는 지표가 없습니다."],ms_MY:["Tiada penunjuk yang sepadan dengan kriteria anda."],nl_NL:["Geen indicator voldeed aan je criteria"],pl:["Brak wskaźników spełniających twoje kryteria"],pt:["Não foram encontrados indicadores que correspondam à escolha selecionada."],ro:["No indicators matched your criteria."],ru:["Нет подходящих индикаторов."],sv:["Inga indikatorer överensstämde med dina kriterier."],th:["ไม่พบตัวชี้วัดตามเกณฑ์ของคุณ"],tr:["Kriterinize uygun gösterge bulunamadı."],vi:["Không có chỉ số nào khớp với tiêu chí của bạn."],zh:["没有符合您搜索条件的指标."],zh_TW:["沒有指標符合您的搜尋條件。"]}},15491:e=>{e.exports={ar:["اسم النص البرمجي"],ca_ES:["Nom de l'script"],cs:"Script name",de:["Skripname"],el:"Script name",
en:"Script name",es:["Nombre del script"],fa:"Script name",fr:["Nom du script"],he_IL:["שם הסקריפט"],hu_HU:["Szkript név"],id_ID:["Nama skrip"],it:["Nome script"],ja:["スクリプト名"],ko:["스크립트 이름"],ms_MY:["Nama skrip"],nl_NL:"Script name",pl:["Nazwa skryptu"],pt:["Nome do script"],ro:"Script name",ru:["Имя скрипта"],sv:["Skriptets namn"],th:["ชื่อชุดคำสั่ง"],tr:["Komut adı"],vi:["Tên Script"],zh:["脚本名称"],zh_TW:["腳本名稱"]}},52298:e=>{e.exports={ar:["بحث"],ca_ES:["Cercar"],cs:["Hledat"],de:["Suche"],el:["Αναζήτησή"],en:"Search",es:["Buscar"],fa:["جستجو"],fr:["Chercher"],he_IL:["חפש"],hu_HU:["Keresés"],id_ID:["Cari"],it:["Cerca"],ja:["検索"],ko:["찾기"],ms_MY:["Cari"],nl_NL:["Zoeken"],pl:["Szukaj"],pt:["Pesquisar"],ro:"Search",ru:["Поиск"],sv:["Sök"],th:["ค้นหา"],tr:["Ara"],vi:["Tìm kiếm"],zh:["搜索"],zh_TW:["搜尋"]}},72482:e=>{e.exports={ar:["حذف من القائمة المفضلة"],ca_ES:["Treure de preferits"],cs:["Odebrat z oblíbených"],de:["Aus Favoriten entfernen"],el:["Διαγραφή απο τα αγαπημένα"],en:"Remove from favorites",es:["Quitar de favoritos"],fa:["حذف از موارد مورد علاقه"],fr:["Retirer des favoris"],he_IL:["הסר ממועדפים"],hu_HU:["Eltávolít kedvencek közül"],id_ID:["Hilangkan dari favorit"],it:["Rimuovi dai preferiti"],ja:["お気に入りから削除"],ko:["즐겨찾기지움"],ms_MY:["Buang dari kegemaran"],nl_NL:["Verwijder van favorieten"],pl:["Usuń z ulubionych"],pt:["Remover dos favoritos"],ro:"Remove from favorites",ru:["Удалить из предпочтений"],sv:["Ta bort från favoriter"],th:["ลบออกจากรายการโปรด"],tr:["Favorilerimden çıkar"],vi:["Loại bỏ khỏi mục yêu thích"],zh:["从收藏中移除"],zh_TW:["從收藏移除"]}}}]);